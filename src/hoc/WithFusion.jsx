import React, { useState, useEffect } from 'react'

const WithFusion = ({ type, addonTextBefore, addonTextAfter, className, tip, ...otherProps }) => {
    const Target = type

    // const _className = !(addonTextBefore || addonTextAfter) && className
    
    return (
        <div className={className || ''}>
            {
                addonTextBefore && <span style={{ paddingRight: '12px' }}>{addonTextBefore}</span>
            }
            <Target {...otherProps} />
            {
                addonTextAfter && <span style={{ paddingLeft: '12px' }}>{addonTextAfter}</span>
            }
            {
                tip && <div className="tip">{tip}</div>
            }
        </div>
    )
  
}

export default WithFusion
