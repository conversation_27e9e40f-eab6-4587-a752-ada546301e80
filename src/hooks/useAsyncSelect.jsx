import React, { useState, useEffect } from 'react'
import { Select } from '@alifd/next'

const useAsyncSelect = ({ action, props, pipeResponse = x => x }) => {
    const [data, setData] = useState([])
    
    useEffect(() => {
        async function fetchData() {
            let r = await fetch(action)
            r = pipeResponse(r)
            setData(r)
        }
        fetchData()
    }, [])

    return [
        <Select {...props} dataSource={data} />,
        data
    ]
}

export default useAsyncSelect
