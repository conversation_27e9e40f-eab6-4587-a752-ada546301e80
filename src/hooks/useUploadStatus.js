/**
 * 和Lock不同提供loading和canel两个状态
 */

import React, { useState } from 'react'

export default asyncFunction => {
  const [{ status, data }, setState] = useState({
    status: 'init',
    data: null,
  })

  async function wrapper(...args) {
    setState(x => {
      return { ...x, status: 'loading' }
    })
    try {
      const data = await asyncFunction(...args)
      if (status !== 'cancelled') {
        setState(x => {
          return {
            ...x,
            status: 'success',
            data,
          }
        })
      }
    } catch (ex) {
      if (status !== 'cancelled') {
        setState(x => {
          return {
            ...x,
            status: 'error',
            error: ex,
          }
        })
      }
    }
  }

  function cancel() {
    setState(x => {
      return { ...x, status: 'cancelled' }
    })
  }

  function reset(state) {
    setState(x => {
      return { status: state, data: null }
    })
  }

  return [
    {
      status,
      data,
    },
    {
      req: wrapper,
      cancel,
      reset,
    },
  ]
}
