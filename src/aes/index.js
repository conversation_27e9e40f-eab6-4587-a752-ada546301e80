import AES from '@ali/aes-tracker';
import AESPluginPV from '@ali/aes-tracker-plugin-pv';
import AESPluginEvent from '@ali/aes-tracker-plugin-event';
import AESPluginJSError from '@ali/aes-tracker-plugin-jserror';
import AESPluginAPI from '@ali/aes-tracker-plugin-api';
import AESPluginResourceError from '@ali/aes-tracker-plugin-resourceError';
import AESPluginPerf from '@ali/aes-tracker-plugin-perf';
import AESPluginEventTiming from '@ali/aes-tracker-plugin-eventTiming';
import AESPluginLongTask from '@ali/aes-tracker-plugin-longtask';
import AESPluginEmogine from '@ali/aes-tracker-plugin-emogine';
import AESPluginSurvey from '@ali/aes-tracker-plugin-survey';
import AESPluginAutolog from '@ali/aes-tracker-plugin-autolog';


const routerList =[
    {
      path: '/',
      breadcrumbName: '卡券商品管理'
    },
    {
      path: '/edit/:id(\\d+)',
      breadcrumbName: '编辑卡券商品'
    },
    {
      path: '/launch',
      breadcrumbName: '卡券商品管理'
    },
    {
      path: '/create',
      breadcrumbName: '创建卡券商品'
    },
    {
      path: '/log/:id(\\d+)',
      breadcrumbName: '操作日志'
    },
    {
      path: '/batch/:scene',
      breadcrumbName: '批量操作'
    }
  ]

const rRegxs = routerList.map(r => {

    return {
      ...r,
      path: new RegExp('^#' + (r.path || '').replace(/:(\w+)/g, '(\\w+)')+ '$')
    } 
  })
  
  console.log('##rRegxs', rRegxs);


const aesInstall = (name, id, loginAccountType) => {
    if (window.AES) {
        return;
    }
    window.AES = new AES({
        pid: 'jEMaCT',
        user_type: '14',
        env: 'prod',
        uid: id,
        username: name,
        dim1: loginAccountType,
    });

    window.AES_CONFIG = window.AES_CONFIG || {};

    // 挂载插件
    window.AES.use([AESPluginEvent, AESPluginJSError, AESPluginAPI, AESPluginResourceError, AESPluginPerf, AESPluginEventTiming, AESPluginLongTask, AESPluginEmogine, AESPluginSurvey, AESPluginAutolog])

    window.AES.use(AESPluginPV, {
        autoPV: true,
        autoLeave: true,
        enableHash: true,
        enableHistory: true,
        getPageId: (u) => {
            try {

                if (!u.startsWith('http:') && !u.startsWith('https:')) {
                    u = `https:${u}`;
                }
                const urlObj = new URL(u);
                const hash = urlObj.hash;
                if (hash.length === 0 || hash === '#' || hash === '#/') {
                    return '首页面重定向';
                }

                const item = (rRegxs || []).find(r => r.path.test(hash))


                console.log('##hash', hash, item);

                if (item) {

                    return item.title || '其他';
                }

                return '其他'

            } catch (err) {
                console.log('AESPluginPV err', err);
                return u;
            }
        }
    });
};

export { aesInstall };
