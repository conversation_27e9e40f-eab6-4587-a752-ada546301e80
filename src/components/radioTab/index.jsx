import React, { useState, useEffect } from 'react'
import { Radio } from '@alifd/next'
import _ from 'lodash'

const RadioGroup = Radio.Group

const RadioTab = (props) => {
  const { dataSource, onChange, value, innerName = 'innerValue', ...others } = props
  const { type } = value || {}
  let radioDataSource = []
  let radioChildren = []
  dataSource && dataSource.length && dataSource.forEach(item => {
    radioDataSource.push({
      label: item.label,
      value: item.value
    })
    radioChildren.push(item.children || null)
  })

  const onRadioChange = (v) => {
    props.onChange && props.onChange({
      type: v,
      [innerName]: undefined
    })
  }
  let radioChildrenEle = null

  let radioIndex = _.findIndex(radioDataSource, { value: type })
  if (radioIndex <= 0) {
    radioIndex = 0
  }
  
  let activeRadioChildren = radioChildren[radioIndex]
  if (activeRadioChildren) {
    radioChildrenEle = React.cloneElement(activeRadioChildren, {
      ...others,
      ...activeRadioChildren.props,
      onChange: (_v) => {
        props.onChange && props.onChange({
          type,
          [innerName]: _v
        })
      },
      value: value[innerName]
    })
  }


  return (
    <div className={`radio-tab`}>
        <RadioGroup dataSource={radioDataSource} onChange={onRadioChange} value={type} />
        <div>
          {
            radioChildrenEle
          }
        </div>
    </div>
  )
}

export default RadioTab
