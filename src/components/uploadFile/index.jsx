import React, { useState } from 'react'
import { Upload, Message, Icon } from '@alifd/next'
import _ from 'lodash'

const UploadDrag = Upload.Dragger

const UploadFile = ({ HeaderCompoment, action, name = 'file', updateFileData, paramsData = {} }) => {
    const onRemove = () => {
      updateFileData({
        file: null,
        key: ''
      })
    }

    const onError = (file) => {
      console.log('onError', file)
      updateFileData({
        file,
        key: ''
      })
      if (file) {
        if (!file.response || !file.response.success) {
          Message.error((file.response && file.response.errorMsg) || '上传失败，请重新上传')
        } else {
          console.log(file.response.msg)
        }
      }
    }
  
    const onSuccess = async (file) => {
      console.log('onsuccess', file)
      if (file && file.response) {
        if (file.response.success) {
            // 获取文件key, 导入数据
            const key = _.get(file, 'response.data.key') || ''
            updateFileData({
              file,
              key
            })
            Message.success('文件上传成功')
        } else {
          Message.error(file.response.errorMsg)
        }
      }
    }

    return (
        <div style={{ padding: '16px' }}>
              <HeaderCompoment />
              <UploadDrag
                listType="text"
                action={action}
                accept=".xlsx,.xls,.csv"
                title="点击或者拖动Excel文件到虚线框内上传"
                method="post"
                limit={1}
                data={paramsData}
                // beforeUpload={beforeUpload}
                name={name}
                onRemove={onRemove}
                onSuccess={onSuccess}
                onError={onError}
              >
                <div className="next-upload-drag">
                  <p className="next-upload-drag-icon"><Icon type="upload" /></p>
                  <p className="next-upload-drag-text">点击或将文件拖拽到这里上传</p>
                  <p className="next-upload-drag-hint">支持扩展名：.xlsx、.xls、.csv</p>
                </div>
              </UploadDrag>
            </div>
    )
}

export default UploadFile
