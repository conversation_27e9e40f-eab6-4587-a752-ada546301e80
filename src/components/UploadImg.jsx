import React, { useState, useEffect, useContext, Component, useReducer } from 'react'
import { Step, Button, Grid, Select, Dialog, Message } from '@alifd/next'
import ImageUploader from '@/components/coupon/ImageUploader'

const UploadImger = (props) => {
  const [images, setImage] = useState(props.value)
  const handleRemove = props.onChange && props.onChange(null)
  const handleChange = (x) => {
    props.onChange && props.onChange(x)
    setImage(x)
  }

  return <ImageUploader
    {...props}
    onRemove={handleRemove}
    value={images}
    onChange={handleChange} />
}

export default UploadImger
