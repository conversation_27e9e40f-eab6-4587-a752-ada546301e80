import React from 'react'
import PropTypes from 'prop-types'
import { Breadcrumb } from '@alifd/next'
import './style.scss'

function renderItems(routes) {
  const paths = routes.map(i => i.path)

  return routes.map((route, index) => {
    const { breadcrumbName } = route

    return index === routes.length - 1 ? (
      <Breadcrumb.Item key={`breadcrumb-${breadcrumbName}`}>{breadcrumbName}</Breadcrumb.Item>
    ) : (
      <Breadcrumb.Item
        link={`#${paths.slice(0, index + 1).join('')}`}
        key={`breadcrumb-${breadcrumbName}`}
      >
        {breadcrumbName}
      </Breadcrumb.Item>
    )
  })
}

const MyBreadcrumb = (props = {}) => {
  const pathname = props.location.pathname
  const path = pathname ? pathname.replace('/', '').split('/') : [];
  const breadcrumbMap = props.breadcrumbMap || {}
  let routes = []
  path.forEach((item, i) => {
    const childPath = `/${path.slice(0, i + 1).join('/')}`
    if (breadcrumbMap[childPath]) {
      routes.push({
        path: childPath,
        breadcrumbName: breadcrumbMap[childPath]
      })
    }
  })

  if (routes.length <= 1) {
    return null
  }

  return (
    <div className="breadcrumb-wrap">
      <Breadcrumb separator="/" style={{ marginTop: '16px' }} routes={routes}>
        {renderItems(routes)}
      </Breadcrumb>
    </div>
  )
}

MyBreadcrumb.defaultProps = {
  location: {},
  breadcrumbMap: {}
}

MyBreadcrumb.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  location: PropTypes.object,
  // eslint-disable-next-line react/forbid-prop-types
  breadcrumbMap: PropTypes.object
}

export default MyBreadcrumb
