import React, { Component, useState, useRef } from 'react'
import { Input, Radio, Form } from '@alifd/next'

const FormItem = Form.Item
const RadioGroup = Radio.Group

const Choice = (props) => {
    const { label, formItemLayout, dataSource, ...others } = props
    const [{ value, show }, setState] = useState({ value: null, show: false })
  
    function handleChange(v) {
  
      let next = v
      if (v === 0) {
        next = null
      }
      setState({
        value: next,
        show: v > 0
      })
      props.onChange && props.onChange(next)
    }
  
    function handleValueChange(v) {
      setState(x => {
        return {
          value: v,
          show
        }
  
      })
      props.onChange && props.onChange(v)
    }  
  
    return <FormItem label={label + ":"} {...formItemLayout}>
      <RadioGroup value={props.value > 0 ? 1 : 0} onChange={handleChange} disabled={props.disabled}>
        {
            dataSource.length > 0 && dataSource.map(item => {
                return (
                    <Radio value={item.value}>{item.label}</Radio>
                )
            })
        }
        {(show || props.value > 0) && <Input
          htmlType="number"
          onChange={handleValueChange}
          disabled={props.disabled}
          value={props.value} />}
      </RadioGroup>
    </FormItem>
  }

  export default Choice
