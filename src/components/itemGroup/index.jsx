import React, { useState } from 'react'
import { Form, Input } from '@alifd/next'
import './style.scss'

const ItemGroup = (props = {}) => {
    const [data, setData] = useState(props.value || {})

    const { dataSource } = props
    const onItemChange = (_name, _v) => {
        const r = {
            ...data,
            [_name]: _v
        }
        setData(r)
        props.onChange && props.onChange(r)
    }

    return (
        <div className={`newkun-itemgroup ${props.inline ? 'next-form next-inline' : ''} ${props.className || ''}`}>
            {
                dataSource.map(item => {
                    const { label, type, name, className, labelCol, wrapperCol, ...others } = item
                    const Ele = type
                    return (
                        <Form.Item label={label} labelCol={labelCol} wrapperCol={wrapperCol} key={name} className={`next-left ${className}`}>
                            <Ele {...others} onChange={(v) => onItemChange(name, v)} value={data[name]} />
                        </Form.Item>
                    )
                })
            }
        </div>
    )
}

export default ItemGroup
