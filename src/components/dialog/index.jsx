import React from 'react'
import PropTypes from 'prop-types'
import { Dialog, Button } from '@alifd/next'

const BoreasDialog = props => {
  const FooterEle = (
    <div style={{ width: '360px' }}>
      <Button onClick={() => dialog.hide()} style={{ marginRight: '10px' }}>
        取消
      </Button>
      <Button
        type="primary"
        onClick={() => {
          dialog.hide()
          props.onOk && props.onOk()
        }}
      >
        确认
      </Button>
    </div>
  )
  const dialog = Dialog.show({
    title: props.title || '提示',
    content: props.content || '',
    footer: props.FooterEle || FooterEle
  })
}

export default BoreasDialog
