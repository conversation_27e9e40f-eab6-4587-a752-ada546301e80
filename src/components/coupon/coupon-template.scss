@font-face {
  font-family: Ali<PERSON>ba-PuHuiTi-Regular;
  src: url('../../assets/coupon-gen/font/Alibaba-PuHuiTi-Regular.ttf');
}

.coupon-template {
  width: 800px;
  height: 800px;
  position: relative;
  font-family: 'Ali<PERSON>ba-PuHuiTi-Regular';
  font-weight: 600;
  transform: scale(0.83);
  transform-origin: left top;
  padding-top: 206px;
  text-align: center;
  &-bg {
    width: 800px;
    height: 800px;
    position: absolute;
    top: 0;
    left: 0;
  }
  &-price {
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
    font-family: PingFangSC-Medium;
    color: #ffffff;
    height: 260px;
    &-unit {
      position: absolute;
      left: -91px;
      bottom: 30px;
      width: 60px;
      font-size: 100px;
      letter-spacing: 0;
      text-align: center;
      line-height: 100px;
    }
    &-number {
      height: 260px;
      font-size: 260px;
      letter-spacing: 0;
      line-height: 260px;
      vertical-align: bottom;
      display: inline-block;
      position: relative;
    }
  }
  .long {
    .coupon-template-price-number {
      height: 260px;
      font-size: 200px;
      line-height: 200px;
      padding-top: 60px;
    }
  }
}
