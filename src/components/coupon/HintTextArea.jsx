import React from 'react'
import { Input } from '@alifd/next'

const LabelTextArea = ({ hints, title, field, name, ...rest }) => {
  const text = field.getValue(hints)
  const hideTextArea = text && (name === 'id1' || name === 'id3')
  return (
    <div>
      {text && <ul className="coupon-hintTextArea-hints">
        <li>{title}</li>
        <li>{text}</li>
      </ul>}
      {!hideTextArea && <Input.TextArea {...rest} name={name} />}
    </div>
  );
}
export default LabelTextArea
