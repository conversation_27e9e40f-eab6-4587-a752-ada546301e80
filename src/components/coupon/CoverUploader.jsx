import React, { Component } from "react";

import ImagePicker from "./ImagePicker";

export default class CoverUploader extends Component {
  constructor(props) {
    super(props);

    this.state = {
      images: props.images || [],
      uploaderImage: undefined
    };
  }

  handleLogoReady = img => {
    this.onCouponGen(img);
    this.setState({ logo: img });
  };

  componentWillReceiveProps(nextProps) {
    if ("value" in nextProps) {
      this.setState({
        logo: nextProps.value ? nextProps.value.logo : null,
        cover: nextProps.value ? nextProps.value.cover : null
      });
    }
  }

  onCouponGen = logo => {
    this.props.onChange &&
      this.props.onChange({
        logo,
        cover: ''
      });
  };

  handleRemove = () => {
    this.props.onChange && this.props.onChange(null);
  };

  render() {
    const { value, field } = this.props;
    const dataSource = field.getValue('logos') || []
    const customlogo = field.getValue('customlogo')
    return (
      <ImagePicker
        disabled={this.props.disabled}
        onRemove={this.handleRemove}
        field={field}
        dataSource={dataSource.filter(d => d !== customlogo)}
        value={value && value.logo}
        text="请上传尺寸174*174px以上图片，不可超过5M，支持png、jpg、jpeg"
        onChange={this.handleLogoReady}
        maxImages={1}
      />
    );
  }
}
