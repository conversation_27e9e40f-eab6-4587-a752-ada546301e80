import React from "react";
import { Upload } from "@alifd/next";
import { request } from "@/packages/request";

import domtoimage from "./dom-to-image";
import "./coupon-template.scss";
import couponBG from "../../assets/coupon-gen/bg.png";
import logoPNG from "../../assets/coupon-gen/logo.png";
import singlePNG from "../../assets/coupon-gen/single.png";
import { releaseScreen } from "@/packages/lock";

export default class CouponTemplate extends React.Component {
  constructor() {
    super();
    this.ref = React.createRef();
  }

  upload = () => {
    return new Promise((resolve, reject) => {
      domtoimage
        .toJpeg(this.ref.current, { quality: 0.92, width: 1326, height: 1326 })
        .then(base64 => {
          // html2canvas(this.ref.current, {
          //   useCORS: true,
          //   width: 800,
          //   height: 800,
          //   backgroundColor: null
          // }).then(canvas => {
          //   const base64 = canvas.toDataURL('image/jpeg', 1)

          request("boreas://pic/uploadBase64", {
            method: "POST",
            body: JSON.stringify({ base64 })
          })
            .then(res => {
              this.props.onChange && this.props.onChange(res.data);
              resolve();
            })
            .catch(err => {
              releaseScreen();
            });
        });
    });
  };

  render() {
    const { price, num } = this.props;
    const cash = num && price && num * price
    const isLong = cash && cash.toString().length >= 4
    return (
      <div
        style={{
          overflow: "hidden",
          width: "664px",
          height: "664px"
        }}
      >
        <div className="coupon-template" ref={this.ref}>
          <img src="https://img.alicdn.com/tfs/TB1jHRsHNz1gK0jSZSgXXavwpXa-1600-1600.png" className="coupon-template-bg" onLoad={this.upload} />
          <div className={`coupon-template-price ${isLong ? 'long' : ''}`}>
            <div className="coupon-template-price-number">
              {cash}
              <span className="coupon-template-price-unit">￥</span>
            </div>
          </div>
          {/* <img src={couponBG} className="coupon-template-bg" />
          <div className="coupon-template-desc">
            {num}张 抵{num * price}元
          </div>
          <div className="coupon-template-price">
            <span className="coupon-template-price--unit">￥</span>
            <span className="coupon-template-price--number">{price}</span>
          </div>
          <div className="coupon-template-limit">
            <span className="coupon-template-limit--threshold">满{threshold}元可用店铺红包</span>
            <span className="coupon-template-limit--relativeDay">购后{relativeDay}天有效</span>
          </div>
          <img className="coupon-template-logo" src={logo} onLoad={this.upload} />
          <div className="coupon-template-brandName">
            {brandName}
          </div> */}
          {/* <img className="coupon-template-cornerlogo" src={logoPNG} /> */}
        </div>
      </div>
    );
  }
}
