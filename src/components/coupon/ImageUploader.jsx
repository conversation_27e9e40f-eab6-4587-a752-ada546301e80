import React, { Component, useState, useEffect } from 'react'
import { Upload, But<PERSON>, Message } from '@alifd/next'
import _ from 'lodash'

import PropTypes from 'prop-types'

import demoImage from '../../assets/coupon-gen/logo-demo.jpg'
import resolve from '@/packages/request/resolve'
import './image-uploader.scss'


export default class ImageUploader extends React.Component {


  constructor(props) {
    super()
    this.state = {
      images: props.value || []
    }
  }

  componentWillReceiveProps(nextProps) {
    if ('value' in nextProps) {
      this.setState({
        images: nextProps.value || []
      })
    }

  }

  onSuccess = (params) => {
    const result = this.props.successResult ? this.props.successResult(params.response) : params.response.data
    const images = this.state.images
    if (result) {
      this.setState({
        images: [...images, result]
      }, () => {
        this.props.onChange && this.props.onChange(this.state.images)
      })
    }
  }

  onError = (img) => {
    console.log(img, '-----------')
    const images = this.state.images
    this.props.onChange && this.props.onChange(this.state.images)
    Message.error(img.error || "上传失败")
  }

  handleRemove = (item) => {
    console.log(item, '------------item')
    this.setState(prevState => {
      const images = prevState.images.filter(x => x !== item.url)
      return {
        images
      }
    }, () => {
      this.props.onChange && this.props.onChange(this.state.images)
    })
  }

  beforeUpload = (file) => {
    console.log(file)
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const img = new Image();
        img.onload = () => {
          if (file.size > 500000) {
            Message.warning('图片大小不能超过2M');
            console.log('图片大小不能超过2M')
            reject('图片大小不能超过2M');
          } else if (img.width !== 750 && img.height !== 600) {
            Message.warning('图片原始尺寸小于最小规定尺寸，请重新上传图片');
            console.log('图片原始尺寸小于最小规定尺寸，请重新上传图片')
            reject('图片原始尺寸小于最小规定尺寸，请重新上传图片');
          } else {
            resolve('upload success.');
          }
        };
        img.src = reader.result;
      };
      reader.readAsDataURL(file);
    });
  }

  render() {
    const { maxImages = 4, text, hints = [], disabled, actionUrl = 'boreas://pic/upload', actionHeader, beforeUpload } = this.props;
    console.log(this.props, '-----------props')
    const action = resolve(actionUrl)
    return <div>
      <p>{text}</p>
      {hints.map(hint => <p style={{ color: "#999999", lineHeight: '18px' }}>{hint}</p>)}
      <div className='image-uploader-rows'>
        <Upload.Card
          className={disabled ? 'disabled' : ''}
          limit={maxImages}
          listType='card'
          accept="image/png, image/jpg, image/jpeg"
          onRemove={this.handleRemove}
          beforeUpload={this.props.pattern ? this.beforeUpload : beforeUpload}
          timeout={20000}
          disabled={disabled}
          action={`${action.protocol}://${action.baseurl}${action.pathname}`}
          headers={actionHeader}
          onSuccess={this.onSuccess}
          value={this.state.images.map((x, i) => {
            return {
              uid: i,
              url: x,
              downloadURL: x
            }
          })}
          onError={this.onError}
          shape="card" style={{ display: 'inline-block' }} />
      </div>
    </div>
  }
}
