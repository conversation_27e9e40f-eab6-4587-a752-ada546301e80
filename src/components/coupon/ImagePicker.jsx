import React from 'react'
import { Upload, Message, Radio, Button } from '@alifd/next'
import _ from 'lodash'

import resolve from '@/packages/request/resolve'
import './image-uploader.scss'

const RadioGroup = Radio.Group

let isDeleteing = false

export default class ImageUploader extends React.Component {
  constructor(props) {
    super()
    this.state = {
      images: props.dataSource || [],
      value: "",
      uploaderImage: null,
    }
    this.uploaderRef = React.createRef();
  }

  componentWillReceiveProps(nextProps) {
    if ('value' in nextProps) {
      this.setState({
        images: nextProps.value || []
      })
    }
  }

  onSuccess = (params) => {
    isDeleteing = false
    const { field } = this.props
    const { setValue } = field;
    const result = this.props.successResult
      ? this.props.successResult(params.response)
      : params.response.data;
    const { onChange } = this.props
    this.setState({
      uploaderState: undefined
    });
    setValue('customlogo', result)
    this.setState({
      uploaderImage: result
    })
    console.log('result: ', result)
    onChange(result)
  }

  onError = (img) => {
    this.setState({
      uploaderState: undefined
    })
    Message.error("上传失败")
  }

  removeUploaderImage = () => {
    isDeleteing = true
    const { dataSource, onChange, field } = this.props
    const { setValue } = field;
    setValue('customlogo', null)
    this.setState({
      uploaderImage: null
    });
    const selectItem = dataSource && dataSource[0];
    console.log('selectItem', selectItem)
    console.log('dataSource', dataSource)
    onChange(selectItem);
  }

  replaceUploaderImage = () => {
    this.removeUploaderImage()
    setTimeout(() => {
      document.querySelector(".next-upload-card").click();
    }, 0)
  }

  render() {
    const {
      text = "",
      disabled,
      actionUrl = "boreas://pic/upload",
      actionHeader,
      dataSource,
      value,
      onChange,
      field,
    } = this.props;
    const { getValue } = field;
    const uploaderImage = getValue('customlogo')
    const action = resolve(actionUrl)
    const uploaderValue = uploaderImage ? [uploaderImage] : []
    return (
      <div>
        <span className="image-uploader-text">{text}</span>
        <div className="image-uploader-rows">
          {dataSource.map(src => {
            return (
              <img
                src={src}
                onClick={() => {
                  onChange(src);
                }}
              />
            );
          })}
          <div
            onClick={() => {
              if (uploaderImage && !isDeleteing) {
                console.log('scarecrow2424: ', isDeleteing)
                onChange(uploaderImage);
              }
            }}
          >
            <Upload.Card
              ref={this.uploaderRef}
              className={disabled ? "disabled" : ""}
              limit={1}
              listType="card"
              accept="image/png, image/jpg, image/jpeg, image/gif, image/bmp"
              timeout={20000}
              disabled={disabled}
              action={`${action.protocol}://${action.baseurl}${action.pathname}`}
              headers={actionHeader}
              onSuccess={this.onSuccess}
              onError={this.onError}
              onRemove={this.removeUploaderImage}
              value={uploaderValue.map((x, i) => ({
                uid: i,
                url: x,
                downloadURL: x
              }))}
              shape="card"
              style={{ display: "inline-block" }}
            />
          </div>
        </div>
        <div>
          <RadioGroup
            aria-labelledby="groupId"
            value={value}
            onChange={value => {
              if (!value) {
                document.querySelector(".next-upload-card").click();
              } else {
                onChange(value);
              }
            }}
          >
            {dataSource.map((src, key) => (
              <Radio
                id={src}
                value={src}
                key={key}
                className="image-uploader-radio"
              >
                推荐 {key + 1}
              </Radio>
            ))}
            <Radio
              id={uploaderImage}
              value={uploaderImage || 0}
              className="image-uploader-radio"
            >
              自定义
            </Radio>
          </RadioGroup>
        </div>
      </div>
    );
  }
}
