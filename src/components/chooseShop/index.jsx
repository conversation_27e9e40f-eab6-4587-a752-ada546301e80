/* eslint-disable react/require-default-props */
import React, { Component } from 'react'
import PropTypes from 'prop-types'
import {
  Input, Radio, Form,
} from '@alifd/next'
import './style.scss'

const RadioGroup = Radio.Group;
const FormItem = Form.Item;

const list = [
  {
    value: 'appale',
    label: '供应商下全部门店',
  }, {
    value: 'pear',
    label: '供应商下部分门店',
  }, {
    value: 'orange',
    label: '散店',
  },
];

class ChooseShop extends Component {
  constructor(props) {
    super(props)
    this.state = {
      placeholder: '请输入供应商ID，一个卡券只能绑定在一个供应商下',
      text: '注：只能输入一个供应商id',
    }
  }

  onChange = (value) => {
    let placeholder = ''
    let text = ''
    if (value === 'appale') {
      placeholder = '请输入供应商ID，一个卡券只能绑定在一个供应商下'
      text = '注：只能输入一个供应商id'
    } else if (value === 'pear') {
      placeholder = '请输入门店ID，以「回车」避开。必须为同一个供应商下的门店'
      text = '注：只能输入同一个供应商下的门店id'
    } else {
      placeholder = '请输入散店ID，一个卡券只能绑定在一个卡券下'
      text = '注：只能输入一个散店id'
    }
    this.setState({
      placeholder,
      text,
    })
  }

  render() {
    const { placeholder, text } = this.state
    return (
      <FormItem>
        <FormItem>
          <RadioGroup dataSource={list} onChange={this.onChange} defaultValue="appale" />
        </FormItem>
        <span className="chooseshop-text">{text}</span>
        <FormItem required requiredMessage="请添加适用门店">
          <Input.TextArea
            name="chooseshop"
            style={{ width: 400, height: 100 }}
            placeholder={placeholder}
            className="chooseshop-input"
          />
        </FormItem>
      </FormItem>
    )
  }
}

export default ChooseShop
