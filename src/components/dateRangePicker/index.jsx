import React, { useState, useEffect } from 'react'
import { DatePicker } from '@alifd/next'
import formatDate from '@/utils/formatDate'

const dateRangePicker = (props) => {
    const { range, onChange, className, value = {}, ...otherProps } = props
    const [startValue, setStartValue] = useState(null)
    const [endValue, setEndValue] = useState(null)
    const [endOpen, setEndOpen] = useState(false)

    const disabledStartDate = (v) => {
        const start = v && v.valueOf()
        const end = endValue && endValue.valueOf()
        const now = new Date(new Date().toLocaleDateString()).getTime()
        return start < now || (end && start > end) 
    }

    const disabledEndDate = (v) => {
        const start = startValue && startValue.valueOf()
        const end = v && v.valueOf()
        const now = new Date(new Date().toLocaleDateString()).getTime()
        const range = props.range || 999999
        return end < now || end < start || (start && range && (end - start > range * 24 * 3600 * 1000))
    }

    const onStartChange = (v) => {
        setStartValue(v)
        props.onChange && props.onChange({
            startTime: v,
            endTime: endValue
        })
    }

    const onEndChange = (v) => {
        setEndValue(v)
        props.onChange && props.onChange({
            startTime: startValue,
            endTime: v
        })
    }

    const handleStartOpenChange = (v) => {
        !v && setEndOpen(true)
    }

    const handleEndOpenChange = (v) => {
        setEndOpen(v)
    }

    return (
        <div className={`datarangepicker ${className}`}>
            <DatePicker
              disabledDate={disabledStartDate}
              placeholder="开始日期"
              {...otherProps}
              value={value.startTime}
              onChange={onStartChange}
              onVisibleChange={handleStartOpenChange}
            />
            <span style={{ margin: '0 6px', color: '#999' }}>-</span>
            <DatePicker
              disabledDate={disabledEndDate}
              placeholder="结束日期"
              {...otherProps}
              value={value.endTime}
              onChange={onEndChange}
              visible={endOpen}
              onVisibleChange={handleEndOpenChange}
            />
        </div>
    )
}

export default dateRangePicker
