/* eslint-disable react/require-default-props */
import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { Button, Icon } from '@alifd/next'
import './style.scss'

/**
 *
 * @param {
 * title
 * buttonText
 * handleClickButton
 * }
 */

const BoreasHeader = props => (
  <div className={`boreas-header ${props.className || ''}`}>
    <div className="boreas-header-title">
      <span>
        <span>{props.title}</span>
        {props.renderCell && props.renderCell()}
      </span>
      {props.buttonText && (
        <Button type="primary" onClick={props.handleClickButton}>
          <Icon type="add" />
          {props.buttonText}
        </Button>
      )}
    </div>
    {
      props.children && <div className="boreas-header-content">{props.children}</div>
    }
  </div>
)

BoreasHeader.propTypes = {
  children: PropTypes.oneOfType([PropTypes.string, PropTypes.array, PropTypes.element]),
  handleClickButton: PropTypes.func,
  title: PropTypes.string,
  buttonText: PropTypes.string,
  renderCell: PropTypes.func
}

export default BoreasHeader
