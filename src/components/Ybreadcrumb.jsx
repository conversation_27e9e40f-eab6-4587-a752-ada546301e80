
import React, { useState, useEffect } from 'react'
import { Breadcrumb } from '@alifd/next'
import { withRouter } from "react-router";

const findArray = (list, func) => [list.find(func)].filter(Boolean)

function Ybreadcrumb({ routerList, ...router }) {
  const parentPath = findArray(routerList, x => x.path === router.location.pathname)
  const [routePath, setRoutePath] = useState(parentPath)

  useEffect(() => {
    const unHistory = router.history.listen(hisObj => {
      let newRoute = []
      if (parentPath.length) {
        newRoute = [...routePath]
        const childPath = findArray(parentPath[0].routes, x => (
          x.path === hisObj.pathname &&
          (typeof x.name === 'function' ? x.name(hisObj) : true)
        ))
        childPath.length && newRoute.push(...childPath)
      } else {
        newRoute = findItemArray(routerList, x => x.path === hisObj.pathname)
      }
      console.log(newRoute)
      setRoutePath(newRoute)
    })
    return () => {
      unHistory();
    };
  });

  return (
    <Breadcrumb separator="/">
      {
        routePath.map((item, index) => (
          <Breadcrumb.Item key={index} link={item.path}>{item.name}2222</Breadcrumb.Item>
        ))
      }
    </Breadcrumb>
  )
}

export default withRouter(Ybreadcrumb)
