import 'babel-polyfill'
import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom'
import { Provider } from 'mobx-react'
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@alife/kunlun-base'
import { HashRouter } from 'react-router-dom'
import { Message } from '@alifd/next'
import { request } from '@/packages/request'
import DomainConfigure from '@/packages/request/DomainConfigure'
import { goToLogin, goToApply } from '@/api/auth';

import { buildEnv } from '@/packages/env'
import { config } from '@/api/config'

import './index.scss'
import '@alife/theme-nr-op/dist/next.css'

const env = buildEnv()
const loginHost = DomainConfigure.login[env]
const aclHost = DomainConfigure.acl[env]

request.globalResponseMapper = json => {
  console.log('crm人群管理权限')
  console.log(json)

  let code = json.code || (json.errors && json.errors[0] && json.errors[0].code)
  let msg = json.msg || (json.errors && json.errors[0] && json.errors[0].msg)
  let data = json && json.data
  if (code === '302') {
    console.log('没有登录')
    return goToLogin(encodeURI(window.location.href))
  }

  if (code === '400') { // 没权限
    Message.error(msg)
    // https:// acl-test.alibaba-inc.com/apply/instance/index.htm?type=all&keyword=yingxiao_treasure
    return goToApply('yingxiao_treasure')
  }

  if (['401', '500'].includes(String(code))) { // 后端拦截没权限
    Message.error(msg)
    // https:// acl-test.alibaba-inc.com/apply/instance/index.htm?type=all&keyword=yingxiao_treasure
    return goToApply('yingxiao_treasure')
  }


  if (code !== "200") {
    Message.error(msg)
    throw {
      code,
      msg
    }
  }


  if (json && json.data) {
    if (json.data.subMenus && json.data.subMenus.length <= 0) {
      Message.error('没有该菜单权限，跳转去申请权限')
      return goToApply('yingxiao_treasure')
    }
    return json.data
  }
  return json
}
request.setCommonErrorHandler(res => {
  console.log('setCommonErrorHandler', res)
  if (res.ret[0].match(/FAIL_SYS_SESSION_EXPIRED/)) {
    // 跳转登录
    const url = `https://${loginHost}/ssoLogin.htm?APP_NAME=ele-newretail-play-platform&RQ_SUFFIX=.html&BACK_URL=${window.location.href}`
    Message.error('请重新登录系统')
    // https://login-test.alibaba-inc.com/ssoLogin.htm?APP_NAME=ele-newretail-marketing-rule&RQ_SUFFIX=.html&BACK_URL=https://market.wapa.taobao.com/app/eleme-b-newretail/new_kun/manjian/index.html#/?pageNum=1&pageSize=10
    if (!(/local/.test(window.location.host))) {
      window.location.href = url
    }
    return true
  }
  if (res.ret[0].match(/NO_ACL_PERSSION/)) {
    window.location.href = res.data.result || `https://${aclHost}/apply/instance/index.htm?type=permission&appName=ele-newretail-play-platform`
  }
  Message.error((res.data && res.data.errorMsg) || res.ret[0] || '接口错误')
  return true
})
request('mtop://mtop.ele.newretail.play.platform.menu.query').then(({ data }) => {
  if (data.data) {
    if (data.data.subMenus) {
      renderApp(data.data.subMenus);
    }
  }
}, (err) => {
  Message.error(err.msg || '系统错误')
})

function renderApp(layoutSource) {
  const App = require('@/app/assemble/routes').default
  // const App = require('@/app/treasure/routes.jsx').default
  ReactDOM.render(
    <Provider>
      <HashRouter>
        <Header subTitle="CRM" />
        <article className="content-wrap">
          <Sider data={layoutSource} />
          <App className="main" />
        </article>
      </HashRouter>
    </Provider>,
    document.getElementById('app')
  )
}


// if (module.hot) {
//   module.hot.accept('../../app/assemble/routes', () => {
//     console.log('render...')
//     renderApp()
//   })
// }
