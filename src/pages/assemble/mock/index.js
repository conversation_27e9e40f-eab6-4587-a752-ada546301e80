/** 接口mock的入口文件
 *
 * @import {object} mockCore mock核心方法
 */
import mockCore from '@common/js/mockCore';

// 定义mock结构
const mock = {
  apiMap: {},
};

// mock接口数据清单
// 这里添加你要的mock接口,记住先写好mock的js文件哦~
const mapApi = [
  'mtop.taobao.mt.data.get.js',
];

// 循环引用下每个js中mock数据
mapApi.map((api) => {
  mock.apiMap[api.substring(0, api.lastIndexOf('.js'))] = require(`./${api}`).default;
  return null;
});

// 执行mock替换
mockCore(mock);
