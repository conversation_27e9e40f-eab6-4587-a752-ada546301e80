import 'babel-polyfill'
import React from 'react'
import ReactD<PERSON> from 'react-dom'
import { HashRouter } from 'react-router-dom'
import { Provider } from 'mobx-react'
import { Header, Sider } from '@alife/kunlun-base'
import { Message } from '@alifd/next'
import { pid } from '@/utils/clue'
import { buildEnv } from '@/packages/env'
import _ from 'lodash'

import Router from '@/app/cardcoupon/routes'
import './index.scss'
import '@alife/theme-nr-op/dist/next.css'

import { request } from '@/app/cardcoupon/lib/request'

window.tracker.log({
  pid,
  code: 11,
  msg: '用户进入卡券页面'
});
function getToken() {
  const env = buildEnv()
  const host =
    env === 'daily' ? 'login-test.alibaba-inc.com' : 'login.alibaba-inc.com'
  const currentUrl = window.location.href
  const url = `https://${host}/ssoLogin.htm?APP_NAME=ele-newretail-item-gateway&BACK_URL=${window.encodeURIComponent(
    currentUrl,
  )}&RQ_SUFFIX=.html`
  window.location.href = url
}

const commodityPath = 'app/eleme-b-newretail/commodity_pc'
const { origin } = window.location;
const layoutSource = [{
  menuId: '101083512',
  menuParentId: '-1',
  menuTitle: '平台商品库',
  menuTitleEN: 'shoppingbag-solid',
  menuUrl: `${origin}/${commodityPath}/kunlun-std/index.html`
},
{
  menuId: '101083512',
  menuParentId: '-1',
  menuTitle: '药品SPU库',
  menuTitleEN: 'medicine-solid',
  menuUrl: `${origin}/${commodityPath}/kunlun-spu/index.html`
},
{
  menuId: '101083512',
  menuParentId: '-1',
  menuTitle: '一键搬品',
  menuTitleEN: 'fund-solid',
  menuUrl: `${origin}/${commodityPath}/kunlun-copy/index.html`
},
{
  menuId: '101083512',
  menuParentId: '-1',
  menuTitle: '卡券商品',
  menuTitleEN: 'kaquan-solid',
  menuUrl: `${window.location.origin}/app/eleme-b-newretail/new_kun/cardcoupon/index.html`
},
  {
    menuId: '101083512',
    menuParentId: '-1',
    menuTitle: '卡券商品',
    menuTitleEN: 'kaquan-solid',
    menuUrl: `${window.location.origin}/app/eleme-b-newretail/new_kun/cardcoupon/index.html`
  },
{
  menuId: '101083512',
  menuParentId: '-1',
  menuTitle: '标注平台',
  menuTitleEN: 'biaozhu-solid',
  menuUrl: 'https://boreas.kunlun.alibaba-inc.com/page/mark/index.html',
},
{
  menuId: '101083512',
  menuParentId: '-1',
  menuTitle: '商户商品清洗',
  menuTitleEN: 'transform-solid',
  menuUrl: `${origin}/${commodityPath}/kunlun-washout/index.html`
},
{
  menuId: '101083512',
  menuParentId: '-1',
  menuTitle: '商品规则',
  menuTitleEN: 'rules-solid',
  menuUrl: `${origin}/${commodityPath}/kunlun-rule/index.html`
},
{
  menuId: '101083513',
  menuParentId: '-1',
  menuTitle: '审批工作台',
  menuTitleEN: 'approve-solid',
  subMenus: [
    {
      menuId: '1',
      menuParentId: '2',
      menuTitle: '价格管控',
      menuUrl: `${origin}/${commodityPath}/kunlun-approve/index.html`,
    },
    {
      menuId: '1',
      menuParentId: '1',
      menuTitle: '产品审核',
      menuUrl: `${origin}/${commodityPath}/kunlun-category-approve/index.html`
    }
  ]
},
]

const App = () => {
  request.setCommonErrorHandler(ex => {
    let env = buildEnv()

    console.log('ex: ', ex)

    if (_.get(ex, 'data.errCode') === "LOGIN_ERROR") {
      if (!window.location.href.includes('local')) {
          getToken()
      }
    }

    if (ex.ret[0].match(/LOGIN_ERROR/)) {
      // 日常：https://boreas.kunlun.alibaba.net
      // 预发：https://pre-item.kunlun.alibaba-inc.com
      // 线上：https://item.kunlun.alibaba-inc.com
      let path = 'https://boreas.kunlun.alibaba.net'
      if (env === 'pre') {
        path = 'https://pre-item.kunlun.alibaba-inc.com'
      } else if (env === 'prod') {
        path = 'https://item.kunlun.alibaba-inc.com'
      }

      // window.location.href =
      //   'https://login.alibaba-inc.com/ssoLogin.htm?APP_NAME=ele-newretail-item-gateway&BACK_URL=' +
      //   encodeURIComponent(
      //     path + '?redirect=' +
      //     encodeURIComponent(window.location.href + '?')
      //   )
      // console.log(window.location.href)
      Message.error('请重新登录系统')
      return true
    }
    if (ex.ret[0].match(/NO_ACL_PERSSION/)) {
      window.location.href = ex.data.result
    }
    Message.error((ex.data && ex.data.errMessage) || ex.ret[0] || '接口错误')
    return true
  })

  return <Provider>
    <HashRouter>
      <Header subTitle="营销权益平台" />
      <article className="content-wrap">
        <Sider data={layoutSource} />
        <div className="right-content">
          <Router />
        </div>
      </article>
    </HashRouter>
  </Provider>
}
ReactDOM.render(<App />, document.getElementById("app"));
