import 'babel-polyfill'
import React from 'react'
import ReactD<PERSON> from 'react-dom'
import { <PERSON>h<PERSON>outer, Router } from 'react-router-dom'
import { Provider } from 'mobx-react'
import { Message, Dialog } from '@alifd/next'
import shellSDK from '@ali/shell-sdk'
import App from '@/app/benefit/routes'
import { request } from '@/packages/request'
import './index.scss'
import '@alife/theme-nr-op/dist/next.css'

request.globalResponseMapper = json => {
  if (!json.data.success) {
    if (+json.status === 401) {
      console.error('用户未登录')

      Dialog.confirm({
        title: '用户未登录',
        content: '是否跳转登录？',
        onOk: () => {
          // 跳转登录
          let backUrl = window.encodeURIComponent(window.location.href)
          window.location.assign('https://sso.rajax.me/auth/entry?from=' + backUrl)
        },
        onCancel: () => console.log('cancel')
      })
    } else {
      Message.error(json.data.errorMessage || json.msg)
      throw {
        code: json.data.errorCode,
        message: json.data.errorMessage || json.msg
      }
    }
  }

  if (json.data && json.data.value) {
    return json.data.value
  }
  return json
}

shellSDK.initialize({ platform: 'kunlun' }).then(() => {
  const history = shellSDK.createHistory()
  const hash = window.location.hash
  if (hash && hash.indexOf('plan') >= 0) {
    shellSDK.navHighlight('937588033166305574')
  }

  ReactDOM.render(
    <Provider>
      <HashRouter history={history}>
        <article className="content-wrap">
          <App />
        </article>
      </HashRouter>
    </Provider>,
    document.getElementById('app')
  )
})
