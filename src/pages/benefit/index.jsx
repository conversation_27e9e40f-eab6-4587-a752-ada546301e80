import 'babel-polyfill'
import axios from 'axios'
import React from 'react'
import ReactD<PERSON> from 'react-dom'
import { <PERSON>h<PERSON>out<PERSON> } from 'react-router-dom'
import { Provider } from 'mobx-react'
import { <PERSON><PERSON>, <PERSON>r } from '@alife/kunlun-base'
import { Message } from '@alifd/next'
import { request } from '@/packages/request'
import App from '@/app/benefit2.0/routes'
import { goAcl, goToLogin } from '@/api/check'
import _ from 'lodash'

import './index.scss'
import '@alife/theme-nr-op/dist/next.css'

request.globalResponseMapper = (json) => {
  const errCode = _.get(json, 'errCode') || _.get(json, 'errors[0].code')
  const errMessage = _.get(json, 'errMessage') || _.get(json, 'errors[0].msg')
  if (['no_menu', 'no_permission'].includes(errCode)) {
    return goAcl('ele-newretail-userights-prophet')
  }

  if (errCode === '302') {
    console.log('没有登录')
    return goToLogin(encodeURI(window.location.href))
  }

  console.log('globalResponseMapper', json)

  if (errMessage) {
    if (json.options && json.options.method !== 'GET') {
      Message.error(errMessage)
    }
    throw {
      code: json.errCode,
      message: errMessage
    }
  }
  return json
}

const layoutSource = [
  {
    menuId: 'quanyi',
    menuParentId: '-1',
    menuTitle: '权益平台',
    menuUrl: '/benefit',
    menuTitleEN: 'quanyi',
    subMenus: [
      {
        menuId: 'pingtaihongbao',
        menuParentId: 'quanyi',
        menuTitle: '平台红包',
        menuUrl: '/benefit/hongbao',
        leaf: true
      },
    ],
  }
]

function renderContent(menus) {
  ReactDOM.render(
    <HashRouter>
      <Header subTitle="营销权益平台" />
      <article className="content-wrap">
        <Sider data={menus} myDefaultOpenKeys="/benefit/hongbao" />
        <App />
      </article>
    </HashRouter>,
    document.getElementById('app')
  )
}

function showTip() {
  Message.show({
    type: 'error',
    offset: [0, 120],
    // closeable: true,
    duration: 0,
    title: '没有权限',
    content: (
      <div>
        缺少执行该操作的权限，请在
        <a
          target="_blank"
          style={{ cursor: 'pointer', color: '#FF7C4D' }}
          rel="noopener noreferrer"
          onClick={() => goAcl('ele-newretail-userights-prophet')}
        >
          点击申请
        </a>
      后重试
      </div>
    ),
    hasMask: true,
    afterClose: () => console.log('Closed the toast')
  })
}

function urlAssemble(...args) {
  const [first, ...paths] = args;
  return [
    first.replace(/\/+$/, ''),
    ...paths.filter(item => !!item).map(path => path.replace(/^\/+/, ''))
  ].join('/');
}

function assembleMenus(domain, menus) {
  menus.forEach(item => {
    const { menuUrl, menuPrefix = '', platform } = item;
    if (item.menuUrl) {
      item.menuUrl = urlAssemble(domain[platform].prod, menuPrefix, menuUrl);
    }
    if (item.menuIcon) {
      item.menuTitleEN = item.menuIcon;
      delete item.menuIcon;
    }
    if (item.subMenus && item.subMenus.length > 0) {
      assembleMenus(domain, item.subMenus);
    }
  });
}

function renderApp() {
  Promise.all([
    axios.get('https://zs.kunlun.alibaba-inc.com/api/v1/component/diamond/MENU/MARKETING_PLATFORM', { withCredentials: true }),
    request('benefit2://api/v1/acl/menuTree.json')
  ]).then(([res1, res2]) => {
    // 校验权限
    const userRightMenus = _.get(res2, 'data.subMenus', [])
    if (!userRightMenus || !userRightMenus.length) {
      showTip()
      return
    }

    // 处理全局菜单
    const domain = _.get(res1, 'data.domain', {})
    let menus = _.get(res1, 'data.subMenus', [])
    const index = menus.findIndex(menu => menu.menuName === 'EQUITY')
    console.log('menus', menus, 'index', index)
    if (index !== -1) {
      menus.splice(index, 1)
    }
    assembleMenus(domain, menus)
    console.log('menus', menus, 'domain', domain)

    // 重新添加权益菜单
    let userRightMenu = userRightMenus[0]
    userRightMenu.menuTitleEN = 'favorites-filling'
    menus.push(userRightMenu)
    console.log('final menus', menus)
    renderContent(menus)
  }).catch(() => {
    // renderContent(layoutSource)
    showTip()
  })
}
renderApp()

if (module.hot) {
  module.hot.accept('../../app/benefit2.0/routes', () => {
    console.log('render...')
    renderApp()
  })
}
