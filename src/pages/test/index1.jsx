import 'babel-polyfill'
import React, { useState, useEffect } from 'react'
import ReactDOM from 'react-dom'
import { Button, Form, Input, Select } from '@alifd/next'
import _ from 'lodash'

import CouponTemplate from '../../components/coupon/CouponTemplate'

import '@alife/theme-nr-op/dist/next.css'
import ImageUploader from '../../components/coupon/ImageUploader'
import QrCode from '../../components/coupon/QrCode'
import logoPNG from '../../assets/coupon-gen/logo-demo.jpg'
import formDataSource from '@/source/formDataSource'
import CoverUploader from '@/components/coupon/CoverUploader'
import fusionInputFix from '@/utils/fusionInputFix'

const FormItem = Form.Item

const InputX = fusionInputFix()(Input)

class App extends React.Component {
  constructor() {
    super()

    console.log('argv', process)
    this.x = React.createRef()
  }

  render() {
    console.log('render ...')
    return (
      <div style={{ padding: '10px' }}>
        <Input value="1" />

        <Select
          defaultValue="1"
          dataSource={[
            {
              label: '1',
              value: '1'
            },
            {
              label: '2',
              value: '2'
            }
          ]}
        />

        <CouponTemplate
          ref={this.x}
          num={4}
          price={120}
          onChange={base64 => {}}
          discounted={1}
          logo={logoPNG}
          promises={['无门槛', '整单退', '过期退', '优惠同享']}
        />
        <Button
          onClick={() => {
            this.x.current.upload()
          }}
        >
          上传
        </Button>
        <QrCode src="https://www.ele.me" />

        <Demo />
      </div>
    )
  }
}

const Demo = formDataSource({
  requestAction: {
    url: 'boreas+mock://test-request-form',
    isActive: () => {
      return true
    }
  },
  submitAction: {
    url: `boreas://helloword`
  }
})(({ field, submit }) => {
  // console.log('here', field.init('abc'))
  console.log('render...', field)
  return (
    <div>
      <ImageUploader {...field.init('xxx')} />
      <CoverUploader num={4} price={30} {...field.init('ooo')} />
      <InputX hasLimitHint maxLength={20} {...field.init('yyy')} />
    </div>
  )
  // return <Form field={field} style={{ margin: 20, border: '1px solid #eee', padding: 40 }}>
  //   <InputX />
  //   <Input id='abc' {...field.init('abc', {
  //     initValue: ''
  //   })} />
  //   <Button onClick={submit}>提交</Button>
  // </Form>
})

ReactDOM.render(<App />, document.getElementById('app'))
