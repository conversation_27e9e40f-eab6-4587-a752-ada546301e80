<% var buildOptions = htmlWebpackPlugin.options.buildOptions; %>
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title>new_kun</title>
  <meta name="aplus-exinfo" content="EXPARAMS" />
  <meta name="aplus-vt-cfg" content="1" />
  <meta name="spm-id" content="a21he." />
  <meta name="data-scene-id" content="" />
  <meta name="data-schema-id" content="" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no,viewport-fit=cover" />
  <meta id="WV.Meta.Share.Disabled" value="true" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="screen-orientation" content="portrait" />
  <% if(buildOptions.debug){ %>
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <% } %> <% if(!buildOptions.inline || buildOptions.debug){ %>
  <link rel="stylesheet" type="text/css" href="<%= buildOptions.resourceBase %>/index.css" />
  <% } %> <% if(buildOptions.debug){ %>
  <script name="mock"></script>
  <% } %>
</head>

<body>
  <div id="app"></div>
  <% if(buildOptions.debug){ %>
  <script>
    window.CONFIG = {
      ENV: 'prepub',
    }
  </script>
  <script
    src="//g.alicdn.com/o2o-fe/res/1.2.4??es6-promise.auto.min.js,object.assign.js,intersection-observer.js,16.8.2/react.development.js,16.8.2/react-dom.development.js,1.3.12/ubt.js,4.1.2/hybrid-api.js,3.1.1/alipayjsapi.js,1.0.0/geohash.js,ebridge/0.14.17.js,addAplus.js,autoGoldlog.js"></script>
  <% } else { %>
  <script
    src="//g.alicdn.com/o2o-fe/res/1.2.4??es6-promise.auto.min.js,object.assign.js,intersection-observer.js,16.8.2/react.min.js,16.8.2/react-dom.min.js,1.3.12/ubt.js,4.1.2/hybrid-api.js,3.1.1/alipayjsapi.js,1.0.0/geohash.js,ebridge/0.14.17.js,addAplus.js,autoGoldlog.js"></script>
  <% } %>
  <script
    src="//g.alicdn.com/mtb/??lib-windvane/3.0.4/windvane.js,lib-login/1.5.8/login.js,lib-mtop/2.3.15/mtop.js"></script>
  <% if(buildOptions.debug){ %>
  <script src="<%= buildOptions.resourceBase %>/index.js"></script>
  <% } %> <% if(!buildOptions.debug){ %>
  <script src="<%= buildOptions.resourceBase %>/index.js" crossorigin></script>
  <% } %>
</body>

</html>