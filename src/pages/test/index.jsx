import 'babel-polyfill'
import React, { useState, useEffect } from 'react'
import ReactDOM from 'react-dom'
import withForm from '@/packages/ali-form/withForm'
import AliForm from '@/packages/ali-form'
import './index.scss'
import '@alife/theme-nr-op/dist/next.css'


const formUISource = {
  title: '规则信息',
  children: [
    {
      xComponent: 'Input',
      name: 'name',
      props: {
        label: '投放名称',
        rules: [
          {
            required: true,
            trigger: 'onBlur',
            message: '投放计划名称不能为空'
          },
          {
            maxLength: 10,
            message: '最多不超过10个字',
            autoValidate: true

          }
        ]
      }
    },
    {
      xComponent: 'Input',
      name: 'planId',
      // initValue: 100,
      props: {
        label: '投放ID:',
        placeholder: '请输入投放ID'
      }
    },

    {
      xComponent: 'Input',
      name: 'num',
      // initValue: 1,
      props: {
        label: '$"投放数量"+name$',
        style: {
          display: '$name=="ramroll" ? "block" : "none"$',
        }
      }
    },

    {
      xComponent: 'Input',
      name: 'total',
      props: {
        label: `$"总金额"+num$`,
        value: '$status*planId$'
      }
    },
    {
      xComponent: 'Select',
      name: 'status',
      props: {
        label: '投放计划状态:',
        placeholder: '$"请为" + name + "选择状态值"$'
      }
    }
  ]
}

@withForm({
  uiSource: formUISource
})
class App extends React.Component {

  render() {
    return <div>
      <AliForm />
    </div>
  }
}



ReactDOM.render(<App />, document.getElementById('app'))
