import 'babel-polyfill'
import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom'
import { HashRouter } from 'react-router-dom'
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@alife/kunlun-base'
import { Message } from '@alifd/next'
import { request } from '@/packages/request'
import App from '@/app/mark/routes'
import _ from 'lodash'
import { buildEnv } from '@/packages/env'
import DomainConfigure from '@/packages/request/DomainConfigure'

import './index.scss'
import '@alife/theme-nr-op/dist/next.css'

const env = buildEnv()

const host = DomainConfigure.mark[env]
const loginHost = DomainConfigure.login[env]
const aclHost = DomainConfigure.acl[env]

request.globalResponseMapper = (json) => {
  const errorCode = _.get(json, 'errors[0].code', '')
  const aclCode = _.get(json, 'code', '')
  console.log('globalResponseMapper', json)
  if (errorCode === '302') {
    // 跳转登录
    window.location.href = `https://${loginHost}/ssoLogin.htm?APP_NAME=ele-newretail-mark&BACK_URL=https://${host}/api/acl/v1/redirectUrl?BZ_URL=${window.location.href}`
    return
  } else if (aclCode === '400') {
    window.location.href = `https://${aclHost}/apply/instance/index.htm?type=permission&appName=bz_commodity`
    return
  }

  const errorMessage = json.msg || (json.data && json.data.errorMessage)

  if (errorMessage) {
    if (json.options && json.options.method !== 'GET') {
      Message.error(errorMessage)
    }
    throw {
      code: json.code,
      message: errorMessage
    }
  }

  if (json.data) {
    return json.data
  }
  return json
}

function renderContent(menus = []) {
  ReactDOM.render(
    <HashRouter>
      <Header subTitle="标注平台" />
      <article className="content-wrap">
        <Sider data={menus} />
        <App />
      </article>
    </HashRouter>,
    document.getElementById('app'),
  )
}

function renderApp() {
  request('mark://api/acl/v1/menuTree.json')
  .then(res => {
    const menus = _.get(res, 'subMenus', [])
    renderContent(menus)
  })
  .catch(() => {
    renderContent()
  })
}
renderApp()

// if (module.hot) {
//   module.hot.accept('./pages/benefit/index.jsx', () => {
//     console.log('here')
//     renderApp()
//   })
// }
