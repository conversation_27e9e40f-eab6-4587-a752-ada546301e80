<% var buildOptions = htmlWebpackPlugin.options.buildOptions; %>
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>标注平台</title>
    <meta name="aplus-exinfo" content="EXPARAMS" />
    <meta name="aplus-vt-cfg" content="1" />
    <meta name="data-scene-id" content="" />
    <meta name="data-schema-id" content="" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,user-scalable=no,viewport-fit=cover"
    />
    <meta id="WV.Meta.Share.Disabled" value="true" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="screen-orientation" content="portrait" />
    <% if(buildOptions.debug){ %>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <% } else { %>
    <script
      type="text/javascript"
      src="https://g.alicdn.com/dt/tracker/4.2.0/??tracker.Tracker.js,tracker.interfaceTrackerPlugin.js,tracker.performanceTrackerPlugin.js"
      crossorigin
    ></script>
    <% } %> <% if(!buildOptions.inline || buildOptions.debug){ %>
    <link rel="stylesheet" type="text/css" href="<%= buildOptions.resourceBase %>/index.css" />
    <% } %> <% if(buildOptions.debug){ %>
    <script name="mock"></script>
    <% } %>
  </head>

  <body>
    <script>
      var tracker = window.Tracker
        ? new window.Tracker({
            pid: 'new-kun',
            plugins: [[window.interfaceTrackerPlugin], [window.performanceTrackerPlugin]],
          })
        : {
            install: function() {},
            log: function() {},
          }
      tracker.install()
    </script>
    <script type="text/javascript" src="https://g.alicdn.com/eleme-b-newretail/ddy-sdk/0.1.5/ddy.min.js">
    </script>
    <script>
      window.DDY.config({
        appId: 'ele-newretail-mark'
      })
    </script>

    <div id="app"></div>
    <% if(buildOptions.debug){ %>
      <script src="//dev.g.alicdn.com/o2o-fe/res/1.2.13??es6-promise.auto.min.js,object.assign.js,16.11.0/react.development.js,16.11.0/react-dom.development.js"></script>

    <% } else { %>
      <script src="//g.alicdn.com/o2o-fe/res/1.2.13??es6-promise.auto.min.js,object.assign.js,16.11.0/react.min.js,16.11.0/react-dom.min.js"></script>
    <% } %>
    <script src="//g.alicdn.com/mtb/??lib-mtop/2.5.4/mtop.js"></script>
    <% if(buildOptions.debug){ %>
    <script src="<%= buildOptions.resourceBase %>/index.js"></script>
    <% } %> <% if(!buildOptions.debug){ %>
    <script src="<%= buildOptions.resourceBase %>/index.js" crossorigin></script>
    <% } %>
  </body>
</html>
