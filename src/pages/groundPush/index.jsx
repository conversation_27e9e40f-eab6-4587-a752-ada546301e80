import 'babel-polyfill'
import React from 'react'
import ReactD<PERSON> from 'react-dom'
import { Provider } from 'mobx-react'
import { <PERSON><PERSON>, Sider } from '@alife/kunlun-base'
import { HashRouter } from 'react-router-dom'
import { Message } from '@alifd/next'
import { request } from '@/packages/request'
import { goToLogin, goToApply } from '@/api/auth';
import { buildEnv } from '@/packages/env'

import './index.scss'
import '@alife/theme-nr-op/dist/next.css'

request.globalResponseMapper = json => {
  console.log('crm人群管理权限')
  console.log(json)

  let code = json.code || (json.errors && json.errors[0] && json.errors[0].code)
  let msg = json.msg || (json.errors && json.errors[0] && json.errors[0].msg)
  let data = json && json.data
  if (code === '302') {
    console.log('没有登录')
    return goToLogin(encodeURI(window.location.href))
  }

  if (code === '400') { // 没权限
    Message.error(msg)
    // https:// acl-test.alibaba-inc.com/apply/instance/index.htm?type=all&keyword=crm_user_grow_up
    return goToApply('crm_user_grow_up')
  }
  if (['401', '500'].includes(String(code))) { // 后端拦截没权限
    Message.error(msg)
    // https:// acl-test.alibaba-inc.com/apply/instance/index.htm?type=all&keyword=crm_user_grow_up
    return goToApply('crm_user_grow_up')
  }


  if (code !== "200") {
    Message.error(msg)
    throw {
      code,
      msg
    }
  }


  if (json && json.data) {
    if (json.data.subMenus && json.data.subMenus.length <= 0) {
      Message.error('没有该菜单权限，跳转去申请权限')
      return goToApply('crm_user_grow_up')
    }
    return json.data
  }

  return json
}

let layoutSource = [
  {
    menuId: 'wanfarukou',
    menuParentId: '-1',
    menuTitle: '玩法配置',
    menuUrl: '/crm',
    menuTitleEN: 'leftnav_toufang'
  },
  {
    menuId: 'test',
    menuParentId: '-1',
    menuTitle: '测试1',
    menuUrl: '/test',
    menuTitleEN: 'test'
  },
  {
    menuId: 'toufangguanli',
    menuParentId: '-1',
    menuTitle: '测试2',
    menuUrl: '/plan',
    menuTitleEN: 'leftnav_toufang'
  }
]

const path = {
  local: 'crm+local://menuTree',
  localhost: 'crm+local://menuTree',
  daily: 'crm+daily:///api/v1//acl/menuTree.json',
  pre: 'crm+pre:///api/v1//acl/menuTree.json',
  ppe: 'crm+ppe:///api/v1//acl/menuTree.json',
  prod: 'crm:///api/v1//acl/menuTree.json'
}
const env = buildEnv()

request(path[env], { query: { onemenutype: 'crm' } }).then(res => {
  let menus = []
  menus = res.subMenus
  layoutSource = menus
  renderApp();
}, (err) => {
  Message.error(err.msg || '系统错误')
})

// renderApp();

function renderApp() {
  const App = require('@/app/crm/routes').default
  ReactDOM.render(
    <Provider>
      <HashRouter>
        <Header subTitle="CRM" />
        <article className="content-wrap">
          <Sider data={layoutSource} />
          <App className="main" />
        </article>
      </HashRouter>
    </Provider>,
    document.getElementById('app')
  )
}

// renderApp()

if (module.hot) {
  module.hot.accept('../../app/crm/routes', () => {
    console.log('render...')
    renderApp()
  })
}
