import 'babel-polyfill'
import React from 'react'
import ReactD<PERSON> from 'react-dom'
import { HashRouter } from 'react-router-dom'
import { Head<PERSON>, Sider } from '@alife/kunlun-base'
import { Message } from '@alifd/next'
import { request } from '@/packages/request'
import App from '@/app/manjian/routes'
import DomainConfigure from '@/packages/request/DomainConfigure'
import { buildEnv } from '@/packages/env'
import _ from 'lodash'

import './index.scss'
import '@alife/theme-nr-op/dist/next.css'

const env = buildEnv()
const loginHost = DomainConfigure.login[env]
const aclHost = DomainConfigure.acl[env]

request.setCommonErrorHandler(res => {
  console.log('setCommonErrorHandler', res)
  if (res.ret[0].match(/FAIL_SYS_SESSION_EXPIRED/)) {
    // 跳转登录
    const url = `https://${loginHost}/ssoLogin.htm?APP_NAME=ele-newretail-marketing-rule&RQ_SUFFIX=.html&BACK_URL=${window.location.href}`
    Message.error('请重新登录系统')
    // https://login-test.alibaba-inc.com/ssoLogin.htm?APP_NAME=ele-newretail-marketing-rule&RQ_SUFFIX=.html&BACK_URL=https://market.wapa.taobao.com/app/eleme-b-newretail/new_kun/manjian/index.html#/?pageNum=1&pageSize=10
    if (!(/local/.test(window.location.host))) {
      window.location.href = url
    }
    return true
  }
  if (res.ret[0].match(/NO_ACL_PERSSION/)) {
    window.location.href = res.data.result || `https://${aclHost}/apply/instance/index.htm?type=permission&appName=ele-newretail-marketing-rule`
  }
  Message.error((res.data && res.data.errorMsg) || res.ret[0] || '接口错误')
  return true
})

function renderContent() {
  ReactDOM.render(
    <HashRouter>
      <App />
    </HashRouter>,
    document.getElementById('app')
  )
}

function renderApp() {
  request('mtop://mtop.ele.newretail.rule.menu')
  .then(res => {
    const menus = _.get(res, 'data.data.subMenus', []).map(item => item.menuName)
    if (!menus || !menus.length || !menus.includes('market_rules')) {
      Message.show({
        type: 'error',
        offset: [0, 120],
        closeable: true,
        duration: 0,
        title: '没有权限',
        content: (
          <div>
            缺少执行该操作的权限，请在
            <a
              href={`https://${aclHost}/apply/instance/index.htm?type=permission&appName=ele-newretail-marketing-rule`}
              target="_blank"
              rel="noopener noreferrer"
            >
              点击申请
            </a>
           后重试
           </div>
        ),
        hasMask: true,
        afterClose: () => console.log('Closed the toast')
      })
    }
    renderContent()
  })
}
renderApp()
