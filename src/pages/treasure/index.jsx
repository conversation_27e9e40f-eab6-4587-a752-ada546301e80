import 'babel-polyfill'
import React from 'react'
import ReactD<PERSON> from 'react-dom'
import { Provider } from 'mobx-react'
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@alife/kunlun-base'
import { HashRouter } from 'react-router-dom'
import { Message } from '@alifd/next'
import { request } from '@/packages/request'
import { goToLogin, goToApply } from '@/api/auth';
import { buildEnv } from '@/packages/env'

import './index.scss'
import '@alife/theme-nr-op/dist/next.css'

request.globalResponseMapper = json => {

  let code = (json.data && json.data.errorCode) || json.code || (json.errors && json.errors[0] && json.errors[0].code)
  let msg = (json.data && json.data.errorDesc) || json.msg || (json.errors && json.errors[0] && json.errors[0].msg)
  if (code === '302') {
    setTimeout(() => {
      console.log("302", json, JSON.stringify(json))
      console.log("302")
      return goToLogin(encodeURI(window.location.href));
    }, 1000);
  }

  if (code === '400') { // 没权限
    Message.error(msg)
    // https:// acl-test.alibaba-inc.com/apply/instance/index.htm?type=all&keyword=yingxiao_treasure
    return goToApply('yingxiao_treasure')
  }

  if (['401', '500'].includes(String(code))) { // 后端拦截的没权限
    Message.error(msg)
    // https:// acl-test.alibaba-inc.com/apply/instance/index.htm?type=all&keyword=yingxiao_treasure
    return goToApply('yingxiao_treasure')
  }

  // test
  if (code !== '0' && code !== '200') {

    Message.error(msg)
    throw {
      code,
      msg
    }
  }


  if (json.data && typeof (json.data.data) !== 'undefined') {
    if (json.data.data.subMenus && json.data.data.subMenus.length <= 0) {
      Message.error('没有该菜单权限，跳转去申请权限')
      return goToApply('yingxiao_treasure')
    }
    return json.data.data
  } else if (json.data) {
    if (json.data.subMenus && json.data.subMenus.length <= 0) {
      Message.error('没有该菜单权限，跳转去申请权限')
      return goToApply('yingxiao_treasure')
    }
    return json.data
  }

  return json
}

let layoutSource = [
  {
    menuId: 'wanfarukou',
    menuParentId: '-1',
    menuTitle: '互动玩法1',
    menuUrl: '/treasure',
    menuTitleEN: 'leftnav_toufang'
  },
]

const path = {
  local: 'treasure+local://menuTree',
  daily: 'treasure+daily:///api/v1//acl/menuTree.json',
  pre: 'treasure+pre:///api/v1//acl/menuTree.json',
  ppe: 'treasure+ppe:///api/v1//acl/menuTree.json',
  prod: 'treasure:///api/v1//acl/menuTree.json'
}
// .json无法在mock环境中作为地址出现，故...
const env = buildEnv()

request(path[env], { query: { onemenutype: 'yingxiao' } }).then(res => {
  let menus = []
  menus = res.subMenus;

  layoutSource = menus
  console.log(menus)

  renderApp();
}, (err) => {
  console.log('错误提示')

  Message.error(err.msg || '系统错误')
})

function renderApp() {
  const App = require('@/app/treasure/routes.jsx').default
  ReactDOM.render(
    <Provider>
      <HashRouter>
        <Header subTitle="互动玩法" />
        <article className="content-wrap">
          <Sider data={layoutSource} />
          <App />
        </article>
      </HashRouter>
    </Provider>,
    document.getElementById('app')
  )
}
// renderApp()

if (module.hot) {
  module.hot.accept('../../app/treasure/routes', () => {
    console.log('render...')
    renderApp()
  })
}
