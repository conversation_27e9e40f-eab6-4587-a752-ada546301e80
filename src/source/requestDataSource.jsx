import React from 'react'
import { Loading } from '@alifd/next'
import { request } from '@/packages/request'

export default function requestDataSource({ action, mapStateToProps = x => x }) {
  return Target => {
    class Proxy extends React.Component {
      constructor() {
        super()

        this.state = {
          inited: false,
          data: null
        }
      }

      componentDidMount() {
        request(action, {}, { props: this.props }).then(data => {
          this.setState({
            data,
            inited: true
          })
        })
      }

      render() {
        if (!this.state.inited) {
          return <Loading style={{ width: '100%', height: '80%' }} />
        }
        return <Target {...this.props} {...mapStateToProps({ data: this.state.data })} />
      }
    }

    return Proxy
  }
}
