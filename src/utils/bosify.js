export default function bosify(url, width = 160, height) {
  if (/image-star\.elemecdn\.com/.test(url)) {
    // ali oss
    const resize = '?x-oss-process=image/resize,'
    const w = width && `w_${width}`
    const h = height && `h_${height}`
    const size = [w, h].filter((v) => v).join(',')
    url = `${url}${size.length ? resize : ''}${size}`
  }
 return url || 'https://img.alicdn.com/tfs/TB1GAmimKH2gK0jSZFEXXcqMpXa-78-78.png'
}
