import React, { useEffect, useState, useRef, forwardRef } from 'react'
import { Input } from '@alifd/next'

function fusionInputFix(type = 'input') {
  return Target => {
    class InputX extends React.Component {
      render() {
        let Comp = null

        switch (type) {
          case 'input':
            Comp = Input
            break
          case 'textarea':
            Comp = Input.TextArea
            break
          default:
            throw 'not impl.'
        }

        return (
          <Input
            {...this.props}
            inputRender={el => {
              const { maxLength, placeholder, value, disabled } = el.props
              const iptProps = {
                maxLength,
                readOnly: el.props.readOnly,
                disabled,
                placeholder,
                onChange: e => {
                  el.props.onChange(e)
                },
                value,
                type: this.props.type || 'text'
              }

              if (type === 'input') {
                return <DummyInput {...iptProps} />
              } else if (type === 'textarea') {
                return <DummyTextArea {...iptProps} />
              } else {
                throw 'not impl.'
              }
            }}
          />
        )
      }
    }

    return InputX
  }
}


const DummyTextArea = forwardRef((props, ref) => {
  const { onChange, value, ...reset } = props
  const iptRef = useRef()

  useEffect(() => {
    iptRef.current.value = value || ''
  }, [value])

  function handleChange(e) {
    onChange && onChange(e)
  }


  return <textarea
    {...reset}
    ref={iptRef}
    onChange={handleChange}
    className='textarea' style={{ minHeight: '60px', padding: '9px 8px', fontSize: '14px', lineHeight: '16px' }} />
})


const DummyInput = forwardRef((props, ref) => {
  const { onChange, value, ...reset } = props

  const iptRef = useRef()

  useEffect(() => {
    iptRef.current.value = value !== undefined ? value : '';// 需要识别value=0
  }, [value])

  function handleChange(e) {
    onChange && onChange(e)
  }

  return <input
    {...reset}
    ref={iptRef}
    onChange={handleChange} />
})

export default fusionInputFix
