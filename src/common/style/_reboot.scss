@charset "UTF-8";
@import '~@alife/next/reset.scss';
@import '~@alife/theme-nr-op/variables.scss';

$bg-color: #fafafa;
$border-color: #ebebeb;
$success-color: #0ca;
$red-color: #f33;
$progressive-color: #fb3;
$end-color: #ccc;

$header-h: 60px;

.only-bottom-border {
  border-width: 0 0 1px !important;
}

.color-link-1 {
  color: $color-link-1;
}

.content-wrap .next-menu-item.next-selected a {
  color: $color-link-1;
}
.content-wrap .next-menu-item.next-selected .next-menu-icon-selected {
  display: none;
}

.next-pagination {
  margin-top: 10px;
}
.next-pagination-total {
  color: $color-text1-2;
  font-size: 14px;
}

.next-tabs-tab-inner {
  font-size: 16px;
}

.bg-fff {
  background: #ffffff;
}

.color-text1-3 {
  color: $color-text1-3;
}

.color-text1-2 {
  color: $color-text1-2;
}
// .next-selected .next-menu-item-text a {
//   color: $color-link-1 !important;
// }
// .align-right {
//   display: flex !important;
//   justify-content: flex-end;
// }

// .space-between {
//   display: flex;
//   justify-content: space-between;
//   flex-wrap: wrap;
// }

// @mixin mx-icon($width, $height, $url) {
//   display: inline-block;
//   width: $width;
//   height: $height;
//   background-image: url($url);
//   background-size: contain;
//   background-repeat: no-repeat;
// }
.next-upload-list-card.disabled .next-upload-tool {
  display: none;
}

html {
  font-family: PingFangSC-Regular;
}
html,
body,
#root,
#app {
  width: 100%;
  height: 100%;
  background-color: #fafafa;
  color: #333;
}
p,
h1,
h2,
h3,
h4,
h5,
h6,
address {
  margin: 0;
  font-weight: normal;
}

ol,
ul {
  list-style: none;
}

#app {
  display: flex;
  flex-direction: column;

  .content-wrap {
    flex: 1;
    display: flex;
    overflow: hidden;

    .sider {
      border-right: 1px solid #ebebeb;
    }

    .right-content {
      background: $bg-color;
      flex: auto;
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
      padding: 0 20px 20px;
      // width: 1200px;

      .main-content {
        background: #fff;
        // margin: 0 21px;
        padding: 30px 20px;
        min-height: calc(100% - 70px - 21px);
        box-sizing: border-box;
        min-width: 690px;
        flex: 1;
      }
    }

    .router-wrap {
      flex: 1;
      overflow-y: auto;
      // display: flex;
      height: 100%;
      //   > div {
      //     flex: 1;
      //     width: 100%;
      //   }
    }
  }
}

.next-col button,
.next-form-item button {
  width: 88px;
}

.cursor-pointer {
  cursor: pointer;
}
.next-dialog {
  max-width: 70%;
}

.boreas-save {
  text-align: right;
  padding: 30px 0;
}

.boreas-pagination {
  padding: 10px 0;
  text-align: right;
  .next-select-inner {
    min-width: 64px !important;
  }
  .next-menu {
    width: 70px !important;
  }
  .next-pagination-total {
    font-size: 14px;
  }
}

.boreas-cell {
  color: #ff7c4d;
  cursor: pointer;
  span {
    cursor: pointer;
  }
  .pl {
    padding-left: 8px;
  }
  .pr {
    padding-right: 8px;
  }
}

.boreas-status {
  font-size: 14px;
  color: #666666;
  position: relative;
  padding-left: 15px;
  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    display: inline-block;
    width: 0;
    height: 0;
    border: 3px solid #00ccaa;
    border-radius: 50%;
    margin-top: -3px;
  }
  &-2 {
    &:before {
      display: inline-block;
      width: 0;
      height: 0;
      border: 3px solid #cccccc;
      border-radius: 50%;
    }
  }
  &-0 {
    &:before {
      display: inline-block;
      width: 0;
      height: 0;
      border: 3px solid #ff0000;
      border-radius: 50%;
    }
  }
}
.next-input-len {
  font-size: 14px !important;
  letter-spacing: 1px;
}

.next-dialog .next-btn {
  width: 88px !important;
}

.next-dialog .next-pagination-item,
.next-dialog .next-pagination-jump-go {
  width: auto !important;
}
.next-dialog-btn + .next-dialog-btn {
  margin-left: 12px !important;
}
.boreas-show {
  display: block;
}
.boreas-hide {
  display: none;
}
// StatusPoint 组件用 (因为需要在弹窗中使用，所以提出来)
.point {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
}

.success-point {
  background-color: $success-color;
}

.fail-point {
  background-color: $red-color;
}

.progressive-point {
  background-color: $progressive-color;
}

.end-point {
  background-color: $end-color;
}
