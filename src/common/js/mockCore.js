/** mock数据的核心方法
 * 其实就是一个方法劫持而已,替换原来的mtop的request方法
 * @param {any} mock
 */
export default (mock) => {
  window.lib.mtop.request = (options, resolve, reject) => {
    mock.apiMap[options.api](options)
      .then((data) => {
        resolve({
          ret: ['SUCCESS::ok'],
          data: {
            data,
          },
        });
      })
      .catch((err) => {
        reject(err);
      });
  };
};
