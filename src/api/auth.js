import { config } from './config';
import DomainConfigure from '@/packages/request/DomainConfigure'
import { buildEnv } from '@/packages/env'

const env = buildEnv()
const loginHost = DomainConfigure.login[env]
const aclHost = DomainConfigure.acl[env]

// export function goToLogin(from = location.href) {
//   const { LOGIN_URL, APP_NAME } = config;
//   location.href = `${LOGIN_URL}?APP_NAME=${APP_NAME}&BACK_URL=${encodeURIComponent(from)}&RQ_SUFFIX=.htm`;
// }


export function goToLogin() {
  const loginURLPrefix = "https://login.alibaba-inc.com/ssoLogin.htm"
  const E = encodeURIComponent
  const loginURL = `${loginURLPrefix}?APP_NAME=ele-newretail-game-crm&RQ_SUFFIX=.html&BACK_URL=${E(window.location.href)}&contextPath=${E('/app/eleme-b-newretail/new_kun_fix/')}`
  window.location.replace(loginURL)
}


export function goToApply(moduleStr) {
  console.log(`${config.APPLY_VALID_URL}&keyword=${moduleStr}`)
  // http://acl-test.alibaba-inc.com/apply/cart/detail.htm?pnames=
  // window.location.href = `${config.APPLY_VALID_URL}&keyword=${moduleStr}`
  window.location.href = `${config.APPLY_URL}?pnames=${moduleStr}`
}

export function goAcl(appName) {
  window.location.href = `https://${aclHost}/apply/instance/index.htm?type=permission&appName=${appName}`
}
