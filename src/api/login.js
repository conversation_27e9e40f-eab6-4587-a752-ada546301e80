/*
 * @Author:
 * @Date: 2019-09-25
 * @Last Modified time: 2019-09-25 18:29:38
 * @Last Modified by:
 *
 * 墨子登录相关
 */

import config from './env'

// 跳转acl登录页
export function goToLogin(from = window.location.href) {
  window.location.href = `${config.API_ROOT}/api/v2/acl/redirectUrl?BRAND_URL=${from}`
}

// 跳转acl退出登录页
const { SSO_ROOT, API_ROOT, APP_NAME } = config
const backUrl = `${API_ROOT}/api/v2/acl/redirectUrl?BRAND_URL=${window.location.href}`
export const logoutUrl = `${SSO_ROOT}/ssoLogout.htm?APP_NAME=${APP_NAME}&BACK_URL=${encodeURIComponent(
  backUrl,
)}`

const { APP_HOST } = config
export const HomePage = `${window.location.protocol}//${APP_HOST}/index.html`
export const LogoutPage = `${window.location.protocol}//${APP_HOST}/logout.htm`
