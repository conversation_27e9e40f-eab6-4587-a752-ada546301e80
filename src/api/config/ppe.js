export const FE_APP_ROOT =
  window.location.origin + "/app/eleme-b-newretail/new_kun";
// api
export const API_ROOT = "https://pre-gamecrm.ele.alibaba.com";
export const PUTIN_API_ROOT = "https://pre-gamecrm.ele.alibaba.com";
// sso
export const APP_NAME = "ele-newretail-game-crm";
export const SSO_ROOT = "https://login.alibaba-inc.com";

// mozi
export const MOZI_ROOT = "https://login.alibaba-inc.com";

export const ZHAO_SHANG = "https://zs.alibaba.net";

export const LOGIN_URL = `${MOZI_ROOT}/ssoLogin.htm`;
export const LOGIN_CALLBACK_PAGE = `${FE_APP_ROOT}`;


export const PLAN_URL = `https://kunlun.ele.me/home/<USER>/https://kun.eleme.test/app/eleme-b-newretail/new_kun/benefit-kunlun/index.html#/plan?pageNum=1&pageSize=10`

export const APPLY_VALID_URL =
  "http://acl.alibaba-inc.com/apply/instance/index.htm?type=permission";
export const APPLY_URL = "http://acl.alibaba-inc.com/apply/cart/detail.htm";

export const CROWED_URL = `https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/list`;

export const CROWED_SELECT_URL = `https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/select`;
export const PLAYSET_URL = `https://kun.eleme.test/app/eleme-b-newretail/new_kun/crm-user/index.html#/crm_user/entry`;
export const PLATSET_LIST_URL = `https://kun.eleme.test/app/eleme-b-newretail/new_kun/crm-user/index.html#/crm_user/list`

export const WHITE_LIST_URL = `https://kun.eleme.test/app/eleme-b-newretail/new_kun/crm-user/index.html#/crm_user/whiteList`

export const LOGOUT_URL = `${MOZI_ROOT}/ssoLogout.htm`;
export const LOGOUT_CALLBACK_PAGE = `${FE_APP_ROOT}/bucSSOLogout.htm`;

export const KL_ROOT = "https://kunlun.alibaba.net";
// 权益功能相关接口path
export const BENEFIT_PATH = `https://ppe-stargate.ele.me/bwm_newretail.userright_app`
