
export const FE_APP_ROOT = window.location.origin

// api
export const API_ROOT = 'https://daily-gamecrm.alibaba.net'
export const PUTIN_API_ROOT = 'https://daily-gamecrm.alibaba.net'

// sso
export const APP_NAME = 'ele-newretail-game-crm'
export const SSO_ROOT = 'https://login-test.alibaba-inc.com'

export const LOGIN_CALLBACK_PAGE = `${FE_APP_ROOT}`
export const APPLY_VALID_URL = 'http://acl-test.alibaba-inc.com/apply/instance/index.htm?type=permission'
export const ZHAO_SHANG = 'https://zs.alibaba.net'
export const APPLY_URL = 'http://acl-test.alibaba-inc.com/apply/cart/detail.htm'
// 权益功能相关接口path
export const BENEFIT_PATH = `https://stargate.alta.elenet.me/bwm_newretail.userright_app`
// 创建投放计划页面
export const PLAN_URL = `http://local.kunlun.alibaba.net/benefit/index.html#/plan`

export const WHITE_LIST_URL = `https://market.wapa.taobao.com/app/eleme-b-newretail/new_kun/crm-user/index.html#/crm_user/whiteList`

export const PLAYSET_URL = `http://boreas.kunlun.alibaba.net:3333/crm-user/index.html#/crm_user/entry`

export const LOGOUT_URL = `${SSO_ROOT}/ssoLogout.htm?APP_NAME=${APP_NAME}`
  + `&BACK_URL=${API_ROOT}/api/v1/acl/redirectUrl`
  + `?ZXDT_URL=${window.location.href}`
export const LOGOUT_CALLBACK_PAGE = `${FE_APP_ROOT}/bucSSOLogout.htm`

export const KL_ROOT = 'https://kunlun.alibaba.net'
