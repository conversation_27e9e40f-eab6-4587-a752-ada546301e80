export const FE_APP_ROOT = window.location.origin + '/app/eleme-b-newretail/new_kun'

// api
export const API_ROOT = 'https://daily-gamecrm.alibaba.net'
export const PUTIN_API_ROOT = 'https://daily-gamecrm.alibaba.net'

// sso
export const APP_NAME = 'ele-newretail-game-crm'
export const SSO_ROOT = 'https://login-test.alibaba-inc.com'

export const LOGIN_CALLBACK_PAGE = `${FE_APP_ROOT}`
export const APPLY_VALID_URL = 'http://acl-test.alibaba-inc.com/apply/instance/index.htm?type=permission'
export const APPLY_URL = 'http://acl-test.alibaba-inc.com/apply/cart/detail.htm'

export const ZHAO_SHANG = 'https://zs.alibaba.net'

export const CROWED_URL = `https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/list`
export const CROWED_SELECT_URL = `https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/select`
export const PLAYSET_URL = `https://market.wapa.taobao.com/app/eleme-b-newretail/new_kun/crm-user/index.html#/crm_user/entry`
export const PLATSET_LIST_URL = `https://market.wapa.taobao.com/app/eleme-b-newretail/new_kun/crm-user/index.html#/crm_user/list`

export const WHITE_LIST_URL = `https://market.wapa.taobao.com/app/eleme-b-newretail/new_kun/crm-user/index.html#/crm_user/whiteList`

export const PLAN_URL = `https://kunlun.ele.me/home/<USER>/https://boreas.ele.me/page/benefit-kunlun/index.html#/benefit?pageNum=1&pageSize=10`

export const LOGOUT_URL = `${SSO_ROOT}/ssoLogout.htm?APP_NAME=${APP_NAME}`
  + `&BACK_URL=${API_ROOT}/api/v1/acl/redirectUrl`
  + `?ZXDT_URL=${window.location.href}`
export const LOGOUT_CALLBACK_PAGE = `${FE_APP_ROOT}/bucSSOLogout.htm`

export const KL_ROOT = 'https://kunlun.alibaba.net'
// 权益功能相关接口path
export const BENEFIT_PATH = `https://stargate.alta.elenet.me/bwm_newretail.userright_app`
