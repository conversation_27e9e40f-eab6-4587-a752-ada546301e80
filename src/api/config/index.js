
// https://yuque.antfin-inc.com/acl_buc_mozi/mozi_usersguide/ya69eg

import * as daily from './daily';
import * as localdev from './localdev';
import * as localhost from './localhost';
import * as prod from './prod';
import * as pre from './pre'
import * as ppe from './ppe'
import { buildEnv } from '@/packages/env'

export const ENV_ENUM = {
  daily: 'daily',
  localdev: 'localdev',
  local: 'local',
  pre: 'pre',
  ppe: 'ppe',
  prod: 'prod',
}
export const env = buildEnv()
console.log('环境变量')
console.log(env)
// for header api
const hdEnv = env === 'prod' ? 'prod' : env
window.CONFIG = { ENV: hdEnv }

export const isProduction = env === ENV_ENUM.prod

export const config = getConfig(env)
console.log('app env running in: ', env, config)

function getConfig(env) {
  switch (env) {
    case ENV_ENUM.daily: return daily
    case ENV_ENUM.prod: return { ...daily, ...prod }
    case ENV_ENUM.pre: return { ...daily, ...pre }
    case ENV_ENUM.ppe: return { ...daily, ...ppe }
    case ENV_ENUM.local: return { ...daily, ...localhost }
    default: return { ...daily, ...localdev }
  }
}
