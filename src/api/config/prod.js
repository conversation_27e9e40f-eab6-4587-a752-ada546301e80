
// common
export const FE_APP_ROOT = window.location.origin

// api
export const API_ROOT = 'https://gamecrm.ele.alibaba.com'
export const PUTIN_API_ROOT = 'https://gamecrm.ele.alibaba.com'
// mozi
export const APP_NAME = 'ele-newretail-game-crm'
export const SSO_ROOT = 'https://login.alibaba-inc.com'

export const ZHAO_SHANG = 'https://zs.kunlun.alibaba-inc.com'

export const LOGOUT_URL = `${SSO_ROOT}/ssoLogout.htm?APP_NAME=${APP_NAME}`
  + `&BACK_URL=${API_ROOT}/api/v1/acl/redirectUrl`
  + `?ZXDT_URL=${window.location.href}`
export const LOGOUT_CALLBACK_PAGE = `${FE_APP_ROOT}/page/bucSSOLogout.htm`

export const PLAN_URL = `https://kunlun.ele.me/home/<USER>/https://boreas.ele.me/page/benefit-kunlun/index.html#/plan?pageNum=1&pageSize=10`

export const CROWED_URL = `https://market.m.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/list`
export const CROWED_SELECT_URL = `https://market.m.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/select`
export const PLAYSET_URL = `https://boreas.kunlun.alibaba-inc.com/page/crm-user/index.html#/crm_user/entry`

export const PLATSET_LIST_URL = `https://boreas.kunlun.alibaba-inc.com/page/crm-user/index.html#/crm_user/list`
export const WHITE_LIST_URL = `https://boreas.kunlun.alibaba-inc.com/page/crm-user/index.html#/crm_user/whiteList`


export const APPLY_VALID_URL = 'http://acl.alibaba-inc.com/apply/instance/index.htm?type=permission'
export const APPLY_URL = 'http://acl.alibaba-inc.com/apply/cart/detail.htm'
export const KL_ROOT = 'https://kunlun.alibaba-inc.com'
// 权益功能相关接口path
export const BENEFIT_PATH = `https://stargate.ele.me/bwm_newretail.userright_app`
