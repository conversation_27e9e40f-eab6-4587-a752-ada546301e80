import Mock from 'mockjs'
import rndImg from './rndimg.jpg'

const Random = Mock.Random

export default function easyMock(descObj, number = 20) {
  if (typeof descObj === 'object') {
    if (Array.isArray(descObj)) {
      if (descObj.length === 0) {
        return []
      }
      const list = []
      for (let i = 0; i < number; i++) {
        if (typeof descObj[0] === 'object') {
          list.push(easyMock(descObj[0]))
        } else {
          list.push(mockValue('xxx', descObj[0]))
        }
      }
      return list
    } else {
      const row = {}
      for (let key in descObj) {
        row[key] = mockValue(key, descObj[key])
      }
      return row
    }
  } else {
    throw 'not impl. descObject must be array or object'
  }
}

function mockValue(key, val) {
  if (key.match(/(data|time)/i)) {
    return Random.date()
  } else if (key.match(/title/i)) {
    return Random.ctitle()
  } else if (typeof val === 'number') {
    if ((val + '').match(/^[0-9]+$/)) {
      return Random.natural(1, 1000)
    } else {
      return Random.float()
    }
  } else if (typeof val === 'string') {
    if (val.match(/^\d+-\d+$/)) {
      const [min, max] = val.split('-')
      return Random.natural(min, max)
    }
    if (key.match(/(img|cover|logo)/i)) {
      return rndImg
    }
    return Random.ctitle()
  } else if (typeof val === 'object') {
    return easyMock(val)
  } else {
    return Random.ctitle()
  }
}
