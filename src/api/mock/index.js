import Mock from 'mockjs'
import easyMock from './easyMock'

export default {
  boreas: {
    '/test-request-form': () => {
      return {
        abc: 'xyz'
      }
    },
    '/CouponItemReadServiceI.itemOpLog': () => {
      return Mock.mock({
        'data|20': [
          {
            'id|+1': 1,
            opName: '123',
            opType: '删除商品券',
            opTypeDesc: '。。。。。',
            opTime: '2016-09-10',
            operatorName: '张三',
            opDesc: ['abc', 'def']
          }
        ]
      })
    },

    '/CouponItemReadServiceI.queryItem': () => {
      return {
        total: 40,
        data: easyMock([
          {
            title: '',
            itemId: 1,
            categoryId: '',
            originPrice: 1,
            currentPrice: 1,
            surfaceImg: '',
            detailImgList: [],
            showImgList: [],
            ticketDetailList: [
              {
                type: '',
                title: '',
                useNum: 1,
                price: 1,
                threshold: 1
              }
            ],
            stock: '',
            canUseFrequency: '',
            status: '1-5',
            limitNum: '',
            usePromotionBoth: false,
            relativeDay: ''
          }
        ])
      }
    },
    '/plan/list': () => {
      return {
        total: 40,
        data: easyMock([
          {
            name: '投放计划',
            planId: 99,
            creator: 'wenjing.he',
            status: '1',
            timeRangeDesc: '投放时间描述',
            gmtCreate: '1972-01-30 14:37:48'
          },
          {
            name: '投放计划2',
            planId: 2,
            creator: 'wenjing.he',
            status: '1',
            timeRangeDesc: '投放时间描述',
            gmtCreate: '1982-03-23 20:54:54'
          },
          {
            name: '投放计划3',
            planId: 3,
            creator: 'wenjing.he',
            status: '1',
            timeRangeDesc: '投放时间描述',
            gmtCreate: '1998-02-06 01:12:59'
          }
        ])
      }
    }
  }
}
