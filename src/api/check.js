import DomainConfigure from '@/packages/request/DomainConfigure'
import { buildEnv } from '@/packages/env'

const env = buildEnv()
const loginHost = DomainConfigure.login[env]
const aclHost = DomainConfigure.acl[env]
const benefit2 = DomainConfigure.benefit2[env]

export function goToLogin(from = window.location.href) {
    // https://test-userights.kunlun.alibaba.net/api/v1/acl/redirectUrl?ZXDT_URL=xxx
    window.location.href = `https://${benefit2}/api/v1/acl/redirectUrl?ZXDT_URL=${from}`
}
  
export function goAcl(appName) {
    window.location.href = `https://${aclHost}/apply/instance/index.htm?type=role&appName=${appName}`
}
