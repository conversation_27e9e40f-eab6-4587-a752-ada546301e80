import React, { Component } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@alife/kunlun-base'
import { Route, HashRouter } from 'react-router-dom'

function toPath(file) {
  const prts = file.split(/[\\.\\/]/)
  prts.pop()
  return '/' + prts.filter(x => x).join('/')

}
export default class PageLayout extends Component {

  render() {

    /* 路由相关 */

    const req = this.props.routeContext
    const files = req.keys()
    const routeList = files.map(file => {
      const C = req(file).default
      const p = toPath(file)
      return {
        path: p,
        component: C
      }
    })
    return <HashRouter>
      <Header />
      <div className='content-wrap'>
        <Sider data={this.props.menuLayout} />
        <div className='right-content'>
          {routeList.map((r) => {
            return <Route key={r.path} exact path={r.path} component={r.component} />
          })}
        </div>
      </div>
    </HashRouter>
  }
}
