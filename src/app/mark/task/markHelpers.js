import React, { Component, useState } from 'react'
import PropTypes from 'prop-types'
import { <PERSON>, withRouter } from 'react-router-dom'
import { Button, Grid, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import _ from 'lodash'
import { request } from '@/packages/request'

const typeMapToState = {
  right: 1,
  rowRight: 1,
  error: 0,
  mark: 3
}

export async function markService({ markList, bzList, categoryData }) {
  // const [{ type, odpsFiledName, record } ]= markList
  // 标注某一项值
  // type 标注类型 正确 right 错误 error 此列准确 rowRight 订正 mark
  // odpsFiledName 标注数据源字段 订正的话为mark
  //   条形码   upc_id                   =>     a19
  // 商品名称 upc_name                 =>     a2
  // 商品图片 photos                   =>     a6
  // 商品类目名称 cat_name_list         =>     a5
  // 预测类目名称 cat_name_predict_list  =>   a18
  const r = markList.map(item => {
    const { type, odpsFiledName, record } = item
    let bzResult = {}
    if (odpsFiledName === 'mark') {
      bzList.forEach((item, index) => {
        bzResult.mark = categoryData
      })
    } else {
      bzResult = {
        [odpsFiledName]: type === 'rowRight' ? record[odpsFiledName] : type
      }
    }
    return {
      taskId: record.taskId,
      allocId: record.allocId, // 任务分配id
      datasetRowId: record.datasetRowId, // 标注数据源行id
      odpsFiledName, // 标注数据源字段
      bzState: typeMapToState[type], // 标注状态  0：错误 1：正确（此例正确）2：无法判断 3：订正
      bzResult: JSON.stringify(bzResult), // 标注结果: 正确、错误、订正后的数据 key:要操作的字段 value:操作的结果集
    }
  })
  
  const res = await request(
    {
      url: `mark://api/mark/v1/markTask.json`,
      method: 'POST'
    },
    {
      body: JSON.stringify(r)
    }
  )
  
  return res
}
