import React, { Component, useState } from 'react'
import PropTypes from 'prop-types'
import { Link } from 'react-router-dom'
import { Grid, Button, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import _ from 'lodash'
// import { Filter, Table } from '../packages/FilterTable'
import { Filter, Table, useFilterTable } from '../packages/FilterTable'
import withRequest from '@/packages/hoc/withRequest'
import { request } from '@/packages/request'
import formatDate from '@/utils/formatDate'
import MarkTable from './markTable'

const { Row, Col } = Grid
@withRequest({
  action: {
    url: `mark://api/mark/v1/getTaskTemplate.json`,
    mapRequest: (json, { query }) => {
      const { pageNum, pageSize, ...o } = json
      return {
        query: {
          taskId: query.taskId
        },
        size: pageSize,
        page: pageNum
      }
    },
  },
  mapStateToProps: ({ data }) => {
    return {
      markColumns: data
    }
  }
})
export default class TaskMark extends Component {
  constructor(props) {
    super(props)
    this.state = {
      detailData: {}
    }
  }

  fecthDetail = async () => {
    const r = await request({
      url: `mark://api/mark/v1/getAllocateStatisticsInfo.json`,
      method: 'POST',
      mapRequest: (json, { query }) => {
        return {
          taskId: query.taskId,
          allocId: query.allocId
        }
      },
    })
    this.setState({
      detailData: r || {}
    })
  }

  componentDidMount() {
    this.fecthDetail()
  }

  render() {
    const { markColumns } = this.props || {}
    const { detailData } = this.state
    return (
      <div className="ali-boreas-main task-mark mark-main">
        <div className="task-mark-title">任务名称：{detailData.taskName}</div>
        <Row className="task-mark-header-content">
          <Col span="7" className="task-mark-header-item">
            <div>标注总量：</div>
            <div>{detailData.bzSampleNum || 0}</div>
          </Col>
          <Col span="7" className="task-mark-header-item">
            <div>总量：</div>
            <div>{detailData.sampleNum || 0}</div>
          </Col>
          <Col span="7" className="task-mark-header-item">
            <div>标注规则：</div>
            <div>{detailData.bzRules || ''}</div>
          </Col>
        </Row>
        {/** MarkTable */}
        <MarkTable columns={markColumns} fecthDetail={this.fecthDetail} />
      </div>
    )
  }
}

