import React, { Component, useState, useEffect } from 'react'
import PropTypes, { func } from 'prop-types'
import { <PERSON>, withRouter } from 'react-router-dom'
import { Button, Grid, Message, Dialog, Icon, Select, MenuButton, Loading } from '@alifd/next'
import _ from 'lodash'
import qs from 'qs'
import bosify from '@/utils/bosify'
import { Filter, Table as BoreasTable, useFilterTable } from '../packages/FilterTable'
import AsyncCascaderSelect from '../packages/AsyncCascaderSelect'
import { request } from '@/packages/request'
import parseUrl from '@/packages/request/parseUrl'
import withRequest from '@/packages/hoc/withRequest'
import useTable from '../packages/hooks/useTable'
import markDialog from './markDialog'
import { markService } from './markHelpers'

const bizStateMap = {
  0: '未标注',
  1: '已标注',
  2: '已标注为无法判断'
}

function MarkTable(props) {
  // console.log('MarkTable props', props)
  const { taskId, dsId } = parseUrl(props.location && props.location.search).query || {}

  const [markList, setMarkList] = useState([])
  const [categoryMap, setCategoryMap] = useState({})
  // const [loading, setLoading] = useState(false)
  const [cascaderCategory, setCascaderCategory] = useState(false)


  // columns
  let columns = (props.columns || []).concat([{
    taskId,
    attributeList: 'bzState',
    listDesc: '标注状态',
    listWidth: 100,
    isFilter: null,
    isBz: null,
    markItem: null,
    markItemData: null,
  }, {
    taskId,
    attributeList: 'mark',
    listDesc: '订正',
    listWidth: 200,
    isFilter: null,
    isBz: null,
    markItem: null,
    markItemData: null,
  }])

  const changeCategory = ({ categoryData, itemData }) => {
    // type, v, record, extra = {}
    // extra.selectedPath
    const chooseCategoryData = categoryData && categoryData[3] && categoryData[3].selectedPath.map(v => {
      return {
        categoryId: v.value,
        categoryName: v.label
      }
    })

    setCategoryMap({
      ...categoryMap,
      [itemData.datasetRowId]: chooseCategoryData
    })
  }

  const renderCell = (_v) => {
    if (_v && _v.match(/^(http|https):/)) {
      return <img src={bosify(_v)} style={{ maxWidth: '80px' }} />
    }
    return _v
  }

  // 标注类型 1.【正确、错误】：当仅一个属性列被置为标注项且不存在订正列时  2.【此列准确】
  let bzType = 2
  const bzList = columns.filter(item => item.isBz)
  const hasMark = columns.filter(item => item.attributeList === 'mark')
  if (bzList.length === 1 && !hasMark.length) {
    bzType = 1
  }
  const tableColumns = columns.map(item => {
    const itemData = {
      title: item.listDesc,
      dataIndex: item.attributeList,
      width: item.listWidth || 100
    }

    
    
    if (item.isBz) {
      itemData.cell = (value, index, record, { reload }) => {
        record.bzResultList = (record.bzResultList || []).slice(0, 1)
        // 处理bzResultList
        let bzResultMap = {}
        record.bzResultList && record.bzResultList.forEach(_bzItem => {
          bzResultMap[_bzItem.odpsFiledName] = _bzItem
        })

        let hasBz = 0
        if (bzResultMap[item.attributeList]) {
          hasBz = 1
        }

        if (item.attributeList === 'a5' && record.a4 === record.a5) {
          return (
            <div>
              <div>{renderCell(value)}</div>
              <Button
                type="primary"
                disabled
                style={{ marginTop: "6px" }}
              >
                同左
              </Button>
            </div>
          )
        }
        return (
          <div>
            <div>{renderCell(value)}</div>
            {bzType === 1 ? (
              <div style={{ marginTop: "6px" }}>
                <Button
                  type="primary"
                  style={{ marginRight: "6px" }}
                  onClick={() => {
                    handleMark("right", value, item, record, reload);
                  }}
                >
                  正确
                </Button>
                <Button
                  onClick={() => {
                    handleMark("error", value, item, record, reload);
                  }}
                >
                  错误
                </Button>
              </div>
            ) : (
              <Button
                type="primary"
                disabled={!!hasBz}
                style={{ marginTop: "6px" }}
                onClick={() => {
                  handleMark("rowRight", value, item, record, reload)
                }}
              >
                此列准确
              </Button>
            )}
          </div>
        )
      }
    }

    if (item.attributeList === 'mark') {
      // itemData.width = 350
      itemData.cell = (value, index, record, { reload }) => {
        record.bzResultList = (record.bzResultList || []).slice(0, 1)
        // 处理标注的mark数据
        let bzResultToMark = {}
        record.bzResultList && record.bzResultList.forEach(_bzItem => {
          if (_bzItem.odpsFiledName === 'mark') {
            let _formatInnerBzResult = {}
            try {
              _formatInnerBzResult = _bzItem.bzResult ? JSON.parse(_bzItem.bzResult || '{}') : {}
            } catch (error) {
              _formatInnerBzResult = {}
            }
            bzResultToMark = {
              _bzItem,
              bzResult: _formatInnerBzResult.mark || []
            }
          }
        })

        const defaultCategory = _.get(bzResultToMark, 'bzResult[2].categoryId', '')
        let markedCategory = _.get(bzResultToMark, 'bzResult', [])
        if (!Array.isArray(markedCategory)) {
          markedCategory = []
        }
        const defaultExpandedValue = markedCategory.reduce((c, n) => (c || []).concat(n.categoryId.toString()), null) || ['201218121', '201221734', '201223557']
        const categoryData = categoryMap[record.datasetRowId]

        let saveDisabled = false
        if (!categoryData && defaultCategory) {
          saveDisabled = true
        }

        // 预览、删除
        return (
          // 点击编辑，进入任务详情页，允许编辑任务描述和审核规则
          <div className="ali-boreas-cell">
            <AsyncCascaderSelect
              defaultExpandedValue={defaultExpandedValue}
              defaultValue={defaultCategory}
              onEvent={(...args) => {
                changeCategory({
                  categoryData: args,
                  itemData: record
                })
              }}
              dataSource={cascaderCategory}
            />
            <Button type="primary" style={{ marginTop: '6px' }} disabled={saveDisabled} onClick={() => {
              handleMark('mark', value, index, record, reload)
            }}>保存</Button>
          </div>
        )
      }
    } else if (item.attributeList === 'bzState') {
      itemData.cell = (value) => {
        return <div className={`status-${value}`}>{bizStateMap[value]}</div>
      }
    } else if (!item.isBz) {
      itemData.cell = (value) => {
        return renderCell(value)
      }
    }

    return itemData
  })

  const handleMark = (type, value, item, record, reload) => {
    // console.log('record1111111', type, value, item, record)
    // 标注某一项值
    // type 标注类型 正确 right 错误 error 此列准确 rowRight 保存 mark
    const categoryData = categoryMap[record.datasetRowId]
    // console.log('categoryData', categoryData, value, record)
    if (type === 'mark' && !categoryData) {
      return Message.error('请选择或变更订正类目')
    }

    let odpsFiledName = ''
    if (type === 'mark') {
      odpsFiledName = 'mark'
    } else {
      odpsFiledName = item.attributeList
    }

    markService({
      markList: [].concat({
        type,
        odpsFiledName,
        record 
      }),
      bzList,
      categoryData,
    }).then(data => {
      // successRowIds  failureRowIds
      if (data && data.successRowIds.length > 0) {
        Message.success('标注成功')
        delete categoryMap[record.datasetRowId]
        setCategoryMap(categoryMap)
        // setTimeout(() => {
          // window.location.reload()
        reload()
        props.fecthDetail && props.fecthDetail()
        // }, 500)
      } else {
        Message.error(data.errorMessage || '标注失败')
      }
    })
  }

  const batchMark = () => {
    if (!markList || !markList.length) {
      return Message.error('请选择批量标注数据')
    }
    markDialog(bzType, cascaderCategory, markList, bzList)
  }

  const batchMarkNoJudge = () => {
    if (!markList || !markList.length) {
      return Message.error('请选择需要标注的数据')
    }
    
    Dialog.confirm({
      title: '提示',
      content: '是否确认将所选择任务的未标注项均置为无法判断？',
      messageProps: {
        type: 'warning'
      },
      onOk: () => {
        const bzR = markList.map(item => {
          return {
            taskId: item.taskId,
            allocId: item.allocId, // 任务分配id
            datasetRowId: item.datasetRowId, // 标注数据源行id
            odpsFiledName: 'mark', // 标注数据源字段
            bzState: 2, // 标注状态  0：错误 1：正确（此例正确）2：无法判断 3：订正
            bzResult: item.bzResult ? JSON.stringify(item.bzResult) : 'datasetrowid', // 标注结果: 正确、错误、订正后的数据 key:要操作的字段 value:操作的结果集
          }
        })
        request(
          {
            url: `mark://api/mark/v1/markTask.json`,
            method: 'POST'
          },
          {
            body: JSON.stringify(bzR)
          }
        ).then(_data => {
          if (_data && _data.successRowIds.length > 0) {
            Message.success('标注成功')
            setTimeout(() => {
              window.location.reload()
              // search()
            }, 500)
          } else {
            Message.error(_data.errorMessage || '标注失败')
          }
        })
      },
    })
  }

  // 渲染
  const tableProps = {
    // hasBorder: false,
    history: props.history,
    emptyContent: "暂无标注任务信息~",
    primaryKey: "datasetRowId",
    rowSelection: {
      onChange: (...args) => {
        setMarkList(args[1])
      },
      // columnProps: () => {
      //   return {
      //     lock: 'left',
      //     // width: 60,
      //     align: 'center'
      //   }
      // },
    }
  }

  const R = useFilterTable({
    tableConfig: tableColumns,
    tableProps,
  })({
    action: async (_values) => {
      const { pageSize, pageNum, ...others } = _values
      // setLoading(true)
      try {
        const r = await request({
          url: `mark://api/mark/v1/getMarkTaskList.json`,
          method: 'POST',
          mapRequest: json => {
            return {
              query: {
                taskId,
                dsId
              },
              size: pageSize || 1,
              page: pageNum || 10
            }
          },
          mapResponse: (json, { _props, _query }) => {
            return {
              total: json.total,
              data: json.rows || []
            }
          }
        })
        // setLoading(false)
        return r
      } catch (error) {
        console.log('useFilterTable catch')
        // setLoading(false)
      }
    },
    mapStateToProps: (_data) => {
      let { dataSource, total } = _data
      dataSource = dataSource && dataSource.map((item, index) => {
        const template = item.template
        const validTemplateData = _.keys(template).reduce((c, n) => {
          if (template[n]) {
            return {
              ...c,
              [n]: template[n]
            }
          }
          return c
        }, {})
        return {
          ...item,
          ...validTemplateData,
        }
      })
      return {
        total,
        dataSource
      }
    }
  })

  useEffect(() => {
    async function init() {
      const _data = await request({
        url: `mark://api/category/v1/getCategory.json`,
      })
      setCascaderCategory(_data)
    }
    init()
  }, [])

  return (
    <div style={{ marginTop: '20px' }}>
      {R.table}
      {/* {loading && <Loading fullScreen />} */}
      <div className="task-mark-table-batch flex">
        <div className="flex-child">
          <Button type="primary" size="large" style={{ width: 'auto' }} onClick={batchMarkNoJudge}>未标注项均无法判断</Button>
        </div>
        <div style={{ textAlign: "right" }}>
          <span style={{ marginRight: '12px' }}>已选{markList.length}条</span>
          <Button type="primary" onClick={batchMark}>批量标注</Button>
        </div>
      </div>
    </div>
  )
}

export default withRouter(MarkTable)
