import React, { Component, useState } from 'react'
import PropTypes from 'prop-types'
import { Link } from 'react-router-dom'
import { Button, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import _ from 'lodash'
// import { Filter, Table } from '../packages/FilterTable'
import { Filter, Table, useFilterTable } from '../packages/FilterTable'
import { request } from '@/packages/request'
import parseUrl from '@/packages/request/parseUrl'
import formatDate from '@/utils/formatDate'

const filterConfig = {
  type: 'Filter',
  // labelTextAlign: 'left',
  // buttonAlign: 'end', // end-line  start-line(仅仅支持一个选项的时候) end start 默认
  // hasExpand: 'rightTop'
  children: [
    {
      name: 'taskName',
      type: 'Input',
      props: {
        label: '任务名称:',
        placeholder: '请选择任务名称',
        hasClear: true,
      }
    },
    {
      name: 'creator',
      type: 'Input',
      props: {
        label: '创建者:',
        placeholder: '请选择创建者',
        hasClear: true,
      }
    },
    {
      name: 'taskState',
      type: 'Select',
      props: {
        label: '状态:',
        placeholder: '请选择状态',
        hasClear: true,
        defaultValue: '',
        dataSource: [{
          // 未开始、进行中、已完成
          label: '全部',
          value: ''
        }, 
        {
          label: '未开始',
          value: 0
        },
        {
          label: '进行中',
          value: 1
        },
        {
          label: '已完成',
          value: 2
        }]
      }
    },
  ],
}

const deleteListItem = (record, reload) => {
  Dialog.confirm({
    title: '提示',
    content: '删除该任务后将无法恢复，只能重新创建。是否确认删除？',
    messageProps: {
      type: 'warning'
    },
    onOk: () => {
      console.log('deleteListItem', record, reload)
      // request(
      //   {
      //     url: `benefit://me.ele.newretail.bz.commodity.api.DatasetService#deleteDatasetById`,
      //     method: 'POST'
      //   },
      //   {
      //     body: {
      //       dsId: record.dsId,
      //     }
      //   }
      // ).then(() => {
      //   Message.success('删除成功')
      //   reload()
      // })
    },
  })
}

const taskStateMap = {
  '': '全部',
  0: '未开始',
  1: '进行中',
  2: '已完成'
}

function TaskList(props) {
  // 任务ID、任务名称、创建者、数据量、任务进度、状态、创建时间、操作（结果统计、编辑、模板编辑、删除、标注、组进度）
  const tableColumns = [
    {
      title: '任务ID',
      dataIndex: 'taskId',
      width: 140,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      width: 160
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      width: 80
    },
    {
      title: '数据量',
      dataIndex: 'sampleNum',
      sortable: true,
      width: 100
    },
    {
      title: '任务进度',
      dataIndex: 'bzSampleNum',
      width: 100,
      cell: (v, i, record) => {
        const { bzSampleNum, sampleNum } = record
        const precent = (sampleNum && (bzSampleNum / sampleNum) * 100).toFixed(2) + '%'
        return <div style={{ maxWidth: '10em', lineHeight: '22px' }}>{precent || '0%'}</div>
      },
    },
    {
      title: '状态',
      dataIndex: 'taskState',
      width: 80,
      cell: v => {
        return <div style={{ maxWidth: '10em', lineHeight: '22px' }} className={`status-${v - 1}`}>{taskStateMap[v]}</div>
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      sortable: true,
      cell: v => {
        return formatDate(v)
      },
    },
    {
      title: '操作',
      dataIndex: 'op',
      cell: (value, index, record, { reload }) => {
        if (+record.taskState >= 1) {
          return null
        }
        
        // 预览、删除
        return (
          // 点击编辑，进入任务详情页，允许编辑任务描述和审核规则
          <div className="ali-boreas-cell">
            <span className="pr" onClick={() => {
              const { pageNum, pageSize } = parseUrl(props.location.search).query || {}
              props.history.push({
                pathname: `/task/list/editor`,
                search: `?taskId=${record.taskId}&pageNum=${pageNum}&pageSize=${pageSize}`
              })
            }}>编辑</span>
            {/**
              <span onClick={() => deleteListItem(record, reload)} className="pl">
              删除
            </span>
          */}
          </div>
        )
      }
    },
  ]

  const handleChange = (type, values, field) => {
    // console.log('list handleChange', type, values)
  }

  const filterProps = {
    history: props.history,
    onEvent: (type, values, field) => {
      // console.log('filterProps onevent', type, values, field)
    },
    // resetType: 'reset',
    formItemLayout: {
      labelCol: { span: 7 },
      wrapperCol: { span: 14 },
    }
  }

  const tableProps = {
    // hasBorder: false,
    emptyContent: "请添加任务~",
    sortIcons: {
      desc: <Icon style={{ top: '6px', left: '4px' }} type={'arrow-down'} size="small" />,
      asc: <Icon style={{ top: '-6px', left: '4px' }} type={'arrow-up'} size="small" />
    },
    defaultSort: {
      sampleNum: 'desc',
      createTime: 'asc'
    }
  }

  const R = useFilterTable({
    filterConfig,
    filterProps,
    tableConfig: tableColumns,
    tableProps,
  })({
    action: async (_values) => {
      console.log(_values)
      const { pageSize, pageNum, ...others } = _values
      const r = await request({
        url: `mark://api/task/v1/taskList.json`,
        method: 'POST',
        mapRequest: json => {
          return {
            query: others,
            size: pageSize,
            page: pageNum
          }
        },
        mapResponse: (json, { _props, _query }) => {
          return {
            total: json.total,
            data: json.rows || []
          }
        }
      })
      return r
    },
  })

  return (
    <div className="ali-boreas-main mark-main">
      {R.filter}
      <Button
        type="primary"
        style={{ marginTop: '16px' }}
        onClick={() => {
          props.history.push('/task/new')
        }}
      >
      新建任务
      </Button>
      <div style={{ marginTop: '20px' }}>
        {R.table}
      </div>
    </div>
  )
}

export default TaskList


