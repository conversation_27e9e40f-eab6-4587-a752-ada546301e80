import React, { Component, useState, useRef } from 'react'
import PropTypes from 'prop-types'
import { Link } from 'react-router-dom'
import { Radio, Grid, Button, Form, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import _ from 'lodash'
import useForm from '../packages/hooks/useForm'
import AsyncCascaderSelect from '../packages/AsyncCascaderSelect'
import { markService } from './markHelpers'

const RadioGroup = Radio.Group

function BatchMarkDialog({ dataSource, setBatchFormData, batchColumns = [] }) {
  const form = useForm({})
  const changeMarkCategory = (type, v, record, extra = {}) => {
    // extra.selectedPath
    const chooseCategoryData = extra.selectedPath.map(v => {
      return {
        categoryId: v.value,
        categoryName: v.label
      }
    })
    const r = form.setValue('categoryData', chooseCategoryData)
    setBatchFormData(r)
  }

  const changeBatchType = (v) => {
    const r = form.setValue('type', v)
    setBatchFormData(r)
  }

  return (
    <div style={{ width: '500px' }}>
      <RadioGroup onChange={changeBatchType}>
        {
          batchColumns.map(item => {
            return (
              <Form.Item>
                <Radio id={item.attributeList} value={item.attributeList}>{item.listDesc}列准确</Radio>
              </Form.Item>
            )
          })
        }
        <Form.Item>
          <Radio id="订正" value="mark">
            订正为
            <div style={{ display: 'inline-block ', paddingLeft: '6px' }}>
              <AsyncCascaderSelect
                onEvent={changeMarkCategory}
                dataSource={dataSource}
            />
            </div>
          </Radio>
        </Form.Item>
      </RadioGroup>
    </div>
  )
}


export default function markDialog(bzType, cascaderCategory, markList, bzList, search) {
  // const [{ type, odpsFiledName, record } ]= markList
  // bzType 1为正确错误 2为此列准确
  let markFormData = {}
  const mark = async () => {
    // 批量订正 categoryData type
    const { type, categoryData } = markFormData
    console.log('mark', markList, bzList, markFormData)
    if (!type) {
      return Message.error('请选择批量订正方式')
    } else if (type === 'mark' && !categoryData) {
      return Message.error('请选择订正类目')
    }

    await markService({
      markList: markList.map(item => {
        return {
          type: type === 'mark' ? 'mark' : 'rowRight',
          odpsFiledName: type,
          record: item 
        }
      }),
      bzList,
      categoryData,
    }).then(data => {
      // successRowIds  failureRowIds
      if (data && data.successRowIds.length > 0) {
        Message.success('批量标注成功')
        setTimeout(() => {
          // search()
          window.location.reload()
        }, 500)
      } else {
        Message.error(data.errorMessage || '标注失败')
      }
    })
    dialog.hide()
  }

  const setBatchFormData = (data) => {
    markFormData = data
  }

  const dialog = Dialog.confirm({
    title: '',
    // isFullScreen: true,
    content: <BatchMarkDialog dataSource={cascaderCategory} setBatchFormData={setBatchFormData} batchColumns={bzList} />,
    footer: (
      <div>
        <Button type="primary" style={{ marginRight: '6px' }} onClick={() => mark()}>
            确认
        </Button>
        <Button onClick={() => dialog.hide()}>
            取消
        </Button>
      </div>
    )
  })
}
