import React, { Component, useState } from 'react'
import PropTypes from 'prop-types'
import { <PERSON>, with<PERSON><PERSON><PERSON> } from 'react-router-dom'
import { Form, Input, Pagination, Button, Field, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import _ from 'lodash'
import boreasForm, { useForm, useFormAction } from '../packages/Form'
import taskFormConfig, { odpsMap, workersMap } from './task-new-config'
import parseUrl from '@/packages/request/parseUrl'
import { request } from '@/packages/request'

export default (props) => {
  const { taskId } = parseUrl(props.location.search).query || {}

  let {
    initFormData,
    submit,
    onSuccess
  } = useFormAction({
    submitAction: {
      action: async (_values) => {
        const { workers, ...params } = _values
        let workersData = {}
        workers && [].concat(workers).forEach(item => {
          const id = workersMap[item] && workersMap[item].ownerUserId
          workersData[id] = item
        })
        const formData = {
          ...params,
          dsId: odpsMap[params.dsName].dsId,
          workers: workersData,
          sampleNum: odpsMap[params.dsName].sampleNum
        }
        const r = request({
          url: `mark://api/task/v1/taskCreate`,
          method: 'POST',
        }, { method: 'POST', body: formData })
        return r
      },
    },
    onSuccess: () => {
      console.log('onsucess')
    },
  })

  const handleSubmit = async () => {
    const formDataValues = field.fields
    console.log('handleSubmit', formDataValues)
    const { firsetErrors } = await field.validate()
    if (firsetErrors) {
      Message.error(firsetErrors[0] && firsetErrors[0].message)
      return
    }
    try {
      const r = await submit(formDataValues)
      if (r > 0) {
        Message.success('创建成功')
        setTimeout(() => {
          props.history && props.history.push('/task/my')
        }, 300)
      }
    } catch (error) {
      console.error('handleSubmit', error)
    }
  }

  const onCancel = () => {
    // field.resetToDefault()
    props.history.goBack()
  }

  initFormData = initFormData || {}
  const { BoreasForm, field } = boreasForm({
    config: taskFormConfig,
    initFormData,
  })

  return (
    <div style={{ paddingTop: '20px' }}>
      {BoreasForm}
      <div style={{ textAlign: 'center', paddingTop: '20px' }}>
        <Button
          type="primary"
          style={{ marginRight: '20px' }}
          onClick={handleSubmit}
        >
        提交
        </Button>
        <Button onClick={onCancel}>
          取消
        </Button>
      </div>
    </div>
  )
}
