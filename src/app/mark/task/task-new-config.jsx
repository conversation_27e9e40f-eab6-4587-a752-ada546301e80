import React, { Component, useState } from 'react'
import { <PERSON>, with<PERSON><PERSON><PERSON> } from 'react-router-dom'
import { Form, Input, Pagination, Button, Field, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import _ from 'lodash'
import boreasForm, { useForm, useFormAction } from '../packages/Form'
import parseUrl from '@/packages/request/parseUrl'
import { request } from '@/packages/request'

const workersMap = {}
const odpsMap = {}

const formConfig = {
  type: 'Form',
  children: [
    {
      name: 'taskName',
      type: 'Input',
      props: {
        label: '任务名称:',
        placeholder: '请输入任务名称',
        // defaultValue: 'aaaa',
        hasClear: true,
        // hasFeedback: true,
        required: true,
        rules: [
          {
            required: true,
            message: '任务名称不能为空',
          },
        ],
      }
    },
    {
      name: 'dsName',
      type: 'Select',
      props: {
        label: '数据集:',
        // placeholder: '数据集',
        hasClear: true,
        showSearch: true,
        required: true,
        // filterLocal: false,
        rules: [
          {
            required: true,
            message: '数据集不能为空',
          },
        ],
        afterCell: () => {
          return (
            <Button type="primary" className="task-new-btn">
              <Link to="/odps/new" style={{ color: '#ffffff' }}>
                + 新建数据集
              </Link>
            </Button>
          )
        },
        dataSource: {
          action: async (v) => {
            const r = await request({
              method: 'POST',
              url: 'mark://api/ds/v1/getDataSet.json',
              mapRequest: () => {
                return {
                  query: v,
                  synState: 3
                }
              },
            })
            console.log(r)
            return r
          },
          mapStateToProps: (data) => {
            const r = _.get(data, 'data', [])
            r.forEach(item => {
              odpsMap[item.dsName] = item
            })
            return _.flatMap(r, (item, i) => ({
              value: item.dsName,
              label: item.dsName,
            })) || []
          },
        }
      }
    },
    {
      name: 'taskDes',
      type: 'Input.TextArea',
      props: {
        label: '任务描述:',
        // placeholder: '任务描述',
        required: true,
        rules: [
          {
            required: true,
            message: '任务描述不能为空',
          },
        ],
      }
    },
    {
      name: 'bzRules',
      type: 'Input.TextArea',
      props: {
        label: '标注规则:',
        // placeholder: '标注规则',
        required: true,
        rules: [
          {
            required: true,
            message: '标注规则不能为空',
          },
        ],
      }
    },
    {
      name: 'workers',
      type: 'Select',
      props: {
        label: '评测人员:',
        placeholder: '请选择评测人员',
        hasClear: true,
        showSearch: true,
        required: true,
        filterLocal: false,
        changeLocal: true,
        // mode: 'tag',
        mode: 'multiple',
        rules: [
          {
            required: true,
            message: '评测人员不能为空',
          },
        ],
        dataSource: {
            action: async (v) => {
              const r = await request({
                url: 'mark://api/buc/v1/findUsers.json',
                mapRequest: () => {
                  const workers = [].concat(v)
                  return {
                    keyWord: workers.pop() || ''
                  }
                },
              })
              return r
            },
            mapStateToProps: (data) => {
              const r = _.get(data, 'rows', [])
              r.forEach(item => {
                workersMap[item.lastName] = item
              })
              return _.flatMap(r, (item, i) => ({
                value: item.lastName,
                label: item.lastName,
              })) || []
            },
          }
        }
    },
  ],
}

export default formConfig

export {
  workersMap,
  odpsMap,
}
