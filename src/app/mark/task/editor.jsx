import React, { Component, useState } from 'react'
import PropTypes from 'prop-types'
import { <PERSON>, with<PERSON><PERSON><PERSON> } from 'react-router-dom'
import { Form, Input, Pagination, Button, Field, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import _ from 'lodash'
import boreasForm, { useForm, useFormAction } from '../packages/Form'
// import taskFormConfig, { odpsMap, workersMap } from './taskFormConfig'
import editorConfig, { odpsMap } from './task-editor-config'
import parseUrl from '@/packages/request/parseUrl'
import { request } from '@/packages/request'

export default (props) => {
  const { taskId, pageNum, pageSize } = parseUrl(props.location.search).query || {}

  let {
    initFormData,
    submit,
    onSuccess
  } = useFormAction({
    requestAction: {
      action: async () => {
        const r = request({
          url: `mark://api/task/v1/getTaskDetail`,
          mapRequest: query => {
            return {
              taskId
            }
          }
        })
        return r
      },
      mapStateToProps: (_data) => {
        const { ext, ...others } = _data
        return {
          ..._data,
          dsName: _.findKey(odpsMap, odpsMap[others.dsId]),
          workers: _.values(JSON.parse(ext || '{}')) || []
        }
      }
    },
    submitAction: {
      action: async (_values) => {
        console.log('_values_values_values', _values)
        const { workers, ext, ...otherParams } = _values
        const formData = {
          ...otherParams,
          dsId: odpsMap[otherParams.dsName].dsId,
          workers: JSON.parse(ext),
          sampleNum: odpsMap[otherParams.dsName].sampleNum
        }
        const r = request({
          url: `mark://api/task/v1/modifyTask`,
          method: 'POST',
        }, {
          method: 'POST',
          body: formData
        })
        return r
      },
    },
    onSuccess: () => {
      console.log('onsucess')
    },
  })

  const handleSubmit = async () => {
    const formDataValues = field.fields
    console.log('handleSubmit', formDataValues)
    const { firsetErrors } = await field.validate()
    if (firsetErrors) {
      Message.error(firsetErrors[0] && firsetErrors[0].message)
      return
    }
    try {
      const r = await submit(formDataValues)
      if (r > 0) {
        Message.success('修改成功')
        setTimeout(() => {
          onCancel()
        }, 300)
      }
    } catch (error) {
      console.error('handleSubmit', error)
    }
  }

  const onCancel = () => {
    // field.resetToDefault()
    // props.history.goBack()
    props.history && props.history.push({
      pathname: `/task/list`,
      search: `?pageNum=${pageNum}&pageSize=${pageSize}`
    })
  }

  initFormData = initFormData || {}
  const { BoreasForm, field } = boreasForm({
    config: editorConfig,
    initFormData,
  })

  return (
    <div style={{ paddingTop: '20px' }}>
      {BoreasForm}
      <div style={{ textAlign: 'center', paddingTop: '20px' }}>
        <Button
          type="primary"
          // loading={queryLoading}
          // disabled={this.props.queryDisabled}
          style={{ marginRight: '20px' }}
          onClick={handleSubmit}
        >
        提交
        </Button>
        <Button onClick={onCancel}>
          取消
        </Button>
      </div>
    </div>
  )
}
