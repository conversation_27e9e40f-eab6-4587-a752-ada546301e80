import React from 'react'
// import Routers from '@/packages/routers'
import _ from 'lodash'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BoreasBreadcrumb } from './packages/SideBar'
import Index from './index'
import OdpsList from './odps/list'
import Preview from './odps/preview'
import OdpsNew from './odps/new'
import TaskList from './task/list'
import My from './task/my'
import TaskMark from './task/mark'
import TaskNew from './task/new'
import TaskEditor from './task/editor'
import './style.scss'

const routerList = [
  {
    menuUrl: '/',
    menuTitle: '首页1',
    component: Index,
    icon: 'account',
  },
  {
    menuUrl: '/odps',
    menuTitle: '数据管理',
    icon: 'leftnav_shuju',
    subMenus: [
      {
        menuUrl: '/odps/list',
        menuTitle: '数据集列表',
        component: OdpsList,
      },
      {
        menuUrl: '/odps/list/preview',
        menuTitle: '预览数据集',
        component: Preview,
      },
      {
        menuUrl: '/odps/new',
        menuTitle: '新建数据集',
        component: OdpsNew,
      },
    ],
  },
  {
    menuUrl: '/task',
    menuTitle: '任务管理',
    icon: 'calendar',
    subMenus: [
      {
        menuUrl: '/task/list',
        menuTitle: '任务列表',
        component: TaskList,
      },
      {
        menuUrl: '/task/new',
        menuTitle: '新建任务',
        component: TaskNew,
      },
      {
        menuUrl: '/task/list/editor',
        menuTitle: '编辑任务',
        component: TaskEditor,
      },
      {
        menuUrl: '/task/my',
        menuTitle: '我的任务',
        component: My,
      },
      {
        menuUrl: '/task/my/mark',
        menuTitle: '标注任务',
        component: TaskMark,
      },
    ],
  },
]

const breadcrumbMap = {
  '/': ['首页'],
  '/odps': ['数据管理'],
  '/odps/list': ['数据管理','数据集列表'],
  '/odps/list/preview': ['数据管理','数据集列表', '数据集预览'],
  '/odps/new': ['数据管理', '新建数据集'],
  '/task': ['任务管理'],
  '/task/list': ['任务管理','任务列表'],
  '/task/new': ['任务管理', '新建任务'],
  '/task/list/editor': ['任务管理','任务列表','编辑任务'],
  '/task/my': ['任务管理','我的任务'],
  '/task/my/mark': ['任务管理','我的任务','标注任务'],
}

export default function App() {
  return (
    <div style={{ flex: 1, overflowX: 'scroll' }}>
      <BoreasRouter routerList={routerList} breadcrumbMap={breadcrumbMap} />
    </div>
  )
}
