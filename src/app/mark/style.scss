@import './packages/index.scss';

.mark-main {
  padding-top: 20px;
}

.odps-preview {
  background: #ffffff;
  margin-top: 20px;
  // min-height: 80vh;
  position: relative;
  margin-left: 20px;
  padding-right: 20px;
  &-btn.next-btn {
    position: absolute;
    left: 10px;
    bottom: 30px;
  }
  .empty {
    bottom: -50px;
  }
}

.task-mark {
  position: relative;
  &-title {
    line-height: 3;
    font-size: 14px;
  }
  &-header-content {
    border-left: 12px solid #f3f3f3;
  }
  &-header-item {
    border-style: solid;
    border-color: #f3f3f3;
    border-width: 12px 16px 12px 0;
    line-height: 2.5;
    &:last-child {
      border-right: 12px solid #f3f3f3;
    }
    div {
      padding-left: 10px;
    }
    div:first-child {
      border-bottom: 1px solid #f3f3f3;
    }
  }
  &-table-batch {
    border-top: 1px solid #f3f3f3;
    padding-top: 20px;
    margin-top: 30px;
    // position: absolute;
    // left: 0;
    // bottom: 0;
  }
  .next-dialog {
    width: 50%;
  }
}

.ali-boreas-form-child {
  .next-form-item-control {
    position: relative;
  }
  .task-new-btn {
    width: auto;
    position: absolute;
    right: -130px;
  }
}

.flex {
  display: flex;
}
.flex-child {
  flex: 1;
}

.bzState {
  &-0 {
    color: red;
  }
  // &-1 {
  //   color: green;
  // }
}

