import React, { Component, useState } from 'react'
import PropTypes from 'prop-types'
import { Link } from 'react-router-dom'
import { Button, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import _ from 'lodash'
// import { Filter, Table } from '../packages/FilterTable'
import { useFilterTable } from '../packages/FilterTable'
import { request } from '@/packages/request'
import parseUrl from '@/packages/request/parseUrl'
import formatDate from '@/utils/formatDate'

const filterConfig = {
  type: 'Filter',
  // labelTextAlign: 'left',
  buttonAlign: 'start-line', // end-line  start-line(仅仅支持一个选项的时候) end start 默认
  // hasExpand: 'rightTop'
  children: [
    {
      name: 'dsName',
      type: 'AutoComplete',
      props: {
        label: '数据集名称:',
        placeholder: '请选择数据集',
        hasClear: true,
        filterLocal: false,
        dataSource: {
          action: async (v) => {
            console.log('vvvv', v)
            const r = request({
              method: 'POST',
              url: 'mark://api/ds/v1/getDataSet.json',
              mapRequest: (body, { props }) => {
                console.log(body, props)
                return {
                  query: v
                }
              },
            })
            return r
          },
          mapStateToProps: (data) => {
            const r = _.get(data, 'data', [])
            return _.flatMap(r, (item, i) => ({
              value: item.dsName,
              label: item.dsName,
            })) || []
          },
        },
      }
    },
  ],
}

// const deleteListItem = (record, reload) => {
//   Dialog.confirm({
//     title: '提示',
//     content: '删除此数据集后，与其关联的所有任务将被删除。是否确认删除？',
//     messageProps: {
//       type: 'warning'
//     },
//     onOk: () => {
//       console.log('deleteListItem', record, reload)
//       request(
//         {
//           url: `mark://api/ds/v1/deleteDatasetById.json`,
//           method: 'POST'
//         },
//         {
//           body: {
//             dsId: record.dsId,
//           }
//         }
//       ).then((res) => {
//         if (res.data > 0) {
//           Message.success('删除成功')
//           reload()
//         } else {
//           Message.error(res.errorMessage)
//         }
//       })
//     },
//   })
// }

function List(props) {
  const tableColumns = [
    {
      title: '数据集ID',
      dataIndex: 'dsId',
      width: 140,
    },
    {
      title: '数据集名称',
      dataIndex: 'dsName',
      width: 220
    },
    {
      title: '样本数',
      dataIndex: 'sampleNum',
      sortable: true,
      cell: v => {
        return <div style={{ maxWidth: '10em', lineHeight: '22px' }}>{v}</div>
      },
    },
    {
      title: '关联任务数',
      dataIndex: 'taskNum',
    },
    {
      title: '导入时间',
      dataIndex: 'createTime',
      cell: v => {
        return formatDate(v)
      },
    },
    {
      title: '操作',
      dataIndex: 'op',
      cell: (value, index, record, { reload }) => {
        // 预览、删除
        return (
          <div className="ali-boreas-cell">
            <span className="pr" onClick={() => {
              const { pageNum, pageSize } = parseUrl(props.location.search).query || {}
              props.history.push({
                pathname: '/odps/list/preview',
                search: `?dsId=${record.dsId}`,
                prePageData: {
                  pageNum,
                  pageSize
                }
              })
            }}>预览</span>
            {/*
            <span onClick={() => deleteListItem(record, reload)} className="pl">
              删除
            </span>
            */}
          </div>
        )
      }
    },
  ]

  const filterProps = {
    history: props.history,
    chunkLength: 1,
    // resetType: 'reset',
    formItemLayout: {
      labelCol: { span: 7 },
      wrapperCol: { span: 15 },
    },
    // onEvent: (type, values, field) => {
    //   console.log('filterProps onevent', type, values, field)
    // }
  }

  const tableProps = {
    // hasBorder: false,
    emptyContent: "请添加数据集~",
    sortIcons: {
      desc: <Icon style={{ top: '6px', left: '4px' }} type={'arrow-down'} size="small" />,
      asc: <Icon style={{ top: '-6px', left: '4px' }} type={'arrow-up'} size="small" />
    },
    defaultSort: {
      sampleNum: 'asc',
    }
  }

  const R = useFilterTable({
    filterConfig,
    filterProps,
    tableConfig: tableColumns,
    tableProps,
  })({
    action: async (_values) => {
      const { pageSize, pageNum, ...others } = _values
      const r = await request({
        url: `mark://api/ds/v1/getDataSet.json`,
        method: 'POST',
        mapRequest: json => {
          // const { pageNum, pageSize, ...o } = json
          return {
            query: others.dsName || '',
            size: pageSize,
            page: pageNum,
            // sort: 'asc'
          }
        },
        mapResponse: (json, { _props, _query }) => {
          console.log(json)
          return {
            total: json.total,
            data: json.data || []
          }
        }
      })
      return r
    },
  })

  return (
    <div className="ali-boreas-main mark-main">
      {R.filter}
      <Button
        type="primary"
        style={{ marginTop: '20px' }}
        onClick={() => {
          props.history && props.history.push('/odps/new')
        }}>
        新建数据集
      </Button>
      <div style={{ marginTop: '20px' }}>
        {R.table}
      </div>
    </div>
  )
}

export default List


