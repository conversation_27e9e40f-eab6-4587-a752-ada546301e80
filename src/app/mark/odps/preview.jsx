import React, { Component, useState } from 'react'
import PropTypes from 'prop-types'
import { Link } from 'react-router-dom'
import { Table, Pagination, Button, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import bosify from '@/utils/bosify'
import _ from 'lodash'
import { Filter, Table as BoreasTable, useFilterTable } from '../packages/FilterTable'
import formatDate from '@/utils/formatDate'
import useTable from '../packages/hooks/useTable'
import { request } from '@/packages/request'


function Preview(props) {
  const { data, pageData, onPageSizeChange, search } = useTable({
    requestAction: async (_data) => {
      const r = await request({
        url: `mark://api/ds/v1/getDataSetDetail.json`,
        method: 'POST',
        mapRequest: (json, { query }) => {
          console.log('json', json, query)
          const { pageNum, pageSize, ...o } = json
          return {
            query: +query.dsId,
            size: _data.pageSize || 10,
            page: _data.pageNum || 1
          }
        }
      })
      return r
    }
  })

  const formatData = () => {
    // dsFileds 数据源的字段名称
    // dsValues 数据源的值
    const { total } = data || {}
    let { dsFileds, dsValues } = _.get(data, 'data', {})
    return {
      total: total || 0,
      dataSource: dsValues || [],
      columns: dsFileds && dsFileds.map(item => {
        return {
          title: item.odpsFiledComment,
          dataIndex: item.mapFiledName,
          cell: v => {
            if (item.mapFiledName === 'createTime') {
              return formatDate(v)
            } else if (v && v.match(/^(http|https):/)) {
              return <img src={bosify(v)} style={{ maxWidth: '80px' }} />
            }
            return v
          },
          width: 150
        }
      })
    }
  }

  const { dataSource, columns, total } = formatData()
  
  // const { dataSource, columns, total, ...otherProps } = props
  if (!columns || !dataSource) {
    return null
  }
  return (
    <div className="odps-preview">
      <BoreasTable
        dataSource={dataSource}
        columns={columns}
        total={total}
        // fixedHeader
        // maxBodyHeight="500px"
        defaultCurrent={pageData.pageNum}
        pageSize={pageData.pageSize}
        changePageSize={onPageSizeChange}
        search={search}
        // useVirtual
      />
      <Button
        type="primary"
        className={`odps-preview-btn ${!total && 'empty'}`}
        onClick={() => {
          const { pageNum, pageSize } = (props.location && props.location.prePageData) || {}
          props.history.push({
            pathname: '/odps/list',
            search: `?pageNum=${pageNum}&pageSize=${pageSize}`
          })
        }}>
        返回
      </Button>
    </div>
  )
}

export default Preview
