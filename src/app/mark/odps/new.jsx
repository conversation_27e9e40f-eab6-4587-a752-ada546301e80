import React, { Component, useState } from 'react'
import PropTypes from 'prop-types'
import { <PERSON>, with<PERSON><PERSON><PERSON> } from 'react-router-dom'
import { Form, Input, Pagination, Button, Field, Message, Dialog, Icon, Select, MenuButton } from '@alifd/next'
import _ from 'lodash'
import boreasForm, { useForm, useFormAction } from '../packages/Form'
import { request } from '@/packages/request'
// import RenderForm from '../packages/Form/renderForm'

const formConfig = {
  type: 'Form',
  children: [
    {
      name: 'dsName',
      type: 'Input',
      // onChange: (...args) => {
      //   console.log('args', args)
      // },
      props: {
        label: '数据集名称:',
        placeholder: '请输入数据集名称',
        trim: true,
        hasClear: true,
        showSearch: true,
        required: true,
        rules: [
          {
            required: true,
            message: '数据集名称不能为空',
          },
        ],
      }
    },
    {
      name: 'dataSource',
      type: 'RadioGroup',
      props: {
        label: '数据来源:',
        defaultChecked: true,
        // innerLabel: 'odps',
        defaultValue: 'odps',
        dataSource: [{
          value: 'odps',
          label: 'odps'
        }]
      }
    },
    {
      name: 'useProject',
      type: 'Input',
      props: {
        label: '使用项目:',
        placeholder: 'gongxiang_dm',
        trim: true,
        hasClear: true,
        required: true,
        rules: [
          {
            required: true,
            message: '使用项目不能为空',
          },
        ],
      }
    },
    {
      name: 'applyProject',
      type: 'Input',
      props: {
        label: '申请项目:',
        placeholder: 'gongxiang_dm',
        trim: true,
        hasClear: true,
        required: true,
        rules: [
          {
            required: true,
            message: '申请项目不能为空',
          },
        ],
      }
    },
    {
      name: 'tableName',
      type: 'Input',
      props: {
        label: '表名:',
        placeholder: '表名',
        trim: true,
        hasClear: true,
        required: true,
        rules: [
          {
            required: true,
            message: '表名不能为空',
          },
        ],
      }
    },
    {
      name: 'partition',
      type: 'Input',
      props: {
        label: '分区名:',
        placeholder: '分区名',
        trim: true,
        hasClear: true,
        required: true,
        rules: [
          {
            required: true,
            message: '分区名不能为空',
          },
        ],
      }
    },
  ],
}

export default (props) => {
  const {
    initFormData,
    submit,
    onSuccess
  } = useFormAction({
    submitAction: {
      action: async (values) => {
        const r = request({
          url: `mark://api/ds/v1/addDataset.json`,
          method: 'POST',
        }, { method: 'POST', body: values })
        return r
      },
      mapStateToProps: (v) => {
        // dsName dataSource useProject applyProject tableName partition
        return v
      }
    },
    onSuccess: () => {
      console.log('onsucess')
    },
  })

  const handleSubmit = async () => {
    const formDataValues = field.fields
    console.log('handleSubmit', formDataValues)
    const { firsetErrors } = await field.validate()
    if (firsetErrors) {
      Message.error(firsetErrors[0] && firsetErrors[0].message)
      return
    }
    try {
      const r = await submit(formDataValues)
      console.log('submit', r)
      if (r.data > 0) {
        Message.success('新建成功')
        props.history.push('/odps/list')
      }
    } catch (error) {
      console.error('handleSubmit', error)
    }
  }

  const onCancel = () => {
    // field.resetToDefault()
    props.history.goBack()
  }

  const { BoreasForm, field } = boreasForm({
    config: formConfig,
    initFormData,
  })

  return (
    <div style={{ paddingTop: '20px' }}>
      {BoreasForm}
      <div style={{ textAlign: 'center', paddingTop: '20px' }}>
        <Button
          type="primary"
          // loading={queryLoading}
          // disabled={this.props.queryDisabled}
          style={{ marginRight: '20px' }}
          onClick={handleSubmit}
        >
        提交
        </Button>
        <Button onClick={onCancel}>
          取消
        </Button>
      </div>
    </div>
  )
}
