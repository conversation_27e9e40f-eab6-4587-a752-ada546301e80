import React, { Component, useState } from 'react'
import { Message, CascaderSelect } from '@alifd/next'
import _ from 'lodash'

import useCascaderSelect from '../hooks/useCascaderSelect'
import './style.scss'

function AsyncCascaderSelect(props) {
  const { onEvent, ...otherProps } = props
  if (!props.dataSource) {
    // console.error('三级联动组件缺少dataSource参数')
    return (
      <div><CascaderSelect placeholder="请选择商品类目" style={{ width: '100%' }} /></div>
    )
  }
  const [AsyncCascaderSelectEle, cascaderSelectForest] = useCascaderSelect({
    // changeOnSelect: true,
    showSearch: true,
    placeholder: '请选择商品类目',
    popupClassName: 'ali-boreas-cascaderselect',
    hasClear: true,
    valueRender: (item) => {
      if (item.label) {
        return item.label
      }
      return ''
    },
    onChange: (v, record, extra) => {
      // extra.selectedPath
      props.onEvent && props.onEvent('onChange', v, record, extra)
    },
    ...otherProps
  })
  return (
    <div>
      {AsyncCascaderSelectEle}
    </div>
  )
}

export default AsyncCascaderSelect

