import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import { CascaderSelect } from '@alifd/next'
import _ from 'lodash'
import { request } from '@/packages/request'
import JsonForest from '../../utils/forest'
import './style.scss'

const jsonForest = new JsonForest()

function WithAsyncCascaderSelect() {
  return (Target) => {
    function AsyncCascaderSelect(props) {
      const [list, setList] = useState([])

      const formatDataSource = (data, parent_id = '') =>
        _.flatMap(data, (item, i) => ({
          id: `${item.categoryId}`,
          parent_id,
          value: `${item.categoryId}`,
          label: `${item.categoryName}`,
        })) || []

      const storageForest = () => {
        // 存储 Forest
        if (props.id && window.localStorage) {
          window.localStorage[`${props.id}Forest`] = JSON.stringify(jsonForest)
        }
      }

      const getDataSourceList = async (v) => {
        // this.props.dataSource.type  forest
        v = v || ''
        const { action, mapStateToProps } = props.dataSource
        const t = await request(
          action,
          {},
          {
            props: {
              categoryParentId: v || '',
            },
          },
        ).then((data) => {
          // data.data.data
          const l = _.get(data, 'data.data', [])
          let r = {
            dataSource: formatDataSource(l, v),
          }
          if (mapStateToProps) {
            r = mapStateToProps({ list: l, parent_id: v })
          }
          return r
        })
        return t
      }

      const onLoadData = (data) => {
        const nextParentId = data.id
        return getDataSourceList(data.value).then((r) => {
          if (nextParentId) {
            jsonForest.addForest(r.dataSource)
            storageForest(jsonForest.forest)
          }
          setList([].concat(jsonForest.forest))
        })
      }

      useEffect(() => {
        getDataSourceList().then((r) => {
          jsonForest.addForest(r.dataSource || [])
          setList(r.dataSource || [])
          storageForest()
        })
      }, [])

      return (
        <Target
          {...props}
          dataSource={list}
          className="ali-boreas-autocomplete"
          loadData={onLoadData}
        />
      )
    }
    return AsyncCascaderSelect
  }
}

export default WithAsyncCascaderSelect
