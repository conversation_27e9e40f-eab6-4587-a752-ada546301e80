import React, { Component, useEffect, useState } from 'react'
import _ from 'lodash'
import formatAction from '../../utils/formatAction'

// 支持dataSource的函数配置
function WithAsyncFuncComponent() {
  return (Target) => {
    function Proxy(props) {
      const [list, setList] = useState([])

      const fetchData = async (v) => {
        const r = await formatAction({
          action: props.dataSource,
          params: v,
        })
        setList(r)
      }
      
      useEffect(() => {
        fetchData()
      }, [props.dataSource])

      const onSearch = (v) => {
        props.onSearch && props.onSearch(v)
        if (props.filterLocal === false) {
          fetchData(v)
        }
      }

      const onChange = (v) => {
        props.onChange && props.onChange(v)
        if (props.filterLocal === false && !props.changeLocal) {
          fetchData(v)
        }
      }

      return (
        <Target
          {...props}
          onSearch={onSearch}
          onChange={onChange}
          dataSource={list}
        />
      )
    }
    return Proxy
  }
}

export default WithAsyncFuncComponent
