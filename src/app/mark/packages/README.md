const form = useForm({
    rules: {
      name: [
        {
          validator: (rule, value, cb, options) => {
            if (value <= 100) {
              cb('Value must be more than 100')
            } else {
              cb()
            }
          }
        },
        {
          pattern: /abcd/,
          message: 'abcd不能缺',
        },
      ],
      file: [{ required: true, message: '请上传文件' }]
    },
    initValue: () => {
      return { name: 90 }
    }
  })


   {
    fields: fieldValues,
    getValue,
    getValues,
    setValue,
    setValues,
    getError,
    getErrors,
    getFirstError,
    validate: () => {
      const { errors, values } = validate()
      return {
        errors,
        values
      }
    },
    validateAsync: async () => {
      const { errors, values } = await validate()
      return {
        errors,
        values
      }
    },
  }


  // 设置值
  form.setValue('aaa.bbb[1]', 100)
  form.setValue(['aaa', 'bbb', 1], 100) // 等价
  form.setValue('xxx', async function(){
    // 复杂的远程过程
  })
