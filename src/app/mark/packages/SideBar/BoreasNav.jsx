import React, { Component, useState } from 'react'
import { Nav, Shell, Breadcrumb, Icon } from '@alifd/next'
import { withRouter, Route, Switch, Redirect, Link } from 'react-router-dom'
import PropTypes from 'prop-types'
import _ from 'lodash'

const { SubNav, Item } = Nav

/*
  Nav 的定义需按照多级目录 path 的包含规则, 如：
  [
    {
      menuUrl: '/odps',
      menuTitle: '数据管理',
      // component: Mark,
      icon: 'leftnav_shuju',
      subMenus: [
        {
          menuUrl: '/odps/list',
          menuTitle: '数据集列表',
          component: OdpsList,
        },
        ...
      ]
    },
    ...
  ]
  一级目录: /marketing
  二级目录：/marketing/coupon
  三级目录：/marketing/coupon/complete/aaa
*/

function useBreasNav(config) {
  const [currentRouter, setCurrentRouter] = useState('')
  const onSelect = (selectedKeys, item, extra) => {
    // console.log('onSelect', selectedKeys, item, extra)
    let { key } = extra
    setCurrentRouter(key)
  }

  return [
    <BoreasNav config={config} onSelect={onSelect} />,
    currentRouter
  ]
}

export default function BoreasNav(props = {}) {
  const onSelect = (...args) => {
    props.onSelect && props.onSelect(...args)
  }

  const jumpEle = (data) => {
    // /(http|https):\/\/([\w.]+\/?)\S*/
    const isUrl = /(http|https):\/\/([\w.]+\/?)\S*/.test(data.menuUrl)
    if (isUrl) {
      return <a href={data.menuUrl} target="_blank" rel="noopener noreferrer">{data.menuTitle}</a>
    } else {
      return <Link to={{ pathname: data.menuUrl }}>{data.menuTitle}</Link>
    }
  }

  const renderItem = (item) => {
    // menuUrl menuTitle icon component subMenus
    const { menuTitle, subMenus, icon, menuUrl } = item
    if (subMenus && subMenus.length) {
      return (
        <SubNav label={menuTitle} icon={icon} key={menuUrl} style={{ minWidth: '120px' }}>
          {
            subMenus.map(child => {
              return (
                <Item icon={child.icon} key={child.menuUrl}>
                  {jumpEle(child)}
                </Item>
              )
            })
          }
        </SubNav>
      )
    }
    return (
      <Item icon={icon} key={menuUrl}>
        {jumpEle(item)}
      </Item>
    )
  }

  return (
    <Shell className="ali-boreas-shell">
      <Shell.Navigation className='ali-boreas-sidebar'>
        <Nav embeddable aria-label='global navigation' onSelect={onSelect}>
          {
            props.config.map(child => (renderItem(child)))
          }
        </Nav>
      </Shell.Navigation>
    </Shell>
  )
}

export {
  useBreasNav,
}
