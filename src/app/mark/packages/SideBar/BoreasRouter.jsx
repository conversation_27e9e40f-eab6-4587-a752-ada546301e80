import React, { Component, useState } from 'react'
import { Nav, Shell, Breadcrumb, Icon } from '@alifd/next'
import PropTypes from 'prop-types'
import { withRouter, Route, Switch, Redirect, Link } from 'react-router-dom'
import _ from 'lodash'
import BoreasBreadcrumb from './BoreasBreadcrumb'

@withRouter
class BoreasRouter extends Component {
  static propTypes = {
    routerList: PropTypes.arrayOf(PropTypes.object),
    redirectUrl: PropTypes.string,
    // eslint-disable-next-line react/forbid-prop-types
    breadcrumbMap: PropTypes.object
  }

  static defaultProps = {
    routerList: [],
    redirectUrl: '/',
    breadcrumbMap: null
  }

  render() {
    const { breadcrumbMap, routerList, redirectUrl, history } = this.props
    return (
      <div className="ali-boreas-router-wrap">
        {
          breadcrumbMap && <BoreasBreadcrumb breadcrumbMap={breadcrumbMap} history={history} />
        }
        <Switch>
          {routerList.map(router => {
            // menuTitle, component, menuUrl
            const { menuUrl, component, subMenus, menuTitle } = router
            if (!subMenus || subMenus.length === 0) {
              return (
                <Route key={menuTitle} exact path={menuUrl} component={component} />
              )
            }
            return subMenus.map((child) => {
              const subUrl = child.menuUrl
              const subComponent = child.component
              return (
                <Route
                  key={subUrl}
                  exact
                  path={`${subUrl}`}
                  component={subComponent}
                />
              )
            })
          })}
          <Redirect from='/*' to={redirectUrl} />
        </Switch>
      </div>
    )
  }
}

export default BoreasRouter
export {
  BoreasBreadcrumb
}

