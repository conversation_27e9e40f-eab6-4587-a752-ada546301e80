import React, { Component, useState } from 'react'
import { Nav, Shell, Breadcrumb, Icon } from '@alifd/next'
import PropTypes from 'prop-types'
import { withRouter, Route, Switch, Redirect, Link } from 'react-router-dom'
import _ from 'lodash'

/*
  breadcrumbMap 如：
  {
    /: ["首页"],
    /odps: ["数据管理"],
    /odps/list: (2) ["数据管理", "数据集列表"],
    ...
  }
*/

class BoreasBreadcrumb extends Component {
  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    breadcrumbMap: PropTypes.object
  }

  static defaultProps = {
    breadcrumbMap: {}
  }

  parseLink = (item) => {
    const breadcrumbMap = this.props.breadcrumbMap
    let r = ''
    _.keys(breadcrumbMap).forEach(i => {
      const child = breadcrumbMap[i]
      const v = child.length && child.slice(-1)
      if (item === v[0]) {
        r = i
      }
    })
    return r
  }

  render() {
    const { breadcrumbMap, history } = this.props
    const pathName = history && history.location.pathname
    const l = pathName && breadcrumbMap && breadcrumbMap[pathName]
    return (
      <div className="ali-boreas-router-wrap">
        {l && l.length > 1 && (
          <Route
            render={routerParams => {
              return (
                <Breadcrumb className="ali-boreas-breadcrumb">
                  {l.map((item, index) => {
                    const childPathName = this.parseLink(item)
                    return (
                      <Breadcrumb.Item key={item}>
                        {
                          (index > 0 && index < l.length - 1)
                            ? <Link to={{ pathname: childPathName }}>{item}</Link>
                            : item
                        }
                      </Breadcrumb.Item>
                    )
                  })}
                </Breadcrumb>
              )
            }}
          />
        )}
        
      </div>
    )
  }
}

export default BoreasBreadcrumb

