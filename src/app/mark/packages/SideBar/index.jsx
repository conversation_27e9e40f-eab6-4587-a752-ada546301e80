import React, { Component, useState } from 'react'
import { Nav, Shell, Breadcrumb, Icon } from '@alifd/next'
import PropTypes from 'prop-types'
import { withRouter, Route, Switch, Redirect, Link } from 'react-router-dom'
import _ from 'lodash'
import BoreasNav, { useBreasNav } from './BoreasNav'
import BoreasRouter, { BoreasBreadcrumb } from './BoreasRouter'
import './style.scss'
/*
  router 的定义需按照多级目录 path 的包含规则
  如：
  一级目录: /marketing
  二级目录：/marketing/coupon
  三级目录：/marketing/coupon/complete/aaa
*/

function SideBar({ config }) {
  let urlMap = {}
  let titleMap = {}
  let breadcrumbMap = {}

  config.forEach(item => {
    let parentUrl = item.menuUrl || ''
    urlMap[parentUrl] = item.menuTitle
    titleMap[item.menuTitle] = parentUrl
    breadcrumbMap[parentUrl] = [].concat(item.menuTitle)
    if (item.subMenus && item.subMenus.length) {
      item.subMenus.forEach(o => {
        const current = `${o.menuUrl}`
        const currentUrlArr = current.split('/')
        urlMap[current] = o.menuTitle
        titleMap[o.menuTitle] = current
        let urlArr = []
        let i = 1
        while (i <= currentUrlArr.length) {
          urlArr.push(currentUrlArr.slice(0, i).join('/'))
          i++
        }
        breadcrumbMap[current] = urlArr.filter(i => i).map(i => urlMap[i])
      })
    }
  })
  // 导航nav  boreasNav
  const boreasNavConfig = config.map(item => {
    if (item.subMenus) {
      const filterItems = item.subMenus.filter(v => !v.leaf).map(o => ({
        ...o,
        menuUrl: titleMap[o.menuTitle]
      }))
      return {
        ...item,
        subMenus: filterItems
      }
    }
    return item
  })

  // console.log(breadcrumbMap, pathMap, titleMap)
  const [BoreasNavDom] = useBreasNav(boreasNavConfig)

  return (
    <div className="ali-boreas-wrap">
      {BoreasNavDom}
      <div className="ali-boreas-content">
        <BoreasRouter
          routerList={config}
          breadcrumbMap={breadcrumbMap}
        />
      </div>
    </div>
  )
}

export default SideBar

export {
  BoreasRouter,
  BoreasBreadcrumb,
  useBreasNav,
  BoreasNav,
}
