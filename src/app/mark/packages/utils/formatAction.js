export default async function formatAction({
  action,
  params,
}) {
  let _action = () => {}
  let _mapStateToProps = x => x
  if (action && Object.prototype.toString.call(action) === '[object Object]') {
    _action = action.action
    _mapStateToProps = action.mapStateToProps || _mapStateToProps
  } else {
    _action = action
  }

  let data = null
  if (typeof _action === 'function') {
    data = _action(params)
  }

  let r = data
  if (r instanceof Promise) {
    r = await data
  }
  return _mapStateToProps(r)
}
