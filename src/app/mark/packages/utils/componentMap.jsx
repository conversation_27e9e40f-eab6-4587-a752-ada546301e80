import React, { Component, useRef, useState } from 'react'
import {
  Form, Input, Grid, Button, Select, CascaderSelect, Radio, Range, Balloon, DatePicker, TimePicker, NumberPicker, Switch
} from '@alifd/next'
import WithAsyncCascaderSelect from '../hoc/withAsyncCascaderSelect'
import Title from '../Title'

const { AutoComplete } = Select
const { RangePicker, MonthPicker, YearPicker } = DatePicker
const RadioGroup = Radio.Group

@WithAsyncCascaderSelect()
class AsyncCascaderSelectComponent extends Component {
  render() {
    const { type, ...others } = this.props
    // 默认组件 CascaderSelect
    const R = COMPOMENT_MAP.CascaderSelect
    return <R {...others} />
  }
}

const COMPOMENT_MAP = {
  Title,
  Form,
  Input,
  'Input.TextArea': Input.TextArea,
  Button,
  Select,
  CascaderSelect,
  AsyncCascaderSelect: AsyncCascaderSelectComponent,
  AutoComplete,
  Radio,
  RadioGroup,
  Range,
  Balloon,
  DatePicker,
  TimePicker,
  NumberPicker,
  Switch,
  RangePicker,
  MonthPicker,
  YearPicker
}

export default COMPOMENT_MAP
