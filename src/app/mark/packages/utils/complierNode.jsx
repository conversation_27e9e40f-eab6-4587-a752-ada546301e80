import React, { Component, useRef, useState } from 'react'
import ReactDom from 'react-dom'
// import 'babel-polyfill'
import PropTypes from 'prop-types'
import {
  Form, Input, Field, Grid, Button, Select, CascaderSelect, Radio, Range, Balloon, DatePicker, TimePicker, NumberPicker, Switch
} from '@alifd/next'
// import WithAsyncComponent from '../hoc/withAsyncComponent'
import WithAsyncFuncComponent from '../hoc/withAsyncFuncComponent'

import COMPOMENT_MAP from './componentMap'

const FormItem = Form.Item
const Title = COMPOMENT_MAP.Title

// @WithAsyncComponent()
// class CompilerAsyncComponent extends Component {
//   // type dataSource
//   render() {
//     const { type, ...others } = this.props
//     const R = COMPOMENT_MAP[type]
//     return <R {...others} />
//   }
// }

@WithAsyncFuncComponent()
class CompilerAsyncFuncComponent extends Component {
  // type dataSource
  render() {
    const { type, ...others } = this.props
    const R = COMPOMENT_MAP[type]
    return <R {...others} />
  }
}

export default class CompilerNode extends Component {
  // constructor(props) {
  //   // config
  //   super(props)
  // }
  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    formItemLayout: PropTypes.object,
  }

  static defaultProps = {
    formItemLayout: {
      labelCol: { span: 6 },
      wrapperCol: { span: 10 },
    }
  }

  formatTag = (name) => {
    let componentName = COMPOMENT_MAP[name]
    if (componentName) {
      return componentName
    }
    if (/^[A-Za-z0-9]+$/.test(name)) {
      componentName = name.toLowerCase()
    } else {
      componentName = React.Fragment
    }
    return componentName
  }

  renderInnerItem = ({ config }) => {
    // config: {name, type, props}
    const {
      name, type, props, id, ...otherItemConfigs
    } = config

    let { placeholder, rules, innerLabel, initValue, errors, ...otherProps } = props
    let params = {
      id: id || name,
      key: `${otherProps.defaultValue}${name}`,
      ...otherProps,
      name,
      rules,
      placeholder: placeholder || '请输入',
      onChange: (...args) => {
        this.props.onEvent && this.props.onEvent('change', name, ...args)
        config.onChange && config.onChange(name, ...args)
      },
      className: 'width100percent',
    }
    if (innerLabel) {
      params.label = innerLabel
    }

    if (params.showSearch) {
      params.onSearch = (...args) => {
        this.props.onEvent && this.props.onEvent('search', name, ...args)
        config.onSearch && config.onSearch(name, ...args)
      }
    }

    let R = null
    // [object AsyncFunction]
    // 异步函数
    // 普通函数
    if (COMPOMENT_MAP[type]) {
      // 处理异步 dataSource
      if (
        params.dataSource
        && ['[object Function]', '[object Object]'].indexOf(Object.prototype.toString.call(params.dataSource)) >= 0
        ) {
          // otherProps.dataSource 可执行的函数
          R = <CompilerAsyncFuncComponent {...params} type={type} />
      } else {
        R = React.createElement(COMPOMENT_MAP[type], params)
      }
    } else {
      if (otherItemConfigs.cell) {
        if (typeof otherItemConfigs.cell === 'function') {
          otherItemConfigs.cell = otherItemConfigs.cell()
        }
        params.children = (
          <div className="next-form-text-align">{otherItemConfigs.cell}</div>
        )
      }
      
      R = React.createElement(this.formatTag(type), params)
    }


    errors = [].concat(errors || [])
    return (
      <React.Fragment>
        {R}
        {
          errors.length 
          ? <span name={name} className="ali-boreas-error">{errors[0].message}</span> 
          : null
        }
      </React.Fragment>
    )
  }

  renderFormItem = (__meta) => {
    const { labelAlign, labelTextAlign } = this.props.meta || {}
    if (!__meta) {
      return
    }
    const {
      type, props, name, ...itemOthers
    } = __meta

    let {
      label,
      hasFeedback,
      required,
      help,
      afterCell,
      ...otherProps // 非formItem参数
    } = props

    if (itemOthers.children) {
      return itemOthers.children.map(child => this.renderFormItem(child))
    }

    if (otherProps.addonBefore) {
      otherProps.addonBefore = this.renderFormItem(otherProps.addonBefore)
    }

    // renderInnerItem
    const R = this.renderInnerItem({
      config: {
        name,
        type,
        ...itemOthers,
        props: otherProps,
      },
      ...this.props
    })

    if (afterCell && typeof afterCell === 'function') {
      afterCell = afterCell()
    }

    return (
      <FormItem
        key={`form-item-${label}`}
        label={label}
        labelAlign={labelAlign || 'left'}
        labelTextAlign={labelTextAlign || 'right'}
        hasFeedback={hasFeedback}
        required={required}
        help={help}
        className={`ali-boreas-form-child`}
      >
        {R}
        {afterCell}
      </FormItem>
    )
  }

  renderParent = _meta => {
    const { type, title, children } = _meta
    const FormFragment = (
      <Form
        role="grid"
        key={`form-${title}`}
        style={this.props.style || {}}
        className={`ali-boreas-form`}
        {...this.props.formItemLayout}
      >
        {title && <Title title={title} />}
        {
          children && children.map((child) => this.renderFormItem(child))
        }
      </Form>
    )

    if (type === 'Form') {
      return FormFragment
    } else {
      // eslint-disable-next-line react/no-children-prop
      return React.createElement(this.formatTag(type), {
        key: `React.Fragment-${type}`,
        children: FormFragment
      })
    }
  }

  render() {
    let { meta } = this.props
    if (!meta) {
      return null
    }
    meta = [].concat(meta)
    return (
      <div className='ali-boreas-form-main'>
        {meta.length && meta.map(item => this.renderParent(item))}
      </div>
    )
  }
  
  
}
