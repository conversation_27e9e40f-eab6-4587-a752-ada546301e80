class JsonForest {
  constructor(selection = []) {
    this.forest = []
    this.id_maps = {}
    this.selection = selection
    this.changeListeners = []
    this.get = this.get
    window.jf = this
  }

  addListener(handler) {
    this.changeListeners.push(handler)
    return () => {
      this.changeListeners = this.changeListeners.filter(x => x !== handler)
    }
  }

  addForest(forest) {
    if (forest && forest.length > 0) {
      forest.forEach((treeNode) => {
        this.addTreeNode(treeNode)
      })

      this.rebuild(this.forest)
    }
  }

  setSelection(selection) {
    this.selection = selection
  }

  triggerUpdate() {
    this.changeListeners.forEach((handler) => {
      handler()
    })
  }

  rebuild(forest, level = 0) {
    for (let i = 0; i < forest.length; i++) {
      forest[i].level = level

      if (level === 2) {
        // 标记第三层叶子节点，无子子树
        forest[i].isLeaf = true
      }
      if (!forest[i].attrs) {
        forest[i].attrs = {}
      }
      if (forest[i].children) {
        this.rebuild(forest[i].children, level + 1)
      }
    }
  }

  get(id, level) {
    const d = this.id_maps[id]
    if (!d) {
      return
    }
    let r = []
    if (level === 0) {
      r = [].concat(d)
    } else if (level === 1) {
      const dd = d && this.id_maps[d.parent_id]
      r = [].concat(dd, d)
    } else if (level === 2) {
      const dd = d && this.id_maps[d.parent_id]
      const ddd = dd && this.id_maps[dd.parent_id]
      r = [].concat(ddd, dd, d)
    }
    return r
  }

  clearAttributes() {
    this._clearAttributes(this.forest)
  }

  _clearAttributes(forest) {
    for (let i = 0; i < forest.length; i++) {
      if (forest[i].attrs) {
        forest[i].attrs = {}
      }
      if (forest[i].children) {
        this._clearAttributes(forest[i].children)
      }
    }
  }

  setAttribute(id, attrs) {
    const item = this.find(id)
    if (!item) {
      return
    }
    item.attrs = { ...attrs }
    // item.data = {...item.data}
  }

  addTreeNode(node) {
    if (this.id_maps[node.id]) {
      return
    }
    this.id_maps[node.id] = node
    if (!this.id_maps[node.parent_id]) {
      this.forest.push(node)
    } else {
      const parent = this.id_maps[node.parent_id]
      if (!parent.children) {
        parent.children = []
      }
      parent.children.push(node)
    }
  }

  select(id, index) {
    this.selection = this.selection.slice(0, index)
    this.selection.push(id)
  }

  find(id) {
    return this.id_maps[id]
  }

  removeChildren(forest) {
    if (!forest) {
      return []
    }
    return forest.map(x => ({
      id: x.id,
      label: x.label,
      parent_id: x.parent_id,
      data: x.data,
      attrs: x.attrs,
    }))
  }

  render(type = 'selector') {
    // console.log('render', this.selection)
    const viewData = [this.removeChildren(this.forest)]
    for (const id of this.selection) {
      const node = this.id_maps[id]
      if (node) {
        viewData.push(this.removeChildren(node.children))
      } else {
        viewData.push([])
      }
    }
    return { options: viewData, selection: this.selection.slice() }
  }
}

export default JsonForest
