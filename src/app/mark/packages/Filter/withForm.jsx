import React, { Component } from 'react'
import { Form, Field } from '@alifd/next'
import parseUrl from '@/packages/request/parseUrl'
import FormContext from './FormContext'

const ctx = React.createContext(null)

function withForm() {
  return (Target) => {
    class FormProxy extends Component {
      constructor(props) {
        super(props)
        this.formContext = new FormContext(this)
      }

      setFieldValues(data) {
        // 设置field数据
        this.formContext.field.setValues(data)
        // 回填表单数据
        this.formContext.updateForm(data)
      }


      componentDidMount() {
        // 根据query回填表单数据
        if (this.props.initFormData) {
          // 初始化表单数据
          this.setFieldValues(this.props.initFormData)
          return
        }
        const { query } = parseUrl(window.location.href)
        const { pageNum, pageSize, ...data } = query
        this.setFieldValues(data)
      }

      render() {
        const { Provider } = ctx
        const { Consumer } = ctx
        return (
          <Provider
            value={{
              formContext: this.formContext,
            }}
          >
            <Consumer>
              {value => <Target {...this.props} formContext={value.formContext} />}
            </Consumer>
          </Provider>
        )
      }
    }
    return FormProxy
  }
}

withForm.withFormConsumer = (Target) => {
  class ConsumerProxy extends Component {
    render() {
      const { Consumer } = ctx
      return (
        <Consumer>{value => <Target {...this.props} formContext={value.formContext} />}</Consumer>
      )
    }
  }
  return ConsumerProxy
}

export default withForm
