import React, { Component } from 'react'
import withForm from './withForm'

const { withFormConsumer } = withForm

function withField(Wrapper = React.Fragment) {
  return (Target) => {
    class FieldProxy extends Component {
      constructor(props) {
        super(props)
        // 校验规则
        const { rules, defaultValue } = props.config.props
        this.field = props.formContext.field.init(props.config.name, {
          initValue: defaultValue,
          rules,
        })

        this.state = {
          value: defaultValue,
        }
        // 注册监听每个表单项数据变化
        props.formContext.listen(props.config.name, (x) => {
          this.handleMessage(x)
        })
      }

      handleMessage({ type, data }) {
        switch (type) {
          case 'set':
            this.setState({
              value: data.value
            })
            break
          default:
            break
        }
      }

      onChange = (value, e) => {
        this.setState({
          value,
        })
        // 表单项变化，更新filed
        this.props.formContext.emitChange
          && this.props.formContext.emitChange(this.props.config.name, value)
      }

      render() {
        return (
          <Wrapper>
            <Target value={this.state.value} onChange={this.onChange} {...this.props} />
          </Wrapper>
        )
      }
    }

    return withFormConsumer(FieldProxy)
  }
}

export default withField
