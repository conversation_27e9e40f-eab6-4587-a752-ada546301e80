import React, { Component, useRef, useState } from 'react'
import ReactDom from 'react-dom'
// import 'babel-polyfill'
import PropTypes from 'prop-types'
import {
  Form, Input, Field, Grid, Button, Select, CascaderSelect, Radio, Range, Balloon, DatePicker, TimePicker, NumberPicker, Switch
} from '@alifd/next'
import _ from 'lodash'
// import WithAsyncComponent from '../hoc/withAsyncComponent'
import WithAsyncFuncComponent from '../hoc/withAsyncFuncComponent'
import WithAsyncCascaderSelect from '../hoc/withAsyncCascaderSelect'
import withField from './withField'
import withForm from './withForm'
import COMPOMENT_MAP from '../utils/componentMap'

import filterMeta from './filterMeta'
import './style.scss'

const FormItem = Form.Item

@WithAsyncFuncComponent()
class CompilerAsyncFuncComponent extends Component {
  // type dataSource
  render() {
    const { type, ...others } = this.props
    const R = COMPOMENT_MAP[type]
    return <R {...others} />
  }
}

// @WithAsyncCascaderSelect()
// class AsyncCascaderSelectComponent extends Component {
//   render() {
//     const { type, ...others } = this.props
//     const R = COMPOMENT_MAP[type]
//     return <R {...others} />
//   }
// }

@withField()
class CompilerNode extends Component {
  constructor(props) {
    super(props)
    this.state = {
      errors: null,
      validateState: ''
    }
  }

  setError = (name) => {
    // 设置错误信息
    const { field } = this.props.formContext
    const errors = field.getErrors()
    const errorsName = errors && errors[name]
    this.state = {
      errors: errorsName || null,
      validateState: errorsName ? 'error' : ''
    }
  }

  compilerNode = ({ config, value, onChange, onEvent }) => {
    const { field } = this.props.formContext
    // config: {name, type, props}
    const {
      name, type, props, id, ...otherItemConfigs
    } = config

    const { placeholder, rules, innerLabel, ...otherProps } = props
    this.setError(name)

    let params = {
      id: id || name,
      key: name,
      ...otherProps,
      name,
      rules,
      placeholder: placeholder || '请输入',
      onChange: (...args) => {
        onChange(...args)
        onEvent && onEvent('change', {
          [name]: args[0]
        }, field)
        this.setError(name)
      },
      value,
      className: 'width100percent',
    }
    if (innerLabel) {
      params.label = innerLabel
    }

    if (params.showSearch) {
      params.onSearch = (args) => {
        this.props.onEvent && this.props.onEvent('search', name, ...args)
        config.onSearch && config.onSearch(name, ...args)
      }
    }

    if (this.state.validateState) {
      params.state = 'error'
    } else if (!this.state.validateState && params.state) {
      delete params.state
    }

    let R = null

    if (COMPOMENT_MAP[type]) {
      if (
        params.dataSource
        && ['[object Function]', '[object Object]'].indexOf(Object.prototype.toString.call(params.dataSource)) >= 0
        ) {
          // otherProps.dataSource 可执行的函数
          R = <CompilerAsyncFuncComponent {...params} type={type} />
      } else {
        R = React.createElement(COMPOMENT_MAP[type], params)
      }
    } else {
      if (otherItemConfigs.cell) {
        params.children = (
          <div className="next-form-text-align">{otherItemConfigs.cell(field.getValues())}</div>
        )
      }
      if (/^[A-Za-z0-9]+$/.test(type)) {
        R = React.createElement(type.toLowerCase(), params)
      } else {
        R = React.createElement(React.Fragment, params)
      }
    }

    const itemErrors = this.state.errors
    return (
      <React.Fragment>
        {R}
        <span name={name} className="ali-boreas-error">{itemErrors && itemErrors[0]}</span>
      </React.Fragment>
    )
  }

  render() {
    return this.compilerNode(this.props || {})
  }
}

@withForm()
class Filter extends Component {
  constructor(props) {
    super(props)
    const rlist = this.splitChunk()
    this.state = {
      queryLoading: false,
      resetLoading: false,
      expandStatus: false,
      filterList: rlist,
    }
  }


  static propTypes = {
    onEvent: PropTypes.func,
    // eslint-disable-next-line react/forbid-prop-types
    meta: PropTypes.object,
    canReset: PropTypes.bool,
    queryDisabled: PropTypes.bool,
    chunkLength: PropTypes.number,
    // eslint-disable-next-line react/forbid-prop-types
    formItemLayout: PropTypes.object,
  }

  static defaultProps = {
    onEvent: () => { },
    meta: filterMeta,
    canReset: true,
    queryDisabled: false,
    chunkLength: 3,
    formItemLayout: {
      // labelCol: { fixedSpan: 4 },
      // inline: true,
      labelCol: { span: 9 },
      wrapperCol: { span: 13 },
    }
  }

  splitChunk = (expandStatus) => {
    // let { expandStatus } = this.state || {}
    const { chunkLength, meta } = this.props || {}
    let list = meta.children || []
    let r = []
    list.forEach(item => {
      if (item.cols === 2 || item.props.addonBefore) {
        r = r.concat([item, null])
      } else {
        r = r.concat(item)
      }
    })
   
    if (r.length > 6 && meta && meta.hasExpand) {
      r = r.map((item, index) => {
        if (index >= 6 && item && item.props) {
          !expandStatus ? (item.props.visible = 'hidden') : delete item.props.visible
        }
        return item
      })
    }
    return _.chunk(r, chunkLength || 3)
  }

  toggleExpand = () => {
    let { expandStatus } = this.state
    const r = this.splitChunk(!expandStatus)
    this.setState({
      expandStatus: !expandStatus,
      filterList: r
    })
  }

  handleQuery = (type = 'query') => {
    const { field } = this.props.formContext
    // 校验

    if (typeof type !== 'string') {
      type = 'query'
    }
    this.setState({
      [`${type}Loading`]: true,
    })
    const t = async () => this.props.onEvent(type, field.getValues(), field)
    t()
      .then(() => {
        this.setState({
          [`${type}Loading`]: false,
        })
      })
      .catch(() => {
        this.setState({
          [`${type}Loading`]: false,
        })
      })
  }

  clickReset = () => {
    const resetType = this.props.resetType || 'resetToDefault'
    this.props.formContext.reset(resetType)
    this.handleQuery('reset')
  }

  renderFormItem = (__meta) => {
    const { labelAlign, labelTextAlign } = this.props.meta || {}
    if (!__meta) {
      return
    }
    const {
      type, props, name, ...itemOthers
    } = __meta

    let { cols } = __meta

    const {
      label,
      hasFeedback,
      required,
      help,
      visible,
      ...otherProps // 非formItem参数
    } = props

    if (itemOthers.children) {
      return itemOthers.children.map(child => this.renderFormItem(child))
    }

    if (otherProps.addonBefore) {
      otherProps.addonBefore = this.renderFormItem(otherProps.addonBefore)
      cols = 2
    }

    // CompilerNode
    const R = (
      <CompilerNode
        config={{
          name,
          type,
          ...itemOthers,
          props: otherProps,
        }}
        {...this.props}
      />
    )

    let addClassNameText = `ali-boreas-filter-child-${this.props.chunkLength || 3}`
    if (cols) {
      addClassNameText = `${addClassNameText}-cols${cols}`
    }

    const style = visible === 'hidden' ? { display: 'none' } : null

    return (
      <FormItem
        key={`form-item-${label}`}
        label={label}
        labelAlign={labelAlign || 'left'}
        labelTextAlign={labelTextAlign || 'right'}
        hasFeedback={hasFeedback}
        required={required}
        help={help}
        style={style}
        className={`ali-boreas-filter-child ${addClassNameText}`}
        {...this.props.formItemLayout}
      >
        {R}
      </FormItem>
    )
  }

  render() {
    const { hasExpand } = this.props.meta
    let { buttonAlign } = this.props.meta
    const { field } = this.props.formContext
    const { filterList, queryLoading, resetLoading, expandStatus } = this.state
    // if (+this.props.chunkLength !== 1 && buttonAlign === 'start-line') {
    //   buttonAlign = 'start'
    // }
    return (
      <div className={this.props.className}>
        <Form
          inline
          field={field}
          role="grid"
          style={this.props.style || {}}
          className={`ali-boreas-filter ali-boreas-filter-${buttonAlign || 'start'}`}
        >
          <div className='ali-boreas-filter-main'>
            {filterList.map((child, i) => (
              <div key={`row-${i}`} className="ali-boreas-filter-list">
                {child && child.map(item => this.renderFormItem(item))}
              </div>
            ))}
          </div>
          {
            hasExpand && <div className={`expand expand-${hasExpand || 'rightTop'}`} onClick={this.toggleExpand}>{expandStatus ? '收起全部选项' : '展开全部选项'}</div>
          }
          <div className='ali-boreas-filter-button'>
            <Button
              type="primary"
              loading={queryLoading}
              disabled={this.props.queryDisabled}
              style={{ marginRight: '8px' }}
              onClick={this.handleQuery}
            >
              查询
            </Button>
            {this.props.canReset && (
              <Button onClick={this.clickReset} loading={resetLoading}>
                重置
              </Button>
            )}
          </div>
        </Form>
      </div>
    )
  }
}

export function withFilter() {
  return (props) => {
    const [filterRef, setFilterRef] = useState(null)
    const [context, setContext] = useState(null)
    return {
      component: <Filter {...props} ref={(v) => {
        setFilterRef(v)
        setContext(v && v.formContext)
      }} />,
      ref: filterRef,
      context,
    }
  }
}

export default Filter
