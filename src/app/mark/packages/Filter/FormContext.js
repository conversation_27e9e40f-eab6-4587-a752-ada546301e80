import React, { Component } from 'react'
import { Field } from '@alifd/next'
import _ from 'lodash'

class FormContext {
  constructor(props) {
    // 初始化field
    this.field = new Field({}, {
      autoValidate: true
    })
    this.handlers = {}
    const configList = _.get(props, 'props.meta.children', [])
    this.initConfig = {}
    configList.length && configList.forEach(item => {
      this.initConfig[item.name] = {
        name: item.name,
        rules: item.props.rules,
        defaultValue: item.props.defaultValue
      }
    })
  }

  // 响应表单项变化，更新field  change
  // 根据field变化，更新表单项  set
  reset = (resetType) => {
    if (resetType === 'reset') {
      this.field.reset()
    } else {
      this.field.resetToDefault()
    }
    const names = this.field.getNames()
    const values = this.field.getValues()
    let changes = []
    names.forEach(i => {
      changes[i] = values[i]
      this.field.init(i, {
        initValue: values[i],
        rules: this.initConfig[i] && this.initConfig[i].rules
      })
    })
    this.updateForm(changes)
  }

  listen = (name, handler) => {
    // 监听表单项变化
    this.handlers[name] = handler
  }

  emitChange = (name, value) => {
    // 针对表单变化做出，更新field
    this.field.setValue(name, value)
    // 校验一下，设置error
    this.field.validate()
  }
  

  emitSet = (name, value) => {
    // 回填某一项表单项的值
    const fieldNames = this.field.getNames()
    if (fieldNames.indexOf(name) === -1) {
      return
    }
    this.handlers[name]({
      type: 'set',
      data: {
        name,
        value,
      },
    })
  }

  updateForm = (changes) => {
    // 更新表单项 set
    const list = _.keys(changes) || []
    list.forEach(i => {
      const value = changes[i]
      this.emitSet(i, value)
    })
  }
}

export default FormContext
