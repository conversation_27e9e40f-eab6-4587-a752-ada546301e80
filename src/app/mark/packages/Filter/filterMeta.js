import React from 'react'
import _ from 'lodash'

const filterUiMeta = {
  type: 'Filter',
  // labelTextAlign: 'left',
  buttonAlign: 'end-line', // end-line  start-line(仅仅支持一个选项的时候) end start 默认
  hasExpand: 'rightTop',
  children: [
    {
      name: 'name',
      type: 'Input',
      props: {
        label: '名称1:',
        placeholder: '请选择名称1',
        // hasClear: true,
        // hasFeedback: true,
        // validateState: "success",
        // help: "Information Checking...",
        // required: true,
        // requiredTrigger: "onBlur"
        rules: [
          {
            required: true,
            trigger: 'onBlur',
            message: '名称不能为空',
          },
          {
            maxLength: 4,
            message: '最多不超过4个字',
            // trigger: 'onBlur'
            autoValidate: true,

            // 自定义验证规则
            // validator: (rule, value, cb) => {
            //   console.log(rule, value, cb)
            //   cb(<span>rule.requiredMessage</span>)
            // }
            // pattern: /[\w]{6,16}/,
            // message: '活动名称格式有误'
            // help: '活动名称有误'
          },
        ],
        // defaultValue: '2',
      }
    },
    {
      name: 'spuId',
      type: 'Input',
      props: {
        label: '产品ID:',
        placeholder: '请输入产品ID',
        hasClear: true,
        // rules: [
        // {
        //   required: true,
        //   trigger: 'onBlur',
        //   message: '投放计划名称不能为空',
        // },
      },
    },
    {
      name: 'status',
      type: 'Select',
      props: {
        label: '状态:',
        placeholder: '请选择状态',
        dataSource: [
          {
            value: '',
            label: '全部',
          },
          {
            value: '1',
            label: '生效中',
          },
          {
            value: '2',
            label: '已下线',
          },
        ],
      }
    },
    {
      name: 'creator',
      type: 'Select',
      props: {
        label: '创建者:',
        hasClear: true,
        dataSource: async () => {
          await fetch('sdad')
          return [{
            value: 'creator1',
            label: 'creator1',
          },]
        }
      },
    },
    {
      name: 'category',
      type: 'CascaderSelect',
      id: 'category', // 三级联动必须从传key
      cols: 2,
      props: {
        label: '商品类目:',
        placeholder: '请选择',
        hasClear: true,
        popupClassName: 'boreas-cascader-select',
        valueRender: (item) => {
          if (item.label) {
            return item.label
          }
          return ''
        },
        dataSource: {
          type: 'forest',
          action: {
            url: 'mtop://mtop.ele.newretail.category.queryChildren',
            method: 'GET',
            mapRequest: (body, { props }) =>
              // console.log(props)
              ({
                categoryParentId: props.categoryParentId || null,
                isControl: true,
              }),
          },
          // mapStateToProps: (data) => {
          //   // list parent_id
          //   console.log(data)
          //   return {
          //     dataSource:
          //       _.flatMap(data.list, (item, i) => ({
          //         id: `${item.categoryId}`,
          //         parent_id: data.parent_id,
          //         value: `${item.categoryId}`,
          //         label: `${item.categoryName}`,
          //       })) || [],
          //   }
          // },
        },
        // dataSource: [
        //   {
        //     value: 'OTC',
        //     label: '非处方药品(OTC)',
        //   },
        //   {
        //     value: 'RX',
        //     label: '处方药品(RX)',
        //   },
        // ],
      },
    },
    {
      name: 'brandName',
      type: 'AutoComplete',
      props: {
        // 支持搜索
        label: '品牌:',
        placeholder: '请输入品牌',
        hasClear: true,
        filterLocal: false,
        // fillProps: 'label',
        // valueRender: item => 'item.label',
        // itemRender: item => item.label,
        dataSource: {
          action: {
            method: 'GET',
            url: 'mtop://mtop.ele.newretail.brand.queryByName',
            mapRequest: (body, { props }) => ({
              brandName: props.brandName,
            }),
          },
          mapStateToProps: (data) => {
            const r = _.get(data, 'data.data.data', [])
            const dataSource = _.flatMap(r, (item, i) => ({
              value: item.valueData,
              label: item.valueData,
              id: item.valueId,
            })) || []
            window.sessionStorage.brandNameDataSource = JSON.stringify(
              dataSource,
            )
            return {
              dataSource,
            }
          },
        },
      },
    },
  ],
}
export default filterUiMeta
