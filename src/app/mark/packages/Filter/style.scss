.width100percent {
  width: 100% !important;
}

.ali-boreas-error {
  color: #ff3000;
  font-size: 12px;
  padding-top: 12px;
  display: inline-block;
}

.ali-boreas-filter {
  background: #ffffff;
  position: relative;
  // padding-top: 20px;
  &-main {
    flex: 1;
  }
  &-list {
  }
  &-button {
    padding-right: 20px;
  }
  &-child {
    margin-right: 0 !important;
  }
  &-child-1 {
    width: 50%;
  }
  &-child-2 {
    width: 50%;
  }
  &-child-3 {
    width: 33.33%;
    &-cols2 {
      width: 66.66%;
      .next-col-9 {
        width: 18.75%;
      }
      .next-col-13 {
        width: 76.92%;
        max-width: 76.92%;
      }
    }
  }
  .expand {
    padding: 0 20px;
    color: #FF7C4D;
    cursor: pointer;
  }
  .expand-rightTop {
    position: absolute;
    right: 0;
    top: 0;
  }
  .expand-leftBottom {
    position: absolute;
    left: 0;
    bottom: 0;
  }
}

.ali-boreas-filter {
  &-start-line {
    // display: flex;
    // align-items: flex-start;
    overflow: hidden;
    .ali-boreas-filter-main {
      float: left;
      min-width: 320px;
      .ali-boreas-filter-child-1 {
        width: 100%;
      }
    }
    .ali-boreas-filter-button {
      float: left;
    }
  }
  &-start {
    display: flex;
    align-items: flex-start;
  }
  &-end {
    display: block;
    .ali-boreas-filter-button {
      padding-right: 2.75%;
      text-align: right;
    }
  }
  &-end-line {
    display: flex;
    align-items: flex-end;
    .ali-boreas-filter-button {
      padding-bottom: 16px;
    }
  }
}



.ali-boreas-cascader-select {
  width: 43.33%;
  .next-cascader-select-dropdown {
    border: 0 none;
  }
  .next-cascader-inner {
    width: 100%!important;
    display: flex;
  }
  .next-cascader-menu-wrapper {
    // width: 100% !important;
    flex: 1;
    border: 1px solid #d7d9db;
  }
}

.ali-boreas-table {
  background: #ffffff;
}

.ali-boreas-form {
  &-child {
  }
  &-button {
    text-align: right;
    button:first-child {
      margin-right: 16px;
    }
  }
}
