import React from 'react'

const formMeta = {
  title: '表单',
  type: 'Form',
  // labelTextAlign: 'left',
  // buttonAlign: 'start-line', // end-line  start-line(仅仅支持一个选项的时候) end start 默认
  // hasExpand: 'rightTop',
  children: [
    {
      type: 'Input',
      name: 'name',
      props: {
        label: '名称',
        rules: [
          {
            required: true,
            trigger: 'onBlur',
            message: '名称不能为空',
          },
          {
            maxLength: 4,
            message: '最多不超过4个字',
            // trigger: 'onBlur'
            autoValidate: true,

            // 自定义验证规则
            // validator: (rule, value, cb) => {
            //   console.log(rule, value, cb)
            //   cb(<span>rule.requiredMessage</span>)
            // }
            // pattern: /[\w]{6,16}/,
            // message: '活动名称格式有误'
            // help: '活动名称有误'
          },
        ],
      },
    },
    {
      type: 'Input',
      name: 'amount',
      props: {
        label: '金额:',
        placeholder: '请输入金额'
      }
    },
    {
      name: 'status',
      type: 'Select',
      props: {
        label: '状态:',
        placeholder: '请选择状态',
        dataSource: [
          {
            value: '',
            label: '全部',
          },
          {
            value: '1',
            label: '生效中',
          },
          {
            value: '2',
            label: '已下线',
          },
        ],
      }
    },
  ],
}
export default formMeta
