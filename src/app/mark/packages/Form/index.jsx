import React, { Component, useRef, useState } from 'react'
import _ from 'lodash'
import useForm from '../hooks/useForm'
import useFormAction from '../hooks/useFormAction'
import CompilerNode from '../utils/complierNode'
import defaultFormMeta from './formMeta'

import '../Filter/style.scss'

function boreasForm({ config = defaultFormMeta, initFormData }) {
  const childrenList = [].concat(config).reduce((c, n) => (c || []).concat(n.children), [])
  let formRules = {}
  let initValue = {}
  childrenList.forEach(v => {
    const { rules, defaultValue } = v.props
    formRules[v.name] = rules || {}
    if (defaultValue !== undefined && defaultValue !== null) {
      initValue[v.name] = defaultValue
    }
  })

  initValue = {
    ...initValue,
    ...initFormData,
  }

  const form = useForm({
    rules: formRules || {},
    initValue,
  })

  const handleChange = async (type, name, v) => {
    console.log(type, name, v)
    if (type === 'change') {
      form.setValue(name, v)
    }
  }

  const { errors, fields } = form

  let meta = [].concat(config)
  meta = meta.map(item => {
    item.children.map(child => {
      let _error = errors && errors[child.name]
      let _fields = fields[child.name]
      // if (value) {
      _.set(child, 'props.value', _fields)
      // }
      if (_error) {
        _.set(child, 'props.errors', _error)
      } else {
        _.set(child, 'props.errors', null)
      }
      return child
    })
    return item
  })

  return {
    BoreasForm: <CompilerNode meta={meta} onEvent={handleChange} />,
    field: form,
  }
}

export default boreasForm

export {
  useForm,
  useFormAction,
  CompilerNode,
}
