import React, { Component, useState, useEffect } from 'react'
import { Field, Loading, Message } from '@alifd/next'
import _ from 'lodash'
import formatAction from '../utils/formatAction'

export default function useFormAction({ requestAction, submitAction, onSuccess } = {}) {
  const [initFormData, setInitFormData] = useState(null)
  const [inited, setInited] = useState(false)

  const submit = async (values, props) => {
    const json = await formatAction({
      action: submitAction,
      params: values
    })
    onSuccess && onSuccess(json)
    return json
  }

  const fetchInitData = async (props) => {
    if (requestAction) {
      const r = await formatAction({
        action: requestAction,
      })
      if (r) {
        setInitFormData(r)
      }
    }
    setInited(true)
  }

  useEffect(() => {
    if (!inited) {
      fetchInitData()
    }
  }, [])

  return {
    inited,
    initFormData,
    submit,
    onSuccess
  }
}
