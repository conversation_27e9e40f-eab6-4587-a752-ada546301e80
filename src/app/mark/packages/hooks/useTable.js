import React, { Component, useState, useEffect } from 'react'

import _ from 'lodash'
import parseUrl from '@/packages/request/parseUrl'

export default function useTable({ requestAction = async () => {}, mapStateToProps = x => x, actionFunc = () => {} }) {
  const [data, setData] = useState({})
  const [pageData, setPageData] = useState({
    pageNum: 1,
    pageSize: 10
  })

  const search = async (_data = {}) => {
    if (_data.pageNum || _data.pageSize) {
      setPageData({
        pageNum: _data.pageNum || pageData.pageNum,
        pageSize: _data.pageSize || pageData.pageSize
      })
    }
    const params = {
      ...pageData,
      ..._data
    }
    let r = await requestAction(params)

    r = mapStateToProps(r)
    actionFunc(r, params)
    setData(r)
    return r
  }

  const onPageSizeChange = (v) => {
    const r = {
      ...pageData,
      pageSize: v
    }
    setPageData(r)
    search(r)
  }

  useEffect(() => {
    const query = parseUrl(window.location.href).query || {}
    search(query)
  }, [])

  return {
    data,
    pageData,
    search,
    onPageSizeChange
  }
}
