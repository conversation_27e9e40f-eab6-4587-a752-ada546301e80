/**
 * 表单处理库
 */
import Reat, { useState, useEffect, useMemo, useReducer } from 'react'
import Validator from '@alifd/next/lib/validate'
import _ from 'lodash'
import { List, Map, Set, fromJS, is } from 'immutable'

// 缓存路径
const keyCache = new Map()
function resolve(key) {
  if (Array.isArray(key)) {
    return key
  }
  if (keyCache.has(key)) {
    return keyCache[key]
  }
  const path = key.split(/[\.\[\]]/).filter(x => x)
  keyCache.set(key, path)
  return path
}

const defaultOptions = {
  validateDelay: 300,
  initValue: {}
}

export default (options) => {
  // 处理rule和initValue
  options = {
    ...defaultOptions,
    ...options
  }
  if (typeof options.rules === 'function') {
    options.rules = options.rules()
  }
  if (typeof options.initValue === 'function') {
    options.initValue = options.initValue()
  }

  const [initFieldValue, setInitFieldValue] = useState(options.initValue)

  // if (!is(Map(initValue), Map(options.initValue))) {
  //   console.log('1')
  // }

  // field
  let [fieldValues, setFieldValues] = useState(() => {
    return fromJS({
      value: options.initValue || {}
    })
  })

  useEffect(() => {
    if (!is(Map(initFieldValue), Map(options.initValue))) {
      setInitFieldValue(options.initValue)
      setFieldValues(fromJS({
        value: options.initValue || {}
      }))
    }
  }, [options.initValue])

  // value
 function setValue(key, value) {
    const path = resolve(key)
    setFieldValues(values => {
      values = values || {}
      return values
      .setIn(['dirty', ...path], true)
      .setIn(['value', ...path], value)
    })
    const itemErros = validateItem(key, value)
    if (itemErros) {
      setFieldErrors({
        ...filedErrors,
        ...itemErros
      })
    } else if (filedErrors && filedErrors[key]) {
      delete filedErrors[key]
      console.log('filedErrors', filedErrors)
      setFieldErrors(filedErrors)
    }
    const preValues = fieldValues.get('value').toJS()
    return {
      ...preValues,
      [key]: value
    }
  }

  function setValues(map) {
    return _.keys(map).map(key => {
      const r = setValue(key, map[key])
      return r
    })
  }

  async function resolveValue(key) {
    const path = resolve(key)
    const val = fieldValues.getIn(['value', ...path])
    if (val instanceof Promise) {
      const asyncValue = await val
      fieldValues = fieldValues.setIn(['value', ...path], asyncValue)
      setFieldValues(() => fieldValues)
      return asyncValue
    }
    if (typeof val === 'function') {
      const promise = val()
      const asyncValue = await promise
      fieldValues = fieldValues.setIn(['value', ...path], asyncValue)
      setFieldValues(() => fieldValues)
      return asyncValue
    }
    else {
      return val 
    }
  }

  async function getValue(key, defaultValue = null) {
    const val = await resolveValue(key)
    if (val !== undefined) {
      return val
    }
    return defaultValue
  }

  async function getValues() {
    let values = new Map()
    const keys = fieldValues.get('value').keys()
    for (let key of keys) {
      // eslint-disable-next-line no-await-in-loop
      const val = await resolveValue(key)
      values = values.setIn(resolve(key), val)
    }
    return values.toJS() || {}
  }

  // reset
  function reset() {
    setErrors(null)
    setFieldValues(fromJS({
      value: {}
    }))
  }

  function resetToDefault() {
    setErrors(null)
    setFieldValues(fromJS({
      value: options.initValue || {}
    }))
  }

  // validate
  async function validate() {
    const rules = options.rules || {}
    const values = await getValues()
    const validator = new Validator(rules)
    let errors = null
    validator.validate(values, (_firstErrors, err) => {
      errors = err
      // console.log('validate err', err)
      // setFieldErrors(err)
    })
    return {
      errors,
      values,
    }
  }

  function validateItem(key, value) {
    if (!options.rules) {
      return
    }
    const rules = options.rules || {}
    const validator = new Validator({
      [key]: rules[key]
    })
    let errors = null
    validator.validate({
      [key]: value
    }, (o, err) => {
      errors = err
      // console.log('validateItem', err)
    })
    return errors
  }

    // errors
    const [filedErrors, setFieldErrors] = useState(null)

    function filterErrors(_errors) {
      if (!_errors) {
        return null
      }
      let errorR = {}
      for (let key in _errors) {
        if (fieldValues.getIn(['dirty', key]) && _errors[key]) {
          errorR[key] = _errors[key]
        }
      }
      if (_.keys(errorR).length <= 0) {
        return null
      }
      // console.log('filterErrors fieldValues', errorR, _errors, fieldValues.toJS())
      return errorR
    }

    async function getError(key) {
      const { errors } = await validate()
      const r = filterErrors(errors)
      return r && r[key]
    }

    async function getErrors() {
      const { errors } = await validate()
      if (!errors) {
        return null
      }
      return filterErrors(errors)
    }

    // async function setError(key, error) {
    //   // const path = resolve(key)
    //   // fieldValues(values => {
    //   //   values = values || {}
    //   //   return values
    //   //     .setIn(['dirty', ...path], true)
    //   // })

    //   if (error) {
    //     setFieldErrors({
    //       ...filedErrors,
    //       ...error
    //     })
    //   } else if (filedErrors && filedErrors[key]) {
    //     delete filedErrors[key]
    //     setFieldErrors(filedErrors)
    //   }
    // }
  
    async function setErrors(_errors) {
      // map {name: [{message: 'xx', field: 'name'}]}
      // .getIn(['dirty', key])
      _errors = _errors || {}
      setFieldErrors({
        ...setFieldErrors,
        ..._errors
      })
    }

  function filterFirstError(_errors) {
    if (!_errors) {
      return null
    }
    let firstErrors = []
    _.keys(_errors).forEach(item => {
      firstErrors.push(_errors[item][0])
    })
    return firstErrors
  }

  async function getFirstErrors() {
    const { errors } = await validate()
    if (!errors) {
      return null
    }
    return filterFirstError(filterErrors(errors))
  }

  return {
    fields: fieldValues && fieldValues.get('value').toJS(),
    errors: filedErrors,
    firsetErrors: filterFirstError(filedErrors),
    getValue,
    getValues,
    setValue,
    setValues,
    getError,
    setErrors,
    getErrors,
    getFirstErrors,
    reset,
    resetToDefault,
    validate: async () => {
      const { errors, values } = await validate()
      setErrors(errors)
      console.log('validate errors', errors)
      return {
        firsetErrors: filterFirstError(errors),
        errors,
        values
      }
    },
  }
}
