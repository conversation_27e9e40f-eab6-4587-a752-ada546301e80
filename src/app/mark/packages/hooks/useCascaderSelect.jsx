import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import { CascaderSelect } from '@alifd/next'
import _ from 'lodash'
import { request } from '@/packages/request'
import JsonForest from '../utils/forest'

let cascaderSelectForest = new JsonForest()

const formatDataSource = (data, parent_id = '') =>
    _.flatMap(data, (item, i) => {
      if (!item.categoryId) {
        return item
      }
      return {
        id: `${item.categoryId}`,
        parent_id,
        value: `${item.categoryId}`,
        label: `${item.categoryName}`,
      }
    }) || []

function Proxy(props) {
  const [list, setList] = useState([])

const getDataSourceList = async (v) => {
    const { action, mapStateToProps, type } = props.dataSource || {}
    if (action && Object.prototype.toString.call(action) === '[object Function]') {
      /**
       * 传参类型
       *
       * @param {
       *  action: async (v) => {
       *    const r = await request('mark://api/category/v1/getCategory.json')
       *    return r
       *  },
       * mapStateToProps: (res) => {
       * // 非必填
       *  return res.list
       * }
       * @returns
       */
     const data = await action(v)
      let r = data
      if (mapStateToProps) {
        r = mapStateToProps({ list: data, parent_id: v })
      }
      return r
    } else if (action && Object.prototype.toString.call(action) === '[object Object]') {
      /**
       * 传参类型
       *
       * @param {
       *   type: 'async',
       *   action: {
       *    url: 'mtop://mtop.ele.newretail.category.queryChildren',
       *    // url: 'mark://api/category/v1/getCategory.json',
       *    method: 'GET',
       *   mapRequest: (body, { props }) => {
       *     return {
       *       categoryParentId: props.categoryParentId || null,
       *      // isControl: false,
       *    }
       *  }
       * @returns
       */
      v = v || ''
      const t = await request(
        action,
        {},
        {
          props: {
            categoryParentId: v || '',
          },
        },
      ).then((data) => {
        // data.data.data
        const l = _.get(data, 'data.data', [])
        let r = formatDataSource(l, v)
        if (mapStateToProps) {
          r = mapStateToProps({ list: l, parent_id: v })
        }
        return r
      })
      return t
    } else {
      return props.dataSource || []
    }
  }

  const onLoadData = (data) => {
    const nextParentId = data.id
    return getDataSourceList(data.value).then((r) => {
      if (nextParentId) {
        cascaderSelectForest.addForest(r)
      }
      setList([].concat(cascaderSelectForest.forest))
    })
  }

  const init = () => {
    getDataSourceList().then((r) => {
      if (props.dataSource && props.dataSource.type === 'async') {
        cascaderSelectForest.addForest(r || [])
      } else {
        cascaderSelectForest = r || []
      }
      setList(r || [])
    })
  }

   useEffect(() => {
    init()
  }, [])

  if (props.dataSource && props.dataSource.type === 'async') {
    props.loadData = onLoadData
  }

  return (
    <CascaderSelect
      {...props}
      dataSource={list}
      className="ali-boreas-autocomplete"
    />
  )
}

function useCascaderSelect(props) {
  return [
    <Proxy {...props} />,
    cascaderSelectForest,
  ]
}

export default useCascaderSelect
