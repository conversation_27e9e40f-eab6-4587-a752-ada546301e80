import React, { useState, useEffect, useMemo } from 'react'
import { List, fromJS } from 'immutable'
import invariant from 'invariant'

/**
 *
 * 支持级联选择器开发的一个Hooks。
 *
 * 内部是一个树状的结构，虽然没有明确定义树，但是利用了immutable.js形成了
 * 树的结构。
 *
 * 用户从options必须定义一个叫做get(path)的方法，通过get方法去服务端（或）
 * 其他数据源获取输入，最终持久化到树中。
 *
 * 正常的数据路径：
 * 用户选择 -> path -> get(path) -> list(服务端返回的数据) -> root(树的跟节点)
 *
 * 有可能get(path)可以返回本地数据，这时可以节省请求资源。（当然这里存在缓存的
 * 问题）
 *
 */

function hash(path) {
  return path.join('-')
}

const defaultOptions = {
}

function wait(ms) {

  return new Promise(resolve => {
    setTimeout(() => {
      resolve()
    }, ms)
  })
}

export default options => {
  options = { ...defaultOptions, ...options }
  invariant(typeof options.get === 'function', '[useSelection] options.get must be function.')

  /* 缓存 option */
  const mem = {} 

  return () => {

    // 根节点
    const [state, setState] = useState(
      fromJS({
        loading: 1, 
        selection: [],
        root: { label: 'root', value: 0, children: [] },
      }),
    )

    /**
     * 根据path选择，如果有需要就从服务端获取数据
     * @param {*} path
     */
    const select = async (path) => {
      // list是从服务端取回的级联数据
      // 支持树状的结构
      setState(s => s.set('loading', path.length + 1))
      try {

        setState(x => {
          load(x.get('root'), path.slice())
            .then(newRoot => {
              setState((s) => {
                s = s.set('loading', 0)
                s = s.set('root', newRoot)
                s = s.set('selection', fromJS(path))
                return s
              })
            })
          return x
        })
        
      } catch (ex) {
        setState(s => s.set('loading', 0))
        throw ex
      }
    }

    /**
     * 这个递归函数检查当前树中是否有足够的数据，如果没有足够的数据，它会递归
     * 请求数据（例如：向服务器发送请求 ），直到树中有足够的数据。
     * @param {*} state 
     * @param {*} path 
     */
    async function load(root, path) {

      // 如果root中不存在和path对应的子树，那么要先加载path.slice(0, path.lenth - 1)
      if (!__findByPath(root, path.slice(0, path.length))) {
        root = await load(root, path.slice(0, path.length - 1))
      }
      const list = await options.get(path)
      // 将返回数据写入root
      // root = root.set('selection', fromJS(path))
      return persist(root, mem, path, list)
    }


    function __findByPath(root, path) {

      let p = root
      while (p && path.length > 0) {
        const key = path.shift()
        const n = p.get('children').find(x => x.get('value') === key)
        if (!n) {
          return null 
        }
        p = n
      }
      return p 

    }

    /**
     * 根据Path获取一个Option
     * @param {*} path
     */
    function find(path) {
      return mem[hash(path)] || null
    }

    useEffect(() => {
      select([])
    }, [])

    return {
      selection: state.get('selection').toJS(),
      columns: extractAsColumns(state),
      find,
      select

    }
  }
}

/**
 * 提取展示层数据（将root的数据转换成多列的格式用于展示)
 */
function extractAsColumns(state) {
  const loading = state.get('loading')
  const root = state.get('root')
  const selection = state.get('selection').toJS()
  const columns = []
  let p = root // 指针
  while (p) {
    const children = p.get('children')
    columns.push({
      list: children
        .map(x => {
          const t = x.delete('children')
          return t.toJS()
        })
        .toJS(),
    })

    const key = selection.shift()
    p = p.get('children').find(x => x.get('value') === key)
  }
  // 如果需要展示的列不足， 可以用loading来凑

  if (loading) {
    columns[loading - 1] = { loading: true }
  }
  return columns
}

/**
 * 递归将数据持久化到root中
 * @param {*} node
 * @param {*} list
 * @param {*} path
 */
function persist(node, mem, path,list , _m = path) {
  const value = path.shift()
  if (value === undefined) {
    const immutableList = fromJS(list)
    immutableList.forEach(x => {
      mem[hash(_m.concat(x.get('value')))] = x
    })
    node = node.set('children', immutableList.map(x => {
      x = x.set('children', new List())
      return x
    }))
    return node
  }

  node = node.updateIn(['children'], children => {
    const subNode = children.find(x => x.get('value') === value)
    if (!subNode) {
      throw `[useSelection] cannot find value:${value} local`  
    }
    const index = children.indexOf(subNode)
    const child = persist(subNode, mem, path, list, _m)
    children = children.set(index, child)
    return children
  })
  return node
}
