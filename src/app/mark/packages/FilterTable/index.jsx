import React, { Component, useState, useEffect } from 'react'
import { Table as FusionTable, Pagination, Message } from '@alifd/next'
import _ from 'lodash'
import qs from 'qs'
import { request } from '@/packages/request'
import parseUrl from '@/packages/request/parseUrl'
import BoreasFilter, { withFilter } from '../Filter'

export function Table(props) {
  const {
    columns,
    total,
    pageSize,
    changePageSize,
    search,
    reload,
    actionFunc,
    defaultCurrent,
    updateRow,
    ...others
  } = props

  function injectCell() {
    return columns.map(x => {
      const y = { ...x }
      if (y.cell) {
        const ocell = x.cell
        y.cell = (value, index, record) => {
          return ocell(value, index, record, {
            search,
            changePageSize,
            updateRow,
            reload,
            actionFunc
          })
        }
      }
      return y
    })
  }

  return (
    <React.Fragment>
      <FusionTable {...others}>
        {injectCell(columns).map((column, i) => {
          return <FusionTable.Column key={i} {...column} />
        })}
      </FusionTable>
      {total && others.dataSource.length ? (
        <Pagination
          className="ali-boreas-pagination"
          total={total}
          totalRender={() => `共${total}条记录`}
          defaultCurrent={defaultCurrent || 1}
          current={defaultCurrent || 1}
          pageSizeSelector='dropdown'
          pageSizePosition="end"
          pageSizeList={[10, 20, 50, 100]}
          shape='arrow-only'
          onPageSizeChange={changePageSize}
          pageSize={+pageSize || 10}
          onChange={no => {
            search({ pageNum: no })
          }}
        />
      ) : null}
    </React.Fragment>
  )
}

export function Filter(props) {
  // search, onEvent, reset, ...
  const { search, onEvent, reset, ...others } = props
  function handleFilterEvent(...args) {
    let [type, values, field] = args
    // args: type, values, field
    // { pageNum: 1, pageSize: props.defaultPageSize || 10 }
    onEvent && onEvent(...args)
    // if (type === 'reset') {
      // }
    if (type === 'query' || type === 'reset') {
      values = _.assign(values, { pageNum: 1, pageSize: props.defaultPageSize || 10 })
      return search(values)
    }
  }

  return (
    // <div className="ali-boreas-filter">
    <BoreasFilter {...others} onEvent={handleFilterEvent} />
    // </div>
  )
}


export function useFilterTable({
  filterConfig,
  filterProps = {},
  tableConfig,
  tableProps = {},
  ...othersProps
}) {
  return ({
    action,
    requestImmediately = () => true,
    mapStateToProps = x => x
  }) => {
    const init = {
      pageNum: 1,
      pageSize: 10
    }

    // 获取默认defaultValue
    let defaultFilterData = null
    if (filterConfig) {
      const childrenList = [].concat(filterConfig).reduce((c, n) => (c || []).concat(n.children), [])
      defaultFilterData = {}
      childrenList.forEach(v => {
        const { defaultValue } = v.props
        if (defaultValue !== undefined && defaultValue !== null) {
          defaultFilterData[v.name] = defaultValue
        }
      })
    }

    const initQueryParams = parseUrl(window.location.href).query
    const [queryParams, setQueryParams] = useState({ ...init, ...initQueryParams })
    const [data, setData] = useState({
      total: 0,
      dataSource: []
    })

    const [originData, setOriginData] = useState({
      total: 0,
      dataSource: []
    })

    const [sort, setSort] = useState(tableProps.defaultSort)

    tableProps.sort = sort
    
    tableProps.onSort = (dataIndex, order) => {
      setSort({
        ...sort,
        [dataIndex]: order
      })
      const r = data.dataSource.sort((a, b) => {
        const result = a[dataIndex] - b[dataIndex]
        if (!result) {
          return r
        }
        // eslint-disable-next-line no-nested-ternary
        return order === 'asc' ? (result > 0 ? 1 : -1) : (result > 0 ? -1 : 1)
      })
      // mapStateToProps
      setData(mapStateToProps({
        total: data.total,
        dataSource: r
      }))
      return r
    }

    const localFilter = (filterParams) => {
      // 本地过滤，布不请求接口
      console.log('localFilter', filterParams)
      _.keys(filterParams).forEach(key => {
        let selectedKeys = filterParams[key].selectedKeys || []
        let r = originData.dataSource || []
        if (selectedKeys.length) {
          r = r.filter(record => {
            return record[key] && selectedKeys.indexOf(record[key].toString()) >= 0
          })
      }
       
        setData(mapStateToProps({
          total: data.total,
          dataSource: r || []
        }))
        return r
      })
    }

    // const asyncFilter = (filterParams) => {
    //   _.keys(filterParams).forEach(key => {
    //     const selectedKeys = filterParams[key].selectedKeys
    //     search({
    //       [key]: selectedKeys[0]
    //     })
    //   })
    // }

    tableProps.onFilter = (filterParams) => {
      localFilter(filterParams)
    }

    const handleHistoryQuery = (history, _query) => {
      if (history) {
        // 更新url上的参数
        for (let key in _query) {
          if (_query[key] === undefined || _query[key] === '') {
            delete _query[key]
          }
          setQueryParams(_query)
        }
        
        history.replace(
          history.location.pathname + '?' + qs.stringify(_query)
        )
        return _query
      }
    }

    
    const search = async (_query = {}) => {
      const query = {
        ...queryParams,
        ..._query
      }
      try {
        const json = await action(query) || {}
         // 更新url上的参数
        const history = filterProps.history || tableProps.history
        handleHistoryQuery(history, query)
        let d = {
          total: json.total || 0,
          dataSource: json.data || []
        }
        d = mapStateToProps(d)
        setData(mapStateToProps(d))
        setOriginData(mapStateToProps(d))
        return d
      } catch (error) {
        console.error('search catch', error)
        Message.error(error.errMessage || '接口错误!')
        throw (new Error())
      }
    }

    const changePageSize = pageSize => {
      if (+pageSize !== +queryParams.pageSize) {
        search({ pageSize })
      }
    }

    const reset = v => {
      setQueryParams(v || init)
      search()
    }

    const reload = async () => {
      console.log('query-reload', queryParams)
      const r = await search()
      return r
    }

    useEffect(() => {
      if (requestImmediately) {
        // 判断是否初始化就调用search
        console.log('requestImmediately', defaultFilterData)
        search(defaultFilterData)
      }
    }, [])

    const { defaultSort, ...otherTableProps } = tableProps

    const mergeProps = {
      filterProps: {
        ...filterProps,
        initFormData: queryParams,
        reload,
        search,
        reset,
      },
      tableProps: {
        ...otherTableProps,
        pageSize: +queryParams.pageSize,
        defaultCurrent: Number(queryParams.pageNum),
        ...data,
        changePageSize,
        reload,
        search,
      },
      ...othersProps
    }

    const filter = (
      filterConfig && <Filter meta={filterConfig} {...mergeProps.filterProps} />
    )

    const table = (
      tableConfig && <Table className="ali-boreas-table" columns={tableConfig} {...mergeProps.tableProps} />
    )
    
    return {
      filter,
      table,
      search,
      reload,
      reset,
      localFilter,
      sort,
      tableData: originData,
      defaultFilterData,
    }
  }
}
