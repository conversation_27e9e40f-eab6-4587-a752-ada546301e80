.ali-boreas{
  &-wrap {
    width: 100%;
    display: flex;
  }
  &-sidebar {
    background: #fafafa;
    font-size: 14px;
    // border-right: 1px solid #ebebeb;
    height: 100%;
    // width: 150px;
    // .next-menu-item-inner span {
    //   font-size: 14px;
    //   cursor: pointer;
    // }
  }

  &-content {
    flex: 1;
    padding: 0 0 40px;
    overflow-y: scroll;
  }
  &-router-wrap {
    
  }
  &-breadcrumb {
    padding: 10px 20px;
    .next-breadcrumb .next-breadcrumb-text {
      font-size: 14px;
      cursor: pointer;
    }
  }
  &-main {
    background: #ffffff;
    padding: 0 20px 20px;
    min-height: 80vh;
  }
  &-pagination {
    padding: 30px 0;
    text-align: right;
    .next-select-inner {
      min-width: 64px !important;
    }
    .next-menu {
      width: 70px !important;
    }
    .next-pagination-total {
      font-size: 14px;
    }
  }
  &-cell {
    color: #ff7c4d;
    cursor: pointer;
    .br {
      border-right: 1px solid #ebebeb;
    }
    span {
      cursor: pointer;
    }
    .pl {
      padding-left: 8px;
    }
    .pr {
      padding-right: 8px;
    }
  }
  &-status {
    font-size: 14px;
    color: #666666;
    position: relative;
    padding-left: 15px;
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      display: inline-block;
      width: 0;
      height: 0;
      border: 3px solid #00ccaa;
      border-radius: 50%;
      margin-top: -3px;
    }
    &-2 {
      &:before {
        display: inline-block;
        width: 0;
        height: 0;
        border: 3px solid #cccccc;
        border-radius: 50%;
      }
    }
    &-0 {
      &:before {
        display: inline-block;
        width: 0;
        height: 0;
        border: 3px solid #ff0000;
        border-radius: 50%;
      }
    }
  }
}
