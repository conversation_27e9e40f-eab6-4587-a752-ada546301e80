import React from 'react'
import { Form } from '@alifd/next'

const FormItem = Form.Item

/**
 * formItemLayout
 * title
 * description
 */

function Title(props) {
  const formItemLayout = props.formItemLayout || {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 }
  }
  return (
    <React.Fragment>
      {props.title && (
        <FormItem label={props.title} className="ali-boreas-form-title" {...formItemLayout}>
          {props.description && <div>{props.description}</div>}
        </FormItem>
      )}
      {props.children && props.children}
    </React.Fragment>
  )
}

export default Title
