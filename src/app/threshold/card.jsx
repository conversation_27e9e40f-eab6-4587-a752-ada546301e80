import React, { useState, Component } from 'react'
import PropTypes from 'prop-types'
import { Dialog, Upload, Message, Button, Icon } from '@alifd/next'
import { request } from '@/packages/request'
import UploadFile from '@/components/uploadFile'
import DomainConfigure from '@/packages/request/DomainConfigure'
import { buildEnv } from '@/packages/env'

const env = buildEnv()

class Card extends Component {
    static propTypes = {
        participateType: PropTypes.number,
        participateName: PropTypes.string,
        operator: PropTypes.string,
        gmtUpdate: PropTypes.string,
    }

    static defaultProps = {
        participateType: 1, // 1 供应商 2 商户
        participateName: '',
        operator: '',
        gmtUpdate: ''
    }

    constructor(props) {
        super(props)
        this.state = {
            currentData: {
                file: null,
                key: ''
            }
        }
    }

    setCurrentData = (_currentData) => {
        this.setState({
            currentData: _currentData
        })
    }

    handleDownload = () => {
        const { participateType } = this.props
        const type = +participateType === 2 ? 'supperThresholdShop' : 'supperThresholdSupplier'
        window.open(`${DomainConfigure.files[env]}/gettemplate?type=${type}`)
    }

    onConfirm = async () => {
        const { participateType } = this.props
        // supperThresholdShop supperThresholdSupplier
        const { file, key } = this.state.currentData
        if (!file) {
            Message.error('请先上传文件')
            return
        } else if (!key) {
            Message.error('文件上传有误，请重新上传')
            return
        }
       const r = await this.uploadKey(key, +participateType + 2)
       return r
    }

    uploadKey = async (key, taskProjectType) => {
        try {
          const r = await request({
            url: `mtop://mtop.ele.newretail.rule.task.create`,
            method: 'POST',
            mapRequest: () => {
              return {
                taskProjectType, // 3:导入供应商超会门槛; 4：导入店铺超会门槛
                uploadKey: key,
              }
            }
          })
          r && Message.success('上传成功，上传状态请查收邮件')
          console.log('rrrrr', r)
          return r
        } catch (error) {
          Message.error('上传失败')
          console.log('uploadKey catch')
        }
      }

      showDialog = () => {
        const HeaderCompoment = () => {
            return (
                <div style={{ textAlign: 'center', paddingBottom: '20px' }}>
                    <Button type="primary" onClick={this.handleDownload} className="width-auto">
                        <Icon type="download" />下载模板文件
                    </Button>
                </div>
            )
        }
        // 批量设置门槛
        const dialog = Dialog.confirm({
            title: '批量设置门槛',
            style: {
                width: 400
            },
            content: (
                <UploadFile
                  HeaderCompoment={HeaderCompoment}
                  updateFileData={this.setCurrentData}
                  action={`${DomainConfigure.files[env]}/upload`}
                  name='userfile'
                />
            ),
            footer: (
                <div>
                    <Button type="primary" onClick={async () => {
                        const r = await this.onConfirm()
                        console.log(r)
                        if (r) {
                            dialog.hide()
                            window.location.reload()
                        }
                    }} style={{ marginRight: '8px' }}>
                        确认
                    </Button>
                    <Button onClick={() => dialog.hide()}>
                        取消
                    </Button>
                </div>
            )
        })
    }

    render() {
        const { participateName, gmtUpdate, operator, participateType } = this.props
        return (
            <div className="threshold-card">
                <div className="threshold-card-title">
                    {
                        +participateType === 2 ? '单店门槛设置' : '供应商门槛设置'
                    }
                </div>
                <div className="threshold-card-content">
                    <Button type="primary" onClick={this.showDialog}>更新配置文件</Button>
                    <div className="threshold-card-text">最新更新时间：{gmtUpdate || '暂无'}<span>|</span>更新人：{operator || '暂无'} </div>
                </div>
            </div>
        )
    }
}


export default Card
