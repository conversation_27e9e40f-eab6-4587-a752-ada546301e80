import React, { useState, useEffect } from 'react'
import { Tab } from '@alifd/next'
import { request } from '@/packages/request'
import _ from 'lodash'
import Card from './card'
import FilterList from './filterList'
import './style.scss'

const Index = () => {
    const [cardList, setCardList] = useState([])

    const columnsList = [
        [
            {
                title: '商户ID',
                dataIndex: 'participateMapping',
            },
            {
                title: '商户名称',
                dataIndex: 'participateName',
            },
            {
                title: '当前门槛',
                dataIndex: 'threshold',
                cell: v => {
                    const r = v ? v / 100 : 0
                    return `${r}元`
                }
            }
        ],
        [
            {
                title: '供应商ID',
                dataIndex: 'participateMapping',
            },
            {
                title: '供应商名称',
                dataIndex: 'participateName',
            },
            {
                title: '当前门槛',
                dataIndex: 'threshold',
                cell: v => {
                    const r = v ? v / 100 : 0
                    return `${r}元`
                }
            }
        ],
    ]

    const tabList = [{
        title: '单店门槛',
        key: 'shop',
        content: <FilterList columns={columnsList[0]} type="shop" />
    }, {
        title: '供应商门槛',
        key: 'supplier',
        content: <FilterList columns={columnsList[1]} type="supplier" />
    }]

    useEffect(() => {
        request('mtop://mtop.ele.newretail.rule.threshold.type.query')
            .then(res => {
                const _data = _.get(res, 'data.data.thresholdQueryResult', {
                    1: {
                        participateType: 1
                    },
                    2: {
                        participateType: 2
                    },
                })
                console.log(_data)
                
                // 供应商1，商户2
                setCardList([].concat(_data[2] || {
                    participateType: 2
                }, _data[1] || {
                    participateType: 1
                }))
            })
    }, [])

    return (
        <div className="threshold">
            <div className="threshold-header">
            {
                cardList.length > 0 && cardList.map(item => {
                    if (!item) {
                        return null
                    }
                    const { participateType, participateName, operator, gmtUpdate } = item || {}
                    return (
                        <Card
                          participateType={participateType}
                          participateName={participateName}
                          gmtUpdate={gmtUpdate}
                          operator={operator}
                          key={participateName}
                        />
                    )
                })
            }
            </div>
            <div className="threshold-content">
                <Tab>
                    {
                        tabList.map(item => {
                            return (
                                <Tab.Item title={item.title} key={item.key}>{item.content}</Tab.Item>
                            )
                        })
                    }
                </Tab>
            </div>
        </div>
    )
}

export default Index
