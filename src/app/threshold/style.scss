.threshold {
    margin: 0 16px;
    padding: 16px;
    background: #ffffff;
    .next-pagination-size-selector-dropdown {
        min-width: 70px;
    }
    &-header {
        display: flex;
    }
    &-card {
        flex: 1;
        border: 20px solid #f5f5f5;
        &:nth-child(1) {
            border-right: 0 none;
        }
        padding: 20px;
        &-title {
            border-bottom: 1px solid #f5f5f5;
            line-height: 1;
            padding: 16px 0;
        }
        &-content {
            padding-top: 20px;
        }
        &-text {
            font-size: 12px;
            line-height: 1;
            padding-top: 16px;
            span {
                padding: 0 10px;
            }
        }
    }
    &-content {
        padding-top: 10px;
    }
    &-filterlist {
        padding-top: 16px;
        min-height: 460px;
        padding-bottom: 150px;
    }
}
.next-dialog .width-auto {
    width: auto!important;
}
