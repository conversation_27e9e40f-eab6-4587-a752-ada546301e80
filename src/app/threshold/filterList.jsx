import React from 'react'
import { Dialog, Upload, Message, Button } from '@alifd/next'
import { FilterTable } from '@ali/boreas2'
import { request } from '@/packages/request'
import { withRouter } from 'react-router-dom'
import formatDate from '@/utils/formatDate'
import _ from 'lodash'

const { useFilterTable } = FilterTable

const ThresholdFilterList = ({ columns, history, type }) => {
  const typeText = type === 'shop' ? '商户' : '供应商'

    const filterConfig = {
        type: 'Filter',
        // labelTextAlign: 'left',
        buttonAlign: 'start-line', // end-line  start-line(仅仅支持一个选项的时候) end start 默认
        // hasExpand: 'rightTop'
        children: [
          {
            name: 'participateId',
            type: 'Input',
            props: {
              label: `${typeText}ID:`,
              placeholder: `请输入${typeText}ID`,
              hasClear: true,
              htmlType: 'number',
              trim: true
            }
          },
        ],
      }

      const filterProps = {
        history,
        chunkLength: 1,
        // resetType: 'reset',
        formItemLayout: {
          labelCol: { span: 7 },
          wrapperCol: { span: 15 },
        },
        // onEvent: (type, values, field) => {
        //   console.log('filterProps onevent', type, values, field)
        // }
      }

    const tableProps = {
        // hasBorder: false,
        history,
        emptyContent: "暂无门槛信息~",
        primaryKey: "participateId",
      }

    const R = useFilterTable({
        filterConfig,
        filterProps,
        tableConfig: columns,
        tableProps,
      })({
        action: async (_values) => {
          const { pageSize, pageNum, participateId, ...others } = _values
          try {
            const r = await request({
              url: `mtop://mtop.ele.newretail.rule.threshold.query`,
              method: 'POST',
              mapRequest: json => {
                return {
                  pageSize: pageSize || 1,
                  currentPage: pageNum || 10,
                  participateId: participateId || '',
                  participateType: type === 'shop' ? 2 : 1
                }
              },
              mapResponse: (json) => {
                const data = _.get(json, 'data.data') || {}
                return {
                  total: data.total,
                  data: data.couponThresholdRuleList || []
                }
              }
            })
            return r
          } catch (error) {
            console.log('useFilterTable catch')
          }
        }
      })
      return (
        <div className="threshold-filterlist">
            {R.filter}
            {R.table}
        </div>
      )
}


export default withRouter(ThresholdFilterList)
