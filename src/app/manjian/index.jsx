import React, { useState, Component } from 'react'
import { Dialog, Upload, Message, Button } from '@alifd/next'
import { request } from '@/packages/request'
import UploadFile from '@/components/uploadFile'
import DomainConfigure from '@/packages/request/DomainConfigure'
import { buildEnv } from '@/packages/env'
import _ from 'lodash'

import Table from './table'

const env = buildEnv()

class Index extends Component {
    constructor(props) {
        super(props)
        this.state = {
            currentData: {
                file: null,
                key: ''
            }
        }
    }

    setCurrentData = (_currentData) => {
        this.setState({
            currentData: _currentData
        })
    }

    importCity = () => {
        const HeaderCompoment = () => {
            return (
                <div style={{ paddingBottom: '20px' }}>
                    <p>1. 点击<a onClick={() => this.handleDownload('cityCategory')} href="javascript:void(0)">下载模板</a></p>
                    <p>2. 导入完成后，请刷新确认是否导入成功</p>
                </div>
            )
        }
        // 导入城市
        const dialog = Dialog.confirm({
            title: '批量导入城市',
            content: (
                <UploadFile
                  HeaderCompoment={HeaderCompoment}
                  updateFileData={this.setCurrentData}
                  action={`${DomainConfigure.files[env]}/upload`}
                  name='userfile'
                />
            ),
            footer: (
                <React.Fragment>
                    <Button type="primary" onClick={async () => {
                        const r = await this.onConfirm(1)
                        console.log(r)
                        if (r) {
                            dialog.hide()
                            window.location.reload()
                        }
                    }} style={{ marginRight: '8px' }}>
                        确认
                    </Button>
                    <Button onClick={() => dialog.hide()}>
                        取消
                    </Button>
                </React.Fragment>
            )
        })
    }

    changeActivity = () => {
        // 下线满减活动
        const HeaderCompoment = () => {
            return (
                <div style={{ textAlign: 'center', paddingBottom: '20px' }}>
                    <p>单次下线商户上限3000家，约在2分钟内生效，请耐心等待</p>
                    <Button type="primary" onClick={() => this.handleDownload('offlineShop')} className="width-auto">下载模板文件</Button>
                </div>
            )
        }
        // 导入城市
        const dialog = Dialog.confirm({
            title: '下线满减活动',
            content: (
                <UploadFile
                  HeaderCompoment={HeaderCompoment}
                  updateFileData={this.setCurrentData}
                  action={`${DomainConfigure.files[env]}/upload`}
                  name="userfile"
                />
            ),
            footer: (
                <React.Fragment>
                    <Button type="primary" onClick={async () => {
                        const r = await this.onConfirm(2)
                        console.log(r)
                        if (r) {
                            dialog.hide()
                            window.location.reload()
                        }
                    }} style={{ marginRight: '8px' }}>
                        确认
                    </Button>
                    <Button onClick={() => dialog.hide()}>
                        取消
                    </Button>
                </React.Fragment>
            )
        })
    }

    handleDownload = (type) => {
        // cityCategory 导入城市管控模板 / offlineShop 下线店铺模板
        window.open(`${DomainConfigure.files[env]}/gettemplate?type=${type}`)
    }

    onConfirm = async (taskProjectType) => {
        // cityCategory offlineShop
        const { file, key } = this.state.currentData
        if (!file) {
            Message.error('请先上传文件')
            return
        } else if (!key) {
            Message.error('文件上传有误，请重新上传')
            return
        }
       const r = await this.uploadKey(key, taskProjectType)
       return r
    }

    uploadKey = async (key, taskProjectType) => {
        try {
          const r = await request({
            url: `mtop://mtop.ele.newretail.rule.task.create`,
            method: 'POST',
            mapRequest: () => {
              return {
                taskProjectType, // 1:导入城市类目满减管控; 2:下线全店满减
                uploadKey: key,
              }
            }
          })
          r && Message.success('上传成功，上传状态请查收邮件')
          console.log('rrrrr', r)
          return r
        } catch (error) {
          Message.error('上传失败')
          console.log('offlineActivity catch')
        }
      }
      

    render() {
        return (
            <div className="right-content manjian-wrap">
                <div className="manjian-opwrap">
                    <Button type="primary" onClick={this.importCity}>导入城市</Button>
                    <Button type="primary" onClick={this.changeActivity}>下线满减活动</Button>
                </div>
                <Table history={this.props.history} />
            </div>
        )
    }


} 

export default Index
