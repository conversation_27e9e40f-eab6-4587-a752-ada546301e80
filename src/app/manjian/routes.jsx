import React from 'react'
import Routers from '@/packages/routers'
import Index from './index'
import './style.scss'

const routerList = [
  {
    subRouterPath: '/',
    subComponent: Index,
    // routerItems: [
    //   {
    //     path: '/',
    //     component: Index,
    //     breadcrumbName: '满减管控'
    //   }
    // ]
  }
]

const breadcrumbMap = {
  '/': '满减管控',
}

function App() {
  return <Routers routerList={routerList} redirectPath="/" breadcrumbMap={breadcrumbMap} />
}

export default App
