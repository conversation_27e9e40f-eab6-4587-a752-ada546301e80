import React from 'react'
import { Dialog, Upload, Message, Button } from '@alifd/next'
import { FilterTable } from '@ali/boreas2'
import { request } from '@/packages/request'
import formatDate from '@/utils/formatDate'
import _ from 'lodash'

const { useFilterTable } = FilterTable

const ManjianTable = ({ history }) => {
  const offlineActivity = (record, reload) => {
    Dialog.confirm({
      title: '提示',
      content: '确认下线此满减管控策略？',
      onOk: async () => {
        try {
          const r = await request({
            url: `mtop://mtop.ele.newretail.rule.reduce.offline`,
            method: 'POST',
            mapRequest: () => {
              return {
                cityId: record.cityId,
              }
            }
          })
          Message.success('下线成功')
          reload && reload()
          return r
        } catch (error) {
          Message.error('下线失败')
          console.log('offlineActivity catch')
        }
      }
    })
  }

    const tableColumns = [
        // 城市、品类、设置时间、操作人、操作
        {
            title: '城市',
            dataIndex: 'cityName',
        },
        {
            title: '品类',
            dataIndex: 'storeCategory',
            cell: (v, record) => {
              const data = JSON.parse(v)
              const storeCategoryName = data.reduce((c, n) => `${c}${c && '、'}${n.categoryName}`, '')
              return (
                <div>{storeCategoryName}</div>
              )
            }
        },
        {
          title: '设置时间',
          dataIndex: 'gmtModified',
        },
        {
            title: '操作人',
            dataIndex: 'operator',
        },
        {
            title: '操作',
            dataIndex: 'op',
            cell: (value, index, record, { reload }) => {
                // 预览、删除
                return (
                    <span onClick={() => offlineActivity(record, reload)} className="op-cell">
                      下线
                    </span>
                )
              }
        },
    ]
    const tableProps = {
        // hasBorder: false,
        history,
        emptyContent: "暂无满减管控信息~",
        primaryKey: "id",
      }

    const R = useFilterTable({
        tableConfig: tableColumns,
        tableProps,
      })({
        action: async (_values) => {
          const { pageSize, pageNum, ...others } = _values
          try {
            const r = await request({
              url: `mtop://mtop.ele.newretail.rule.reduce.query`,
              method: 'POST',
              mapRequest: json => {
                return {
                  pageSize: pageSize || 1,
                  currentPage: pageNum || 10,
                }
              },
              mapResponse: (json) => {
                const data = _.get(json, 'data.data') || {}
                return {
                  total: data.total,
                  data: data.cityReduceDTOList || []
                }
              }
            })
            return r
          } catch (error) {
            console.log('useFilterTable catch', error)
          }
        }
      })

      return (
        <div className="manjian-table">
            {R.table}
        </div>
      )
}


export default ManjianTable
