.app {
    &-list {

    }
    &-item {
        padding: 20px;
        &-title {
            font-size: 16px;
            font-family: PingFangSC-Medium;
        }
        &-entry {
            padding-top: 20px;
            overflow: hidden;
            .hasicon {
                width: 260px;
                margin-right: 30px;
                .card-item {
                    float: left;
                }
                .card-title {
                    text-align: left;
                }
            }
            &-child {
                // flex: 1;÷
                float: left;
                width: 210px;
                border: 1px solid #dfdfdf;
                border-radius: 6px;
                overflow: hidden;
                position: relative;
                padding: 50px 20px 40px;
                margin-right: 20px;
                &:hover {
                    border-color: #FF845B;
                }
                cursor: pointer;
                .card-icon {
                    float: left;
                    width: 60px;
                    margin-right: 6px;
                }
                .card-item {
                    float: auto;
                }
                .card-title {
                    font-size: 16px;
                    line-height: 30px;
                    text-align: center;
                }
                .card-sub-title {
                   font-size: 12px;
                   color: #999;
                   line-height: 20px;
                }
                .card-tag {
                    position: absolute;
                    right: 20px;
                    top: 10px;
                    background: #9e9e9e2b;
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 12px;
                    color: #666;
                }
                .card-btn {
                    position: absolute;
                    right: 20px;
                    bottom: 10px;
                    font-size: 12px;
                    color: #666;
                    &:hover {
                      color: #FF845B;
                    }
                }

            }
        }
        // .next-btn.next-large {
        //     height: 60px;
        //     padding-top: 20px;
        //     padding-bottom: 20px;
        //     img {
        //         float: left;
        //     }
        //     span {
        //         line-height: 20px;
        //         float: left;
        //     }
        //     // line-height: 60px;
        // }
        &-dailog {
            padding: 0 40px;
            .button-item {
               margin-right: 10px;
               width: 170px!important;
               margin-bottom: 10px;
            }
        }
    }

}
.hongbao {
    padding-top: 20px;
    &-form {
        padding-top: 20px;
        &-content {
            padding-left: 40px;
            padding-top: 20px; 
        }
        .expire-input {
            .next-number-picker-normal {
                width: 80%;
            }
        }
        .radio-tab {
           
        }
        .next-number-picker-normal {
            width: 100px;
        }
    }
    &-baseform {
    }
    &-opbtn {
        text-align: center;
        margin-left: -10%;
        padding-top: 20px;
        & > button {
            margin-right: 8px;
        }
    }
    &-list {
        &-table {
            position: relative;
            .add-btn {
                position: absolute;
                right: 20px;
                top: -46px;
            }
        }
    }
    &-detail {
        padding: 0 20px 40px;
        position: relative;
        .back-btn {
            position: absolute;
            left: 20px;
            bottom: 70px;
        }
    }
}

.next-table table{ table-layout: fixed}

.tip {
    font-size: 12px;
    padding-top: 10px;
    color: #999;
}

.ali-boreas-error {
    padding-top: 0;
}
