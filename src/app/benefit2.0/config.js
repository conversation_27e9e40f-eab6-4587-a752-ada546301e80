import _ from 'lodash'

function mapToSource(_map) {
    if (!_map) {
        return []
    }
    return _.keys(_map).map(item => {
        return {
            label: _map[item],
            value: item
        }
    })
}

const ziyuanweiCodeMap = {
    // 频道红包雨、频道闪购值、会场组件-券墙、主站互动城、主站组队PK、会场组件-一键领红包、会场组件-准点抢红包、真实惠频道、购后返渠道、时令好货
    "elem_newretail_daogou.pingdao.hongbaoyu": "频道红包雨", // "新零售-导购-频道页-红包雨",
    "elem_newretail_daogou.jifen": "频道闪购值", // "新零售-导购-玩法-积分",
    "elem_fd.huichang.quanqiang": "会场组件-券墙", // "主站福袋-会场-券墙",
    "elem_fd.hudongcheng": "主站互动城", // "主站福袋-互动城",
    "elem_fd.zutuanPK": "主站组队PK", // "主站福袋-组团PK",
    "elem_fd.huichang.hongbao": "会场组件-一键领红包", // "主站福袋-会场-红包",
    "elem_fd.huichang.zhundianhongbao": "会场组件-准点抢红包", // "主站福袋-会场-准点红包",
    "elem_newretail_daogou.pindao.zhenshihui": "真实惠频道", // "新零售-导购-频道页-真实惠",
    "elem_newretail_order.gouhoufan": "购后返", // "新零售-订单-购后返",
    "elem_newretail_daogou.pindao.shilinghaohuo": "时令好货", // "新零售-导购-频道页-时令好货",
}

const ziyuanweiDataSource = mapToSource(ziyuanweiCodeMap)

const dingtouCodeMap = {
    // 新客三重礼、开宝箱、老带新、抽奖玩法
    "elem_newretail_daogou.pindao.sannew": "新客三重礼", // "新零售-导购-频道页-三重有礼",
    "elem_newretail_daogou.wanfa.kaibaoxiang": "开宝箱", // "新零售-导购-玩法-开宝箱",
    "elem_newretail_daogou.wanfa.laodaixin": "老带新", // "新零售-导购-玩法-老带新",
}
const dingtouDataSource = mapToSource(dingtouCodeMap)

const qudaoCodeMap = {
    // 海王、口碑抽发奖
    "koubei.nongchang": "口碑抽发奖",
    "elem_haiwang": "海王",
}
const qudaoDataSource = mapToSource(qudaoCodeMap)

export {
    ziyuanweiCodeMap,
    ziyuanweiDataSource,
    dingtouCodeMap,
    dingtouDataSource,
    qudaoCodeMap,
    qudaoDataSource,
} 
