import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { <PERSON>rid, Button, Message, Dialog, Input } from '@alifd/next'
import { Form as boreasForm, useFormAction, useForm, FilterTable, useTable } from '@ali/boreas2'
import qs from 'qs'
import _ from 'lodash'
import formatDate from '@/utils/formatDate'
import { request } from '@/packages/request'
import parseUrl from '@/packages/request/parseUrl'
import formatNumber from '@/utils/formatNumber'
import { benefitStatusMap, varietyInfoMap , userStatusMap, outerNameTypeMap, scenarioMap } from '../map'

const { Filter, Table, useFilterTable } = FilterTable
function Detail(props) {
  // 权益内部名称 权益生效期
  // 所有创建时填写的字段+权益id、活动创建时间、创建人、当前状态、剩余库存情况（总剩余、每日剩余）
  const columns = [
    {
      title: '活动ID',
      dataIndex: 'couponId',
      width: 140,
    },
    {
      title: '权益内部名称',
      dataIndex: 'name',
      width: 140,
    },
    {
      title: '权益外部名称',
      dataIndex: 'outerNameInfo',
      width: 140,
      cell: v => {
        if (+v.outerNameType === 0) {
          return v.name
        } 
        return outerNameTypeMap[v.outerNameType]
      }
    },
    {
      title: '预算信息',
      dataIndex: 'budgetId',
      width: 140,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      cell: value => {
        return formatDate(value)
      },
      width: 140,
    },
    {
      title: '当前状态',
      dataIndex: 'status',
      width: 140,
      cell: v => {
        return benefitStatusMap[v]
      }
    },
    {
      title: '剩余库存情况',
      dataIndex: 'stockInfo',
      width: 140,
      cell: v => {
        return (
          <React.Fragment>
            <div>
              总剩余: {v.leftStock}
            </div>
            <div>
              每日剩余: {v.leftDayStock}
            </div>
          </React.Fragment>
        )
      }
    },
    {
      title: '权益生效日期',
      dataIndex: 'validity',
      width: 200,
      cell: (v, i, record) => {
        let _start = v.startTime * 1000
        let _end = v.endTime * 1000
        return (
          <React.Fragment>
            <div>{`${formatDate(_start)} - `}</div>
            <div>{`${formatDate(_end)}`}</div>
          </React.Fragment>
        )
      }
    },
    
    {
      title: '满减类型',
      dataIndex: 'ruleInfo',
      width: 140,
      cell: v => {
        if (v.type === 1) {
          // 定额
          return `满${formatNumber(v.threshold, '/', 100)}减${formatNumber(v.amount, '/', 100)}`
        } else if (v.type === 2) {
          // 区间
          return (
            <React.Fragment>
              <div>{`满${formatNumber(v.threshold, '/', 100)}-${formatNumber(v.maxThreshold, '/', 100)}减${formatNumber(v.amount, '/', 100)}-${formatNumber(v.maxAmount, '/', 100)}`}</div>
              <div>优惠比例：{`${formatNumber(v.discountRate, '*', 100)}% - ${formatNumber(v.maxDiscountRate, '*', 100)}%`}</div>
            </React.Fragment>
          )
        }
      }
    },
    {
      title: '红包有效期',
      dataIndex: 'couponId',
      width: 140,
      cell: (v, i, record) => {
        const { validStartTime, validEndTime, duration } = record.validity || {}
        if (duration !== null && duration >= 0) {
          return `${duration}天`
        }
        return (
          <React.Fragment>
            <div>{formatDate(validStartTime * 1000)} - </div>
            <div>{formatDate(validEndTime * 1000)}</div>
          </React.Fragment>
        )
      }
    },
    {
      title: '红包类型',
      dataIndex: 'varietyInfo',
      width: 140,
      cell: v => {
        // 红包类型 1｜品类红包   2｜会场券
        if (v.varietyType === 2) {
          return `会场券id: ${v.venueId}`
        } else if (v.varietyType === 1) {
          return v.flavors && v.flavors.reduce((c, n) => `${c}${c && ','}${varietyInfoMap[n]}`, '')
        }
      }
    },
    {
      title: '业务场景',
      dataIndex: 'scenario',
      width: 140,
      cell: v => {
        return scenarioMap[v]
      }
    },
    {
      title: '核销时用户身份',
      dataIndex: 'userStatus',
      width: 140,
      cell: v => {
        return userStatusMap[v]
      }
    },
    {
      title: '库存限制',
      dataIndex: 'stockInfo',
      width: 140,
      cell: v => {
        return (
          <React.Fragment>
            <div>总库存:{v.stock}</div>
            <div>每日库存:{v.dayStock}</div>
          </React.Fragment>
        )
      }
    },
    {
      title: '限领规则',
      dataIndex: 'userLimit',
      width: 140,
      cell: v => {
        return (
          <React.Fragment>
            <div>总限领:{ v.limit }</div>
            <div>每日限领:{ v.typeLimit && v.typeLimit.limit }</div>
          </React.Fragment>
        )
      }
    },
    {
      title: '消耗成本',
      dataIndex: 'consumeCost',
      width: 140,
      cell: v => {
        return v ? `闪购值${v}` : '无'
      }
    }
  ]

  const searchParams = parseUrl(props.location.search).query || {}
  const { data, pageData, onPageSizeChange, search } = useTable({
    requestAction: async (_data) => {
      const r = await request({
        url: `benefit2://coupon/detail`,
        method: 'POST',
        mapRequest: (json, { query }) => {
          return {
            couponId: searchParams.couponId
          }
        }
      })
      return r && r.data
    }
  })
  
  if (!columns || !data || !data.couponId) {
    return null
  }
  return (
    <div className="hongbao-detail">
    <Table
      dataSource={[data]}
      columns={columns}
      total={1}
      // fixedHeader
      // maxBodyHeight="500px"
      defaultCurrent={pageData.pageNum}
      pageSize={pageData.pageSize}
      changePageSize={onPageSizeChange}
      search={search}
      // useVirtual
    />
      <Button
        type="primary"
        className="back-btn"
        onClick={() => {
          let sessionSearch = JSON.parse(window.sessionStorage.getItem('benefit_list_search') || '{}')
          props.history.push({
            pathname: '/benefit/hongbao',
            search: `?${qs.stringify(sessionSearch)}`
          })
        }}>
        返回
      </Button>
    </div>
  )
}

export default Detail
