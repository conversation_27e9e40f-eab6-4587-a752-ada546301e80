import formatDate from '@/utils/formatDate'
import _ from 'lodash'
import BigNumber from 'bignumber.js'
import formatNumber from '@/utils/formatNumber'

const formatValidaty = (time, rule = 'YYYY-MM-DD HH:mm:ss') => {
  return Date.parse(formatDate(time, rule)) / 1000
}

const frontAdapterToServer = (_data = {}) => {
    let hongbaoValidity = {}
    const hongbaoType = _.get(_data, 'hongbaoValidity.type')
    if (hongbaoType === 1) {
      hongbaoValidity = {
        duration: _.get(_data, 'hongbaoValidity.innerValue')
      }
    } else if (hongbaoType === 2) {
      hongbaoValidity = {
        startTime: _.get(_data, 'hongbaoValidity.innerValue.startTime', null),
        endTime: _.get(_data, 'hongbaoValidity.innerValue.endTime', null)
      }
    }

    let outerNameInfo = {}
    const outerNameInfoType = _.get(_data, 'outerNameInfo.type')
    if (outerNameInfoType === 0) {
      outerNameInfo = {
        outerNameType: 0,
        name: _.get(_data, 'outerNameInfo.innerValue', '')
      }
    } else if (outerNameInfoType === 1) {
      outerNameInfo = {
        outerNameType: +_.get(_data, 'outerNameInfo.innerValue', 1),
        name: ''
      }
    }

    let ruleInfo = {}
    const ruleType = _.get(_data, 'ruleInfo.type')
    if (ruleType === 1) {
      // 定额
      ruleInfo = {
        type: 1,
        threshold: formatNumber(_.get(_data, 'ruleInfo.innerValue.threshold', null), '*', 100),
        amount: formatNumber(_.get(_data, 'ruleInfo.innerValue.amount', null), '*', 100)
      }
    } else if (ruleType === 2) {
      // 区间
      ruleInfo = {
        type: 2,
        threshold: formatNumber(_.get(_data, 'ruleInfo.innerValue.threshold.min', null), '*', 100), // 门槛
        maxThreshold: formatNumber(_.get(_data, 'ruleInfo.innerValue.threshold.max', null), '*', 100),
        amount: formatNumber(_.get(_data, 'ruleInfo.innerValue.amount.min', null), '*', 100), // 金额
        maxAmount: formatNumber(_.get(_data, 'ruleInfo.innerValue.amount.max', null), '*', 100),
        discountRate: formatNumber(_.get(_data, 'ruleInfo.innerValue.discountRate.min', null), '/', 100), // 优惠比例
        maxDiscountRate: formatNumber(_.get(_data, 'ruleInfo.innerValue.discountRate.max', null), '/', 100)
      }
    }

    let varietyInfo = {}
    const varietyInfoType = _.get(_data, 'varietyInfo.type')
    if (varietyInfoType === 1) {
      varietyInfo = {
        varietyType: 1,
        flavors: _.get(_data, 'varietyInfo.innerValue'),
        venueId: '', // 会场id
      }
    } else if (varietyInfoType === 2) {
      varietyInfo = {
        varietyType: 2,
        flavors: [],
        venueId: _.get(_data, 'varietyInfo.innerValue'), // 会场id
      }
    }

    const data = {
        type: 50,
        // 基本信息
        name: _data.name,
        validity: {
          validStartTime: formatValidaty(hongbaoValidity.startTime, 'YYYY-MM-DD 00:00:00'), // 红包生效期
          validEndTime: formatValidaty(hongbaoValidity.endTime, 'YYYY-MM-DD 23:59:59'),
          duration: hongbaoValidity.duration >= 0 ? hongbaoValidity.duration : null, // 券有效天数, 自领取日起，填写固定天数，范围0~120，0代表领取后当天24点失效
          startTime: _data.benefitValidity && formatValidaty(_data.benefitValidity[0]), // 券有效期
          endTime: _data.benefitValidity && formatValidaty(_data.benefitValidity[1])
        },
        outerNameInfo: {
          //  0｜自定义   1｜果蔬商超红包  2｜生鲜专享红包
          outerNameType: outerNameInfo.outerNameType,
          name: outerNameInfo.name
        },
        budgetId: _data.budgetId,
        // 优惠信息
        // 满减类型 1｜定额 满X元减X元    2｜区间 满X-Y元减X-Y元
        ruleInfo,
        // {
        //   type: 'type',
        //   threshold: 'threshold', // 门槛
        //   maxThreshold: 'maxThreshold',
        //   amount: 'amount', // 金额
        //   maxAmount: 'maxAmount',
        //   discountRate: 'discountRate', // 优惠比例
        //   maxDiscountRate: 'maxDiscountRate'
        // },
        // 红包类型 1｜品类红包   2｜会场券
        varietyInfo,
        // {
        //   varietyType: 'varietyType',
        //   flavors: [],
        //   venueId: 'venueId', // 会场id
        // },
        scenario: _data.scenario, // 业务场景 0 仅外卖  仅到店自提 1   不限2
        userStatus: _data.userStatus, // 核销用户身份 0不限  1限平台新客  2 限新零售新客
        stockInfo: {
          // stockType  1｜底层券库存   2｜活动库存
          stockType: 1,
          stock: _data.stockInfo.stock || null,
          dayStock: _data.stockInfo.dayStock || null
        }, // 库存限制
        userLimit: {
          // 规则限制  限领
          // type;    1｜日限领   2｜分时段限领  每日限领传type为1， 再传里面的limit; 总限领传外面的limit
            typeLimit: { type: 1, limit: _data.userLimit.dayLimit || null, extension: null },
            limit: _data.userLimit.limit || null
        },
        // 消耗闪购值 null 为无
        consumeCost: _data.consumeCost.type === 1 ? null : _data.consumeCost.innerValue
    }
    return data
}


const serverAdapterToFront = (_data) => {
  let outerNameInfo = null
  if (_data.outerNameInfo.outerNameType === 0) {
    // 0｜自定义   1｜果蔬商超红包  2｜生鲜专享红包
    outerNameInfo = {
      type: 0,
      innerValue: _data.outerNameInfo.name
    }
  } else {
    outerNameInfo = {
      type: 1,
      innerValue: _data.outerNameInfo.outerNameType
    }
  }

  let ruleInfo = {}
  // 满减类型 1｜定额 满X元减X元    2｜区间 满X-Y元减X-Y元
  if (_data.ruleInfo.type === 1) {
    ruleInfo = {
      type: 1,
      innerValue: {
        threshold: formatNumber(_data.ruleInfo.threshold, '/', 100),
        amount: formatNumber(_data.ruleInfo.amount, '/', 100),
      }
    }
  } else if (_data.ruleInfo.type === 2) {
    ruleInfo = {
      type: 2,
      innerValue: {
        threshold: {
          min: formatNumber(_data.ruleInfo.threshold, '/', 100),
          max: formatNumber(_data.ruleInfo.maxThreshold, '/', 100),
        },
        amount: {
          min: formatNumber(_data.ruleInfo.amount, '/', 100),
          max: formatNumber(_data.ruleInfo.maxAmount, '/', 100),
        },
        discountRate: {
          min: formatNumber(_data.ruleInfo.discountRate, '*', 100),
          max: formatNumber(_data.ruleInfo.maxDiscountRate, '*', 100)
        }
      }
    }
  }

  let hongbaoValidity = {}
  if (_data.validity.duration !== null && _data.validity.duration >= 0) {
    hongbaoValidity = {
      type: 1,
      innerValue: _data.validity.duration
    }
  } else {
    hongbaoValidity = {
      type: 2,
      innerValue: {
        startTime: formatDate(_data.validity.validStartTime * 1000, 'YYYY-MM-DD'),
        endTime: formatDate(_data.validity.validEndTime * 1000, 'YYYY-MM-DD')
      }
    }
  }

  let consumeCost = {}
  if (_data.consumeCost !== null && _data.consumeCost > 0) {
    consumeCost = {
      type: 2,
      innerValue: _data.consumeCost
    }
  } else {
    consumeCost = {
      type: 1
    }
  }

  return {
    name: _data.name,
    benefitValidity: [formatDate(_data.validity.startTime * 1000), formatDate(_data.validity.endTime * 1000)],
    outerNameInfo,
    budgetId: _data.budgetId,
    // 优惠规则
    ruleInfo,
    hongbaoValidity,
    varietyInfo: {
      type: _data.varietyInfo.varietyType,
      innerValue: _data.varietyInfo.varietyType === 1 ? _data.varietyInfo.flavors : _data.varietyInfo.venueId,
    },
    scenario: _data.scenario,
    userStatus: _data.userStatus,
    stockInfo: {
      stock: _data.stockInfo.stock,
      dayStock: _data.stockInfo.dayStock
    },
    userLimit: {
      limit: _data.userLimit.limit,
      dayLimit: _data.userLimit.typeLimit.limit,
    },
    consumeCost
  }
}

export {
  frontAdapterToServer,
  serverAdapterToFront
} 
