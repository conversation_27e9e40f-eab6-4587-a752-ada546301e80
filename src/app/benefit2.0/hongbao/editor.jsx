import React, { Component, useState, useEffect } from 'react'
import { request } from '@/packages/request'
import parseUrl from '@/packages/request/parseUrl'
// import requestDataSource from '@/source/requestDataSource'
import Create from './create'
import { serverAdapterToFront } from './adapter'

// @requestDataSource({
//     action: {
//       url: `benefit2://rights/pool/list`,
//       method: 'POST',
//       mapResponse: json => {
//         return json.list[0]
//       }
//     },
//     mapStateToProps: ({ data }) => {
//       return {
//         pool: data
//       }
//     }
// })
// class Editor extends Component {
//     constructor(props) {
//         console.log(props)
//       super(props)
//     }

//     render() {
//         return (
//             <div>asda</div>
//         )
//     }
// }

const Editor = (params) => {
    const [data, setData] = useState(null)

    const getData = async (couponId) => {
        try {
            const r = await request({
                url: 'benefit2://coupon/detail',
                method: 'POST',
                mapRequest: (json, { query }) => {
                    return {
                        couponId
                    }
                  }
             }) 
             r && r.data && setData({
                 ...serverAdapterToFront(r.data),
                 couponId
             })
        } catch (error) {
            console.log(error)
        }
        
    }
    useEffect(() => {
        const search = parseUrl(params.location.search).query || {}
        search.couponId && getData(search.couponId)
    }, [])

    if (!data) {
        return null
    }

    return (
        <Create data={data} {...params} type="editor" />
    )
}

export default Editor
