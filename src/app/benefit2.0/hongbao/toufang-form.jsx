import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { Grid, Button, Message, Dialog, Input } from '@alifd/next'
import { Form as boreasForm, useFormAction, useForm } from '@ali/boreas2'

const { Row, Col } = Grid

function ToufangForm(props, parentRef) {
  const formMeta = {
    type: 'Form',
    children: [
      {
        name: 'group',
        type: 'RadioGroup',
        props: {
          label: '投放人群:',
          required: true,
          defaultValue: 1,
          dataSource: [
            {
              label: '不限',
              value: 1
            },
            {
              label: '平台新客',
              value: 6
            },
            {
              label: '新零售新客',
              value: 2
            },
            {
              label: '新零售流失老客',
              value: 3
            },
            {
              label: '新零售活跃老客',
              value: 4
            },
            {
              label: '离线人群包',
              value: 5
            }
          ]
        },
      },
      {
        name: 'city',
        type: 'RadioGroup',
        props: {
          label: '投放城市:',
          required: true,
          defaultValue: 1,
          dataSource: [
            {
              label: '不限',
              value: 1
            },
            {
              label: '仅直营城市',
              value: 2
            },
            {
              label: '仅代理城市',
              value: 3
            },
          ]
        },
      },
    ]
  }

  let {
    initFormData,
    submit,
    onSuccess
  } = useFormAction({
    requestAction: {
      action: async (_values = {}) => {
        console.log('_values', _values)
        const r = await fetch('https://os.alipayobjects.com/rmsportal/ODDwqcDFTLAguOvWEolX.json')
        return r.json()
      },
    },
    submitAction: {
      action: async (_values = {}) => {
        console.log('_values', _values)
        const r = await fetch('https://os.alipayobjects.com/rmsportal/ODDwqcDFTLAguOvWEolX.json')
        return r.json()
      },
    },
    onSuccess: () => {
      console.log('onsucess')
    },
  })

  initFormData = initFormData || {}
  const { BoreasForm, field } = boreasForm({
    config: formMeta,
    initFormData,
  })

  useImperativeHandle(parentRef, () => {
    return {
      field
    }
  })

  const clickSelfCheck = () => {
    let checkPhone = ''
    const changeIphone = (v) => {
      console.log(v)
      checkPhone = v
    }
    Dialog.alert({
      title: '自测',
      messageProps: {
        type: 'warning'
      },
      content: <div>领券手机号：<Input onChange={changeIphone} /></div>,
      autoFocus: true,
      onOk: () => {
        console.log('checkPhone', checkPhone)
        // request(
      //   {
      //     url: `benefit://me.ele.newretail.bz.commodity.api.DatasetService#deleteDatasetById`,
      //     method: 'POST'
      //   },
      //   {
      //     body: {
      //       dsId: record.dsId,
      //     }
      //   }
      // ).then(() => {
      //   Message.success('触发成功')
     
      //   reload()
      // })
      },
      onClose: () => {
       
      },
      okProps: {
        children: '确认触发'
      }
    })
  }

  return (
    <div>
      {BoreasForm}
      <Row>
        <Col span={6} style={{ textAlign: 'right' }}>
          <Button type="secondary" onClick={clickSelfCheck}>触发自测</Button>
        </Col>
      </Row>
    </div>
  )
}

export default forwardRef(ToufangForm)
