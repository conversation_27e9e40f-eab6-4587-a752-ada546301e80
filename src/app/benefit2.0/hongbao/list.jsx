import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { Link } from 'react-router-dom'
import { Grid, Button, Message, Dialog, Input, CascaderSelect, Icon } from '@alifd/next'
import { Form as boreasForm, useFormAction, useForm, FilterTable } from '@ali/boreas2'
import formatDate from '@/utils/formatDate'
import { request } from '@/packages/request'
import parseUrl from '@/packages/request/parseUrl'
import _ from 'lodash'
import formatDateToTimeStamp from '@/utils/formatDateToTimeStamp'
import { benefitStatusMap, auditStatusMap, outerNameTypeMap } from '../map'

const { useFilterTable } = FilterTable

export default function HongbaoList(props) {
  const filterConfig = {
    type: 'Filter',
    // labelTextAlign: 'left',
    // buttonAlign: 'start-line', // end-line  start-line(仅仅支持一个选项的时候) end start 默认
    // hasExpand: 'rightTop'
    children: [
      {
        name: 'name',
        type: 'Input',
        props: {
          label: '红包内部名称:',
          hasClear: true,
        }
      },
      {
        name: 'outName',
        type: 'Input',
        props: {
          label: '红包外部名称:',
          hasClear: true,
        }
      },
      {
        name: 'couponId',
        type: 'Input',
        props: {
          label: '红包活动id:',
          hasClear: true,
        }
      },
      {
        name: 'creatorName',
        type: 'Input',
        props: {
          label: '创建人:',
          hasClear: true,
        }
      },
    ],
  }

  const handleChangeCoupon = async (record, type, reload) => {
    if (+type === 2) {
      // 下线
      Dialog.show({
        title: '红包活动下线',
        content: '确定下线此红包活动吗？',
        onOk: () => changeCouponStatus(record, type, reload)
      })
    } else {
      // 重新上线
      Dialog.show({
        title: '红包活动重新上线',
        content: '确定重新上线此红包活动吗？',
        onOk: () => changeCouponStatus(record, type, reload)
      })
    }
  }

  const changeCouponStatus = async (record, type, reload) => {
    try {
      const r = await request({
        url: `benefit2://coupon/change`,
        method: 'POST',
        mapRequest: () => {
          return {
            couponId: record.couponId,
            type
          }
        }
      })
      r && Message.success(`${+type === 2 ? '下线成功' : '重新上线成功'}`)
      reload && reload()
    } catch (error) {
      console.log('error', error)
      Message.error('操作失败')
    }
  }
  
  const tableColumns = [
    {
      title: '红包内部名称',
      dataIndex: 'name',
      width: 140,
    },
    {
      title: '红包外部名称',
      dataIndex: 'outerNameInfo',
      cell: v => {
        if (+v.outerNameType === 0) {
          return v.name
        } 
        return outerNameTypeMap[v.outerNameType]
      }
    },
    {
      title: '活动ID',
      dataIndex: 'couponId',
      width: 100
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 80
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      cell: value => {
        return formatDate(value)
      }
    },
    {
      title: '活动时间',
      dataIndex: 'validity',
      cell: (v, i, record) => {
        let _start = v.startTime * 1000
        let _end = v.endTime * 1000
        return (
          <React.Fragment>
            <div>{`${formatDate(_start)} - `}</div>
            <div>{`${formatDate(_end)}`}</div>
          </React.Fragment>
        )
      },
      width: 180,
    },
    // {
    //   title: '优惠内容',
    //   dataIndex: 'activityText',
    // },
  // 权益状态（待开始、进行中、已结束）、审核状态（无需审核、审核中、审核通过、审核失败）
    {
      title: '权益状态',
      dataIndex: 'status',
      cell: v => {
        // <span className={`boreas-status boreas-status-${value}`}>
        //   {value == 1 ? '生效中' : '已下线'}
        // </span>
        
        return <div style={{ maxWidth: '10em', lineHeight: '22px' }}>{benefitStatusMap[v]}</div>
      },
      width: 100
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      cell: v => {
        return <div style={{ maxWidth: '10em', lineHeight: '22px' }}>{auditStatusMap[v]}</div>
      },
      width: 100
    },
    {
      title: '操作',
      dataIndex: 'op',
      cell: (value, index, record, { reload }) => {
        // 详情、下线、编辑
        // 审核中状态的权益无法编辑
        return (
          <div className="ali-boreas-cell">
            <a onClick={() => {
              const searchParams = parseUrl(props.location.search).query || {}
              searchParams && window.sessionStorage.setItem('benefit_list_search', JSON.stringify(searchParams))
              props.history && props.history.push({
                pathname: `/benefit/hongbao/detail`,
                search: `?couponId=${record.couponId}`
              })
            }}>
              <span className="pr">详情</span>
            </a>
            {
              record.status === 1 && (
                <a
                  onClick={() => {
                    const searchParams = parseUrl(props.location.search).query || {}
                    searchParams && window.sessionStorage.setItem('benefit_list_search', JSON.stringify(searchParams))
                    props.history && props.history.push({
                      pathname: `/benefit/hongbao/editor`,
                      search: `?couponId=${record.couponId}`
                    })
                  }}
                >
                  <span className="pr">编辑</span>
                </a>
              )
            }
            {record.status === 0 && (
              <span onClick={() => handleChangeCoupon(record, 2, reload)} className="pl">
                下线
              </span>
            )}
            {
              /**
               * {record.status === 2 && (
                  <span onClick={() => handleChangeCoupon(record, 1, reload)} className="pl">
                    重新上线
                  </span>
                )}
               * 
               */
            }
          </div>
        )
      },
    },
  ]

  const filterProps = {
    history: props.history,
    // resetType: 'reset',
    formItemLayout: {
      labelCol: { span: 8 },
      wrapperCol: { span: 14 },
    }
  }

  const tableProps = {
    // hasBorder: false,
    emptyContent: "暂无红包活动~",
  }

  const R = useFilterTable({
    filterConfig,
    filterProps,
    tableConfig: tableColumns,
    tableProps,
  })({
    action: async (_values) => {
      // /coupon/list
      console.log('_values', _values)
      const { pageSize, pageNum, ...others } = _values
      try {
        const r = await request({
          url: `benefit2://coupon/list`,
          method: 'POST',
          mapRequest: () => {
            return {
              currentPage: pageNum,
              pageSize,
              ...others
            }
          }
        })
        return {
          total: r.total,
          data: r.data
        }
      } catch (error) {
        return {
          total: 0,
          data: []
        }
      }
    },
    // mapStateToProps: (data) => {
    //   return data
    // },
  })

  return (
    <div className="right-content hongbao-list">
      <div className="bg-fff" style={{ padding: '20px' }}>
        {R.filter}
        <div style={{ marginTop: '20px' }} className="hongbao-list-table">
          <Button
            type="primary"
            className="add-btn"
            onClick={() => {
              props.history && props.history.push({
                pathname: '/benefit/hongbao/new',
              })
            }}
          >
            <Icon type="add" />创建红包
          </Button>
          {R.table}
        </div>
      </div>
    </div>
  )
}
