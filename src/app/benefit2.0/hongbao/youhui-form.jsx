import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { Input, NumberPicker, Radio, Message, Select, DatePicker } from '@alifd/next'
import { Form as boreasForm } from '@ali/boreas2'
import RadioTab from '@/components/radioTab'
import ItemGroup from '@/components/itemGroup'
import DateRangePicker from '@/components/dateRangePicker'
// import useAsyncSelect from '@/hooks/useAsyncSelect'
import WithFusion from '@/hoc/WithFusion'
import _ from 'lodash'
import { userStatusDataSource, scenarioDataSource, varietyInfoDatSource } from '../map'
// import boreasForm from '../packages/component/Form'

function YouhuaForm(props, parentRef) {
  // const [CategoryEle] = useAsyncSelect({
  //   action: 'https://os.alipayobjects.com/rmsportal/ODDwqcDFTLAguOvWEolX.json',
  //   props: {
  //     placeholder: '请选择品类红包类目',
  //     mode: 'multiple'
  //   },
  //   pipeResponse: (r) => {
    
  //     console.log(r)
  //     return [{
  //       lable: '类目1',
  //       value: '类目1'
  //     }, {
  //       lable: '类目2',
  //       value: '类目2'
  //     }]
  //   }
  // })

  const formMeta = {
    type: 'Form',
    children: [
      {
        name: 'ruleInfo',
        type: RadioTab,
        props: {
          label: '满减类型:',
          required: true,
          rules: [
            {
              required: true,
              message: '满减类型不能为空',
            },
            {
              validator: (rule, v, callback) => {
                if (v.type === 1) {
                  const threshold = _.get(v, 'innerValue.threshold', '')
                  const amount = _.get(v, 'innerValue.amount', '')
                  if (!threshold || threshold > 999 || threshold < 1) {
                    return Promise.reject('满减门槛必须在1~999范围内')
                  } else if (!amount || amount > 99 || amount < 1) {
                    return Promise.reject('满减数额必须在1~99范围内')
                  } else if (threshold < amount) {
                    return Promise.reject('满减门槛必须大于满减金额')
                  }
                } else {
                  const { threshold, amount, discountRate } = _.get(v, 'innerValue', {})
                  if (!threshold || !threshold.min || !threshold.max || threshold.min > threshold.max) {
                    return Promise.reject('门槛区间填写有误')
                  } else if (!amount || !amount.min || !amount.max || amount.min > amount.max) {
                    return Promise.reject('满减区间填写有误')
                  } else if (!discountRate || !discountRate.min || !discountRate.max || discountRate.min > discountRate.max) {
                    return Promise.reject('优惠比例填写有误')
                  }
                }
              }
            }
          ],
          dataSource: [
            {
              label: '定额',
              value: 1,
              children: (
                <ItemGroup
                  inline
                  dataSource={[
                    {
                      label: '满',
                      name: 'threshold',
                      type: NumberPicker,
                      precision: 1,
                      min: 1,
                      max: 999,
                      addonTextAfter: '元'
                    },
                    {
                      label: '减',
                      name: 'amount',
                      type: NumberPicker,
                      precision: 1,
                      min: 1,
                      max: 99,
                      addonTextAfter: '元'
                    },
                ]}
                />
              )
            },
            {
              label: '区间',
              value: 2,
              children: (
                <WithFusion
                  type={ItemGroup}
                  dataSource={[
                    {
                      label: '门槛区间:',
                      name: 'threshold',
                      type: ItemGroup,
                      inline: true,
                      dataSource: [{
                        label: '从',
                        name: 'min',
                        min: 1,
                        max: 999,
                        precision: 1,
                        type: NumberPicker,
                        addonTextAfter: '元'
                      }, {
                        label: '到',
                        name: 'max',
                        min: 1,
                        max: 999,
                        precision: 1,
                        type: NumberPicker,
                        addonTextAfter: '元'
                      }]
                    },
                    {
                      label: '满减区间:',
                      name: 'amount',
                      type: ItemGroup,
                      inline: true,
                      dataSource: [{
                        label: '从',
                        name: 'min',
                        min: 1,
                        max: 99,
                        precision: 1,
                        type: NumberPicker,
                        addonTextAfter: '元'
                      }, {
                        label: '到',
                        name: 'max',
                        min: 1,
                        max: 99,
                        precision: 1,
                        type: NumberPicker,
                        addonTextAfter: '元'
                      }]
                    },
                    {
                      label: '优惠比例:',
                      name: 'discountRate',
                      type: ItemGroup,
                      inline: true,
                      dataSource: [{
                        label: '从',
                        name: 'min',
                        min: 0,
                        max: 100,
                        type: NumberPicker,
                        addonTextAfter: '%'
                      }, {
                        label: '到',
                        name: 'max',
                        min: 0,
                        max: 100,
                        type: NumberPicker,
                        addonTextAfter: '%'
                      }]
                    }
                  ]}
                />
              )
            }
          ]
        },
      },
      {
        name: 'hongbaoValidity',
        type: RadioTab,
        props: {
          label: '红包有效期:',
          required: true,
          rules: [
            {
              required: true,
              message: '红包有效期不能为空',
            },
            {
              validator: (rule, v, callback) => {
                console.log(v.innerValue)
                if (v.type === 1 && !(v.innerValue >= 0)) {
                  return Promise.reject('红包有效期不能为空')
                } else if (v.type == 2 && !v.innerValue) {
                  return Promise.reject('红包有效期范围不能为空')
                } else if (v.type == 2 && v.innerValue && !v.innerValue.startTime) {
                  return Promise.reject('红包有效期的开始时间不能为空')
                } else if (v.type == 2 && v.innerValue && !v.innerValue.endTime) {
                  return Promise.reject('红包有效期的结束时间不能为空')
                }
              }
            }
          ],
          dataSource: [
            {
              label: '自领取日起',
              value: 1,
              children: <WithFusion type={NumberPicker} className="expire-input" min={0} max={120} addonTextAfter="天" tip="（填写固定天数，范围0~120，0代表领取后当天24点失效）" />
            },
            {
              label: '指定时间范围',
              value: 2,
              children: <DateRangePicker range="120" />
            }
          ]
        },
      },
      {
        name: 'varietyInfo',
        type: RadioTab,
        props: {
          label: '红包类型:',
          hasClear: true,
          required: true,
          rules: [
            {
              required: true,
              message: '红包类型不能为空',
            },
            {
              validator: (rule, v, callback) => {
                if (!v.innerValue) {
                  return Promise.reject('红包类型不能为空')
                }
              }
            }
          ],
          dataSource: [
            {
              label: '品类红包',
              value: 1,
              children: <Select placeholder='请选择品类红包类目' mode="tag" dataSource={varietyInfoDatSource} />
            },
            {
              label: '会场券',
              value: 2,
              children: <Input placeholder="请填写会场券ID" />
            }
          ]
        },
      },
      {
        name: 'scenario',
        type: 'RadioGroup',
        props: {
          label: '业务场景:',
          required: true,
          // defaultValue: 1,
          dataSource: scenarioDataSource
        },
      },
      {
        name: 'userStatus',
        type: 'RadioGroup',
        props: {
          label: '核销时用户身份限制:',
          required: true,
          // defaultValue: 0,
          dataSource: userStatusDataSource
        },
      },
      {
        name: 'stockInfo',
        type: ItemGroup,
        props: {
          label: '库存限制:',
          required: true,
          rules: [
            {
              required: true,
              message: '库存限制不能为空',
            },
            {
              validator: (rule, v, callback) => {
                if (!(v.stock || v.dayStock)) {
                  return Promise.reject('库存限制不能为空')
                }
              }
            }
          ],
          inline: true,
          dataSource: [
            {
              label: <span style={{ width: '5em', display: 'inline-block' }}>总库存</span>,
              name: 'stock',
              type: NumberPicker,
              min: 1,
              max: 9999999,
            }, {
              label: <span style={{ width: '6em', display: 'inline-block' }}>每日库存</span>,
              name: 'dayStock',
              type: NumberPicker,
              min: 1,
              max: 9999999,
            }
          ]
        },
      },
      {
        name: 'userLimit',
        type: ItemGroup,
        props: {
          label: '规则限制:',
          required: true,
          rules: [
            {
              required: true,
              message: '规则限制不能为空',
            },
            {
              validator: (rule, v, callback) => {
                if (!(v.limit || v.dayLimit)) {
                  return Promise.reject('规则限制不能为空')
                }
              }
            }
          ],
          inline: true,
          dataSource: [
            {
              label: '每人总限领',
              name: 'limit',
              type: NumberPicker,
              min: 1,
              max: 9999,
            }, {
              label: '每人每日限领',
              name: 'dayLimit',
              type: NumberPicker,
              min: 1,
              max: 99,
            }
          ]
        },
      },
      {
        name: 'consumeCost',
        type: RadioTab,
        props: {
          label: '消耗成本:',
          required: true,
          rules: [
            {
              required: true,
              message: '消耗成本不能为空',
            },
            {
              validator: (rule, v, callback) => {
                if (v.type === 2 && !v.innerValue) {
                  return Promise.reject('消耗闪购值不能为空')
                }
              }
            }
          ],
          dataSource: [
            {
              label: '不消耗',
              value: 1,
            },
            {
              label: '消耗',
              value: 2,
              children: <WithFusion type={NumberPicker} min={1} max={999999} addonTextBefore='闪购值：' addonTextAfter="个" />
            },
          ]
        },
      },
    ]
  }

  const initFormData = {
    ruleInfo: {
      type: 1
    },
    hongbaoValidity: {
      type: 1
    },
    varietyInfo: {
      type: 1
    },
    consumeCost: {
      type: 1
    },
    userStatus: 0,
    scenario: 1,
    ...props.data
  }
  const { BoreasForm, field } = boreasForm({
    config: formMeta,
    initFormData
  })

  useImperativeHandle(parentRef, () => {
    return {
      field
    }
  })

  return BoreasForm
}

export default forwardRef(YouhuaForm)
