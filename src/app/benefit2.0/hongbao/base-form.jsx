import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { Input, Button, Radio, Message, Select } from '@alifd/next'
import { Form as boreasForm, useFormAction } from '@ali/boreas2'
import RadioTab from '@/components/radioTab'
import formatDate from '@/utils/formatDate'
// import useAsyncSelect from '@/hooks/useAsyncSelect'
import { request } from '@/packages/request'
import _ from 'lodash'
// import boreasForm from '../packages/component/Form'

const baseFormMeta = {
  type: 'Form',
  children: [
    {
      name: 'name',
      type: 'Input',
      props: {
        label: '权益内部名称:',
        placeholder: '用于内部活动识别，如："双12红包新人券"',
        hasClear: true,
        required: true,
        rules: [
          {
            required: true,
            message: '权益内部名称不能为空',
          },
          {
            maxLength: 20,
            message: '最多不超过20个字',
          },
        ],
      }
    },
    {
      name: 'benefitValidity',
      type: 'RangePicker',
      props: {
        label: '权益生效日期:',
        hasClear: true,
        required: true,
        showTime: true,
        // disabledDate: (date, view) => {
        //   // console.log(date, view)
        // },
        rules: [
          {
            required: true,
            message: '权益生效日期不能为空',
          },
          {
            validator: (rule, value, callback, options, jj) => {
              const [_start, _end] = value
              if (!_start) {
                return Promise.reject('权益开始日期不能为空')
              } else if (!_end) {
                return Promise.reject('权益结束日期不能为空')
              } else if (Date.parse(_start) >= Date.parse(_end)) {
                return Promise.reject('权益结束日期必须大于开始日期')
              }
            }
          }
        ],
      }
    },
    {
      name: 'outerNameInfo',
      type: RadioTab,
      props: {
        label: '权益外部名称:',
        hasClear: true,
        required: true,
        rules: [
          {
            required: true,
            message: '权益外部名称不能为空',
          },
          {
            validator: (rule, value, callback, options, jj) => {
              if (!value.innerValue) {
                return Promise.reject('红包名称不能为空')
              }
            }
          }
        ],
        dataSource: [
          {
            label: '通用红包名称',
            value: 1,
            children: <Select dataSource={[
              {
                label: '果蔬商超红包',
                value: 1
              },
              {
                label: '生鲜专享红包',
                value: 2
              }
            ]} />
          },
          {
            label: '自定义名称',
            value: 0,
            children: <Input placeholder="请输入自定义名称" maxLength={15} />
          }
        ]
      },
    },
    {
      name: 'budgetId',
      type: 'Select',
      props: {
        label: '预算信息:',
        placeholder: '请选择预算',
        hasClear: true,
        required: true,
        rules: [
          {
            required: true,
            message: '预算信息不能为空',
          },
        ],
        dataSource: {
          action: async (v) => {
            // /coupon/budget/list
           try {
            const r = await request({
              url: `benefit2://coupon/budget/list`,
              method: 'POST'
            })
            return r.data
           } catch (error) {
             return []
           }

          },
          mapStateToProps: (data) => {
            return _.flatMap(data, item => ({
              value: item.budgetId,
              label: item.budgetId,
            })) || []
          },
        }
      }
    },
  ]
}

function BaseForm(props, parentRef) {
  // const [HongbaoEle] = useAsyncSelect({
  //   action: 'https://os.alipayobjects.com/rmsportal/ODDwqcDFTLAguOvWEolX.json',
  //   props: {
  //     placeholder: '请选择通用红包名称'
  //   },
  //   pipeResponse: (r) => {
  //     console.log(r)
  //     return [{
  //       lable: 'name1',
  //       value: 'name1'
  //     }]
  //   }
  // })

  // let {
  //   initFormData,
  //   submit,
  //   onSuccess
  // } = useFormAction({
  //   // requestAction: {
  //   //   action: async (_values = {}) => {
  //   //     console.log('_values', _values)
  //   //     const r = await fetch('https://os.alipayobjects.com/rmsportal/ODDwqcDFTLAguOvWEolX.json')
  //   //     return r.json()
  //   //   },
  //   //   mapStateToProps: (_data) => {
  //   //     return {
  //   //       activityName: {
  //   //         type: 2,
  //   //         innerValue: '12'
  //   //       }
  //   //     }
  //   //   }
  //   // },
  //   submitAction: {
  //     action: async (_values = {}) => {
  //       console.log('_values', _values)
  //       const r = await request({
  //         url: `benefit2://coupon/add`,
  //         method: 'POST',
  //         mapRequest: (json, { query }) => {
  //           console.log('json', json, query)
  //           const { pageNum, pageSize, ...o } = json
  //           return {
  //             query: +query.dsId,
  //             size: _data.pageSize || 10,
  //             page: _data.pageNum || 1
  //           }
  //         }
  //       })
  //     },
  //   },
  //   onSuccess: () => {
  //     console.log('onsucess')
  //   },
  // })

  const initFormData = {
    outerNameInfo: {
      type: 1,
      innerValue: ''
    },
    ...props.data,
    ...props.formData
  }

  const { BoreasForm, field } = boreasForm({
    config: baseFormMeta,
    initFormData,
  })

  useImperativeHandle(parentRef, () => {
    return {
      field
    }
  })

  return BoreasForm
}

export default forwardRef(BaseForm)
