import React, { useState, useEffect, useRef } from 'react'
import { Step, Button, Message } from '@alifd/next'
import moment from 'moment'
import qs from 'qs'
import BaseForm from './base-form'
import YouhuiForm from './youhui-form'
import ToufangForm from './toufang-form'
import { request } from '@/packages/request'
import { frontAdapterToServer } from './adapter'

moment.locale('zh-cn')

export default function HongbaoForm(props) {
  const steps = ['基本信息', '优惠规则'] // , '发放规则'
  const FormList = [BaseForm, YouhuiForm, ToufangForm]
  const [currentStep, setCurrentStep] = useState(0)

  const [formData, setFormData] = useState(null)

  let parentRef = useRef(null)

  const handleSubmit = async (field) => {
    const formDataValues = field.fields
    const { firstErrors } = await field.validatePromise()
    const errors = firstErrors || field.firstErrors
    if (errors && errors[0]) {
      Message.error(errors[0] && errors[0].message)
      return
    }
    return formDataValues
  }

  const clickPrev = () => {
    setCurrentStep(currentStep - 1)
  }

  const clickNext = async () => {
    const { field } = parentRef.current
    const r = await handleSubmit(field)
    console.log('clickNext', r, 'formData', formData)
    if (r && currentStep < steps.length - 1) {
      const data = {
        ...formData,
        ...r
      }
      setFormData(data)
      setCurrentStep(currentStep + 1)
    } else if (r && currentStep === steps.length - 1) {
      const { name, budgetId, benefitValidity, outerNameInfo } = formData
      const data = {
        ...r,
        name,
        budgetId,
        benefitValidity,
        outerNameInfo
      }
      console.log('data', data)
      submit(data)
    }
  }

  const submit = async (_data) => {
    const data = frontAdapterToServer(_data)
    let url = `benefit2://coupon/add`
    if (props.type === 'editor') {
      url = 'benefit2://coupon/update'
      data.couponId = props.data.couponId
    }
    // formData
    try {
      const r = await request({
        url,
        method: 'POST',
        mapRequest: (json, { query }) => {
          return {
            ...data
          }
        }
      })

      if (props.type === 'editor') {
        if (r && r.success) {
          Message.success('活动更新成功')
          setTimeout(() => {
            let sessionSearch = JSON.parse(window.sessionStorage.getItem('benefit_list_search') || '{}')
            props.history && props.history.push({
              pathname: '/benefit/hongbao',
              search: `?${qs.stringify(sessionSearch)}`
            })
          }, 300)
        } else {
          Message.error((r && r.errMessage) || '活动更新失败')
        }
      } else if (props.type !== 'editor') {
        if (r && r.data && r.data.couponId) {
          Message.success('活动创建成功')
          setTimeout(() => {
            props.history && props.history.push('/benefit/hongbao')
          }, 300)
        } else {
          Message.error((r && r.errMessage) || '活动创建失败')
        }
      }
      return r
    } catch (error) {
      console.log('error', error)
      Message.error('提交失败')
    }
  }

  const FormEle = FormList[currentStep]

  return (
    <div className="hongbao-form">
      <Step current={currentStep} shape="circle" labelPlacement="hoz">
          {
            steps.map(item => <Step.Item key={item} title={item} />)
          }
      </Step>
      <div className="hongbao-form-content">
        <FormEle ref={parentRef} data={props.data} formData={formData} />
      </div>
      <div className="hongbao-opbtn">
        {
          currentStep !== 0 && <Button onClick={clickPrev}>上一步</Button>
        }
        {
          <Button type="primary" onClick={clickNext}>{`${currentStep !== 1 ? '下一步' : '提交'}`}</Button>
        }
      </div>
    </div>
  )
}
