import React, { useState, useEffect } from 'react'
import { Button, Icon, Message, Dialog } from '@alifd/next'
import { ziyuanweiCodeMap, ziyuanweiDataSource, dingtouDataSource, qudaoDataSource } from './config'

const Item = (data) => {
    const { title, entry } = data
    const handleClick = (item) => {
        if (item.children) {
            const dialog = Dialog.show({
                title: `${item.text}投放渠道选择`,
                content: (
                    <div className="app-item-dailog">
                        {
                            item.children.map(child => {
                                return (
                                    <Button className="button-item" key={child.value} onClick={() => {
                                        window.open(`${item.link}?channelCode=${child.value}`, '_blank')
                                    }}>{child.label}</Button>
                                )
                            })
                        }
                    </div>
                ),
                footer: (
                    <Button type="primary" onClick={() => dialog.hide()}>
                        确认
                    </Button>
                )
            })
        } else if (!item.children && item.link) {
            window.open(item.link) 
       } else {
            Message.notice('筹建中，敬请期待......')
       }
    }
    return (
        <div className="app-item">
            <div className="app-item-title">{title}</div>
            <div className="app-item-entry">
                {
                    entry.map(item => {
                        return (
                            <div key={item.text} onClick={() => handleClick(item)} className={`app-item-entry-child ${item.icon ? 'hasicon' : ''}`}>
                                {
                                    item.icon && <img src={item.icon} className="card-icon" />
                                }
                                <div className="card-item">
                                   <div className="card-title">{item.text}</div>
                                   {
                                       item.subText && <div className="card-sub-title">{item.subText}</div>
                                   }
                                </div>
                                {
                                    item.tag && <div className="card-tag">{item.tag}</div>
                                }
                                <span className="card-btn">立即创建</span>
                            </div>
                        )
                    })
                }
            </div>
        </div>
    )
}

export default function Index() {
    // TODO：嵌入 iframe 无法正确写入 cookie，后面再研究一下
    // const origin = 'https://boreas.ele.me'
    // const shellPath = 'index.html#/shell/'
    // const { pathname } = window.location
    // const originEncodeWithPath = window.encodeURIComponent(origin + pathname.replace('benefit', 'benefit-kunlun'))
    // const originWithPath = origin + pathname.replace('benefit', 'benefit-kunlun')
    const originWithPath = 'https://boreas.ele.me/page/benefit-kunlun/index.html'

    const list = [
        {
            title: '场景选择',
            entry: [
                {
                    text: '资源位定投',
                    subText: '红包雨、购后返等流量渠道',
                    tag: '引流促活',
                    icon: 'https://img.alicdn.com/tfs/TB1Lw6fDAY2gK0jSZFgXXc5OFXa-200-200.png',
                    size: 'large',
                    link: `${originWithPath}#/plan/new`,
                    // 频道红包雨、频道闪购值、会场组件-券墙、主站互动城、主站组队PK、会场组件-一键领红包、会场组件-准点抢红包、真实惠频道、购后返渠道、时令好货
                    children: ziyuanweiDataSource
                },
                {
                    text: '离线直塞',
                    subText: '离线发券&短信push触达',
                    tag: '人群营销',
                    icon: 'https://img.alicdn.com/tfs/TB169_fDy_1gK0jSZFqXXcpaXXa-240-200.png',
                    size: 'large',
                    link: `${originWithPath}#/plan/new?channelCode=elem_zhisai`,
                },
                {
                    text: '定投玩法',
                    subText: '开宝箱、抽奖等玩法',
                    tag: '互动玩法',
                    icon: 'https://img.alicdn.com/tfs/TB19SDhDxD1gK0jSZFyXXciOVXa-200-200.png',
                    size: 'large',
                    link: `${originWithPath}#/plan/new`,
                    // 新客三重礼、开宝箱、老带新、一键抽奖
                    children: dingtouDataSource
                },
                {
                    text: '渠道定投',
                    subText: '海王等外部发奖系统投放',
                    tag: '渠道外投',
                    icon: 'https://img.alicdn.com/tfs/TB1OJDcDEz1gK0jSZLeXXb9kVXa-200-200.png',
                    size: 'large',
                    link: `${originWithPath}#/plan/new`,
                    // 海王、口碑抽发奖
                    children: qudaoDataSource
                }
            ]
        },
        {
            title: '权益管理',
            entry: [
                {
                    text: '权益池管理',
                    link: `${originWithPath}#/benefit`
                },
                {
                    text: '全店满减券',
                    link: 'https://zs.kunlun.alibaba-inc.com/bd/activityType'
                },
                {
                    text: '商品满减券',
                    link: 'https://zs.kunlun.alibaba-inc.com/bd/activityType'
                },
                {
                    text: '商品运费券',
                }
            ]
        },
        {
            title: '规则引擎',
            entry: [
                {
                    text: '策略中心',
                },
                {
                    text: '过滤引擎',
                },
                {
                    text: '渠道管理',
                }
            ]
        }
    ]
    return (
        <div className="app-list">
            {
                list.map(item => {
                    return (
                        <Item {...item} key={item.title} />
                    )
                })
            }
        </div>
    )
}
