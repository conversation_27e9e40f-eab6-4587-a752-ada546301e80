import React from 'react'
import Routers from '@/packages/routers'
import { Router } from '@ali/boreas2'
import Index from './index'
// 权益——红包权益
import BenefitHongbaoCreate from './hongbao/create'
import BenefitHongbaoEditor from './hongbao/editor'
import BenefitHongbaoList from './hongbao/list'
import BenefitHongbaoDetail from './hongbao/detail'

// iframe 容器
import Shell from './shell/index'

import './index.scss'
import '@ali/boreas2/lib/index.css'

/*
  router 的定义需按照多级目录 path 的包含规则
  如：
  一级目录: /marketing
  二级目录：/marketing/coupon
  三级目录：/marketing/coupon/complete/aaa
*/
const routerList = [
  {
    menuUrl: '/',
    menuTitle: '首页',
    icon: 'leftnav_shuju',
    component: Index,
  },
  {
    menuUrl: '/shell/:targetURL',
    menuTitle: '营销权益',
    component: Shell,
  },
  {
    menuUrl: '/benefit',
    menuTitle: '权益平台',
    icon: 'leftnav_shuju',
    subMenus: [
      {
        menuUrl: '/benefit/hongbao',
        menuTitle: '红包列表',
        component: BenefitHongbaoList,
      },
      {
        menuUrl: '/benefit/hongbao/new',
        menuTitle: '创建红包',
        component: BenefitHongbaoCreate,
      },
      {
        menuUrl: '/benefit/hongbao/editor',
        menuTitle: '编辑红包',
        component: BenefitHongbaoEditor
      },
      {
        menuUrl: '/benefit/hongbao/detail',
        menuTitle: '红包详情',
        component: BenefitHongbaoDetail,
      },
    ],
  },
]

const breadcrumbMap = {
  '/benefit': ['权益平台'],
  '/benefit/hongbao': ['权益平台', '红包列表'],
  '/benefit/hongbao/new': ['权益平台', '红包列表', '创建红包'],
  '/benefit/hongbao/editor': ['权益平台', '红包列表', '编辑红包'],
  '/benefit/hongbao/detail': ['权益平台', '红包列表', '红包详情'],
}

function App() {
  return (
    <div style={{ flex: 1, overflowX: 'scroll' }}>
      <Router routerList={routerList} redirectUrl="/" breadcrumbMap={breadcrumbMap} />
    </div>
  )
}

export default App
