import _ from 'lodash'

function mapToSource(_map) {
    if (!_map) {
        return []
    }
    return _.keys(_map).map(item => {
        return {
            label: _map[item],
            value: +item
        }
    })
}

const userStatusMap = {
    // 核销用户身份
    0: '不限',
    1: '限平台新客',
    2: '限新零售新客'
}
const userStatusDataSource = mapToSource(userStatusMap)

const scenarioMap = {
    0: '不限',
    1: '仅外卖',
    2: '仅到店自提'
}
const scenarioDataSource = mapToSource(scenarioMap)

const benefitStatusMap = {
    // 权益状态
    0: '进行中',
    1: '未开始',
    2: '已结束',
    3: '超出预算'
}

const auditStatusMap = {
    // 审核状态
    0: '无需审核',
    1: '审核通过',
    2: '审核失败',
    3: '审核中止',
    10: '审核中'
}

const outerNameTypeMap = {
    // 外部名称类型   0｜自定义   1｜果蔬商超红包  2｜生鲜专享红包
    0: '自定义',
    1: '果蔬商超红包',
    2: '生鲜专享红包'
}

const varietyInfoMap = {
    1426: '水果',
    1434: '厨房生鲜',
    252: '商店超市',
    275: '鲜花绿植',
    276: '医药健康'
}

const varietyInfoDatSource = mapToSource(varietyInfoMap)


export {
    userStatusMap,
    userStatusDataSource,
    scenarioMap,
    scenarioDataSource,
    benefitStatusMap,
    auditStatusMap,
    outerNameTypeMap,
    varietyInfoMap,
    varietyInfoDatSource,
}
