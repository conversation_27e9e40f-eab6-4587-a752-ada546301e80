import React from 'react'
import { Select, Input, Radio, NumberPicker, Checkbox, DatePicker } from '@alifd/next';
import CoverUploader from '@/components/coupon/CoverUploader'
import HintTextArea from '@/components/coupon/HintTextArea'
import CustomInput from '@/components/coupon/CustomInput'
import moment from 'moment'
import WingSelect from './WingSelect'
import Cardform from './Cardform'

const ITEM_MAP = new Map()
const RadioGroup = Radio.Group;
const { RangePicker } = DatePicker
moment.locale('zh-cn');

ITEM_MAP
  .set('string', (item) => item.value)
  .set('html', (item) => <div dangerouslySetInnerHTML={{ __html: item.value }} />)
  .set('select', (item) => <Select {...item} />)
  .set('input', (item) => <Input {...item} />)
  .set('customInput', (item) => <CustomInput {...item} />)
  .set('textArea', (item) => <Input.TextArea {...item} />)
  .set('hintTextArea', (item) => <HintTextArea {...item} style={{ width: '100%' }} />)
  .set('numberPicker', (item) => <NumberPicker {...item} />)
  .set('radioGroup', (item) => <RadioGroup {...item} style={{ width: 550 }} />)
  .set('checkbox', ({ text, ...item }) => <Checkbox {...item}>{text}</Checkbox>)
  .set('coverUploader', CoverUploader)
  .set('imageUploader', (item) => {
    const { default: ImageUploader } = require('@/components/coupon/ImageUploader')
    return <ImageUploader {...item} />
  })
  .set('yradioGroup', (item) => {
    const { default: CardradioGroup } = require('./CardradioGroup')
    return <CardradioGroup {...item} />
  })
  .set('yinsetInput', (item) => {
    const { default: CardinsetInput } = require('./CardinsetInput')
    // return <CardinsetInput {...item} />
    return <CardinsetInput dataSource={item} />
  })
  .set('RangePicker', item => <RangePicker {...item} />)
  .set('wings', props => <WingSelect {...props} />)
  .set('CardForm', (item) => {
    return <Cardform {...item} />
  })

export default ITEM_MAP
