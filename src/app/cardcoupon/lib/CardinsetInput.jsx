import React from 'react'
import { Grid, Select } from '@alifd/next';
import Cardform from './Cardform'

const { Row, Col } = Grid

function CardinsetInput({ dataSource }) {
  const {
    starthtml: { html: starthtml, ...startGrid },
    endhtml: { html: endhtml, ...endGrid },
    inputSpan = 6,
    ...item } = dataSource
  return (
    <Row align="baseline" justify="start">
      <Col {...startGrid} dangerouslySetInnerHTML={{ __html: starthtml }} />
      <Col span={inputSpan}>
        <Cardform fields={[{ type: 'select', ...item }]} />
      </Col>
      <Col {...endGrid} style={{ marginLeft: '-10px' }} dangerouslySetInnerHTML={{ __html: endhtml }} />
    </Row>
  )
}
/* 
const CardinsetInput = (onChange, value) => {
  return <Select value={value} onChange={onChange} />
}
 */
export default CardinsetInput
