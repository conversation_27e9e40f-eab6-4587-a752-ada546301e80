/**
 * 卡券组合
 */
import React from 'react'
import { Form } from '@alifd/next';
import Cardlayout from './Cardlayout'
import Cardform from './Cardform'
import { fieldContext } from './CardrenderForm'

const FormItem = Form.Item;

class CardradioGroup extends React.Component {

  static contextType = fieldContext;

  state = {
    dataSource: [],
    radioVal: this.props.value || this.props.defaultValue
  }

  componentDidMount() {
    this.changeVal(this.analysisData(this.props))
  }

  changeVal = (obj) => {
    this.setState(({ radioVal }) => {
      return obj
    })
  }

  analysisData = (data) => {
    const {
      commonData: totaldata,
      dataSource,
      condition,
      value: oldVal,
      defaultValue,
      onChange,
      max,
      ...params
    } = data
    const conditiondata = {}
    const radioData = dataSource && dataSource.map(({
      value = oldVal || defaultValue,
      label,
      condition,
      disabled }) => {
      conditiondata[value] = condition
      return {
        value,
        label,
        disabled
      }
    })

    return {
      dataSource: [
        Object.assign({}, params, {
          type: data.type || 'radioGroup',
          name: data.name,
          defaultValue,
          value: data.value,
          dataSource: radioData,
          max,
          placeholder: data.placeholder,
          onChange: (v) => {
            typeof onChange === 'function' && onChange(v)
            this.setState({
              radioVal: v
            })
          },
        })
      ],
      totaldata,
      conditiondata,
      condition,
    }
  }

  onClick = () => {
    console.log(this.field.getValue('name'));
  }

  handleVal(name, value) {
    this.setState({
      [name]: value
    });
  }

  render() {
    const { shrink, extra } = this.props
    const radioVal = this.context.getValue(this.props.name)
    const { dataSource = [], conditiondata, condition } = this.analysisData(this.props)
    const totaldata = this.props.commonData
    const data = typeof condition === 'function' ? condition(radioVal) : conditiondata[radioVal]
    const isRetract = typeof condition === 'function'
    return (
      dataSource.length ? <FormItem labelTextAlign="right">
        <Cardlayout retract={isRetract} shrink={shrink}>
          <Cardform fields={dataSource} />
          {
            data && data.map((item, index) => {
              return typeof item === 'string' ? <p key={radioVal + index}>{item}</p> : <Cardform fields={item} inline key={radioVal + index} />
            })
          }
        </Cardlayout>
        {totaldata && (
          <Cardlayout retract shrink={shrink}>
            {
              totaldata && totaldata.map((item, index) => {
                return typeof item === "string" ? (
                  <p
                    key={"total" + index}
                    style={{ color: "#999999", marginTop: "-16px" }}
                  >
                    {item}
                  </p>
                ) : (
                  <Cardform fields={item} inline key={"total" + index} />
                );
              })
            }
          </Cardlayout>
        )}
        { extra }
      </FormItem> : null
    )
  }
}

export default CardradioGroup
