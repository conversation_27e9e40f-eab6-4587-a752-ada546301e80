import { Progress, Icon } from '@alifd/next'
import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'

class UploadLoading extends PureComponent {
  static propTypes = {
    upStatus: PropTypes.string,
  }

  static defaultProps = {
    upStatus: 'close',
  }

  state = {
    progress: 0,
    upState: 'normal',
  }

  componentDidMount = () => {
    this.fakeProgress()
  }

  // getDerivedStateFromProps
  componentDidUpdate = (prevProps, prevState, snapshot) => {
    if (this.props.upStatus !== prevProps.upStatus) {
      if (this.props.upStatus === 'uploading') {
        this.fakeProgress('start')
      } else {
        this.fakeProgress()
      }
    }
  }

  // upStatus=success|error|uploading|close
  fakeProgress = (arg) => {
    const { progress } = this.state
    const { upStatus } = this.props

    if (arg === 'start') {
      this.setState({ progress: 0, upState: 'normal' })
    }
    if (progress < 90 && upStatus === 'uploading') {
      this.timer = setTimeout(() => {
        this.setState(state => ({ progress: state.progress + 5 }))
        this.fakeProgress()
      }, 100)
    } else if (upStatus === 'success') {
      setTimeout(() => {
        this.setState({ progress: 100, upState: 'success' })
      }, 100)
    } else {
      this.setState({ upState: 'error' })
      clearTimeout(this.timer)
    }
  }

  static timer = 0

  render() {
    const { upStatus } = this.props
    let upIcon = ''
    if (upStatus === 'uploading') {
      upIcon = <span>上传中</span>
    } else if (upStatus === 'success') {
      upIcon = <span>上传成功</span>
    } else if (upStatus === 'error') {
      upIcon = <span>上传失败</span>
    }
    return (
      <div>
        {upStatus !== 'close' && (
          <Progress
            percent={this.state.progress}
            state={this.state.upState}
            textRender={() => <span>{upIcon}</span>}
          />
        )}
      </div>
    )
  }
}

export default UploadLoading
