/* eslint-disable */
import React, { PureComponent, useEffect, useState } from 'react'
import {
  Button,
  Radio,
  Transfer,
  Tab,
  Upload,
  Icon,
  Message,
  Dialog,
} from '@alifd/next'
import './index.less'
import UploadLoading from './UploadLoading'
import useUploadStatus from '@/hooks/useUploadStatus'

export default props => {
  const [{ status, data }, { req, cancel, reset }] = useUploadStatus(
    props.uploadAction,
  )
  const reqAndAbort = (...arg) => {
    req(...arg)
    return {
      abort() {
      },
    }
  }
  const [seed, setSeed] = useState(0)

  useEffect(() => {
    if (status === 'success') {
      props.onChange && props.onChange(data.data.key)
      Message.show('上传成功')
    }
  }, [status])

  const beforeUpload = file => {
    if (file.size > 1024 * 1024 * 10) {
      Message.warning('文件大小超过10M，请分多次上传')
      setSeed(x => x + 1)
      return false
    }

    if (!file.name.match(/(csv|xlsx|xls)$/)) {
      Message.warning('请上传csv,xlsx或xls格式的文件')
      setSeed(x => x + 1)
      return false
    }
    return true
  }

  let uploadTit = null
  if (status === 'loading') {
    uploadTit = (
      <p className="next-upload-drag-text">
        <Icon type="loading" />
        文件上传中{' '}
        <Button
          type="primary"
          text
          onClick={() => {
            cancel()
            setSeed(x => x + 1)
          }}
        >
          取消
        </Button>
      </p>
    )
  } else if (status === 'success') {
    uploadTit = (
      <p className="next-upload-drag-text" style={{ color: 'green' }}>
        上传成功
      </p>
    )
  } else if (status === 'error') {
    uploadTit = (
      <p className="next-upload-drag-text" style={{ color: 'rgb(216,93,85)' }}>
        上传失败，请重新上传
      </p>
    )
  } else {
    uploadTit = (
      <p className="next-upload-drag-text">点击或将文件拖拽到这里上传</p>
    )
  }

  let upStatus = status
  if (status === 'init') {
    upStatus = 'close'
  }

  return (
    <div className="upload upload-goods">
      <section className={'upload-section ' + status}>
        <h3 className="upload-describe">
          请先下载
          <Button
            text
            onClick={props.downloadAction}
            className="upload-download"
            style={{ color: '#FF7C4D' }}
            download="demo"
            target="_blank"
          >
            {props.downloadText}
          </Button>
          ，并按照模板内要求完成填写
        </h3>
        <Upload
          key={seed}
          listType="text"
          limit={1}
          dragable
          request={reqAndAbort}
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
          className={'upload-input'}
          beforeUpload={beforeUpload}
          onCancel={() => {
            return new Promise((resolve, reject) => {
              if (status === 'success') {
                const dialog = Dialog.show({
                  title: '提示',
                  content: '是否删除已上传文件',
                  footer: (
                    <React.Fragment>
                      <Button
                        type="primary"
                        onClick={() => {
                          dialog.hide()
                          props.onChange && props.onChange(null)
                          cancel && cancel()
                          resolve(true)
                          // setSeed(x => x + 1)
                        }}
                      >
                        确认
                      </Button>
                      <Button
                        onClick={() => {
                          dialog.hide()
                          reject(false)
                        }}
                        style={{ marginLeft: '10px' }}
                      >
                        取消
                      </Button>
                    </React.Fragment>
                  ),
                })
              } else {
                props.onChange && props.onChange(null)
                cancel && cancel()
                // setSeed(x => x + 1)
                resolve(true)
              }
            })
          }}
          // onRemove={() => {
          //   props.onChange && props.onChange(null)
          //   cancel && cancel()
          //   setSeed(x => x + 1)
          // }}
          onChange={arg => {
            if (arg.length === 0) {
              reset('init')
            }
          }}
        >
          <div className="next-upload-drag">
            <p className="next-upload-drag-icon">
              <Icon type="upload" />
            </p>
            {uploadTit}
            <p
              className="next-upload-drag-hint no-margin"
              style={{ marginTop: '6px' }}
            >
              支持扩展名：.xlsx，.xls
            </p>
            <p className="next-upload-drag-hint">
              为保证上传效率，请上传10M以内的文件
            </p>
          </div>
        </Upload>
        {/**进度条 */}
        <UploadLoading upStatus={upStatus} />
      </section>
      {props.isShowTransfer && (
        <BatchTransfer checkSelected={this.checkSelected} />
      )}
    </div>
  )
}
// class BatchOperation extends PureComponent {
//   state = {
//     error: false,
//     upStatus: 'close',
//     // 已选门店
//     shopIdList: [],
//     otherParams: {},
//   }

//   static propTypes = {
//     tabKey: PropTypes.string,
//     uploadUrl: PropTypes.string,
//   }

//   static defaultProps = {
//     tabKey: 'add',
//     uploadUrl: '',
//   }

//   checkRadio = arg => {
//     console.log(arg)
//     // this.setState({
//     //   isAll: arg,
//     // })
//   }

//   onClick = e => {
//     console.log('onClick', e)
//   }

//   handleChange = (value, data, extra) => {
//     console.log(value, data, extra)
//   }

//   // showItem = (item) => {}
//   onDragOver = () => {
//     console.log('dragover callback')
//   }

//   handleClick = () => {}

//   error = (file, value) => {
//     this.setState({
//       error: true,
//       upStatus: 'error',
//     })
//   }

//   checkSelected = arr => {
//     this.setState({
//       shopIdList: arr,
//     })
//   }

//   // 下载模板
//   downloadDemo = () => {

//   }

//   onChange = arg => {
//     if (arg.length === 0) {
//       this.setState(() => {
//         return {
//           upStatus: 'close',
//         }
//       })
//     }
//   }

//   render() {
//     let uploadTit = ''
//     // 上传三种状态
//     if (this.state.upStatus === 'uploading') {
//       uploadTit = (
//         <p className="next-upload-drag-text">
//           <Icon type="loading" />
//           文件上传中{' '}
//           <Button type="primary" text onClick={cancel}>
//             取消
//           </Button>
//         </p>
//       )
//     } else if (this.state.error) {
//       uploadTit = (
//         <p className="next-upload-drag-text" style={{ color: 'rgb(216,93,85)' }}>
//           上传失败，请重新上传
//         </p>
//       )
//     } else {
//       uploadTit = <p className="next-upload-drag-text">点击或将文件拖拽到这里上传</p>
//     }

//   }
// }

// export default BatchOperation
