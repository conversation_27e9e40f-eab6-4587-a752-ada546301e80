export const objQueryString = (obj) => {
  return typeof obj === 'object' ? Object.entries(obj).filter(x => x[1] !== '' && x[1] !== undefined).map(item => item.join('=')).join('&') : ''
}

export const parseToObj = (str) => {
  const ret = {}
  str.split('&').forEach(item => {
    if (item) {
      let [key, val] = item.split('=').map(v => decodeURIComponent(v))
      if (!val) {
        val = ''
      } else if (/^\[[\s\S]*\]$|^\{[\s\S]*\}$/.test(val)) {
        try {
          val = JSON.parse(val)
        } catch (err) { console.log(err) }
      }
      ret[key] = val
    }
  })
  return ret
}
