import React from 'react'
import { BOREAS_HOST } from './config'
import _ from 'lodash'
import { Message, Dialog, Notification, Button } from '@alifd/next'
import { request } from '@/app/cardcoupon/lib/request'

const resolveOuterHeaders = () => {
  const ssot = window.localStorage.getItem('SKYWORK_KUNLUN_SSOT')
  let outerHeaders = {}
  outerHeaders['x-ele-newkunlun-token'] = ssot
  outerHeaders['x-ele-platform'] = 'new_kunlun'
  return outerHeaders
}

/**
 * 上传文件
 * @param {*} uploadUrl
 */
export function uploadFile(uploadUrl) {
  return async function uploadFile(option) {
    const formData = new FormData()
    formData.append('file', option.file)
    // TODO 待元林测试是否需要此参数，暂不加
    // formData.append('fileName', option.file.name)
    const res = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
      credentials: 'omit',
      headers: {
        ...resolveOuterHeaders(),
      },
    })
    const json = await res.json()
    if (!json.success) {
      Message.error(json.errMessage)
      throw json
    }
    return json
  }
}


export function downloadTemplate(url, jobType) {
  request(url, {
    query: {
      jobTypeName: jobType,
    },
  }).then(res => {
    window.location.href = res.data.data.url
  })
}

/**
 * 提交 JOB
 * @param {*} param0
 */
export async function createJob(jobType, jobKey, file, ext) {
  for (let key in ext) {
    if (ext[key] === undefined || ext[key] === null) {
      delete ext[key]
    }
  }
  if (ext.storeId == 0) {
    delete ext.storeId
  }

  let path = 'mtop.ele.newretail.itemJob.create'
  ext = JSON.stringify(ext)
  if (jobKey === 'std') {
    path = 'mtop.ele.newretail.platformItem.createJob'
    ext = null
  }
  console.log('ext: ', ext)
  try {
    // const r = await request(`mtop://mtop.ele.newretail.itemJob.create`, {
    //   query: {
    //     jobType,
    //     jobKey,
    //     uploadFileKey: file,
    //     ext: JSON.stringify(ext),
    //   },
    // })
    const r = await request(`mtop://${path}`, {
      method: 'POST',
      body: {
        jobType,
        jobKey: jobKey === 'std' ? 'platform_item' : jobKey,
        uploadFileKey: file,
        ext,
      }
    })
    successNotifyCation()
    return r
  } catch (ex) {
    console.log('catch-----', ex)
    Message.error(ex.message)
    throw ex
  }
}

export function successNotifyCation() {
  const title = '任务提交成功'
  const content = '具体任务进度详情请到【任务进度】中查看。'
  Notification.success({
    title,
    content,
  })
}

async function fetchUploadFile(file, name) {
  const formData = new FormData()
  formData.append('file', file, file.name || name)
  const res = await request(
    {
      url: `https://${BOREAS_HOST}/job/uploadFile`,
      method: 'POST',
    },
    {
      body: formData,
      credentials: 'omit',
      headers: {
        ...resolveOuterHeaders(),
      },
      type: 'origin',
    }
  )

  return res
}

export async function uploadFilesInfomation(data) {
  console.log('uploadFilesInfomation data', data)
  const file = new Blob([JSON.stringify(data)], {
    type: 'text/plain;charset=utf-8',
  })
  const res = await fetchUploadFile(file, 'spudata')
  return res
}
