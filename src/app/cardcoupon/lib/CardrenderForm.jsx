import React from 'react'
import { Form, Field } from '@alifd/next';

export const fieldContext = React.createContext()


const CardrenderForm = originSource => WrappedComponent => class extends React.Component {

  field = new Field(this, {
    onChange: () => {
      // console.log('---')
      // this.field.setProp('type2', 'disabled', false)
    }
  });

  render() {
    return <fieldContext.Provider value={this.props.field}><Form field={this.props.field}>
      <WrappedComponent {...this.props} field={this.props.field}
        uiSource={originSource(this.props.field)} />
    </Form></fieldContext.Provider>
  }
}

export default CardrenderForm
