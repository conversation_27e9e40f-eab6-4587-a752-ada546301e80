import React from 'react'
import cx from 'classnames'
import '../launch.scss'

function Cardlayout({
  title,
  children,
  type,
  retract,
  shrink,
}) {
  return (
    <div
      className={cx("cardlayout", {
        "cardlayout-hide": type === "hidden",
        "cardlayout-topLevel": !!title,
        "cardlayout-retract": retract,
        "cardlayout-shrink": shrink,
      })}
    >
      <p className="cardlayout-title">{title}</p>
      <div
        className="cardlayout-form"
        style={{ paddingLeft: title ? "64px" : 0 }}
      >
        {children}
      </div>
    </div>
  );
}

export default Cardlayout
