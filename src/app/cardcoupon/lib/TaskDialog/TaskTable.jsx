import React, { PureComponent } from 'react'
import {
  Button,
  Dialog,
  Table,
  Pagination,
  Grid,
  Icon,
  Message,
  Balloon,
} from '@alifd/next'
import { getJobList, jobStop, downloadFile } from '../../api'
import errHandler from './errHandler'
import './index.less'

const { Row, Col } = Grid
const TaskStatus = [
  { text: '排队中', color: '#313336' },
  { text: '进行中', color: '#313336' },
  { text: '已完成', color: '#00CCAA' },
  { text: '已中断', color: '#FF2D4B' },
  { text: '已完成', color: '#00CCAA' },
]

export default class TaskTable extends React.Component {
  constructor() {
    super()
    this.state = {
      taskInfo: {
        total: 0,
        dataSource: [],
      },
      page: 1,
      loading: false,
      refreshLoading: false,
    }
  }

  componentDidMount = async () => {
    this.getData()
  }

  getData = async (page = 1) => {
    const { jobKey, jobType } = this.props
    this.setState({ loading: true })
    const params = {
      page: `${page}`,
      pageSize: '10',
    }

    if (jobKey) {
      params.jobKey = JSON.stringify([jobKey])
    }

    if (jobType) {
      params.jobType = JSON.stringify([jobType])
    }

    if (jobKey === 'rule') {
      delete params.jobKey
      params.jobType = JSON.stringify([
        'TB_SEARCH_RULE_SET',
        'TB_SEARCH_RULE_EXPORT',
      ])
    }

    const res = await getJobList(params, {
      nest: 'total',
    }).catch(err => {
      console.log(err)
      Dialog.alert({
        title: '提示',
        content: err.message,
      })
      this.setState({ loading: false })
    })
    this.setState({
      taskInfo: res,
      page,
      loading: false,
    })
    return res
  }

  stopTask = async record => {
    console.log(record)
    await jobStop({
      jobId: record.jobId,
    })
      .then(data => {
        // list
        // console.log('llll')
        Message.success('操作成功')
        this.getData(this.state.page)
      })
      .catch(err => {
        errHandler(err)
      })
  }

  downFile = async fileKey => {
    await downloadFile({
      key: fileKey,
    })
      .then(data => {
        // list
        console.log(data)
        window.location.href = data.url
      })
      .catch(err => {
        errHandler(err)
      })
  }

  resultCell = (value, index, record) => {
    const { jobNoStop } = record
    console.log('jobNoStop: ', jobNoStop)
    switch (record.jobStatus) {
      case 1:
        return jobNoStop ? null : (
          <Balloon
            trigger={
              <Button text className="stop-task-button">
                中断任务&gt;
              </Button>
            }
            triggerType="click"
          >
            <p style={{ marginTop: '25px' }}>是否确认中断任务</p>
            <Button
              style={{ float: 'right' }}
              type="primary"
              size="small"
              onClick={() => this.stopTask(record)}
            >
              确认
            </Button>
          </Balloon>
        )
      case 2:
        return jobNoStop ? null : (
          <Balloon
            trigger={
              <Button text className="stop-task-button">
                中断任务&gt;
              </Button>
            }
            triggerType="click"
          >
            <p style={{ marginTop: '25px' }}>是否确认中断任务</p>
            <Button
              style={{ float: 'right' }}
              type="primary"
              size="small"
              onClick={() => this.stopTask(record)}
            >
              确认
            </Button>
          </Balloon>
        )

      case 3:
        return (
          <div>
            <span className={record.resultMsg ? 'stop-task-text' : ''}>
              {record.resultMsg}
            </span>
            {record.downloadFileKey ? (
              <a
                className="stop-task-download"
                onClick={() => this.downFile(record.downloadFileKey)}
              >
                点击下载
              </a>
            ) : null}
          </div>
        )
      case 4:
        return <span>任务被中断</span>
      case 5:
        return (
          <div>
            <span className="stop-task-text">
              {record.resultMsg || (record.downloadFileKey ? '失败列表' : '')}
            </span>
            {record.downloadFileKey ? (
              <a
                className="stop-task-download"
                onClick={() => this.downFile(record.downloadFileKey)}
              >
                点击下载
              </a>
            ) : null}
          </div>
        )
      default:
        return ''
    }
  }

  renderSourceFileColumn = value => {
    const cell = value ? (
      <div>
        <a className="stop-task-download" onClick={() => this.downFile(value)}>
          点击下载
        </a>
      </div>
    ) : null
    return cell
  }

  render() {
    const { taskInfo, page, loading, refreshLoading } = this.state
    const { total, dataSource } = taskInfo
    return (
      <React.Fragment>
        <Row className="task-dialog-header">
          <Col span="15">下载文件链接保留48小时</Col>
          <Col span="7" style={{ textAlign: 'right', paddingRight: '16px' }}>
            共{total}条任务
          </Col>
          <Col span="2">
            <Button
              onClick={async () => {
                this.setState({
                  refreshLoading: true,
                })
                await this.getData(1).catch(err => {
                  this.setState({
                    refreshLoading: false,
                  })
                })
                this.setState({
                  refreshLoading: false,
                })
              }}
              className="task-dialog-header_btn"
              text
              type="primary"
            >
              {refreshLoading && <Icon type="loading" />}
              刷新列表
            </Button>
          </Col>
        </Row>
        <Table
          dataSource={dataSource}
          primaryKey="key"
          fixedHeader
          maxBodyHeight={300}
          hasBorder={false}
          className="task-table"
          loading={loading}
        >
          <Table.Column title="任务名称" dataIndex="jobTypeText" width={80} />
          <Table.Column title="操作时间" dataIndex="gmtCreate" width={100} />
          <Table.Column title="操作人" dataIndex="opName" width={110} />
          <Table.Column
            title="当前状态"
            dataIndex="jobStatus"
            width={70}
            cell={(status, index, record) => {
              const { executeProgress } = record
              const taskStyleIndex = status - 1
              const { color, text } = TaskStatus[taskStyleIndex]
              const PROCESSING = 2
              return (
                <div style={{ color }}>{`${text}${
                  status === PROCESSING ? `（${executeProgress}%）` : ''
                }`}</div>
              )
            }}
          />
          <Table.Column
            title="结果说明"
            dataIndex="result"
            width={100}
            cell={this.resultCell}
          />
          <Table.Column
            title="原始文件"
            dataIndex="uploadFileKey"
            width={80}
            cell={this.renderSourceFileColumn}
          />
        </Table>
        <Pagination
          defaultCurrent={1}
          current={page}
          total={total}
          pageSize={10}
          shape="arrow-only"
          onChange={this.getData}
        />
      </React.Fragment>
    )
  }
}
