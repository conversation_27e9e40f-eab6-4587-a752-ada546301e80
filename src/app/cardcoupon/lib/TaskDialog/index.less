.status-finish {
  color: #00ccaa;
}
.status-fail {
  color: #ff2d4b;
}

.task-dialog {
  max-width: 90%;

  .next-pagination {
    .next-pagination-pages {
      float: right;
    }
  }
}

.task-dialog-header {
  margin-bottom: 20px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #666666;
  height: 20px;
  line-height: 20px;
  &_btn {
    vertical-align: top;
  }
}
.task-table {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #333333;
  .stop-task-button{
    color: #ff2d4b !important;
  }
  .stop-task-download{
    color: #007aff;
    cursor: pointer;
  }
  .stop-task-text {
    margin-right: 5px;
  }
}

.message-dialog {
  .next-dialog-close {
    display: none;
  }
  .task-dialog-tit {
    position: relative;
    .next-btn-text {
      font-size: 16px;
    }
  }
  .next-dialog-header {
    background: #f7f8fa;
    padding: 13px 20px 13px 20px;
  }
  .next-dialog-body {
    padding: 10px 20px;
    max-height: none !important;
  }
  .speed-dialog_icon {
    width: 14px;
    height: 14px;
    font-size: 14px;
    color: #666666;
    position: absolute;
    right: 0;
    margin-right: 8px;
    .next-icon {
      width: 14px;
    }
    &-up {
      transform: rotate(180deg);
    }
    .next-icon.next-medium:before {
      font-size: 14px;
      transform: rotate(180deg);
    }
  }
  .speed-li {
    font-size: 14px;
    color: #333333;
    line-height: 14px;
    height: 34px;
    line-height: 34px;
    &:after {
      clear: both;
    }
    .success {
      color: rgb(70, 188, 21);
    }
    .error {
      color: rgb(231, 43, 0);
    }
    .clock {
      color: rgb(255, 147, 0);
    }
    &_icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    &_download.next-medium {
      color: #007aff;
      height: 100%;
      float: right;
    }
  }
}
