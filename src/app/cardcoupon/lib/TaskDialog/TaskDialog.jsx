import React, { PureComponent } from 'react'
import {
  Dialog,
  Grid,
} from '@alifd/next'
import PropTypes from 'prop-types'
import TaskTable from './TaskTable'
import './index.less'

class TaskDialog extends PureComponent {

  static propTypes = {
    visible: PropTypes.bool,
    onClose: PropTypes.func,
    afterClose: PropTypes.func,
    jobKey: PropTypes.string.isRequired,
  }

  static defaultProps = {
    visible: false,
    onClose: () => { },
    afterClose: () => { },
  }



  render() {
    const { visible, onClose, afterClose } = this.props
    return (
      <Dialog
        className="task-dialog"
        title="任务进度查询（刷新列表可查看任务实时进度）"
        visible={visible}
        footer={false}
        onClose={onClose}
        height={"550px"}
        afterClose={afterClose}
        shouldUpdatePosition
        animation={{ in: 'fadeInDown', out: 'fadeOutUp' }}
        minMargin={40}
        closeable={'mask,esc,close'}
      >
        <TaskTable jobKey={this.props.jobKey} />
      </Dialog>
    )
  }
}

export default TaskDialog
