import { Dialog } from '@alifd/next'

function errH<PERSON><PERSON>(error, onOk, onClose) {
  const msgSegments = error.message.split('Error:')
  error.message = msgSegments[msgSegments.length - 1]
  let alertOptions = {
    title: '提示',
    content: error.message,
  }
  if (onOk) alertOptions.onOk = onOk
  if (onClose) alertOptions.onClose = onClose
  Dialog.alert(alertOptions)
}

export default errHandler
