import React from 'react'
import <PERSON>actDOM from 'react-dom'
import TaskDialog from './TaskDialog'

const IS_REACT_16 = !!ReactDOM.createPortal
function start(config) {
  const div = document.createElement('div')
  document.body.appendChild(div)
  const { type } = config
  let Comp = TaskDialog
  // switch (type) {
  //   case 'cat':
  //     Comp = Cat
  //     break
  //   default:
  //     Comp = TaskDialog
  // }

  function render(props) {
    ReactDOM.render(<Comp {...props} />, div)
  }

  function destroy() {
    const unmountResult = ReactDOM.unmountComponentAtNode(div)
    if (unmountResult && div.parentNode) {
      div.parentNode.removeChild(div)
    }
  }

  function close() {
    if (IS_REACT_16) {
      render({ ...config, visible: false, afterClose: destroy.bind(this) })
    } else {
      destroy()
    }
  }

  render({ ...config, visible: true, onClose: close })

  return {
    destroy,
  }
}

function show(job<PERSON><PERSON>) {
  start({ jobKey, type: 'show' })
}

function loop(jobKey) {
  start({ jobKey, type: 'loop' })
}

export default { show, loop }
