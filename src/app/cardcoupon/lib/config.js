import { buildEnv } from '@/packages/env'

const env = buildEnv()

export const BOREAS_MAPS = {
    daily: 'boreas.kunlun.alibaba.net',
    pre: 'pre-item.kunlun.alibaba-inc.com',
    prod: 'item.kunlun.alibaba-inc.com',
}

export const BOREAS_HOST = BOREAS_MAPS[env]

export const BATCH_OPTIONS = {
    jobType: 'BD_COUPON_CREATE',
    jobKey: 'bd_coupon',
    downloadUrl: 'mtop://mtop.ele.newretail.itemJob.getJobTemplate',
    uploadFileUrl: `https://${BOREAS_HOST}/job/uploadFile`
}