/* eslint-disable react/no-unused-state */
/* eslint-disable prefer-rest-params */
import React, { Component } from 'react'
import { Notification } from '@alifd/next'
import { getJobInfo } from '../api'

let orignalSetItem = localStorage.setItem
localStorage.setItem = function (key, newValue) {
  let setItemEvent = new Event('setItemEvent')
  setItemEvent.newValue = newValue
  setItemEvent.storageKey = key
  window.dispatchEvent(setItemEvent)
  orignalSetItem.apply(this, arguments)
}

let timeout = null
let timeInterval = null
const jobStatusList = [
  { status: '排队中', title: '排队中', type: 'warning' },
  { status: '进行中', title: '进行中', type: 'warning' },
  { status: '已完成', title: '任务执行成功', type: 'success' },
  { status: '已中断', title: '任务执行中断', type: 'warning' },
  { status: '已完成', title: '任务执行失败', type: 'error' },
]

const Notice = WrappedComponent => {
  return class RoleProvider extends Component {
    state = {
      oldValue: window.localStorage.getItem('ebaiShopTask'),
    }

    componentDidMount() {
      if (this.state.oldValue) {
        this.getJob({
          storageKey: 'ebaiShopTask',
          newValue: window.localStorage.getItem('ebaiShopTask'),
        })
      }
      window.addEventListener('storage', this.onChangeStorage)
      window.addEventListener('setItemEvent', this.onChangeStorage)
    }

    onChangeStorage = e => {
      if (
        (e.storageKey === 'ebaiShopTask' || e.key === 'ebaiShopTask') &&
        e.newValue !== this.state.oldValue
      ) {
        console.log(e)
        this.setState(
          {
            oldValue: e.newValue,
          },
          () => {
            this.getJob(e)
          },
        )
      }
    }

    getJob = async e => {
      // let num = 0
      // Notification.open({
      //   title: '提示',
      //   content: '任务成功',
      // })
      this.setState({ oldValue: e.newValue })
      console.log(e.newValue)
      console.log('clear-----------------0000000000000000')

      timeInterval && clearInterval(timeInterval)
      timeout && clearTimeout(timeout)
      if (e.newValue !== '') {
        console.log('getjob')
        const fetch = e => {
          console.log(e.newValue)
          clearInterval(timeInterval)
          const res = getJobInfo({
            jobIdList: JSON.stringify([e.newValue]),
          })
            .then(data => {
              console.warn('lyf:', data)
              let job = data.jobDetailDTOList[0]
              console.log(job)
              // num++
              // console.log(num)
              if (job.jobStatus !== 1 && job.jobStatus !== 2) {
                if (job.jobStatus !== 4) {
                  console.warn('lyf 提示！')
                  Notification.open({
                    title: jobStatusList[job.jobStatus - 1].title,
                    content: `${job.jobTypeText}${
                      jobStatusList[job.jobStatus - 1].status
                    }，完成详情请到【任务进度】查询`,
                    type: jobStatusList[job.jobStatus - 1].type,
                  })
                }
                window.localStorage.setItem('ebaiShopTask', '')
                clearInterval(timeInterval)
                timeInterval = null
              } else {
                // if (num !== 1) {
                timeInterval = setInterval(() => fetch(e), 20000)
                // }
              }
            })
            .catch(err => {
              console.log(err)
              window.localStorage.setItem('ebaiShopTask', '')
              clearInterval(timeInterval)
              timeInterval = null
              // const message = err.message.split('Error:')
              //   Dialog.alert({
              //     title: '提示',
              //     content: message[message.length-1],
              //   })
            })
        }

        // clearInterval(timeout)
        // timeout = null
        // console.log(num)
        timeout = setTimeout(() => fetch(e), 5000)
      } else {
        clearInterval(timeout)
        timeout = null
      }
    }

    componentWillUnmount() {
      timeInterval && clearInterval(timeInterval)
      timeout && clearTimeout(timeout)
      window.removeEventListener('storage', this.onChangeStorage)
      window.removeEventListener('setItemEvent', this.onChangeStorage)
    }

    render() {
      return (
        <div>
          <WrappedComponent {...this.props}></WrappedComponent>
          {/* {Notification.open({
            title: 'Notification Title',
            content: 'This is the content of the notification. This is the content of the notification. This is the content of the notification.',
            onClick: () => {
                console.log('Notification Clicked!');
            },
        })} */}
        </div>
      )
    }
  }
}

export { Notice }
