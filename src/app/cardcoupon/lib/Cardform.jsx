import React, { Component } from 'react'
import { Form, Grid, Input } from '@alifd/next';
import { fieldContext } from './CardrenderForm'
import itemMap from './itemMap'


const notExsit = valArr => valArr.find(item => item !== undefined && item !== null)

const FormItem = Form.Item;
const { Row, Col } = Grid;
const fs = 7
const formItemLayout = {
  labelCol: {
    fixedSpan: fs
  },
  wrapperCol: {
    span: 24 - fs
  },
  labelTextAlign: "right"
};
const renderItem = (renderMap, item, field, i) => {
  const { type, initfunc, fieldItem, props, ...k } = item

  const FormItemComponent = renderMap.get(type)
  let initField = {}
  if (k.name) {

    initField = {
      ...field.init(
        k.name,
        { initValue: notExsit([k.value, k.defaultValue, k.checked]) }
      )
    }
  }
  if (FormItemComponent) {
    return <FormItemComponent
      key={item.name || i}
      {...k}
      {...props}
    />

  } else {
    throw `component of ${type} not found`
  }
}

class YForm extends Component {

  static contextType = fieldContext;

  render() {
    const { fields, inline } = this.props
    return inline ? (
      <React.Fragment>
        <Row gutter="4">
          {fields &&
            fields.map(({ formItem, label, required, ...k }, i) => (
              <Col key={i}>
                <FormItem
                  label={label}
                  required={required}
                  labelTextAlign="right"
                  {...formItem}
                  key={i}
                >
                  {renderItem(itemMap, k, this.context, i)}
                </FormItem>
              </Col>
            ))}
        </Row>
      </React.Fragment>
    ) : (
      <React.Fragment>
        {fields &&
          fields.map(({ formItem = {}, label, required, ...k }, i) => {
            return (
              <FormItem
                label={label}
                required={required}
                labelTextAlign="right"
                {...Object.assign({}, formItemLayout, formItem, i)}
                key={i}
              >
                {renderItem(itemMap, k, this.context)}
              </FormItem>
            );
          })}
      </React.Fragment>
    );
  }
}

export default YForm
