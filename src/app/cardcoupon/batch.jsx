import React, { useState, useRef } from 'react';
import _ from 'lodash'
import {
  Tab,
  Message,
  Dialog,
  Radio,
  Button,
  Breadcrumb,
  Form,
  Balloon,
  Icon,
  Field,
} from '@alifd/next';
import Upload from './lib/upload'
import * as helpers from './lib/helpers'
import { BATCH_OPTIONS } from './lib/config'
import './batch.less'

const { jobType, jobKey, downloadUrl, uploadFileUrl } = BATCH_OPTIONS
const RadioGroup = Radio.Group;

export default function Batch(props) {
  const { history, match } = props
  const [loadingStatus, setLoading] = useState(false)
  const [file, setFile] = useState('')
  const [couponItemType, setCouponItemType] = useState(1)

  const submit = () => {
    // 防止重复提交
    if (loadingStatus) {
      return
    }
    if (!file) {
      Message.error('请上传文件')
      return
    }
    setLoading(true)
    try {
      helpers.createJob(jobType, jobKey, file, {
        couponItemType
      })
        .then((res) => {
          window.localStorage.setItem(
            'ebaiShopTask',
            _.get(res, 'data.data', 0),
          )
          setLoading(false)
          res && history.goBack()
        })
        .catch((err) => {
          setLoading(false)
          Message.error('已经存在待执行的任务或者正在执行的任务，请稍后再试')
          console.error('createJob', err)
          throw new Error(err)
        })
    } catch (ex) {
      console.error('confirm', ex)
      setLoading(false)
      Message.error('上传失败')
      throw new Error(ex)
    }
  }

  const cancel = () => {
    setFile('')
    Dialog.confirm({
      title: '提示',
      content: '您所编辑的内容还未同步，确认退出该页面吗',
      messageProps: {
        type: 'warning',
      },
      onOk: () => {
        history.goBack()
      },
    })
  }

  return (
    <div style={{ padding: 24, paddingBottom: 0 }}>
      <Breadcrumb>
        <Breadcrumb.Item
          link="javascript:void(0);"
          onClick={() => {
            history.push("/");
          }}
        >
          卡券商品管理
        </Breadcrumb.Item>
        <Breadcrumb.Item link="javascript:void(0);">
          批量新增
        </Breadcrumb.Item>
      </Breadcrumb>

      <div
        style={{
          backgroundColor: "#ffffff",
          paddingTop: 24,
          paddingBottom: 40,
          marginTop: 10
        }}
      >
        <div className="cardlayout-topLevel" style={{ width: 500 }}>
          <div style={{ marginBottom: 20 }}>
            <span style={{ color: 'red' }}>*</span>卡券类型：
            <RadioGroup
              dataSource={[
                {
                  value: 1,
                  label: "普通卡券商品"
                },
                {
                  value: 2,
                  label: "超级SKU专用子品",
                  disabled: false
                }
              ]}
              value={couponItemType}
              onChange={setCouponItemType}
              aria-labelledby="groupId"
            />
          </div>
          <Upload
            loadingStatus={loadingStatus}
            downloadText={'批量新增卡券商品模板'}
            uploadAction={helpers.uploadFile(uploadFileUrl)}
            onChange={(url) => {
              setFile(url)
            }}
            downloadAction={() =>
              helpers.downloadTemplate(downloadUrl, jobType, jobKey)
            }
          />
        </div>
        <div className="cardlayout-topLevel" style={{ width: 500 }}>
          <Button type="normal" onClick={cancel}>
            取消
          </Button>
          <Button type="primary" style={{ marginLeft: 10 }} onClick={submit}>
            确认提交
          </Button>
        </div>
      </div>
    </div>
  );
}
