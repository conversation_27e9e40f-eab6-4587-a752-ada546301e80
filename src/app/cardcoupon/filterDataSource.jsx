import React, { useState, useEffect, Component } from 'react'
import { Message } from '@alifd/next'
import qs from 'qs'
import _ from 'lodash'
import { request } from '@/app/cardcoupon/lib/request'
import parseUrl from '@/packages/request/parseUrl'

export default function filterDataSource({
  action,
  requestImmediately = () => true,
  mapStateToProps = x => x
}) {
  return Target => {
    class Proxy extends Component {
      constructor(props) {
        super()

        this.state = {
          data: null
        }
        this.query = {
          pageNum: 1,
          pageSize: 10
        }

        this.hydrateSearchParams(props)
      }

      hydrateSearchParams(props) {
        const searchParams = parseUrl(window.location.href).query
        this.query = { ...this.query, ...searchParams }
      }

      componentDidMount() {
        if (requestImmediately(this.query)) {
          this.search()
        }
      }

      searchForFilter = (_query) => {
        _query.pageNum = 1
        return this.search(_query)
      }

      changePage = (_query) => {
        return this.search(_query)
      }

      search = (_query = {}) => {
        // console.log('query-search-start', this.query)
        this.query = { ...this.query, ..._query }
        // console.log('query-search-aftger', this.query)

        return new Promise((resolve, reject) => {
          request(action, { query: this.query }, { props: this.props })
            .then(json => {
              if (this.props.history) {
                for (let key in this.query) {
                  if (this.query[key] === undefined || this.query[key] === '') {
                    delete this.query[key]
                  }
                }
                this.props.history.replace(
                  this.props.history.location.pathname + '?' + qs.stringify(this.query)
                )
              }
              this.setState({
                data: {
                  total: json.total || 0,
                  dataSource: json.data || []
                }
              })
              resolve()
            })
            .catch(err => {
              console.log('search', err)
              this.setState({
                data: {
                  total: 0,
                  dataSource: []
                }
              })
              reject()
              Message.error(err.errMessage || '接口错误!')
            })
        })
      }

      changePageSize = pageSize => {
        console.log('query-change-page', this.query)
        if (+pageSize !== +this.query.pageSize) {
          this.search({ pageSize })
        }
      }

      reset = v => {
        this.query = v || {
          pageNum: 1,
          pageSize: 10
        }
        this.search({})
      }

      updateRow = (i, p) => {
        let row = this.state.data.dataSource[i]
        const updator = p(row)
        row = { ...row, ...updator }
        const newData = { ...this.state.data }
        newData.dataSource = {}
        newData.dataSource[i] = { ...this.state.data.dataSource }
        newData.dataSource[i] = row
        this.setState({
          data: newData
        })
      }

      reload = () => {
        console.log('query-reload', this.query)
        return new Promise((resolve, reject) => {
          request(action, { query: this.query }, { props: this.props })
            .then(json => {
              this.setState({
                data: {
                  total: json.total,
                  dataSource: json.data || []
                }
              })
              resolve()
            })
            .catch(err => {
              reject()
            })
        })
      }

      render() {
        const { data } = this.state
        return (
          <Target
            {...mapStateToProps({
              tableProps: {
                pageSize: +this.query.pageSize,
                defaultCurrent: Number(this.query.pageNum),
                ...data,
                updateRow: this.updateRow,
                search: this.changePage,
                changePageSize: this.changePageSize,
                reload: this.reload
              },
              filterProps: {
                initValues: this.query,
                reload: this.reload,
                search: this.searchForFilter,
                reset: this.reset
              }
            })}
            {...this.props}
          />
        )
      }
    }

    return Proxy
  }
}
