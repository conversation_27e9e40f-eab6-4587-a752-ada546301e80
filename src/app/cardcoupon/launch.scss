.launch-op {
  display: flex;
  &-span {
    flex: 1;
    border: none !important;
  }
  &-picNtext {
    display: flex;
    align-items:center;
    &--pic {
      height: 60px;
      width: 60px;
      max-width: 60px !important;
      margin-right: 8px;
    }
    &--text {
      display: flex;
      flex-direction: column;
      width: 210px;
      &--title {
        font-size: 14px;
        margin-bottom: 6px;
      }
      &--id {
        font-size: 14px;
        color: #999999;
      }
    }
  }
}
.next-time-picker-menu {
  width: 100%;
}
.next-overlay-wrapper.opened .next-overlay-backdrop {
  opacity: 1 !important;
  background: rgba(0, 0, 0, 0.2);
}
.next-form-item .next-form-item-label {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}
.cardlayout {
  &-topLevel {
    width: 900px;
    margin: 0 auto;
    margin-bottom: 40px;
  }
  &-retract {
    padding-left: 140px;
  }
  &-shrink {
    margin-bottom: -16px;
  }
  &-hide {
    overflow: hidden;
    margin: 0;
    padding: 0;
    height: 0;
  }
  &-title {
    font-size: 14px;
    color: #999999;
    margin-bottom: 24px;
  }
}

.next-input-group-addon:first-child > * > .next-input {
  border-bottom-right-radius: 0!important;
  border-top-right-radius: 0!important;
}

.next-input-group-addon:first-child > * {
  margin-right: -1px;
}

.next-notification {
  width: 384px;
  position: fixed;
  z-index: 1010;
  padding: 0;
  margin: 0; }
  .next-notification .next-message {
    margin-bottom: 16px;
    overflow: hidden; }

.next-notification-fade-leave {
  -webkit-animation-duration: 300ms;
          animation-duration: 300ms;
  -webkit-animation-play-state: paused;
          animation-play-state: paused;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  -webkit-animation-timing-function: ease;
          animation-timing-function: ease; }

.next-notification-fade-leave.next-notification-fade-leave-active {
  -webkit-animation-name: NotificationFadeOut;
          animation-name: NotificationFadeOut;
  -webkit-animation-play-state: running;
          animation-play-state: running; }

@-webkit-keyframes NotificationFadeOut {
  0% {
    max-height: 150px;
    margin-bottom: 16px;
    opacity: 1; }
  100% {
    max-height: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0; } }

@keyframes NotificationFadeOut {
  0% {
    max-height: 150px;
    margin-bottom: 16px;
    opacity: 1; }
  100% {
    max-height: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0; } }
