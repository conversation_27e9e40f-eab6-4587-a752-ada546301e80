import React from 'react'
import moment from 'moment';
import omit from 'object.omit'
import _ from 'lodash'
import { request } from '@/app/cardcoupon/lib/request'
import BigNumber from 'bignumber.js';

const testNum = require('./testNum').default

const USE_NUM = 1

export const ELE_ID = {
  1: 'eleSupplierId',
  2: 'eleWidList',
  3: 'eleWidList'
}
export const hintsName = {
  1: 'chainHints',
  2: 'subShopHints',
  3: 'storeHints'
}

export const TICKET_NUM = {
  1: 4,
  2: 2
}

const precisionMap = {
  num: 0,
  price: 2,
  threshold: 2,
  currentPrice: 2,
  stock: 0
}

const resolveValidator = (key, itemConfig, values) => {
  const { name, tips, rules: validRules } = itemConfig
  return {
    placeholder: tips,
    validator: (rules, value, cb) => {
      let validResults = []
      validRules.forEach(rule => {
        const { verifyType, field, min, max } = rule
        if (verifyType === 'simpleRange') {
          validResults.push(testNum(value, {
            min: min || 0,
            max: max || Infinity,
            precision: precisionMap[key],
            closed: true
          }))
        } else if (verifyType === 'relatedFieldRange') {
          validResults.push(testNum(value, {
            min: min && values[field] ? values[field] * min : 0,
            max: max && values[field] ? values[field] * max : Infinity,
            precision: precisionMap[key],
            closed: true
          }))
        }
      })
      const valid = !validResults.some(ret => !ret)
      if (!valid) {
        cb(tips)
        return
      }
      cb()
    }
  }
}

const resolveConfig = (config, values) => {
  const { ticketDetailList, ...others } = config
  const flatConfig = { ...others, ...ticketDetailList }
  let validators = {}
  Object.keys(flatConfig).forEach(key => {
    validators[key] = resolveValidator(key, flatConfig[key], values)
  })
  return validators
}

export const originSource = (field) => {
  const {
    getValue,
    setValue,
    setValues,
    getValues,
  } = field

  const cover = getValue
  const couponConfig = getValue('couponConfig') || {}
  const { relativeDay, ...others } = couponConfig[getValue('couponItemType') === 2 ? 'related' : 'common'] || {}
  const propConfig = resolveConfig(others, getValues())
  const relativeDayData = _.get(relativeDay, 'rules[0].values', [])

  let stockExtra = null
  const { applySellerType, storeInfoListSize } = getValues()
  if (applySellerType === 1 && storeInfoListSize) {
    stockExtra = (
      <div className="input-extra-info">
        所选连锁下共有
        <span className="keyword">{storeInfoListSize}</span>
        个已上线的门店，请设置充足的库存。
      </div>
    )
  } else if (applySellerType === 2 && storeInfoListSize) {
    stockExtra = (
      <div className="input-extra-info">
        您选择了
        <span className="keyword">{storeInfoListSize}</span>
        个门店，请设置充足的库存。
      </div>
    )
  }

  const status = Number(getValue("status"));
  let statusText = "";
  if ([0, 1].includes(status)) {
    statusText = "已上架";
  } else if ([-2, -3, -5, -9].includes(status)) {
    statusText = "已下架";
  } else if ([-1, -4].includes(status)) {
    statusText = "已删除";
  }

  return [
    {
      title: "基本信息",
      children: [
        getValue("isEdit") && {
          type: "html",
          label: "商品ID",
          value: `<div style="line-height: 36px;">${getValue("itemId")}</div>`
        },
        getValue("isEdit") && {
          type: "html",
          label: "状态",
          value: `<div style="line-height: 36px;">${statusText}</div>`
        },
        {
          type: "yradioGroup",
          label: "卡券类型",
          required: true,
          disabled: !!getValue("isEdit"),
          formItem: {
            help: '选择后无法修改！',
          },
          props: {
            name: "couponItemType",
            defaultValue: 1,
            dataSource: [
              {
                value: 1,
                label: "普通卡券商品"
              },
              {
                value: 2,
                label: "超级SKU专用子品 " , //+ (getValue("isEdit") ? "" : "选择后无法修改！")
              }
            ],
            onChange: () => {
              setValue("timeranger", []);
            },
          },
          // eslint-disable-next-line no-nested-ternary
          extra: ((getValue("couponItemType") === 2 && !!getValue("isEdit")) ? (
            getValue('superItemId') ?
              <div>已关联主品 ID=<a
                href={'/app/eleme-b-newretail/commodity_pc/kunlun-tmall-coupon/index.html#/update?type=update&id=' + getValue('superItemId')}
                rel="noreferrer"
                target={'_blank'}
              >
                {getValue('superItemId')}</a>
              </div> :
              <div>未关联主品</div>
          ) : null)
        },
        {
          type: "yradioGroup",
          label: "适用门店",
          required: !getValue("isEdit"),
          shrink: true,
          props: {
            required: !getValue("isEdit"),
            formItem: {
              asterisk: false
            },
            name: "applySellerType",
            defaultValue: 1,
            disabled: !!getValue("isEdit"),
            dataSource: [
              {
                value: 1,
                label: "供应商下全部门店",
                condition: [
                  [
                    {
                      type: "hintTextArea",
                      label: "",
                      trim: true,
                      name: "id1",
                      title: "已选供应商：",
                      required: !getValue("isEdit"),
                      hints: "chainHints",
                      disabled: !!getValue("isEdit"),
                      field,
                      formItem: {
                        requiredMessage: "请输入供应商id",
                        asterisk: false,
                      },
                      placeholder:
                        "请输入供应商ID，一个卡券只能绑定在一个供应商下",
                      onBlur: e => {
                        const id = e.target.value;
                        if (id) {
                          request(
                            // TODO
                            // "mtop://mtop.ele.newretail.item.coupon.queryStorePicUrlList",
                            "mtop://mtop.ele.newretail.item.virtual.queryStorePicUrlList",
                            {
                              query: {
                                storePicUrlQuery: JSON.stringify({
                                  eleSupplierId: id
                                })
                              }
                            }
                          ).then(res => {
                            let logos = res.data.data;
                            logos = logos.map(
                              logo => `https://img.alicdn.com/${logo}`
                            );
                            setValue("logos", logos);
                          });
                          request(
                            "mtop://mtop.ele.newretail.store.queryBdCanOperateSellerAndStore",
                            {
                              query: {
                                bdSellerStoreQuery: JSON.stringify({
                                  eleSupplierId: id,
                                  applySellerType: 1,
                                  isAdmin: true
                                })
                              }
                            }
                          ).then(res => {
                            const { data } = res.data;
                            if (data && data.length) {
                              const { storeInfoList } = data[0]
                              setValue('storeInfoListSize', storeInfoList && storeInfoList.length)
                            }
                          });
                        } else {
                          setValue("logos", []);
                        }
                      }
                    }
                  ]
                ]
              },
              {
                value: 2,
                label: "供应商下部分门店",
                condition: [
                  [
                    {
                      type: "hintTextArea",
                      label: "",
                      name: "id2",
                      title: "已选供应商部分门店：",
                      hints: "subShopHints",
                      field,
                      formItem: {
                        requiredMessage: "请输入供应商下的门店id",
                        asterisk: false,
                        validator: (rules, value, cb) => {
                          if (
                            value
                              .replace(/\s/g, ',')
                              .split(',')
                              .filter((x) => x).length > 200) {
                            cb('最多输入200个id')
                          }
                          else {
                            cb();
                          }
                        }
                      },
                      required: !getValue("isEdit"),
                      placeholder:
                        "请输入门店ID，以「回车」隔开。必须为同一个供应商下的门店。",
                      onBlur: e => {
                        const id = e.target.value;
                        if (id) {
                          const widList = id.split("\n").filter(x => x)
                          setValue('storeInfoListSize', widList && widList.length)
                          request(
                            // TODO
                            // "mtop://mtop.ele.newretail.item.coupon.queryStorePicUrlList",
                            "mtop://mtop.ele.newretail.item.virtual.queryStorePicUrlList",
                            {
                              query: {
                                storePicUrlQuery: JSON.stringify({
                                  eleWidList: widList
                                })
                              }
                            }
                          ).then(res => {
                            let logos = res.data.data;
                            logos = logos.map(
                              logo => `https://img.alicdn.com/${logo}`
                            );
                            setValue("logos", logos);
                          });
                        } else {
                          setValue("logos", []);
                        }
                      }
                    }
                  ]
                ]
              },
              {
                value: 3,
                label: "散店",
                condition: [
                  [
                    {
                      type: "hintTextArea",
                      label: "",
                      name: "id3",
                      hints: "storeHints",
                      title: "已选散店：",
                      required: !getValue("isEdit"),
                      disabled: !!getValue("isEdit"),
                      field,
                      formItem: {
                        requiredMessage: "请输入散店id",
                        asterisk: false,
                        validator: (rules, value, cb) => {
                          if (
                            value
                              .replace(/\s/g, ',')
                              .split(',')
                              .filter((x) => x).length > 200) {
                            cb('最多输入200个id')
                          }
                          else {
                            cb();
                          }
                        }
                      },
                      trim: true,
                      placeholder: "请输入散店ID，一个卡券只能绑定在一个散店下",
                      onBlur: e => {
                        const id = e.target.value;
                        setValue('storeInfoListSize', 1)
                        if (id) {
                          request(
                            // TODO
                            // "mtop://mtop.ele.newretail.item.coupon.queryStorePicUrlList",
                            "mtop://mtop.ele.newretail.item.virtual.queryStorePicUrlList",
                            {
                              query: {
                                storePicUrlQuery: JSON.stringify({
                                  eleWidList: id.split("\n")
                                })
                              }
                            }
                          ).then(res => {
                            let logos = res.data.data;
                            logos = logos.map(
                              logo => `https://img.alicdn.com/${logo}`
                            );
                            setValue("logos", logos);
                          });
                        } else {
                          setValue("logos", []);
                        }
                      }
                    }
                  ]
                ]
              }
            ]
          }
        },
        {
          label: (getValue("couponItemType") === 2 ? '连锁' : '品牌') + '/散店名称',
          type: "customInput",
          name: "brandName",
          htmlType: "text",
          disabled: getValue("couponItemType") === 2 && getValue('superItemId'),
          placeholder: `选填，不填默认使用${getValue("couponItemType") === 2 ? '连锁' : '品牌'}/散店名称。举例，填写大润发${getValue("couponItemType") === 2 ? '(华东)' : ''}、家乐福${getValue("couponItemType") === 2 ? '(上海)' : ''}`,
          style: {
            width: '100%'
          },
          maxLength: 25,
          hide: getValue('applySellerType') === 3 && getValue("couponItemType") !== 2,
          extra: (
            <React.Fragment>
              <p>1、无门槛：{getValue("couponItemType") === 2 ? '连锁' : '品牌'}／散店名称（大润发）+张数（例如：4张）+券金额（例如：5元）+无门槛代金券+与满减同享</p>
              <p>2、有门槛：{getValue("couponItemType") === 2 ? '连锁' : '品牌'}／散店名称（大润发）+张数（例如：4张）+满*减*（例如：满10减5）+代金券+与满减同享</p>
            </React.Fragment>
          )
        },
        {
          type: "RangePicker",
          label: "售卖时间",
          name: "timeranger",
          required: true,
          disabledDate: (date) => {
            if (window.sessionStorage.getItem('backdoor')) {
              return false
            }
            const yesterday = moment().subtract(1, 'days')
            return (date.valueOf() <= yesterday.hour(23).minute(59).second(59).valueOf() && getValue("couponItemType") !== 2)
              || date.valueOf() >= moment('2100 12 31 23:59:59')
          },
          disabled: !!getValue("isEdit"),
          formItem: {
            requiredMessage: "请选择售卖时间"
          },
          showTime: { format: "HH:mm" }
        }
      ]
    },
    {
      title: "卡券组合",
      children: [
        {
          type: "input",
          required: true,
          name: "num",
          disabled: !!getValue("isEdit"),
          formItem: {
            requiredMessage: "请输入券张数",
            validator: _.get(propConfig, 'num.validator', (rules, value, cb) => cb())
          },
          label: "券张数",
          style: {
            width: 200
          },
          placeholder: _.get(propConfig, 'num.placeholder', '')
        },
        {
          type: "yradioGroup",
          props: {
            name: "",
            label: "",
            htmlType: "hidden",
            required: true,
            placeholder: "2≤券张数≤100",
            type: "input",
            disabled: !!getValue("isEdit"),
            onChange: value => {
            },
            commonData: [
              [
                {
                  type: "input",
                  formItem: {
                    labelAlign: "top"
                  },
                  style: { width: 188 },
                  label: "原总价",
                  name: "originPrice",
                  htmlType: "number",
                  addonTextAfter: "元",
                  required: true,
                  disabled: true,
                  value: getValue("num") * getValue("price")
                },
                {
                  type: "input",
                  formItem: {
                    labelAlign: "top",
                    requiredMessage: "请输入现总售价",
                    validator: _.get(propConfig, 'currentPrice.validator', (rules, value, cb) => cb())
                  },
                  style: { width: 200 },
                  label: "现总售价",
                  name: "currentPrice",
                  required: true,
                  disabled: !!getValue("isEdit"),
                  placeholder: _.get(propConfig, 'currentPrice.placeholder', ''),
                }
              ],
              "原总价为券金额之和，现总价为顾客付款金额"
            ],
            condition: value => {
              let conditionData = [
                [
                  {
                    type: "select",
                    formItem: {
                      labelAlign: "top"
                    },
                    label: "类型",
                    required: true,
                    name: `type1`,
                    defaultValue: `option1`,
                    disabled: true,
                    dataSource: [
                      {
                        label: "店铺代金券",
                        value: `option1`
                      }
                    ],
                    style: {
                      width: 188
                    }
                  },
                  {
                    label: "券金额",
                    type: "input",
                    name: `price`,
                    style: {
                      width: 188
                    },
                    required: true,
                    formItem: {
                      requiredMessage: "请输入券金额",
                      validator: _.get(propConfig, 'price.validator', (rules, value, cb) => cb())
                    },
                    placeholder: _.get(propConfig, 'price.placeholder', ''),
                    disabled: !!getValue("isEdit"),
                    dataSource: [
                      {
                        label: "5",
                        value: 5
                      },
                      {
                        label: "6",
                        value: 6
                      },
                      {
                        label: "7",
                        value: 7
                      },
                      {
                        label: "8",
                        value: 8
                      },
                      {
                        label: "10",
                        value: 10
                      }
                    ],
                    onChange: value => {
                      setValues({
                        originPrice:
                          getValue(`price${getValue("num")}`) *
                          TICKET_NUM[String(getValue("num"))] *
                          USE_NUM,
                        currentPrice: null
                      });
                    }
                  },
                  {
                    type: "input",
                    label: "使用门槛",
                    name: `threshold`,
                    addonTextAfter: "元",
                    placeholder: _.get(propConfig, 'threshold.placeholder', ''),
                    disabled: !!getValue("isEdit"),
                    required: true,
                    formItem: {
                      requiredMessage: "请输入使用门槛",
                      validator: _.get(propConfig, 'threshold.validator', (rules, value, cb) => cb())
                    },
                    style: {
                      width: 200
                    },
                  },
                ]
              ];
              return conditionData;
            }
          }
        }
      ]
    },
    ...(Number(getValue('couponItemType')) === 2 ? [{
      title: '出资信息',
      children: [{
        type: "radioGroup",
        label: '出资类型',
        name: 'contributionType',
        dataSource: [{
          label: '商户全资',
          value: '1'
        }, {
          label: '平台出资',
          value: '2'
        }],
        defaultValue: '1',
        required: true,
        disabled: !!getValue("isEdit"),
      },
      ...(getValue('contributionType') === '2' ? [{
        type: 'CardForm',
        inline: true,

        fields: [{
          type: "input",
          formItem: {
            labelAlign: "top",
            requiredMessage: "请输入预算ID",
            style: {
              marginLeft: 60,
            }
          },
          style: { width: 188 },
          label: "预算ID",
          name: "budgetId",
          required: true,
          disabled: !!getValue("isEdit"),
        },
        {
          type: "numberPicker",
          formItem: {
            labelAlign: "top",
            requiredMessage: "请输入补贴金额",
            validator: (rules, value, cb) => {
              if (_.isNil(value)) {
                return cb()
              }
              const num = BigNumber(getValue('num'))
              const avg = BigNumber(value).div(num)

              if (avg.isLessThanOrEqualTo(BigNumber(30))) {

                const [integer, decimal] = avg.toString().split('.')

                if (decimal && decimal.length > 2) {
                  return cb('分摊补贴金额小数不能大于2位')
                }

                const originPrice = getValue('originPrice')
                const currentPrice = getValue('currentPrice')

                if (_.isNil(originPrice) || _.isNil(currentPrice)) {

                  return cb()
                }

                if (BigNumber(value).isLessThanOrEqualTo(BigNumber(originPrice).minus(currentPrice))) {
                  return cb()
                }

                console.log('让利金额', originPrice, currentPrice)

                return cb('补贴金额不能超出单张30元及让利金额')

              }
              console.log('补贴金额不能超出单张30元', avg)

              return cb('补贴金额不能超出单张30元及让利金额')
            },
          },
          style: { width: 188 },
          label: "补贴金额",
          name: "elemeSubsidy",
          // htmlType: "number",
          addonTextAfter: "元",
          precision: 2,
          min: 0,
          step: 0.01,
          hasTrigger: false,
          required: true,
          disabled: !!getValue("isEdit"),
          onChange: val => {

            console.log('elemeSubsidy', val)

            return val + ''
          }
        }]
      }] : [])
      ]
    }] : []),
    {
      title: "图片信息",
      children: [
        // {
        //   label: "品牌logo",
        //   type: "coverUploader",
        //   name: "cover",
        //   price: getValue("price"),
        //   num: TICKET_NUM[String(getValue("num"))],
        //   required: true,
        //   field,
        //   formItem: {
        //     requiredMessage: "请传入logo图片",
        //     asterisk: false
        //   }
        // },
        {
          label: (
            <span>
              <span style={{ color: "#999999" }}>(选填)</span>详情图片
            </span>
          ),
          props: {
            maxImages: getValue("couponItemType") === 1 ? 4 : 10
          },
          type: "imageUploader",
          name: "detailImgList",
          text: "展示售卖的商品图片，可包括商品包装、原材料、特写等方面",
          disabled: !!getValue("isEdit") && getValue('superItemId') && getValue("couponItemType") === 2,
          hints: [
            `最多可上传${getValue("couponItemType") === 1 ? 4 : 10}张，宽度750px以上，不可超过5M，支持png、jpg、jpeg`
          ],
          onChange: e => {
            setValue("detailImgList", e);
          }
        }
      ]
    },
    {
      title: "购买规则",
      children: [
        {
          type: "customInput",
          label: "库存",
          name: "stock",
          formItem: {
            requiredMessage: "请输入库存",
            validator: _.get(propConfig, 'stock.validator', (rules, value, cb) => cb())
          },
          required: true,
          trim: true,
          htmlType: "number",
          placeholder: _.get(propConfig, 'stock.placeholder', ''),
          step: 1,
          style: { width: "300px" },
          max: 99999999,
          min: 0,
          extra: stockExtra
        },
        {
          type: "input",
          label: "每人每天购买",
          name: "limitNum",
          style: { width: "300px" },
          step: 1,
          max: 10,
          required: true,
          placeholder: "1≤限购≤10",
          formItem: {
            requiredMessage: "请输入限购量",
            validator: (rules, value, cb) => {
              if (!testNum(value, { min: 1, max: 11, precision: 0 })) {
                cb("1≤限购≤10");
                return
              }
              cb()
            }
          },
          disabled: !!getValue("isEdit"),
          min: 0,
          addonTextAfter: "份",
        }
      ]
    },
    {
      title: "使用规则",
      children: [
        {
          type: "wings",
          addonTextAfter: "天内有效",
          addonTextBefore: "购买后",
          label: "有效期",
          name: "relativeDay",
          required: true,
          formItem: {
            requiredMessage: "请选择有效期"
          },
          dataSource: relativeDayData,
          disabled: !!getValue("isEdit") || !getValue("num"),
          trim: true,
          min: 0
        },
        {
          type: "checkbox",
          label: "优惠规则",
          name: "usePromotionBoth",
          disabled: true,
          checked: true,
          text: "卡券商品与门店其他所有优惠同享"
        }
      ]
    },
    {
      type: "hidden",
      children: [
        {
          name: "isEdit",
          type: "input",
          htmlType: "hidden",
          style: {
            display: "none"
          },
          value: getValue("isEdit")
        },
        // 品牌logo推荐图片
        {
          name: "logos",
          type: "input",
          htmlType: "hidden",
          style: {
            display: "none"
          },
          value: getValue("logos")
        },
        // 自定义图片
        {
          name: "customlogo",
          type: "input",
          htmlType: "hidden",
          style: {
            display: "none"
          },
          value: getValue("customlogo")
        },
        // 适用门店已选门店
        {
          name: "chainHints",
          type: "input",
          htmlType: "hidden",
          style: {
            display: "none"
          },
          value: getValue("chainHints")
        },
        {
          name: "storeHints",
          type: "input",
          htmlType: "hidden",
          style: {
            display: "none"
          },
          value: getValue("storeHints")
        },
        {
          name: "subShopHints",
          type: "input",
          style: {
            display: "none"
          },
          htmlType: "hidden",
          value: getValue("subShopHints")
        }
      ]
    }
  ].map(item => {
    return {
      ...item,
      children: item.children.filter(item2 => !!item2)
    }
  });
}

export const formatData = (values) => {
  const { timeranger, applySellerType, brandName } = values
  let ids = values[`id${applySellerType}`]
  const sourceHints = values[hintsName[applySellerType]] || ''
  const hints = sourceHints ? `${sourceHints}`.split(', ') : ''
  console.log('sourceHints: ', sourceHints)
  console.log('hints: ', hints)
  if (Number(applySellerType) === 1) {
    if (sourceHints) {
      ids = sourceHints
    }
  } else {
    ids = ids.split('\n')
    if (hints) {
      ids = ids.concat(hints)
    }
    ids = ids.filter(x => x)
  }
  const _brandName = Number(applySellerType) === 1 || Number(applySellerType) === 2 || values.couponItemType === 2 ?
    brandName :
    undefined
  const params = {
    brandName: _brandName,
    status: values.status,
    saleStart: timeranger[0].format("YYYY-MM-DD HH:mm:ss"),
    saleEndDate: timeranger[1].format("YYYY-MM-DD HH:mm:ss"),
    applySellerType: values.applySellerType,
    originPrice: values.originPrice * 100,
    currentPrice: values.currentPrice * 100,
    surfaceImg: values.cover.cover,
    logoUrl: values.cover.logo,
    detailImgList: values.detailImgList,
    stock: values.stock,
    limitNum: values.limitNum,
    relativeDay: values.relativeDay,
    usePromotionBoth: values.usePromotionBoth,
    couponItemType: values.couponItemType,
    num: values.num,
    ...{
      [ELE_ID[String(values.applySellerType)]]: ids
    },
    ticketDetailList: [
      {
        threshold: values.threshold * 100,
        price: values.price * 100,
        num: values.num
      }
    ],
    ...(values.id && { itemId: values.id }),
    ...(values.couponItemType === 2 && values.contributionType === '2' ? {
      budgetId: values.budgetId,
      elemeSubsidy: new BigNumber(values.elemeSubsidy).times(100).toNumber()
    } : {})

  };
  return params
}

export const parseData = (data) => {
  const values = data.couponItem
  const idType = parseInt(values.applySellerType, 10) === 1 ? values[ELE_ID[String(values.applySellerType)]] : (values[ELE_ID[String(values.applySellerType)]] || []).join('\n')
  const params = {

    itemId: values.itemId,
    status: values.status,
    superItemId: values.superItemId,
    couponItemType: values.couponItemType,
    brandName: values.brandName,
    applySellerType: values.applySellerType,
    originPrice: values.originPrice / 100,
    currentPrice: values.currentPrice / 100,
    surfaceImg: values.surfaceImg,
    stock: values.stock,
    detailImgList: values.detailImgList,
    limitNum: values.limitNum,
    relativeDay: values.relativeDay,
    usePromotionBoth: values.usePromotionBoth,
    cover: {
      cover: values.surfaceImg,
      logo: values.logoUrl
    },
    num: values.num,
    [`id${values.applySellerType}`]: idType,
    timeranger: [moment(values.saleStart), moment(values.saleEndDate)],
    budgetId: values.budgetId,
    elemeSubsidy: values.elemeSubsidy ? new BigNumber(values.elemeSubsidy).div(100).toNumber() : ''
  }

  values.ticketDetailList.forEach((item) => {
    params.threshold = item.threshold / 100
    params.price = item.price / 100
  })
  return params
}

export const ignoreValid = (error, { getValue }) => {
  let ignoredId = []
  const isEdit = !!getValue('isEdit')
  let errorList = { ...error }
  const applySellerType = getValue('applySellerType')
  for (let i = 1; i <= 3; i++) {
    if (i !== Number(applySellerType)) {
      ignoredId.push(`id${i}`)
    }
  }

  if (isEdit) {
    ignoredId = ['id1', 'id2', 'id3']
  }

  errorList = omit(errorList, ignoredId)
  console.log('ignoredId: ', ignoredId)
  console.log('errorList: ', errorList)

  return !Object.keys(errorList).length
}
