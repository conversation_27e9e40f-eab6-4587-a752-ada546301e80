import React, { Component, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Button, Icon, Message, Dialog, MenuButton, Balloon } from "@alifd/next";
import filterDataSource from "./filterDataSource";
import requestDataSource from "./requestDataSource";
import { Filter, Table } from "@/packages/table/FilterTable";
import { request } from "@/app/cardcoupon/lib/request";
import QrCode from "@/components/coupon/QrCode";
import { objQueryString } from "./lib/_tools";
import { pid } from "@/utils/clue";
import parseUrl from "@/app/cardcoupon/lib/request/parseUrl";
import TaskDialog from './lib/TaskDialog'
import { Notice } from './lib/Notice'

import "./launch.scss";

const CANNOT_DELETE = ["0", "1", "-9", "-2", "-3", "-5"];

const getPublishStatus = (status) => {
  switch (String(status)) {
    case "0":
    case "1":
      return ["下", -2];
    case "-2":
    case "-3":
    case "-5":
      return ["上", 0];
    default:
      return false;
  }
};

const renderInfo = (status, errInfo) => {
  switch (String(status)) {
    case "0":
      return "上架成功";
    case "-2":
      return "下架成功";
    default:
      return errInfo || "success";
  }
};

const deleteConfirm = ({ name, index, itemId, reload }) => {
  Dialog.confirm({
    title: "提示",
    content: `确认删除【${name}】吗？`,
    onOk: changeStatus(index, -1, itemId, reload),
    onCancel: () => console.log("cancel"),
  });
};

const qrConfirm = (record) => {
  Dialog.show({
    title: "浏览外卖券",
    content: <RenderQr bizId={record.itemId} />,
    footer: false,
    align: "cc cc",
    isFullScreen: true,
  });
};

@requestDataSource({
  action: {
    method: "POST",
    url: "mtop://mtop.ele.newretail.item.qRCodeServiceI.geneQRCode",
    mapRequest: (body, { props }) => {
      return {
        bizId: props.bizId,
        linkTypeCode: 1,
        linkRoutCode: 9,
      };
    },
  },

  mapStateToProps: (data) => {
    return {
      src: data.data.data.data,
    };
  },
})
class RenderQr extends Component {
  render() {
    return (
      <div className="qrcode-preview">
        <p>用淘宝扫描二维码预览外卖券</p>
        <QrCode src={this.props.src} />
      </div>
    );
  }
}

/**
 * 改变状态
 */
// TODO
function changeStatus(i, status, itemId, reload) {
  return async () => {
    try {
      await request({
        // url: "mtop://mtop.ele.newretail.item.coupon.editCouponItemStatus",
        url: "mtop://mtop.ele.newretail.item.virtual.editItemStatus",
        mapRequest: (query) => {
          return {
            status,
            couponItemId: itemId,
            ...query,
          };
        },
      }).then((res) => {
        if (Number(res.data.errCode) === 10000) {
          window.tracker.log({
            pid,
            code: status === -1 ? 13 : 12,
            msg: status === -1 ? "卡券商品删除" : "卡券商品上下架",
          });
          reload();
          Message.show({
            type: "success",
            content: renderInfo(status),
          });
        } else {
          throw new Error(res.data.errMessage);
        }
      });
    } catch (ex) {
      throw ex;
    }
  };
}

const columns = [
  {
    title: "组合卡券包",
    dataIndex: "title",
    width: "320px",
    cell: (value, index, record) => {
      const { logoUrl } = record;
      return (
        <div className="launch-op-picNtext">
          {/* <img src={logoUrl} className="launch-op-picNtext--pic" /> */}
          <div className="launch-op-picNtext--text">
            <span className="launch-op-picNtext--text--title">{value}</span>
            <span className="launch-op-picNtext--text--id">
              ID: {record.itemId}
            </span>
            {
              record.couponItemType === 2 && (
                record.superItemId ?
                  <p>已关联主品ID:<a
                    href={'/app/eleme-b-newretail/commodity_pc/kunlun-tmall-coupon/index.html#/update?type=update&id=' + record.superItemId}
                    rel="noreferrer"
                    target={'_blank'}
                  >
                    {record.superItemId}</a></p> :
                  <p>未关联主品</p>
              )
            }
          </div>
        </div>
      );
    },
  },
  {
    title: "价格（元）",
    dataIndex: "price",
    cell: (value, index, record) => {
      return (
        <div>
          <p>{record.currentPrice / 100}</p>
          <p
            style={{
              textDecoration: "line-through",
              color: "#cccccc",
            }}
          >
            {record.originPrice / 100}
          </p>
        </div>
      );
    },
  },
  {
    title: "卡券类型",
    dataIndex: "couponItemType",
    width: "120px",
    cell: (value) => <span>{
      {
        1: '普通卡券商品',
        2: '天猫专用品',
        3: '天猫超级单品',
      }[value]
    }</span>,
  },
  { title: "库存", dataIndex: "stock" },
  {
    title: "有效期",
    dataIndex: "relativeDay",
    cell: (value) => <span>购买后{value}天</span>,
  },
  {
    title: "状态",
    dataIndex: "status",
    cell: (value) => {
      const status = Number(value);
      const ON_SALE = [0, 1];
      const SOLD_OUT = [-2, -3, -5, -9];
      const DELETED = [-1, -4];
      let text = "";
      let color = "";
      if (ON_SALE.includes(status)) {
        text = "已上架";
        color = "#00CCAA";
      } else if (SOLD_OUT.includes(status)) {
        text = "已下架";
        color = "#FFBB33";
      } else if (DELETED.includes(status)) {
        text = "已删除";
        color = "#cccccc";
      }
      return (
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
          }}
        >
          <div
            style={{
              height: "6px",
              width: "6px",
              borderRadius: "50%",
              backgroundColor: color,
              marginRight: "4px",
            }}
          />
          <span>{text}</span>
        </div>
      );
    },
  },
  {
    title: "创建人",
    dataIndex: "operatorName",
  },
  {
    title: "操作",
    dataIndex: "ops",
    cell: (value, index, record, { reload, actionFunc }) => {
      const { status, stock } = record;
      const canPublish = getPublishStatus(status);
      const publishDom = <span
        className="launch-op-span"
        style={{
          color: record.superItemId ? '#cacaca' : ''
        }}
        onClick={() => {
          if (!record.superItemId) {
            String(stock) === "0" && canPublish[0] === "0"
              ? Message.error("商品库存为0，请修改库存后操作上架")
              : changeStatus(
              index,
              canPublish[1],
              record.itemId,
              reload
              )()
          }
        }}
      >{canPublish[0]}架</span>

      return (
        <div className="boreas-cell launch-op">
          {CANNOT_DELETE.includes(String(status)) && (
            <React.Fragment>
              <Link to={`/edit/${record.itemId}`} className="launch-op-span">
                编辑
              </Link>
              {/* <span
                onClick={() => qrConfirm(record)}
                className="launch-op-span"
              >预览</span> */}
              {
                canPublish && (
                  record.superItemId ?
                    <Balloon
                      trigger={publishDom}
                      closable={false}
                    >
                      请先将子品从主品（超级单品）中移除
                    </Balloon> : publishDom
                )
              }
              {
                <span
                  className="launch-op-span"
                  onClick={() =>
                    deleteConfirm({
                      name: record.title,
                      index,
                      itemId: record.itemId,
                      reload,
                    })
                  }
                >
                删除
              </span>
              }
            </React.Fragment>
          )}
          <Link to={`/log/${record.itemId}`} className="launch-op-span">
            日志
          </Link>
        </div>
      );
    },
  },
];

function Launch(props) {
  const { tableProps = {} } = props;
  const searchParmas = parseUrl(window.location.href).query || {};

  const handleEvent = (e, values, field) => {
    if (e === "reset") {
      field.setValues({
        shopType: 0,
        eleSupplierOrStoreId: "",
        itemId: "",
        couponItemType: ''
      });
      props.filterProps.search({
        shopType: 0,
        eleSupplierOrStoreId: undefined,
        itemId: undefined,
        couponItemType: undefined
      });
    }
  };

  const filterUISource = {
    "x-component": "Filter",
    labelTextAlign: "right",
    children: [
      {
        label: "适用门店",
        placeholder: "供应商id／散店id",
        name: "eleSupplierOrStoreId",
        "x-component": "Input",
        defaultValue: searchParmas.eleSupplierOrStoreId,
        addonBefore: {
          label: "店铺类型",
          name: "shopType",
          "x-component": "Select",
          defaultValue: searchParmas.shopType || 0,
          dataSource: [
            {
              label: "连锁店",
              value: 0,
            },
            {
              label: "散店/子门店",
              value: 1,
            },
          ],
        },
        style: {
          width: 350
        }
      },
      {
        label: "商品ID:",
        placeholder: "请输入",
        name: "itemId",
        "x-component": "Input",
        defaultValue: searchParmas.itemId,
        itemLayout: {
          labelCol: { span: 6 },
          wrapperCol: { span: 18 },
        },
        style: {
          width: 280
        }
      },
      {
        label: "卡券类型:",
        placeholder: "请输入",
        name: "couponItemType",
        "x-component": "Select",
        dataSource: [
          {
            label: "普通卡券商品",
            value: 1,
          },
          {
            label: "超级SKU专用子品",
            value: 2,
          },
        ],
        itemLayout: {
          labelCol: { span: 8 },
          wrapperCol: { span: 16 },
        },
        style: {
          width: 250
        },
        defaultValue: searchParmas.couponItemType || 1,
      }
    ],
  };

  return (
    <div>
      <p style={{ margin: "20px 0", fontSize: 20, color: "#333333" }}>
        卡券商品管理
      </p>
      <div className="main-content">
        <Filter
          uiSource={filterUISource}
          {...props.filterProps}
          onEvent={handleEvent}
          formItemLayout={{
            labelCol: { span: 6 },
            wrapperCol: { span: 18 },
          }}
          resetType="reset"
        />
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            marginBottom: "10px"
          }}
        >
          <Button
            onClick={() => {
              window.open(
                "https://fbi.alibaba-inc.com/dashboard/view/page.htm?id=395451&hiddenTitle=true&hiddenOwner=true&copyright=ture"
              );
            }}
            text
            type="primary"
            style={{ textDecoration: "underline" }}
          >
            查看全量卡券信息。点击前往FBI&gt;&gt;
          </Button>
          <div>
            <MenuButton type="primary" label="新增卡券商品" onSelect={keys => {}}>
              <MenuButton.Item>
                <Link to={`/create`}>
                  <Button text>
                    单个创建卡券
                  </Button>
                </Link>
              </MenuButton.Item>
              <MenuButton.Item>
                <Link to={`/batch/add`}>
                  <Button text>
                    批量创建卡券
                  </Button>
                </Link>
              </MenuButton.Item>
            </MenuButton>
            <Button
              type="normal"
              style={{ marginLeft: "10px" }}
              onClick={() => {
                TaskDialog.show('bd_coupon')
              }}
            >
              查询异步任务
            </Button>
          </div>
        </div>
        <Table
          className="boreas-table"
          {...props.tableProps}
          columns={columns}
          hasBorder={false}
          emptyContent="请使用适用门店、或卡券商品ID查询已创建的组合券包"
        />
      </div>
    </div>
  );
}

export default filterDataSource({
  requestImmediately: (query) => {
    // return true
    return !!(query.eleSupplierOrStoreId || query.itemId);
  },
  action: {
    // url: `mtop://mtop.ele.newretail.gateway.CouponItemReadServiceI.pageQueryItem`,
    url: `mtop://mtop.ele.newretail.item.virtual.pageQueryItem`,
    method: "GET",
    mapResponse: (json) => {
      return json.data;
    },
    mapRequest: (query) => {
      // return query
      const { ssoToken, ...others } = query;

      const { itemId } = others;
      if (itemId) {
        others.itemId = Number(itemId);
      }

      const params = {
        pageNum: others.pageNum,
        pageSize: others.pageSize,
        itemId: others.itemId,
        eleSupplierOrStoreId: others.eleSupplierOrStoreId,
        couponItemType: others.couponItemType
      };

      return {
        ssoToken,
        couponItemQuery: JSON.stringify({
          shopType: query.shopType || 0,
          ...params,
        }),
      };
    },
  },
})(Notice(Launch));
