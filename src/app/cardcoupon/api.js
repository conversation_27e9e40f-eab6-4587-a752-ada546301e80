import { request } from '@/app/cardcoupon/lib/request'
import { Message } from '@alifd/next'

// 创建异步任务
export const createAsyncJob = async options => {
    try {
        const res = await request(
            'mtop://mtop.ele.newretail.itemJob.create', {
                method: 'POST',
                body: options
            }
        )
        return res
    } catch (error) {
        Message.error(error)
        return error
    }
}

// 获取异步任务列表
export const getJobList = async(params) => {
    const res = await request(
        'mtop://mtop.ele.newretail.itemJob.getJobList', {
            body: params
        }
    )
    res.dataSource = res.data && res.data.data && res.data.data.jobDetailDTOList
    res.total = Number(res.data && res.data.data && res.data.data.total)
    return res
}

// 中断异步任务
export const jobStop = async params => {
    const res = await request('mtop://mtop.ele.newretail.itemJob.stop', {
        method: 'POST',
        body: params
    })

    return res
}

// 异步任务下载链接
export const downloadFile = async params => {
    const res = await request(
        'mtop://mtop.ele.newretail.itemJob.downloadFile', {
            body: params,
        }
    )

    return res.data && res.data.data
}

// 获取异步任务详情
export const getJobInfo = async params => {
    const res = await request(
            'mtop://mtop.ele.newretail.itemJob.getJobInfo', {
                body: params,
            }
        )
        // res.dataSource = res.jobDetailDTOList
        // res.total = Number(res.total)
        // return omit(res, 'jobDetailDTOList')
    return res.data && res.data.data
}

// 获取卡券配置
export const getCouponConfig = async() => {
    const { data } = await request(
        // 'mtop://mtop.ele.newretail.config.coupon', {}
        'mtop://mtop.ele.newretail.item.virtual.config.coupon', {}
    )
    if (data && data.data) {
        return JSON.parse(data.data)
    }
    return null
}

// 生成卡券图片
export const generatePicture = async params => {
    const res = await request(
            // 'mtop://mtop.ele.newretail.image.generatePicture',
            // TODO
            "mtop://mtop.ele.newretail.item.virtual.generatepicture", {
                body: {
                    generatePictureCmd: JSON.stringify(params)
                },
            }
        )
        // res.dataSource = res.jobDetailDTOList
        // res.total = Number(res.total)
        // return omit(res, 'jobDetailDTOList')
    return res.data && res.data.data
}