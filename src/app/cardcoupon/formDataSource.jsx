import React, { Component, useState, useEffect } from 'react'

import _ from 'lodash'
import { Field } from '@alifd/next'
import { request } from '@/app/cardcoupon/lib/request'

export default function formDataSource({
  submitAction,
  requestAction,
  onSuccess

} = {}) {

  return Target => {


    class Proxy extends Component {

      constructor() {
        super()

        this.field = new Field(this, {
          parseName: true,
          forceUpdate: true,
          onChange: (name, value) => {
            this.props.onFormChange && this.props.onFormChange(name, value)
          }
        })

        this.state = {
          inited: false,
          data: null
        }
        window.fld = this.field
      }

      submit = async () => {
        const values = this.field.getValues()
        const json = await request(submitAction, { method: 'POST', body: values }, { props: this.props })
        onSuccess && onSuccess()
        return json
      }

      initData() {
        if (requestAction && requestAction.isActive({ props: this.props })) {
          request(requestAction, {}, { props: this.props }).then(data => {

            this.setState({ inited: true, data }, () => {
             
              this.field.setValues(this.state.data)

              requestAction.afterInitData && requestAction.afterInitData(this.field, data)
            })
          })
        } else {
          this.setState({ inited: true })
        }
      }

      render() {
        const { inited } = this.state

        if (!inited) {
          this.initData()
          return null
        }

        return <Target {...this.props} submit={this.submit} field={this.field} />
      }

    }

    return Proxy
  }
}


