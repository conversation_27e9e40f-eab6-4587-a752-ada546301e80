.batch-tab {
  padding: 30px 0;
}
.batch-container {
  background: rgb(245, 247, 250);
  .export {
  }
  .next-tabs-content {
    padding: 0;
  }

  .fixed-footer {
    position: relative;
  }
}

.export {
  min-width: 920px;
}

.next-pagination-size-selector-dropdown .next-select-inner {
  min-width: 78px !important;
}

.batch-section {
  h2 {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #333;
    letter-spacing: 0;
    text-align: left;
    line-height: 14px;
  }
  &-cicle {
    width: 20px;
    background-color: #e4e5e6;
    height: 20px;
    display: inline-block;
    border-radius: 10px;
    line-height: 20px;
    text-align: center;
    margin-right: 15px;
    color: black;
  }
  &-hint {
    font-size: 14px;
    color: #999;
    line-height: 20px;

    &.has-idx {
      padding-left: 35px;
    }
  }
  margin-bottom: 20px;
  .next-transfer-panel-footer {
    display: none;
  }
  &-content {
    margin: 0 auto;
    width: 79%;
    min-width: 860px;
  }
  &-content-updateimage {
    margin: 0 auto;
    position: relative;
    min-height: 50vh;
    .upload-section {
      width: 79%;
      margin: 0 auto;
      min-width: 860px;
    }
  }
}

.next-upload-drag-err {
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: #ff2d4b;
  text-align: center;
  line-height: 16px;
}

.is-main-p {
  position: absolute;
  bottom: 0;
  left: 0;
}
.upload {
  &-describe {
    font-size: 14px;
    line-height: 14px;
    margin-bottom: 20px;
    font-family: PingFangSC-Regular;
    .next-btn-text.upload-download {
      vertical-align: text-bottom;
      color: #5166b9;
      text-decoration: underline;
    }
  }
}

.warn-message {
  line-height: 20px;
}
