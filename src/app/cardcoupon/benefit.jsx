import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Form, Button, Message, Dialog, Breadcrumb, Table, Pagination } from '@alifd/next';
import _ from 'lodash'
import Cardlayout from './lib/Cardlayout'
import Cardform from './lib/Cardform'
import CardrenderForm from './lib/CardrenderForm'
import { request } from '@/app/cardcoupon/lib/request'
import { originSource, formatData, parseData, ignoreValid } from './data'
import { getCouponConfig, generatePicture } from './api'
import formDataSource from './formDataSource'
import { pid } from '@/utils/clue'
import { lockScreen, releaseScreen } from '@/packages/lock'
import './benefit.less'

const FormItem = Form.Item;

class ErrorList extends Component {
  state = {
    current: 1,
    pageSize: 10
  };

  render() {
    const { current, pageSize } = this.state;
    const { dataSource } = this.props;
    const data = dataSource.slice((current - 1) * pageSize, current * pageSize);
    return (
      <div>
        <Table dataSource={data}>
          <Table.Column title="门店ID" dataIndex="id" width={150} />
          <Table.Column title="失败原因" dataIndex="reason" width={300} />
        </Table>
        <Pagination
          popupProps={{
            align: "bl tl"
          }}
          onChange={current => {
            console.log("current: ", current);
            this.setState({ current });
          }}
          onPageSizeChange={pageSize => {
            console.log("pageSize: ", pageSize);
            try {
              this.setState({ pageSize });
            } catch (err) {
              console.error(err);
            }
          }}
          total={dataSource.length}
          pageSizeSelector="dropdown"
          {...this.state}
        />
      </div>
    );
  }
}

@formDataSource({
  requestAction: {
    // url: `mtop://mtop.ele.newretail.gateway.CouponItemReadServiceI.queryItem?itemId={props.match.params.id}`,
    url: `mtop://mtop.ele.newretail.item.virtual.queryItem?itemId={props.match.params.id}`,
    isActive: ({ props }) => {
      return !!props.match.params.id
    },
    afterInitData: async (field, data) => {
      const { setValues, getValues } = field;
      const { id1, cover, id2, id3, budgetId, elemeSubsidy } = getValues();
      const getLogosParams = id1 ? {
        eleSupplierId: id1
      } : {
        eleWidList: (id2 || id3).split('\n').filter(x => x)
      }
      // TODO
      // let tasks = [request('mtop://mtop.ele.newretail.item.coupon.queryStorePicUrlList', {
      let tasks = [request('mtop://mtop.ele.newretail.item.virtual.queryStorePicUrlList', {

        query: {
          storePicUrlQuery: JSON.stringify(getLogosParams)
        }
      })]
      if (id1) {
        tasks.push(request(
          "mtop://mtop.ele.newretail.store.queryBdCanOperateSellerAndStore",
          {
            query: {
              bdSellerStoreQuery: JSON.stringify({
                eleSupplierId: id1,
                applySellerType: 1,
                isAdmin: true
              })
            }
          }
        ).then(res => {
          const { data } = res.data;
          if (data && data.length) {
            const { storeInfoList } = data[0]
            return storeInfoList && storeInfoList.length
          }
          return 0
        }))
      } else {
        const idList = (id2 || id3).split('\n').filter(x => x)
        tasks.push(Promise.resolve(idList.length))
      }
      const [res, storeInfoListSize] = await Promise.all(tasks)
      let logos = []
      try {
        logos = res.data.data.map(logo => `https://img.alicdn.com/${logo}`)
      } catch (err) {
        logos = []
      }
      logos = logos.filter(logo => logo !== cover.logo);
      setValues({
        chainHints: id1,
        subShopHints: id2 && id2.replace(/\n/g, ', '),
        storeHints: id3 && id3.replace(/\n/g, ', '),
        id1: '',
        id2: '',
        id3: '',
        logos,
        customlogo: cover.logo,
        storeInfoListSize,
        contributionType: budgetId ? '2' : '1',
        budgetId,
        elemeSubsidy,
      })
    },
    mapResponse: (res) => {
      return parseData({ couponItem: res.data.data })
    }
  },
  submitAction: {
    url: ``
  }
})
@CardrenderForm(originSource)
class CouponBenefit extends React.Component {

  state = {
    visible: false,
    status: null,
  }

  async componentDidMount() {
    if (this.props.match.params.id && this.props.match.params.id.length > 0) {
      this.props.field.setValue('isEdit', 1)
    } else {
      this.props.field.setValue('isEdit', 0)
    }
    try {
      const config = await getCouponConfig()
      console.warn(config)
      this.props.field.setValue('couponConfig', config)
    } catch (error) {
      Message.error('券规则获取失败，请稍后重试')
      console.error(error)
    }
  }

  submit = (status) => {
    this.props.field.validate(async (error, values) => {
      const isValid = ignoreValid(error, this.props.field)
      if (isValid) {
        lockScreen('若门店数量较多，创建时间较长，请稍等...')
        this.setState({
          status
        })
        try {
          const { getValue } = this.props.field
          const num = Number(getValue('num'), 10)
          const price = Number(getValue('price'), 10)
          const threshold = Number(getValue('threshold'), 10)
          const imgUrl = await generatePicture({
            ticketPrice: price,
            ticketNum: num,
            ticketThreshold: threshold
          })
          this.coverChange(imgUrl)
        } catch (error) {
          Message.error('生成卡券图失败，请稍后再试')
          releaseScreen()
        }
        releaseScreen()
      }
    })
  }

  coverChange = async (url) => {
    this.props.field.setValue('cover', { ...this.props.field.getValue('cover'), cover: url })
    this.surfacePreview(url)
  }

  onSendApi = async (params) => {
    // TODO
    const path = `mtop://mtop.ele.newretail.item.virtual.${this.props.match.params.id ? 'edit' : 'add'}Item`
    this.setState({
      status: null
    })
    // 修改时不能传status，问题提出人：xuyulong
    if (this.props.match.params.id) {
      delete params.status
    }
    let res = await request(path, {
      query: { couponItem: JSON.stringify(params) }
    })
    const participateErrorDtoList = _.get(res, 'data.data.participateErrorDtoList', [])
    if (participateErrorDtoList.length) {
      const dataSource = []
      participateErrorDtoList.forEach(part => {
        const { errorParticipateReason: reason, failParticipateId } = part
        failParticipateId.forEach(id => {
          dataSource.push({ id, reason });
        })
      })
      const content = <ErrorList dataSource={dataSource} />
      Dialog.show({
        title: '以下商户绑定失败',
        content,
        okProps: { children: '知道了' },
        onOk: () => {
          const data = _.get(res, 'data.data', [])
          this.props.history.push(
            `/?pageNum=1&pageSize=10&shopType=0&itemId=${data.itemId}`
          )
        },
        footerActions: ['ok']
      })
      return false
    }
    return res
  }

  surfacePreview = (src) => {
    Dialog.show({
      title: "封面图预览",
      content: (
        <section>
          <img src={src} style={{ width: "100%" }} />
        </section>
      ),
      shouldUpdatePosition: true,
      onCancel: () => {
        this.setState({
          status: null
        })
      },
      onClose: () => {
        this.setState({
          status: null
        })
      },
      onOk: async () => {
        try {
          console.log('----------发送数据----------')
          console.log('----------发送数据----------')
          console.log('----------发送数据----------')
          console.log(this.props.field.getValues())
          console.log(formatData({
            ...this.props.field.getValues(),
            status: this.state.status,
            id: this.props.match.params.id
          }))
          const res = await this.onSendApi(
            formatData({
              ...this.props.field.getValues(),
              status: this.state.status,
              id: this.props.match.params.id
            })
          );
          if (res) {
            const data = res.data.data
            this.props.history.push(
              `/?pageNum=1&pageSize=10&shopType=0&itemId=${data.itemId}`
            )
          }
        } catch (err) {
          Message.error('请求失败')
          window.tracker.log({
            pid,
            code: this.props.match.params.id ? 15 : 14,
            msg: this.props.match.params.id ? '编辑卡券商品' : '新增卡券商品'
          });
        }
        this.setState({
          status: null
        })
      },
      okProps: { children: "确认" },
      cancelProps: { children: "修改" },
      footerActions: ["cancel", "ok"]
    });
  }

  render() {
    const { history, match } = this.props
    const id = match.params.id || ''
    const { getValue } = this.props.field
    const isEdit = !!getValue('isEdit')
    const { status } = this.state
    const breadLabel = window.location.href.includes('create') ? "创建卡券商品" : "编辑卡券商品"

    return (
      <div style={{ padding: 24, paddingBottom: 0 }}>
        <Breadcrumb>
          <Breadcrumb.Item
            link="javascript:void(0);"
            onClick={() => {
              history.push("/");
            }}
          >
            卡券商品管理
          </Breadcrumb.Item>
          <Breadcrumb.Item link="javascript:void(0);">
            {breadLabel}
          </Breadcrumb.Item>
        </Breadcrumb>
        <div
          style={{
            backgroundColor: "#ffffff",
            paddingTop: 24,
            paddingBottom: 40,
            marginTop: 10,
            position: 'relative'
          }}
        >
          {this.props.uiSource.map((k, i) => {
            return (
              <Cardlayout key={i} title={k.title} type={k.type}>
                <Cardform fields={k.children} />
              </Cardlayout>
            );
          })}
          <FormItem wrapperCol={{ offset: 8 }}>
            <Button
              type="primary"
              onClick={() => {
                this.submit(-2);
              }}
              style={{ margin: "0 10px" }}
            >
              保存
            </Button>
            {isEdit ? null : (
              <Form.Submit
                validate
                type="primary"
                onClick={() => {
                  this.submit(0);
                }}
                style={{ margin: "0 10px", width: "100px" }}
              >
                保存并上架
              </Form.Submit>
            )}
            <Link to={`/?pageNum=1&pageSize=10${id ? `&itemId=${id}` : ''}`}>
              <Button>取消</Button>
            </Link>
          </FormItem>
        </div>
      </div>
    );
  }
}

export default CouponBenefit
