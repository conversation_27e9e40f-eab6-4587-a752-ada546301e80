import React from 'react'
import { Breadcrumb } from '@alifd/next'
import filterDataSource from './filterDataSource'
import { Filter, Table } from '@/packages/table/FilterTable'
// import MyBreadcrumb from '@/components/breadcrumb/index'

const breads = [
  {
    // path: '/cardcoupon',
    breadcrumbName: '卡券商品管理'
  },
  {
    // path: '/cardcoupon/log',
    breadcrumbName: '日志'
  }
]

const columns = [
  { title: '操作类型', dataIndex: 'opType' },
  {
    title: '操作描述', dataIndex: 'opContent', width: 500, cell: (value) => (<div dangerouslySetInnerHTML={{ __html: value }} />)
  },
  { title: '操作时间', dataIndex: 'opTime' },
  {
    title: '操作人', dataIndex: 'opUser'
  }

]

function Log(props) {
  return <div>
    <div className='breadcrumb-wrap'>
      <Breadcrumb style={{ margin: '16px 0' }}>
        <Breadcrumb.Item>卡券商品管理</Breadcrumb.Item>
        <Breadcrumb.Item>日志</Breadcrumb.Item>
      </Breadcrumb>
    </div>
    <Table {...props.tableProps} columns={columns} />
  </div>
}


export default filterDataSource({
  action: {
    url: 'mtop://mtop.ele.newretail.item.virtual.queryOpLog',
    mapResponse: json => {
      return json.data
    },
    mapRequest: (json, { props }) => {
      const { pageNum: pageNumber, pageSize } = json
      const { match } = props
      const { id: bizId } = match.params
      const option = {
        pageNumber,
        pageSize,
        bizId,
        opType: 24
      }
      return { 
        req: JSON.stringify(option)
      }
    }
  },
})(Log)




