import React, { Component, Suspense, lazy } from 'react'
import { withRouter, Route, Switch } from 'react-router-dom'
// import loadable from '@loadable/component'
import { lazy as hotLazy } from 'react-imported-component'
import Benefit from './benefit'
import Launch from './launch'
import Log from './log'
import Batch from './batch'
import newLog from './newLog'
import { buildEnv } from '@/packages/env'
import { aesInstall } from '../../aes'

// import Loading from '../../common/components/loading'

const isProd = process.env.NODE_ENV === 'production'

// function lazyImport(path) {
//   if (isProd) return lazy(() => import(/* webpackChunkName: "[request]" */ `${path}`))
//   return hotLazy(() => import(/* webpackChunkName: "[request]" */ `${path}`))
// }
// const LoadableComponent = loadable(() => import('./Dashboard'), {
//   // fallback: Loading,
// })

/*
  router 的定义需按照多级目录 path 的包含规则
  如：
  一级目录: /marketing
  二级目录：/marketing/coupon
  三级目录：/marketing/coupon/complete/aaa
*/
const routerList = [
  {
    // 权益池
    subRouterPath: '',
    // subComponent: Benefit,
    routerItems: [
      {
        path: '/',
        component: Launch,
        breadcrumbName: '卡券商品管理'
      },
      {
        path: '/edit/:id(\\d+)',
        component: Benefit,
        breadcrumbName: '编辑卡券商品'
      },
      {
        path: '/launch',
        component: Launch,
        breadcrumbName: '卡券商品管理'
      },
      {
        path: '/create',
        component: Benefit,
        breadcrumbName: '创建卡券商品'
      },
      {
        path: '/log/:id(\\d+)',
        component: newLog,
        breadcrumbName: '操作日志'
      },
      {
        path: '/batch/:scene',
        component: Batch,
        breadcrumbName: '批量操作'
      }
    ],
  },
]

@withRouter
class App extends Component {

  constructor(props) {
    super(props)

    const env = buildEnv()

    if(env === 'prod') {

    console.log('###aesInstall',)
      aesInstall()
    }
  }


  render() {
    return (
      <div className="router-wrap">
        {routerList.map((router) => {
          const { subRouterPath, subComponent, routerItems } = router
          if (!routerItems || routerItems.length === 0) {
            return <Route key={subRouterPath} exact path={subRouterPath} component={subComponent} />
          }
          return routerItems.map(({ path, component }) => (
            <Route key={path} exact path={`${subRouterPath}${path}`} component={component} />
          ))
        })}
      </div>
    )
  }
}

export default App
