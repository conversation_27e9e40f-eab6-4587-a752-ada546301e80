function testNum(initValue, rules) {
  let value = initValue
  const { min, max, precision, closed } = rules
  if (!value) {
    return true
  }
  // eslint-disable-next-line no-restricted-globals
  if (isNaN(value)) {
    console.log('nan')
    return false
  }
  const numValue = Number(value)
  console.log(numValue * 1e3)
  console.log(min * 1e3)
  if (!closed && (numValue * 1e3 < min * 1e3 || numValue * 1e3 >= max * 1e3)) {
    console.log('overload')
    return false
  } else if (closed && (numValue * 1e3 < min * 1e3 || numValue * 1e3 > max * 1e3)) {
    console.log('overload')
    return false
  }
  value += ''
  const matches = typeof value === 'string' ? value.match(/\.\d*/) : undefined
  if (
    Array.isArray(matches) &&
    (matches[0].length - 1 > precision || matches[0].length === 1)
  ) {
    return false
  }
  return true
}
export default testNum
