.newplan-title {
  padding: 10px 0 0 20px;
}

.plan-log-fitertable {
  width: 900px;
  margin: 0 auto;
  font-size: 16px;
  .next-table-header {
    height: 80px;
  }
  .next-table-row {
    height: 80px;
  }
}

.plan-log {
  .boreas-table {
    width: 900px;
    margin: 0 auto;
  }
}

.boreas-table {
  .next-table-cell-wrapper {
    padding: 31px 0px 31px 20px !important;
  }
}
.next-table .next-table-cell {
  // padding-right: 40px !important;
}

.plan-form-table.next-table .next-table-cell {
  padding-right: 0px !important;
}

.plan-detail {
  .boreas-form-title {
    width: 900px;
    margin: 0 auto;
    .next-form-item-label {
      text-align: left;
    }
  }
  &-header {
    span {
      color: #999999;
    }
  }
  &-block {
    width: 900px;
    margin: 0 auto;
    font-size: 14px;
    padding: 30px 44px;
    border: 1px solid #ebebeb;
    .next-table {
      width: 513px;
      border: 1px solid #ececec;
      // padding-top: 10px;
      display: inline-block;
    }
    span {
      color: #999999;
      padding-right: 11px;
      vertical-align: top;
    }
  }
}

.plan-detail-content {
  position: relative;
  &-editor {
    position: absolute;
    right: 88px;
    top: 30px;
    font-size: 16px;
  }
}

.pool-info {
  display: flex;
  flex-direction: row;
  div {
    margin-right: 40px;
  }
}

.benefit-detail .next-btn {
  margin-right: 20px;
}

.boreas-dialog {
  width: 50%;
  .next-form-item-label {
    text-align: left;
  }
}
.newplan-title .next-tabs .next-tabs-tab {
  margin-right: 15px;
  .next-tabs-tab-inner {
    font-size: 16px;
    padding: 12px 0;
  }
}

.tip-icon {
  vertical-align: middle;
  line-height: 20px;
  i {
    margin-left: 5px;
    color: #908c8c;
  }
}

.next-balloon-medium {
  position: relative !important;
}

.tip-text {
  position: relative;
  .next-input textarea {
    text-indent: 60px;
    height: 105px;
  }
  .top {
    transform: translateY(25px);
    display: inline-block;
  }
  .bottom {
    transform: translate(8px, -21px);
    display: inline-block;
    color: #999999;
  }
  .error {
    transform: translate(-60px, 5px);
    display: inline-block;
    color: #FF3000;
    font-size: 12px;
  }
}

.help {
  color: #9c9c9c;
  font-size: 10px;
  padding-top: 6px;
}
