import React from 'react'
import Routers from '@/packages/routers'
// 权益
import BenefitPoolList from './benefit-pool-list'
import BenefitDetail from './benefit-detail'
// 投放
import Plan from './plan'
import OpPlan from './op-plan'
import NewPlan from './new-plan'
// import PlanLog from './plan-log'
import PlanDetail from './plan-detail'
import './style.scss'

/*
  router 的定义需按照多级目录 path 的包含规则
  如：
  一级目录: /marketing
  二级目录：/marketing/coupon
  三级目录：/marketing/coupon/complete/aaa
*/
const routerList = [
  {
    // 权益池
    subRouterPath: '/benefit',
    // subComponent: Benefit,
    routerItems: [
      // {
      //   path: '/',
      //   component: BenefitPoolList,
      //   breadcrumbName: '权益池列表'
      // },
      {
        path: '/list',
        component: BenefitPoolList,
        breadcrumbName: '权益池列表'
      },
      {
        path: '/detail',
        component: BenefitDetail,
        breadcrumbName: '权益池详情'
      }
    ]
  },
  {
    // 投放管理
    subRouterPath: '/plan',
    // subComponent: Plan,
    routerItems: [
      {
        path: '/',
        component: Plan,
        breadcrumbName: '投放计划'
      },
      {
        path: '/op',
        component: OpPlan,
        breadcrumbName: '编辑投放计划'
      },
      {
        path: '/new',
        component: NewPlan,
        breadcrumbName: '新建投放计划'
      },
      // {
      //   path: '/log',
      //   component: PlanLog,
      // breadcrumbName: '投放计划日志'
      // },
      {
        path: '/detail',
        component: PlanDetail,
        breadcrumbName: '投放计划详情'
      }
    ]
  },
]

const breadcrumbMap = {
  // '/': '权益池列表',
  '/benefit': '权益池列表',
  '/benefit/list': '权益池管理',
  '/benefit/detail': '权益池详情',
  '/plan': '投放计划',
  '/plan/list': '投放计划管理',
  '/plan/op': '编辑投放计划',
  '/plan/new': '新建投放计划',
  '/plan/detail': '投放计划详情'
}

function App() {
  return <Routers routerList={routerList} redirectPath="/benefit/list" breadcrumbMap={breadcrumbMap} />
}

export default App
