import React, { Component } from 'react'
import { Message, Dialog, Form, Input, NumberPicker, Select, Radio, Button, Grid, Balloon, Icon, Upload } from '@alifd/next'
import formDataSource from '@/source/formDataSource'
import fusionInputFix from '@/utils/fusionInputFix'
import BatchResult from './BatchResult'
import { request } from '@/packages/request'
import { config } from '@/api/config'

import './style.scss'

const FormItem = Form.Item
const UploadDrag = Upload.Dragger
const XInput = fusionInputFix()(Input)
const RadioGroup = Radio.Group
const Tooltip = Balloon.Tooltip;

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 }
}

class RangeInput extends React.Component {
  render() {
    const value = this.props.value || {}
    const commonProps = { style: { width: 150 }, step: 0.01, precision: 2, hasTrigger: false }
    return (
      <div>
        <NumberPicker value={value.start}
          {...commonProps}
          placeholder="请输入最小值"
          onChange={start => {
            this.props.onChange({ ...value, start })
          }} />
        &nbsp;-&nbsp;
        <NumberPicker value={value.end}
          {...commonProps}
          placeholder="请输入最大值"
          onChange={end => {
            this.props.onChange({ ...value, end })
          }} />
      </div>
    )
  }
}

function checkRange(rule, value) {
  if (!value) {
    return Promise.reject("请输入数值")
  }
  if (!value.start && value.start !== 0) {
    return Promise.reject("请输入最小值")
  }
  if (!value.end && value.end !== 0) {
    return Promise.reject("请输入最大值")
  }
  if (value.start > value.end) {
    return Promise.reject("最小值不应该超过最大值")
  }
  return Promise.resolve(null)
}


function onClick(fileList) {
  console.log('click callback : ', fileList);
}


// function handleUpload(fileList) {
//   console.log('handleUpload', fileList)

//   request(
//     {
//       url: `benefit+daily://rights/add/file`,
//       method: 'POST'
//     },
//     {
//       body: {
//         fileName: 'abc',
//         fileUrl: '13213'
//       }
//     }
//   ).then(() => {
//     Message.success('操作成功')

//   })
// }

const CreateBenefit = formDataSource({
  submitAction: {
    url: `benefit://rights/add?poolId={query.poolId}`,
    mapRequest: (query, obj) => {
      return {
        poolId: obj.query.poolId,
        sourceCode: query.sourceCode,
        peopleRangeType: query.peopleRangeType,
        sourceImportId: query.sourceCode === 'ALSC_ACT' && query.actCode ? query.sourceImportId + '_' + query.actCode : query.sourceImportId,
        districtRangeType: query.districtRangeType,
        actCode: query.sourceCode === 'ALSC_ACT' ? query.actCode : undefined,
        redPacketAttr: (query.sourceCode === 'HAIWANG_ACT' || query.sourceCode === 'ALSC_ACT') ? {
          amountType: 2,
          limit: query.limit.start,
          maxLimit: query.limit.end,
          amount: query.amount.start,
          maxAmount: query.amount.end,
        } : undefined,
        ...+query.peopleRangeType === 5 && { crmGroupIds: query.crmGroupIds }
        // operationCtxDTO: {
        //   appName: '权益B',
        //   requestId: 11111,
        //   operatorDTO: {
        //     operatorId: 'oper123',
        //     operatorName: '张大拿'
        //   }
        // }
      }
    }
  }
})(
  ({
    visible,
    setVisible,
    poolId,
    addId,
    createBenefitLoading,
    setCreateBenefitLoading,
    onCreated,
    file,
    updateFile,
    resultVisible,
    resultConfirm,
    checkFile,
    setResultShow,
    setResultConfirm,
    field,
    submit,
    reloadAllList
  }) => {

    // 追加传参descrption
    function handledata() {
      console.log('handledata')
      return {
        description: `批量添加权益模版_${poolId}.xlsx`,
        // poolId: poolId || ''
      }
    }

    function beforeUpload(info) {
      let fileName = info.name
      let fileArray = fileName.split('_')
      let poolIdStr = fileArray && fileArray.length > 1 && fileArray[fileArray.length - 1]
      if (!poolIdStr || (poolIdStr && poolIdStr.indexOf(poolId) < 0)) {
        console.log('文件名中未包含当前的权益池id或格式不正确，请移除文件并重新命名')
        Message.error('文件名中无当前导入的权益池id，请检查文件名中是否包含“_权益池id”')
        return false;
      }
    }

    function onError(file, value) {
      if (file && file.response) {
        if (file.response.code !== '200') {
          Message.error(file.response.msg)
          setResultShow(false)
          setResultConfirm(false)
          updateFile([])
        } else {
          console.log(file.response.msg)
        }
      }
    }

    function onSuccess(file, value) {
      if (file && file.response) {
        if (file.response.code === '200') {
          if (file.response.data && file.response.data.errorCode && file.response.data.errorCode !== null) {
            Message.error(file.response.data.errorMessage || '系统错误')
            updateFile(file)
          } else {
            Message.success('上传成功，上传状态请下载结果查看')
            setResultShow(true)
            setResultConfirm(true)
            updateFile(file)
          }
        } else {
          Message.error(file.response.msg)
          setResultShow(true)
        }

      }
    }

    function onRemove() {
      setResultShow(false)
      setResultConfirm(false)
      updateFile([])
    }

    const showAmountAndLimit = (
      field.getValue('sourceCode') === 'HAIWANG_ACT' ||
      field.getValue('sourceCode') === 'ALSC_ACT'
    )

    return (
      <Dialog
        title="添加权益"
        okProps={{
          loading: createBenefitLoading,
          children: `${+field.getValue('addType') === 1 ? '添加' : '确定'}`
        }}
        footerActions={['cancel', 'ok']}
        onOk={() => {
          let addType = field.getValue('addType')
          if (+addType === 1) { // 手动添加
            const r = field.validate((err, values) => {
              if (err) {
                return
              }
              setCreateBenefitLoading(true)
              submit()
                .then(() => {
                  Message.success('新建成功')
                  setVisible(false)
                  reloadAllList()
                  setTimeout(() => {
                    setCreateBenefitLoading(false)
                  }, 500)
                })
                .catch(() => {
                  setTimeout(() => {
                    setCreateBenefitLoading(false)
                  }, 500)
                })
            })
          } else {
            if (!resultVisible) {
              file ? Message.success('上传文件有误！') : Message.success('请先上传文件！')
              return
            }
            setVisible(false)
            reloadAllList()
            setTimeout(() => {
              setCreateBenefitLoading(false)
              setResultShow(false)
            }, 500)
          }

        }}
        style={{
          width: '676px'
        }}
        onCancel={() => {
          setVisible(false)
          setCreateBenefitLoading(false)
        }}
        onClose={() => {
          setVisible(false)
          setCreateBenefitLoading(false)
        }}
        visible={visible}
        shouldUpdatePosition
      >
        <div style={{ marginTop: '20px' }} className="bg-fff">
          <Form field={field}>
            {/* 年货节 */}
            <FormItem label="操作方式:" {...formItemLayout}>
              <RadioGroup
                dataSource={[
                  {
                    label: '手动添加',
                    value: 1
                  },
                  {
                    label: '上传excel',
                    value: 2
                  }
                ]}
                {...field.init('addType', {
                  initValue: null
                })}
              />
            </FormItem>
            <div style={{ display: +field.getValue('addType') === 1 ? '' : 'none' }}>
              <FormItem label="添加方式:" {...formItemLayout}>
                <Select
                  style={{ width: '100%' }}
                  placeholder="请选择添加方式"
                  //  通过品牌活动添加  -- 品牌单品券  BRAND_ACT
                  // 通过昆仑用户营销添加  -- crm平台红包 KUNLUN_ACT
                  // 通过昆仑营销活动添加  -- 店铺券  MARKET_ACT
                  // 通过饿百营销活动添加  -- 饿百单品券  EBAI_ACT
                  dataSource={[
                    {
                      label: '通过品牌活动添加',
                      value: 'BRAND_ACT'
                    },
                    {
                      label: '通过昆仑用户营销添加',
                      value: 'KUNLUN_ACT'
                    },
                    {
                      label: '通过昆仑营销活动添加',
                      value: 'MARKET_ACT'
                    },
                    {
                      label: '通过新昆仑用户营销添加',
                      value: 'NEW_KUNLUN_ACT',
                    },
                    {
                      label: '通过饿百营销活动添加',
                      value: 'EBAI_ACT'
                    },
                    {
                      label: '海王权益',
                      value: 'HAIWANG_ACT'
                    }, {
                      label: '百川',
                      value: 'ALSC_ACT'
                    }
                  ]}
                  {...field.init('sourceCode', {
                    rules: {
                      required: true,
                      message: '请选择添加方式'
                    }
                  })}
                />
              </FormItem>

              {showAmountAndLimit &&
                <FormItem label="门槛区间:" {...formItemLayout}>
                  <RangeInput {...field.init('limit', {
                    rules: [{
                      required: true,
                      message: '请输入门槛区间',
                    }, {
                      validator: checkRange
                    }]
                  })} />
                </FormItem>}
              {field.getValue('sourceCode') === 'HAIWANG_ACT' &&
                <FormItem label="   " {...formItemLayout}>
                  <div style={{ color: 'red', fontSize: 12 }}>门槛区间和面额区间只用于C端展示，真实的面额以海王配置的算法红包为主</div>
                </FormItem>}
              {showAmountAndLimit &&
                <FormItem label="面额区间:" {...formItemLayout}>
                  <RangeInput {...field.init('amount', {
                    rules: [{
                      required: true,
                      message: '请输入面额区间'
                    }, {
                      validator: checkRange
                    }]
                  })} />
                </FormItem>}

              <FormItem label="来源活动ID:" {...formItemLayout}>
                <XInput
                  type={field.getValue('sourceCode') === 'HAIWANG_ACT' || field.getValue('sourceCode') === 'ALSC_ACT' ? undefined : "Number"}
                  placeholder={field.getValue('sourceCode') === 'HAIWANG_ACT' ? "请填写海王渠道号" : "请输入来源活动ID"}
                  {...field.init('sourceImportId', {
                    rules: {
                      required: true,
                      message: '请填写来源活动ID'
                    }
                  })}
                />
              </FormItem>
              {field.getValue('sourceCode') === 'ALSC_ACT' &&
                <FormItem label="百川活动码:" {...formItemLayout}>
                  <Input
                    placeholder="请输入百川活动码"
                    {...field.init('actCode')}
                  />
                </FormItem>}
              <FormItem label="人群管理:" {...formItemLayout}>
                <RadioGroup
                  dataSource={[
                    {
                      label: '不限',
                      value: 1
                    },
                    {
                      label: '新零售新客',
                      value: 2
                    },
                    {
                      label: '新零售流失老客',
                      value: 3
                    },
                    {
                      label: '新零售活跃老客',
                      value: 4
                    },
                    {
                      label: <span className="tip-icon">离线人群标签<Tooltip trigger={<Icon type="prompt" size='xs' />} align="r">实时标签⽆法用于离线直塞</Tooltip></span>,
                      value: 5
                    },
                    {
                      label: '平台新客',
                      value: 6
                    },
                    {
                      label: '新零售新客(三新)',
                      value: 7
                    }
                  ]}
                  {...field.init('peopleRangeType', {
                    initValue: 1
                  })}
                />
              </FormItem>
              {
                +field.getValue('peopleRangeType') === 5 &&
                <FormItem label=" " {...formItemLayout}>
                  <XInput
                    type="Number"
                    placeholder="填写人群id"
                    addonBefore={(
                      <Select
                        autoWidth={false}
                        dataSource={[{
                          label: '饿了么人群',
                          value: 1
                        }, {
                          label: '集团人群',
                          value: 2
                        }]} />
                    )}
                    {...field.init('crmGroupIds', {
                      rules: {
                        required: true,
                        message: '请填写人群id'
                      }
                    })}
                  />
                  <div className="help">提示：饿了么人群请填写平台CRM的人群标签id，集团人群请填写品牌人群id</div>
                </FormItem>
              }
              <FormItem label="地区限制:" {...formItemLayout}>
                <RadioGroup
                  dataSource={[
                    {
                      label: '不限制',
                      value: 1
                    },
                    {
                      label: '限直营地区',
                      value: 2
                    },
                    {
                      label: '非直营地区',
                      value: 3
                    }
                  ]}
                  {...field.init('districtRangeType', {
                    initValue: 1,
                    rules: {
                      required: true,
                      message: '请选择地区限制'
                    }
                  })}
                />
              </FormItem>

            </div>
            <div style={{ display: +field.getValue('addType') === 2 ? '' : 'none', padding: '16px' }}>
              <p>请先
                <a href={`${config.BENEFIT_PATH}/rights/add/template/download`}>
                  下载Excel模板
                </a>，填写完成后上传到系统。<br />
                为防止误操作，文件名必须包含当前所属权益池ID，示例：批量添加权益模板_{poolId}.xlsx
              </p>
              <UploadDrag
                listType="text"
                action={`${config.BENEFIT_PATH}/rights/add/file`}
                accept=".xlsx"
                title="点击或者拖动Excel文件到虚线框内上传"
                method="post"
                limit={1}
                beforeUpload={beforeUpload}
                data={handledata}
                onRemove={onRemove}
                onSuccess={onSuccess}
                onError={onError}
                onClick={onClick}
              >
                <div className="next-upload-drag">
                  <p className="next-upload-drag-icon"><Icon type="upload" /></p>
                  <p className="next-upload-drag-text">点击或者拖动Excel到虚线框内上传</p>
                  <p className="next-upload-drag-hint">仅支持扩展名：.xlsx且单次上传一个文件</p>
                </div>
              </UploadDrag>

              <div style={{ display: resultVisible ? '' : 'none' }}>
                <p>
                  <a target="_blank"
                    rel="noopener noreferrer"
                    onClick={() => checkFile(file.name)}
                  // href={`${config.BENEFIT_PATH}/rights/add/file/download?fileName=${file.name}`}
                  >
                    <Icon type="download" size="small" />下载结果
                  </a>
                </p>
              </div>
            </div>

            {/* <FormItem label="地区限制:" {...formItemLayout}>
              <RadioGroup
                dataSource={[
                  {
                    label: '不限制',
                    value: 1
                  },
                  {
                    label: '限直营地区',
                    value: 2
                  },
                  {
                    label: '非直营地区',
                    value: 3
                  }
                ]}
                {...field.init('districtRangeType', {
                  initValue: 1,
                  rules: {
                    required: true,
                    message: '请选择地区限制'
                  }
                })}
              />
            </FormItem> */}
          </Form>
        </div>
        {/* 查看结果 */}
        <BatchResult
          visible={resultConfirm}
          checkFile={checkFile}
          setVisible={setResultConfirm}
          file={file}
          poolId={poolId}
        />
      </Dialog>
    )
  }
)

export default CreateBenefit
