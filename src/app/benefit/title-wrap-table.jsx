import React from 'react'
import { Input, Radio, Message, Form, Table, Button, Dialog, Field } from '@alifd/next'
import PropTypes from 'prop-types'

import MyBreadcrumb from '@/components/breadcrumb'
import PlanHeader from '@/components/header/index'


import Title from '@/components/title'

const FormItem = Form.Item
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 }
}
function TitleWrapTable({ ...props }) {
  // console.log(props)
  return (
    <Title title={props.title}>
      <FormItem label={props.opLabel} {...formItemLayout}>
        <div>
          <div
            className="color-link-1 next-form-text-align cursor-pointer"
            onClick={() => props.onChange(props.changeValue)}
          >
            {props.btnText}
          </div>
          {props.tableSource.length ? (
            <Table
              dataSource={props.tableSource}
              className="plan-form-table"
              style={{ width: props.tableWidth, border: '1px solid #ECECEC' }}
            >
              {props.uiSource.map(right => (
                <Table.Column key={right.title} className="only-bottom-border" {...right} />
              ))}
            </Table>
          ) : null}
        </div>
      </FormItem>
    </Title>
  )
}

export default TitleWrapTable
