import React, { Component, useState } from 'react'
import { observer, inject } from 'mobx-react'
import { toJS } from 'mobx'
import PropTypes from 'prop-types'
import Moment from 'moment'
import { Link } from 'react-router-dom'
import { Button, Message, Dialog } from '@alifd/next'
import filterDataSource from '@/source/filterDataSource'
import { Filter, Table } from '@/packages/table/FilterTable'
import { request } from '@/packages/request'
import Header from '@/components/header'
import BoreasDialog from '@/components/dialog'
import formatDate from '@/utils/formatDate'

const filterUiSource = {
  // 'id': 'base-2a616e9d-45b8-4d7f-b1e7-6073e2c5fbb3',
  'x-component': 'Filter',
  labelAlign: 'top',
  children: [
    {
      defaultValue: '',
      label: '投放计划名称:',
      name: 'name',
      placeholder: '请输入投放计划名',
      'x-component': 'Input'
    },
    {
      defaultValue: '',
      label: '投放ID:',
      name: 'planId',
      placeholder: '请输入投放ID',
      'x-component': 'Input',
      'data-type': 'number'
    },
    {
      defaultValue: '',
      label: '创建人:',
      name: 'creatorName',
      placeholder: '请输入创建人姓名',
      'x-component': 'Input'
    },
    {
      defaultValue: '',
      label: '投放计划状态:',
      name: 'status',
      placeholder: '请选择状态',
      'x-component': 'Select',
      dataSource: [
        {
          value: '',
          label: '全部'
        },
        {
          value: '1',
          label: '生效中'
        },
        {
          value: '2',
          label: '已下线'
        }
      ]
    }
  ]
}

function changePlan(record, type, reload) {
  request(
    {
      url: `benefit://plan/change`,
      method: 'POST'
    },
    {
      body: {
        planId: record.planId,
        type
      }
    }
  ).then(() => {
    Message.success('操作成功')
    reload()
  })
}

function handleChangePlan(record = {}, reload) {
  if (+record.status === 1) {
    BoreasDialog({
      title: '投放计划下线',
      content: '确定要下线此投放计划吗？',
      onOk: () => changePlan(record, 2, reload)
    })
  } else if (+record.status === 2) {
    BoreasDialog({
      title: '投放计划重新上线',
      content: '确定重新上线此投放计划吗？',
      onOk: () => changePlan(record, 1, reload)
    })
  }
}

function List(props) {
  const columns = [
    {
      title: '投放ID',
      dataIndex: 'planId',
      width: 140
    },
    {
      title: '投放计划名称',
      dataIndex: 'name',
      cell: v => {
        return <div style={{ maxWidth: '10em', lineHeight: '22px' }}>{v}</div>
      }
    },
    {
      title: '创建人',
      dataIndex: 'creator'
    },
    {
      title: '投放计划状态',
      dataIndex: 'status',
      cell: value => {
        return (
          <span className={`boreas-status boreas-status-${value}`}>
            {value == 1 ? '生效中' : '已下线'}
          </span>
        )
      }
    },
    {
      title: '投放时间',
      dataIndex: 'timeRange',
      cell: value => {
        const { startTime, endTime } = value || {}
        return (
          <div style={{ lineHeight: '22px' }}>
            <div>{`${formatDate(startTime)}~`}</div>
            <div>{`${formatDate(endTime)}`}</div>
          </div>
        )
      }
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      cell: value => {
        return formatDate(value)
      }
    },
    {
      title: '操作',
      dataIndex: 'opreate',
      cell: (value, index, record, { reload }) => {
        return (
          <div className="boreas-cell">
            <Link
              to={{
                pathname: `/plan/detail`,
                search: `?planId=${record.planId}`
              }}
            >
              <span className="pr">详情</span>
            </Link>
            <Link
              to={{
                pathname: `/plan/op`,
                search: `?pageNum=1&pageSize=5&planId=${record.planId}`
              }}
            >
              <span className="pr pl">编辑</span>
            </Link>
            {+record.status === 1 && (
              <span onClick={() => handleChangePlan(record, reload)} className="pl">
                下线
              </span>
            )}
            {+record.status === 2 && (
              <span onClick={() => handleChangePlan(record, reload)} className="pl">
                重新上线
              </span>
            )}
          </div>
        )
      }
    }
  ]

  return (
    <div className="right-content">
      <div className="newplan-title">
        <Header
          title="投放计划"
          buttonText="新建投放计划"
          handleClickButton={() =>
            props.history.push({
              pathname: '/plan/new',
              search: '?pageNum=1&pageSize=5'
            })
          }
        />
      </div>

      <div className="bg-fff" style={{ padding: '20px' }}>
        <Filter uiSource={filterUiSource} {...props.filterProps} />
        <Table
          className="boreas-table"
          {...props.tableProps}
          columns={columns}
          hasBorder={false}
          emptyContent="请添加投放计划~"
        />
      </div>
    </div>
  )
}

export default filterDataSource({
  action: {
    url: `benefit://plan/list`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, ...o } = json
      return {
        ...o,
        currentPage: pageNum
      }
    },
    mapResponse: (json, { props, query }) => {
      return {
        total: json.total,
        data: json.list || []
      }
    }
  }
})(List)
