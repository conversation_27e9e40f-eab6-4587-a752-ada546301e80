import _ from 'lodash'

function mapToSource(_map) {
    if (!_map) {
        return []
    }
    return _.keys(_map).map(item => {
        if (+item === 0 || !!+item) {
            item = +item
        }
        return {
            label: _map[item],
            value: item
        }
    })
}

const channelCodeMap = {
    "elem_fd.hudongcheng": "主站福袋-互动城",
    "elem_fd.zutuanPK": "主站福袋-组团PK",
    "elem_fd.huichang.hongbao": "主站福袋-会场-红包",
    "elem_fd.huichang.zhundianhongbao": "主站福袋-会场-准点红包",
    "elem_fd.huichang.quanqiang": "主站福袋-会场-券墙",
    "elem_newretail_daogou.pingdao.hongbaoyu": "新零售-导购-频道页-红包雨",
    "elem_newretail_daogou.pindao.sannew": "新零售-导购-频道页-三重有礼",
    "elem_newretail_daogou.wanfa.kaibaoxiang": "新零售-导购-玩法-开宝箱",
    "elem_newretail_daogou.pindao.zhenshihui": "新零售-导购-频道页-真实惠",
    "elem_zhisai": "离线直塞",
    "elem_newretail_daogou.wanfa.laodaixin": "新零售-导购-玩法-老带新",
    "elem_newretail_order.gouhoufan": "新零售-订单-购后返",
    "elem_newretail_daogou.pindao.shilinghaohuo": "新零售-导购-频道页-时令好货",
    "elem_newretail_daogou.jifen": "新零售-导购-玩法-积分",
    "koubei.nongchang": "口碑抽发奖",
    "elem_haiwang": "海王",
    "yili": "抽奖玩法",
    "elem_newretail_daogou.wanfa.dituilaxin": "新零售-导购-玩法-地推拉新",
    "elem_newretail_pinpaidian": "新零售-品牌店",
    'elem_newretail_daogou.free_delivery_fee': '新零售-导购-免配送费',
    'alipay.huichang.quanqiang': '支付宝会场',
    'elem_newretail_daogou.wanfa.fenxiangling': '新零售-导购-玩法-分享领券',
    'elem_newretail_pinpai_unimarket': '品牌会域营销',
    'elem_newretail_daogou.wanfa.fuli': '导购玩法福利中心',
    'elem_newretail_daogou.wanfa.fuli.shoplist': '导购玩法福利中心店铺列表',
    'elem_newretail_daogou.jinggangshan': '新零售-导购-井冈山',
    'elem_newretail_daogou.guangpinpai': '新零售-导购-逛品牌',
    'elem_newretail.huichang.shuangdan': '主站福袋-会场-双旦'
}

const planDataSource = mapToSource(channelCodeMap)

const algoMap = {
    3: '新零售综合排序算法',
    4: '千人千面不定额算法',
    5: '无LBS过滤算法'
}

const algoDataSource = mapToSource(algoMap)

const benefitTypeMap = {
    30: '新零售店铺券',
    40: '新零售单品券',
    50: '新零售平台红包',
    80: '跨店商品满减券'
}

// 卡券包渠道
const displayChannelMap = {
    'ELEME': '饿了么',
    'KOUBEI': '口碑',
    'ALIPAY': '支付宝'
}

const displayChannelDataSource = mapToSource(displayChannelMap)

export {
    channelCodeMap,
    planDataSource,
    algoMap,
    algoDataSource,
    benefitTypeMap,
    displayChannelMap,
    displayChannelDataSource
} 
