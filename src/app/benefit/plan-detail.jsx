import React, { Component, useState, useEffect } from "react";
import PropTypes from "prop-types";
import { Radio, Message, Form, Table, Button, Dialog, Grid } from "@alifd/next";
import OpPlanHeader from "@/components/header";
import Title from "@/components/title";
import formatDate from "@/utils/formatDate";
import requestDataSource from "@/source/requestDataSource";
import { channelCodeMap, displayChannelMap } from './config'

const { Row, Col } = Grid;

const TYPE_NAME = ['领取式', '直塞式']
const PUSH_TYPE = ['短信触达']
const SEND_TYPE = ['⽴即发放']

@requestDataSource({
  action: {
    url: `benefit://plan/detail?planId={query.planId}`,
    method: "POST"
  },
  mapStateToProps: ({ data }) => {
    return {
      planDetailData: data
    };
  }
})
class PlanDetailWrap extends Component {
  render() {
    const { planDetailData, history } = this.props;
    return <PlanDetail planDetailData={planDetailData} _history={history} />;
  }
}

function PlanDetail({ planDetailData, _history }) {
  const headerSource = {
    title: "投放计划详情",
    renderCell: () => {
      return (
        <span
          className={`boreas-status boreas-status-${planDetailData.status}`}
          style={{ marginLeft: "20px" }}
        >
          {+planDetailData.status === 2 ? "已下线" : "生效中"}
        </span>
      );
    }
  };

  const rightPoolTableUiSource = [
    {
      title: "权益池ID",
      dataIndex: "bizId"
    },
    {
      title: "权益池名称",
      dataIndex: "name"
    },
    {
      title: "创建人",
      dataIndex: "creatorName"
    }
  ];

  function goEditor() {
    _history.push({
      pathname: "/plan/op",
      search: `?pageNum=1&pageSize=5&planId=${planDetailData.planId}`
      // state: planDetailData
    });
  }

  let rightPoolList = [];
  // 兜底权益池
  let priorityList = [];

  planDetailData.targetBusiness &&
    planDetailData.targetBusiness.length &&
    planDetailData.targetBusiness.forEach(item => {
      if (item.priority == 0) {
        priorityList.push(item);
      } else {
        rightPoolList.push(item);
      }
    });

  const ruleInfosText =
    planDetailData.ruleInfos &&
    planDetailData.ruleInfos
      .reduce((c, n) => `${c}${n.typeName},`, "")
      .slice(0, -1);

    // 卡券包渠道
    const displayChannelListText =
      planDetailData.displayChannelList &&
      planDetailData.displayChannelList.reduce((c, n) => `${c}${displayChannelMap[n]},`, "")
      .slice(0, -1);

  return (
    <div className="right-content plan-detail">
      <OpPlanHeader {...headerSource} className="bg-fff newplan-title">
        <Row className="plan-detail-header">
          <Col span={5}>
            <span>投放计划ID：</span>
            {planDetailData.planId}
          </Col>
          <Col span={5}>
            <span>创建人：</span>
            {planDetailData.creator}
          </Col>
          <Col span={6}>
            <span>创建时间：</span>
            {formatDate(planDetailData.gmtCreate)}
          </Col>
        </Row>
      </OpPlanHeader>
      <div
        style={{ marginTop: "10px", padding: "0 20px 30px" }}
        className="bg-fff plan-detail-content"
      >
        {/*
        <div className="plan-detail-content-editor color-link-1 cursor-pointer" onClick={goEditor}>
          编辑
        </div>
      */}
        {/* 基本信息 */}
        <Title title="基本信息">
          <Row className="plan-detail-block" style={{ borderBottom: 0 }}>
            <Col>
              <span>投放计划名称：</span>
              {planDetailData.name}
            </Col>
            <Col>
              <span>投放时间：</span>
              {`${formatDate(planDetailData.timeRange.startTime) ||
                ""} ~ ${formatDate(planDetailData.timeRange.endTime) || ""}`}
            </Col>
          </Row>
          <Row
            className="plan-detail-block"
            style={{ borderTop: 0, paddingTop: 0 }}
          >
            <Col>
              <span>投放渠道：</span>
              {channelCodeMap[planDetailData.channelCode] || "暂无"}
            </Col>
          </Row>
        </Title>
        {/* 权益投放配置 */}
        <Title title="权益投放配置">
          <Row className="plan-detail-block">
            <Col>
              <span>投放方式：</span>{TYPE_NAME[planDetailData.type - 1]}
            </Col>
            <Limit
              label="单用户总限领次数"
              _path={"userLimit.limit"}
              data={planDetailData}
            />
            <Limit
              label="单用户每天限领次数"
              _path={"userLimit.categoryLimit.limit"}
              data={planDetailData}
            />
            <Col>
              <span>卡券包渠道：</span>{displayChannelListText || '暂无'}
            </Col>
          </Row>
        </Title>
        {+planDetailData.type === 2 && planDetailData.channelSettings && <Title title="直塞规则">
          <Row className="plan-detail-block">
            <Col>
              <span>发放时机：</span>{SEND_TYPE[planDetailData.channelSettings.pushSettings[0].type - 1]}
            </Col>
            <Col>
              <span>触达方式：</span>{PUSH_TYPE[planDetailData.channelSettings.pushSettings[0].pushType - 1]}
            </Col>
            <Col>
              <span>触达模版：</span>暂不开放自定义
              {/* <span>触达模版：</span>{planDetailData.channelSettings.pushSettings[0].value} */}
            </Col>
          </Row>
        </Title>}
        {/* 添加权益池 */}
        <Title title="选择权益">
          <Row className="plan-detail-block">
            <Col>
              <span>添加权益池：</span>
              {rightPoolList.length ? (
                <Table dataSource={rightPoolList}>
                  {rightPoolTableUiSource.map(right => (
                    <Table.Column
                      key={right.title}
                      className="only-bottom-border"
                      {...right}
                    />
                  ))}
                </Table>
              ) : (
                  "暂无"
                )}
            </Col>
          </Row>
        </Title>
        {/* 选择投放算法 */}
        <Title title="选择投放算法">
          <Row className="plan-detail-block">
            <Col>
              <span>选择投放算法：</span>
              {ruleInfosText}
            </Col>
          </Row>
        </Title>
        {/* 兜底策略 */}
        <Title title="兜底策略">
          <Row className="plan-detail-block">
            <Col>
              <span>添加权益池：</span>
              {priorityList.length ? (
                <Table dataSource={priorityList}>
                  {rightPoolTableUiSource.map(right => (
                    <Table.Column
                      key={right.title}
                      className="only-bottom-border"
                      {...right}
                    />
                  ))}
                </Table>
              ) : (
                  "暂无"
                )}
            </Col>
          </Row>
        </Title>
      </div>
    </div>
  );
}

const Limit = ({ label, _path, data }) => {
  const value = recGet(data, _path.split("."));
  if (value === null) {
    return null;
  }
  return (
    <Col>
      <span>{label}：</span>
      {value}
    </Col>
  );
};

function recGet(data, _path) {
  // console.log(data, _path);
  const x = _path.shift();
  if (data[x] === undefined || data[x] === null) {
    return null;
  } else if (typeof data[x] === "object") {
    return recGet(data[x], _path);
  }
  return data[x];
}

export default PlanDetailWrap;
