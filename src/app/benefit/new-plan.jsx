import React, { Component, useState } from 'react'
import PropTypes from 'prop-types'
import _ from 'lodash'
import PlanForm from './plan-form'
import path from '@/api/modules/benefit'
import { request } from '@/packages/request'
import parseUrl from '@/packages/request/parseUrl'
import PlanFormUiSource from './source/opplan-form'
import { algoMap, channelCodeMap } from './config'

function NewPlanWrap(props) {
  const { channelCode } = parseUrl(props.location.search).query || {}
  const [newPlanData, setNewPlanData] = useState({
    channelCode: channelCodeMap[channelCode] ? channelCode : ''
  })
  const [saveDisable, setSaveDisable] = useState(false)

  const cancel = () => {
    console.log('cancel')
    props.history.replace('/plan')
    // props.history.back()
  }

  const save = (dataParams) => {
    console.log('dataParams', dataParams)
    setSaveDisable(true) // 让保存按钮禁止先
    const { userLimit } = dataParams

    const query = {
      name: newPlanData.name,
      timeRange: {
        type: 1,
        startTime:
          newPlanData.timeRange &&
          newPlanData.timeRange[0] &&
          newPlanData.timeRange[0]._d.getTime(),
        endTime:
          newPlanData.timeRange && newPlanData.timeRange[1] && newPlanData.timeRange[1]._d.getTime()
      },
      channelCode: newPlanData.channelCode,
      displayChannelList: newPlanData.displayChannelList,
      type: dataParams.type, // 投放类型
      ruleInfos: [{
        type: dataParams.planAlgo,
        name: algoMap[dataParams.planAlgo]
      }],
      // [
        // {
        //   type: 3,
        //   name: '新零售综合排序算法'
        // },
        // {
        //   type: 5,
        //   name: '千人千面不定额算法'
        // }
      // ],
      targetObjects: newPlanData.targetBusiness
        ? [].concat(
          ...newPlanData.targetBusiness.map(x => {
            return {
              type: 1, // 权益类型：权益/权益池
              bizId: x.poolId,
              ...x
            }
          })
        )
        : [],
      ...+dataParams.type === 2 && { channelSettings: dataParams.ChannelSettings }
    }

    if (userLimit) {
      query.userLimit = userLimit
    }

    request(`benefit://${path.createPlan}`, {
      method: 'POST',
      body: query
    })
      .then(res => {
        setTimeout(() => {
          setSaveDisable(false)
          props.history.push('/plan')
        }, 300)
      })
      .catch(err => {
        setSaveDisable(false)
        console.error(err)
      })
  }



  const updateData = v => {
    v && setNewPlanData(v)
  }

  return (
    <PlanForm
      saveDisable={saveDisable}
      planData={newPlanData}
      updateData={updateData}
      planId={''}
      cancel={cancel}
      save={save}
      uiSource={PlanFormUiSource}
      history={props.history}
    />
  )
}

export default NewPlanWrap
