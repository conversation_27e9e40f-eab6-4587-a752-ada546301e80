import React from 'react'
import filterDataSource from '@/source/filterDataSource'
import { Filter, Table } from '@/packages/table/FilterTable'
import MyBreadcrumb from '@/components/breadcrumb'
import PlanHeader from '@/components/header/index'

const breads = [
  {
    path: '/plan',
    breadcrumbName: '投放管理'
  },
  {
    path: '/plan/log',
    breadcrumbName: '日志'
  }
]

const columns = [
  { title: '变更人', dataIndex: 'operatorName' },
  { title: '变更内容', dataIndex: 'opTypeDesc' },
  { title: '变更时间', dataIndex: 'opTime' }
]

function Log({ ...props }) {
  return (
    <div className="right-content plan-log">
      <MyBreadcrumb routes={breads} />
      <PlanHeader title={'投放日志'} />
      <div className="plan-log-fitertable">变更记录</div>
      <Table {...props.tableProps} columns={columns} className="plan-log-fitertable" />
    </div>
  )
}

export default filterDataSource({
  action: `boreas+mock://CouponItemReadServiceI.itemOpLog`,
  pageSize: 5
})(Log)
