import { planDataSource } from '../config'

export default {
  // id: 'base-2a616e9d-45b8-4d7f-b1e7-6073e2c5fbb3',
  title: "基本信息",
  "x-component": "Form",
  children: [
    {
      label: "投放计划名称:",
      name: "name",
      placeholder: "请输入投放计划名称，不超过20个字",
      "x-component": "Input",
      maxLength: 20,
      disabled: true,
      hasLimitHint: true,
      required: true,
      rules: {
        required: true,
        maxLength: 20,
        minmaxLengthMessage: "最多不超过20个字",
        // requiredTrigger: 'onBlur',
        autoValidate: true,
        message: "投放计划名称不能为空"
      }
    },
    {
      label: "投放时间:",
      name: "timeRange",
      showTime: true,
      "data-type": "string",
      "x-component": "RangePicker",
      required: true,
      rules: {
        required: true,
        message: "投放时间不能为空"
      },
      disabledDate: true,
      initValue: []
    },
    {
      label: "投放渠道:",
      name: "channelCode",
      disabled: true,
      "x-component": "Select",
      placeholder: "请输入投放渠道",
      required: true,
      dataSource: planDataSource,
      rules: {
        required: true,
        message: "投放渠道不能为空"
      }
    }
  ]
};
