import React, { Component, useState } from 'react'
import { observer, inject } from 'mobx-react'
import PropTypes from 'prop-types'
import { toJS } from 'mobx'
import PlanForm from './plan-form'
import formatDateToTimeStamp from '@/utils/formatDateToTimeStamp'
import formatDate from '@/utils/formatDate'
import { request } from '@/packages/request'
import requestDataSource from '@/source/requestDataSource'
import PlanFormUiSource from './source/editplan-form'

import path from '@/api/modules/benefit'

@requestDataSource({
  action: {
    url: `benefit://plan/detail?planId={query.planId}`,
    method: 'POST'
  },
  mapStateToProps: ({ data }) => {
    let l = data.targetBusiness.map(item => {
      return {
        ...item,
        poolId: item.bizId
      }
    })
    return {
      planDetailData: {
        ...data,
        targetBusiness: l || [],
        timeRange: [formatDate(data.timeRange.startTime), formatDate(data.timeRange.endTime)]
      }
    }
  }
})
class OpPlanWrap extends Component {
  constructor(props) {
    super(props)
    // const r = this.props.location.state || {}
    const query = window.parseUrl(window.location.href).query || {}
    this.state = {
      planDetailData: this.props.planDetailData,
      planId: query.planId || '',
      saveDisable: false,
    }
  }

  setSaveDisable = v => {
    this.setState({
      saveDisable: v
    })
  }

  cancel = () => {
    this.props.history.push({
      pathname: '/plan/detail',
      search: {
        planId: this.state.planId
      }
    })
  }

  edit = ({ userLimit, displayChannelList }) => {
    let { planDetailData } = this.state;
    this.setSaveDisable(true) // 让保存按钮禁止先

    const query = {
      planId: planDetailData.planId,
      name: planDetailData.name,
      timeRange: {
        type: planDetailData.type,
        startTime: formatDateToTimeStamp(planDetailData.timeRange[0]),
        endTime: formatDateToTimeStamp(planDetailData.timeRange[1])
      },
      channelCode: planDetailData.channelCode,
      displayChannelList,
      type: planDetailData.type, // 投放类型
      ruleInfos: [
        {
          type: planDetailData.ruleInfos && planDetailData.ruleInfos[0] && planDetailData.ruleInfos[0].type,
          name: planDetailData.ruleInfos && planDetailData.ruleInfos[0] && planDetailData.ruleInfos[0].typeName
        }
      ],
      targetObjects: this.state.planDetailData.targetBusiness
        ? [].concat(
          ...this.state.planDetailData.targetBusiness.map(x => {
            return {
              type: 1, // 权益类型：权益/权益池
              bizId: x.poolId,
              ...x
            }
          })
        )
        : []
    }

    if (userLimit) {
      query.userLimit = userLimit
    }

    request(`benefit://${path.editPlan}`, {
      method: 'POST',
      body: query
    })
      .then(res => {
        setTimeout(() => {
          this.setSaveDisable(false) // 让保存按钮禁止先
          this.props.history.push('/plan')
        }, 300)
      })
      .catch(err => {
        this.setSaveDisable(false) // 让保存按钮禁止先
        console.error(err)
      })
  }


  updateData = v => {

    this.setState({
      planDetailData: { ...this.state.planDetailData, ...v }
    })

  }

  render() {

    return (
      <PlanForm
        planData={this.state.planDetailData}
        planId={this.state.planId}
        saveDisable={this.state.saveDisable}
        cancel={this.cancel}
        save={() => { }}
        edit={this.edit}
        uiSource={PlanFormUiSource}
        editStatus
        updateData={this.updateData}
        history={this.props.history}
      />
    )
  }
}

OpPlanWrap.propTypes = {
  // planDetailData: PropTypes.objectOf(PropTypes.object),
  // updateData: PropTypes.func
}

OpPlanWrap.defaultProps = {
  // planDetailData: {},
  // updateData: () => {}
}

export default OpPlanWrap
