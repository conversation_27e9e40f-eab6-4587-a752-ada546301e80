import React, { Component, useState, useRef } from 'react'
import PropTypes from 'prop-types'
import { toJS } from 'mobx'
import { Input, Radio, Message, Form, Table, Button, Dialog, Field, Balloon, Checkbox } from '@alifd/next'
import _ from 'lodash'
import OpPlanForm from '@/packages/form/index'
import OpPlanHeader from '@/components/header/index'
import fusionInputFix from '@/utils/fusionInputFix'
import Title from '@/components/title'
import BoreasDialog from '@/components/dialog'
import Choice from '@/components/choice'
import { displayChannelDataSource, algoDataSource } from './config'

import DialogFilterTable from './dialog-filtertable'

import TitleWrapTable from './title-wrap-table'

const FormItem = Form.Item
const RadioGroup = Radio.Group
const TextArea = fusionInputFix('textarea')(Input.TextArea)

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 }
}

const PlanForm = ({ saveDisable, planId, planData = {}, updateData, cancel, save, edit, history, editStatus, uiSource }, props) => {
  const [poolVisible, setPoolVisible] = useState(false)
  const [isError, setError] = useState(false)

  const { userLimit, channelSettings, type, displayChannelList } = planData

  // console.log('------plandata')
  // console.log(planData)
  // console.log(type)
  // 表单数据
  const [formData, setFormData] = useState({
    timeRange: planData.timeRange,
    name: planData.name,
    channelCode: planData.channelCode
  })
  const [formField, setFormField] = useState(null)
  const [cansave, setCansave] = useState(true)
  // 操作是兜底还是权益池
  const [opPoolType, setOpPoolType] = useState('')

  const headerSource = {
    title: planId ? '编辑投放计划' : '新建投放计划'
  }

  const rightPoolTableUiSource = [
    {
      title: '权益池ID',
      dataIndex: 'poolId'
    },
    {
      title: '权益池名称',
      dataIndex: 'name'
    },
    {
      title: '创建人',
      dataIndex: 'creatorName'
    },
    {
      title: '操作',
      dataIndex: 'ops',
      cell: (value, index, record) => {
        return (
          <span onClick={() => handleRemoveFromPool(1, record)} className="boreas-cell">
            删除
          </span>
        )
      }
    }
  ]
  const priorityPoolTableUiSource = [
    {
      title: '权益池ID',
      dataIndex: 'poolId'
    },
    {
      title: '权益池名称',
      dataIndex: 'name'
    },
    {
      title: '创建人',
      dataIndex: 'creatorName'
    },
    {
      title: '操作',
      dataIndex: 'ops',
      cell: (value, index, record) => {
        return (
          <span onClick={() => handleRemoveFromPool(0, record)} className="boreas-cell">
            删除
          </span>
        )
      }
    }
  ]

  let rightPoolList = []
  // 兜底权益池
  let priorityList = []

  planData.targetBusiness &&
    planData.targetBusiness.length &&
    planData.targetBusiness.forEach(item => {
      if (item.priority == 0) {
        priorityList.push(item)
      } else {
        rightPoolList.push(item)
      }
    })

  function handleChangeForm(type, k, v, field) {
    setFormField(field)
    if (type === 'change') {
      const fieldData = field.values

      // console.log('handleChangeForm', type, k, v, field, fieldData)
      setFormData({
        name: fieldData.name,
        timeRange: fieldData.timeRange,
        channelCode: fieldData.channelCode
      })
      updateData(fieldData)
    }
  }

  function handleAddRightPool(dialogType) {
    // planPriorityList, planRightPoolList
    if (dialogType === 1 && rightPoolList.length >= 20) {
      return Message.error('添加的权益池不能超过20个')
    } else if (dialogType === 0 && priorityList.length >= 1) {
      return Message.error('兜底策略最多只能添加一个权益池')
    }
    // priority
    setOpPoolType(dialogType)
    setPoolVisible(true)
  }

  function handleRemoveFromPool(type, record) {
    BoreasDialog({
      title: '',
      content: '确定删除权益池吗',
      onOk: () => {
        let { targetBusiness, ...others } = planData
        targetBusiness = [].concat(planData.targetBusiness.filter(x => x.poolId !== record.poolId))
        updateData &&
          updateData({
            ...others,
            targetBusiness
          })
      }
    })
  }

  function handleDialogOk(recordList) {
    // updateData 更新数据在这里
    let r = planData
    r.targetBusiness = (r.targetBusiness || []).concat(recordList)
    updateData && updateData(r)
  }

  function handleCancel() {
    if (planId) {
      history.goBack()
    } else {
      cancel()
    }

  }

  function handleSave() {
    const values = formField.getValues()

    formField.validate((err, value) => {
      const x1 = value.x1
      const x2 = value.x2
      let userLimit = null

      if (x1 !== null || x2 !== null) {
        userLimit = {
        }
        if (x1 !== null) {
          userLimit.limit = x1
        }
        if (x2 !== null) {
          userLimit.categoryLimit = {
            type: 1,
            limit: x2
          }
        }
      }

      if (err) {
        // if (+formField.getValue('type') === 2 && err.XX3) {
        //   setError(true)
        // } else {
        //   delete err.XX3
        // }
        if (Object.keys(err).length) {
          Message.error(Object.keys(err).map(x => err[x])[0].errors[0])
          return
        }
      }
      if (!value.timeRange[0]) {
        return Message.error('请选择投放开始时间')
      } else if (!value.timeRange[1]) {
        return Message.error('请选择投放结束时间')
      } else if ((value.timeRange[0] && value.timeRange[0]._d && value.timeRange[0]._d.getTime()) >= (value.timeRange[1]._d && value.timeRange[1]._d.getTime())) {
        return Message.error('开始时间必须小于结束时间')
      } else if (!planData.targetBusiness || !planData.targetBusiness.length) {
        return Message.error('投放计划需要至少关联一个权益池')
      }

      if (planId) {

        edit({
          userLimit,
          type: value.type,
          ChannelSettings: {
            pushSettings: [{
              type: value.XX1,
              pushType: value.XX2,
              value: value.XX3,
              pushTypeData: ""
            }]
          },
          planAlgo: value.planAlgo,
          displayChannelList: value.displayChannelList
        })
      } else {
        save({
          userLimit,
          type: value.type,
          ChannelSettings: {
            pushSettings: [{
              type: value.XX1,
              pushType: value.XX2,
              value: value.XX3,
              pushTypeData: ""
            }]
          },
          planAlgo: value.planAlgo,
          displayChannelList: value.displayChannelList
        })
        // save({ userLimit })
      }


    })
  }

  const priorityProps = {
    title: '兜底策略',
    opLabel: '添加权益池:',
    btnText: '+添加',
    tableSource: priorityList,
    uiSource: priorityPoolTableUiSource,
    onChange: handleAddRightPool,
    changeValue: 0,
  }

  const rightsProps = {
    title: '选择权益',
    opLabel: '添加权益池:',
    btnText: '+添加',
    tableSource: rightPoolList,
    uiSource: rightPoolTableUiSource,
    onChange: handleAddRightPool,
    changeValue: 1,
  }

  return (
    <div className="right-content">
      <OpPlanHeader {...headerSource} className="bg-fff newplan-title" />
      <div style={{ marginTop: '10px' }} className="bg-fff">
        {/* 基本信息表 */}
        <OpPlanForm disabled={editStatus} uiSource={uiSource} onEvent={handleChangeForm} dataSource={formData} />
        {/* 权益投放配置 */}
        <Title title="权益投放配置">
          {formField && <FormItem label="投放方式:" {...formItemLayout}>
            <RadioGroup
              disabled={editStatus}
              dataSource={[
                {
                  label: '领取式',
                  value: '1'
                },
                {
                  label: <Balloon type="normal" trigger='直塞式' closable={false} style={{ maxWidth: 500, width: 500 }} popupContainer={(trigger) => trigger.parentNode} followTrigger visible={formField.getValue('type') === '2'}>
                    <FormItem label="发放时机:" {...formItemLayout}>
                      <RadioGroup
                        dataSource={[
                          {
                            label: '立即发放',
                            value: '1'
                          }
                        ]}
                        {...formField.init('XX1', {
                          initValue: ((planData && planData.channelSettings && planData.channelSettings.pushSettings[0] && typeof (planData.channelSettings.pushSettings[0].type) !== 'undefined' && planData.channelSettings.pushSettings[0].type) || '1'),
                          rules: {
                            required: true,
                            message: '请选择发放时机'
                          }
                        })}
                      />
                    </FormItem>
                    <FormItem label="触达方式:" {...formItemLayout}>
                      <RadioGroup
                        dataSource={[
                          {
                            label: '短信触达',
                            value: '1'
                          }
                        ]}
                        {...formField.init('XX2', {
                          initValue: ((planData && planData.channelSettings && planData.channelSettings.pushSettings[0] && typeof (planData.channelSettings.pushSettings[0].pushType) !== 'undefined' && planData.channelSettings.pushSettings[0].pushType) || '1'),
                          rules: {
                            required: true,
                            message: '请选择触达方式'
                          }
                        })}
                      />
                    </FormItem>
                    <FormItem label="触达模版:" {...formItemLayout} className="tip-text">
                      {/* <span className="top">【饿了么】</span>
                      <TextArea placeholder="最多输入55个汉字" maxLength={55} row={6} state={isError ? 'error' : null} hasLimitHint {...formField.init('XX3', {
                        rules: {
                          required: true,
                          message: '请输入触达模版'
                        }
                      })} />
                      <span className="bottom">回TD退订</span>
                      {isError && <span className="error">请输入触达模版</span>} */}
                      <span style={{ lineHeight: '36px' }}>暂不开放自定义</span>
                    </FormItem>
                  </Balloon>,
                  value: '2'
                }
              ]}
              {...formField.init('type', {
                initValue: (type && String(type)) || '1',
                rules: {
                  required: true,
                  message: '请选择投放方式'
                }
              })}
            />
          </FormItem>}

          {formField && <React.Fragment>
            <Choice 
              {...formField.init('x1', {
                initValue: (userLimit && userLimit.limit) ? userLimit.limit : null,
                rules: [{ min: 1, max: 9999, message: '限领次数值应该在1-9999之间' }]
              })} 
              label='单用户总限领次数'
              formItemLayout={formItemLayout}
              dataSource={[
                {
                  label: '不限制',
                  value: 0
                },
                {
                  label: '限制',
                  value: 1
                }
              ]}
            />
            <Choice 
              {...formField.init('x2', {
                initValue: (userLimit && userLimit.categoryLimit && userLimit.categoryLimit.limit) ? userLimit.categoryLimit.limit : null,
                rules: [{ min: 1, max: 9999, message: '限领次数值应该在1-9999之间' }]
              })} 
              label='单用户每天限领次数'
              formItemLayout={formItemLayout}
              dataSource={[
                {
                  label: '不限制',
                  value: 0,
                },
                {
                  label: '限制',
                  value: 1,
                }
              ]}
            />
            <FormItem label="卡券包渠道:" {...formItemLayout}>
              <Checkbox.Group
                dataSource={displayChannelDataSource}
                {...formField.init(
                  'displayChannelList',
                  {
                    initValue: displayChannelList || ['ELEME'],
                  },
                  {
                    onChange: (_v, _e) => {
                      const _currentChoiceValue = _e.target.value
                      if (['KOUBEI', 'ALIPAY'].includes(_currentChoiceValue) && _v.includes(_currentChoiceValue)) {
                        const dialog = Dialog.confirm({
                          title: '提示',
                          content: '投放口碑、支付宝卡包需要先与相关运营方确认，若不经沟通直接同步会有较大风险，请谨慎选择',
                          footer: (
                            <Button warning type="primary" onClick={() => dialog.hide()}>
                                确认
                            </Button>
                          )
                        })
                      }
                    }
                  }
                )} 
              />
          </FormItem>
            {/* <Choice field={formField} label='总库存' name='x3' />
            <Choice field={formField} label='每日库存' name='x4' />
            <Choice field={formField} label='无库存券是否展示' name='x5' /> */}
          </React.Fragment>
          }

        </Title>
        {/* 添加权益池 */}
        <TitleWrapTable {...rightsProps} />

        {/* 选择投放算法 */}
        <Title title="选择投放算法">
          <FormItem label="投放算法:" {...formItemLayout}>
            {
              formField && <RadioGroup
                disabled={editStatus}
                dataSource={algoDataSource}
                defaultValue={3}
                {...formField.init('planAlgo', {
                initValue: (planData && planData.ruleInfos && planData.ruleInfos[0] && planData.ruleInfos[0].type) || 3,
                rules: {
                  required: true,
                  message: '请选择投放算法'
                }
              })}
            />
            }
          </FormItem>
        </Title>
        {/* 兜底策略 */}
        <TitleWrapTable {...priorityProps} />
        {/* <Title title="兜底策略">
          <FormItem label="添加权益池:" {...formItemLayout}>
            <div>
              <div
                className="color-link-1 next-form-text-align cursor-pointer"
                onClick={() => handleAddRightPool(0)}
              >
                +添加
              </div>
              {priorityList.length ? (
                <Table
                  dataSource={priorityList}
                  className="plan-form-table"
                  style={{ width: '513px', border: '1px solid #ECECEC' }}
                >
                  {priorityPoolTableUiSource.map(right => (
                    <Table.Column key={right.title} className="only-bottom-border" {...right} />
                  ))}
                </Table>
              ) : null}
            </div>
          </FormItem>
        </Title> */}
        <FormItem className="boreas-save" {...formItemLayout}>
          <Button style={{ marginRight: '12px' }} onClick={handleCancel}>
            取消
          </Button>
          <Button type="primary" disabled={!cansave || saveDisable} onClick={handleSave}>
            保存
          </Button>
        </FormItem>
      </div>
      {/* 弹框 */}
      <DialogFilterTable
        visible={poolVisible}
        handleDialogOk={handleDialogOk}
        changeVisible={setPoolVisible}
        opPoolType={opPoolType}
        targetBusiness={planData.targetBusiness || []}
        history={history}
      />
    </div>
  )
}

// PlanForm.propTypes = {
//   opPlanData: PropTypes.objectOf(PropTypes.object),
//   updateData: PropTypes.func,
//   save: PropTypes.func,
//   edit: PropTypes.func
// }

// PlanForm.defaultProps = {
//   opPlanData: {},
//   updateData: () => { },
//   edit: () => { },
//   save: () => { },

// }





export default PlanForm
