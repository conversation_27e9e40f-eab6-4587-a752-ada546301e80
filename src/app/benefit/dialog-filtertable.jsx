import React, { Component, useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import { Dialog, Message } from '@alifd/next'
import { toJS } from 'mobx'
import filterDataSource from '@/source/filterDataSource'
import { Filter, Table } from '@/packages/table/FilterTable'

const filterUiSource = {
  // 'id': 'base-2a616e9d-45b8-4d7f-b1e7-6073e2c5fbb3',
  'x-component': 'Filter',
  labelAlign: 'top',
  children: [
    {
      defaultValue: '',
      label: '权益池ID:',
      name: 'rightsPoolId',
      placeholder: '请输入权益ID/权益池ID',
      'x-component': 'Input',
      'data-type': 'number'
    }
  ]
}

const columns = [
  {
    title: '权益池ID',
    dataIndex: 'poolId'
  },
  {
    title: '权益池名称',
    dataIndex: 'name'
  },
  {
    title: '创建人',
    dataIndex: 'creatorName'
  }
]

function AddPoolFilterTable({ ...props }) {
  // opPoolType  1 rightPoolList  0 priorityList  targetBusiness
  const [recordList, setRecordList] = useState([])
  const [listMap, setListMap] = useState({})
  const [disableAdd, setDisableAdd] = useState(true)

  function handleBussiness() {
    let rightPoolList = []
    // 兜底权益池
    let priorityList = []

    props.targetBusiness.length &&
      props.targetBusiness.forEach(item => {
        if (item.priority == 0) {
          priorityList.push(item)
        } else {
          rightPoolList.push(item)
        }
      })
    return {
      rightPoolList,
      priorityList
    }
  }
  const othersProps = {
    primaryKey: 'poolId',
    rowSelection: {
      onChange: (ids, list) => {
        setDisableAdd(!ids.length)
        const data = props.tableProps.dataSource || []
        let r = []
        let map = listMap || {}
        data.forEach(item => {
          map[item.poolId] = item
        })
        setListMap({
          ...listMap,
          ...map
        })
        ids.forEach(item => {
          r.push({
            ...map[item],
            priority: props.opPoolType
          })
        })
        setRecordList(r)
      },
      getProps: record => {
        const { rightPoolList, priorityList } = handleBussiness()
        const l =
          props.targetBusiness.length &&
          props.targetBusiness.filter(item => item.poolId === record.poolId)
        if (props.opPoolType === 1) {
          return {
            disabled: rightPoolList.length >= 20 || !!l.length
          }
        } else if (props.opPoolType === 0) {
          return {
            disabled: priorityList.length >= 1 || !!l.length
          }
        }
      }
    }
  }

  function onOk() {
    const { rightPoolList, priorityList } = handleBussiness()
    const rl = recordList.length + rightPoolList.length
    const pl = recordList.length + priorityList.length
    if (props.opPoolType === 1 && rl > 20) {
      // 权益池
      return Message.error(`添加的权益池不能超过20个, 您已选择${rl}个`)
    } else if (props.opPoolType === 0 && pl > 1) {
      // 兜底策略
      return Message.error(`兜底策略最多只能添加一个权益池, 您已选择${pl}个`)
    }
    props.handleDialogOk(recordList)
    onHide()
  }

  function onHide() {
    const query = window.parseUrl(window.location.href).query || {}
    const r = {
      pageNum: 1,
      pageSize: 5
    }
    query.planId && (r.planId = query.planId)
    props.changeVisible(false)
    setDisableAdd(true)
    setRecordList([])
    props.filterProps.reset(r)
  }

  return (
    <div>
      <Dialog
        title="添加权益池"
        className="boreas-dialog"
        okProps={{ children: '添加', disabled: disableAdd }}
        footerActions={['cancel', 'ok']}
        style={{ width: '60%', maxWidth: '60%' }}
        shouldUpdatePosition={props.visible}
        visible={props.visible}
        onOk={() => onOk()}
        onCancel={() => onHide()}
        onClose={() => onHide()}
      >
        <Filter uiSource={filterUiSource} {...props.filterProps} defaultPageSize={5} />
        <p style={{ color: 'red' }}>注意：若选择离线式投入方式，则必须选择离线人群标签的权益，选择实时人群标签的权益将不会投放出去。</p>
        <Table {...props.tableProps} columns={columns} {...othersProps} />
        <p style={{ color: 'red' }}> 注意：若选择离线式投放方式，则必须选择离线人群标签的权益，选择实时人群标签的权益将不会投放出去。</p>
      </Dialog>
    </div>
  )
}

const DialogFilterTable = filterDataSource({
  action: {
    url: `benefit://rights/pool/list`,
    method: 'POST',
    mapRequest: query => {
      const { pageNum, ...o } = query
      return {
        ...o,
        currentPage: query.pageNum,
        type: 1
      }
    },
    // onResponse: res => {
    //   console.log('here', res)
    //   // this.props.updateData()
    // },
    mapResponse: json => {
      return {
        total: json.total,
        data: json.list || []
      }
    }
  }
})(AddPoolFilterTable)

export default DialogFilterTable

DialogFilterTable.propTypes = {
  // updateData: PropTypes.func
}
