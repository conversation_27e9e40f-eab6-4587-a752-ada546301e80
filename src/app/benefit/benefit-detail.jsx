import React, { Component } from 'react'
import { Message, Tab, Grid } from '@alifd/next'
import Header from '@/components/header'
import { Filter, Table } from '@/packages/table/FilterTable'

import filterDataSource from '@/source/filterDataSource'
import requestDataSource from '@/source/requestDataSource'
import { request } from '@/packages/request'
import formatDate from '@/utils/formatDate'
import BoreasDialog from '@/components/dialog'
import CreateBenefit from './create-benefit'
import { config } from '@/api/config'
import { benefitTypeMap } from './config'

// import AddIdChoice from './AddIdChoice'

const { Row, Col } = Grid

// const peopleRangeTypeMap = {
//   1: '不限',
//   2: '新零售新客',
//   3: '新零售流失老客',
//   4: '新零售活跃老客'
// }

// const districtRangeTypeMap = {
//   1: '不限制',
//   2: '限直营地区'
// }

async function deleteIt(record, reload, actionFunc) {
  const detailQuery = window.parseUrl(window.location.href).query
  BoreasDialog({
    title: '移除关联活动',
    content: '移除后该活动所有相关的权益也会移除，确认移除该活动吗？',
    onOk: () => {
      request(
        {
          url: `benefit://rights/source/remove`,
          method: 'POST'
        },
        {
          body: {
            sourceImportId: record.sourceImportId,
            sourceCode: record.sourceCode || '',
            poolId: detailQuery.poolId || ''
          }
        }
      ).then(() => {
        Message.success('操作成功')
        actionFunc && actionFunc()
      })
    }
  })
}

const sourceCodeMap = {
  ALSC_ACT: '百川',
  BRAND_ACT: '通过品牌活动添加',
  KUNLUN_ACT: '通过昆仑用户营销添加',
  NEW_KUNLUN_ACT: '通过新昆仑用户营销添加',
  MARKET_ACT: '通过昆仑营销活动添加',
  EBAI_ACT: '通过饿百营销活动添加',
  HAIWANG_ACT: '海王权益'
}

// 已关联活动
const relatedColumns = [
  {
    title: '外部活动ID',
    dataIndex: 'sourceImportId'
  },
  {
    title: '外部活动名称',
    dataIndex: 'extName'
  },
  {
    title: '限制信息',
    dataIndex: 'peopleRangeTypeDesc',
    cell: (value, index, record) => {
      return (
        <div>
          {record.peopleRangeTypeDesc !== null && (
            <div style={{ paddingBottom: '5px' }}>人群限制：{`${record.peopleRangeTypeDesc} ${+record.peopleRangeType === 5 ? `(${record.peopleGroupIds})` : ''}`}</div>
          )}
          {record.districtRangeTypeDesc !== null && (
            <div>地区限制：{record.districtRangeTypeDesc || ''}</div>
          )}
        </div>
      )
    }
  },
  {
    title: '来源方式',
    dataIndex: 'sourceCode',
    cell: value => {
      return sourceCodeMap[value]
    }
  },
  {
    title: '添加人',
    dataIndex: 'creatorName'
  },
  {
    title: '添加时间',
    dataIndex: 'createdAt',
    cell: value => {
      return formatDate(value)
    }
  },
  {
    title: '操作',
    dataIndex: 'op',
    cell: (value, index, record, { reload, actionFunc }) => {
      return (
        <div className="boreas-cell" onClick={() => deleteIt(record, reload, actionFunc)}>
          删除
        </div>
      )
    }
  }
]

// 权益列表
const benefitColumns = [
  {
    title: '权益ID',
    dataIndex: 'rightsId'
  },
  {
    title: '权益名称',
    dataIndex: 'name'
  },
  {
    title: '权益类型',
    dataIndex: 'type',
    cell: (value, index, record) => {
      return benefitTypeMap[+value] ? benefitTypeMap[+value] : record.typeDesc
    }
  },
  {
    title: '优惠信息',
    dataIndex: 'ruleDesc'
  },
  {
    title: '库存信息',
    dataIndex: 'stock',
    cell: (value, index, record) => {
      return (
        <div>
          {record.stock !== null && (
            <div style={{ paddingBottom: '5px' }}>当前总库存：{record.stock}</div>
          )}
          {record.dayStock !== null && <div>当前每日库存：{record.dayStock}</div>}
        </div>
      )
    }
  },
  {
    title: '限领规则',
    dataIndex: 'limit',
    cell: (value, index, record) => {
      return (
        <div>
          {record.limit !== null && (
            <div style={{ paddingBottom: '5px' }}>总限领：{record.limit}</div>
          )}
          {record.dayLimit !== null && <div>每日限领：{record.dayLimit}</div>}
        </div>
      )
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    cell: value => {
      return formatDate(value)
    }
  },
  {
    title: '来源ID',
    dataIndex: 'sourceImportId'
  }
]

const filterUISource = {
  'x-component': 'Filter',
  labelAlign: 'top',
  children: [
    {
      defaultValue: '',
      label: '权益名称：',
      name: 'name',
      placeholder: '请输入权益名称',
      'x-component': 'Input'
    },
    {
      defaultValue: '',
      label: '权益ID：',
      name: 'rightsId',
      placeholder: '请输入权益ID',
      'x-component': 'Input'
    }
  ]
}

@requestDataSource({
  action: {
    url: `benefit://rights/pool/list?rightsPoolId={query.poolId}`,
    method: 'POST',
    mapResponse: json => {
      return json.list[0]
    }
  },
  mapStateToProps: ({ data }) => {
    return {
      pool: data
    }
  }
})
@filterDataSource({
  action: {
    url: `benefit://rights/source/list?poolId={query.poolId}`,
    method: 'POST',
    mapResponse: json => {
      return {
        data: json.list,
        total: json.total
      }
    },
    mapRequest: json => {
      const { pageNum, ...o } = json
      return {
        ...o,
        currentPage: pageNum
      }
    }
  },
  mapStateToProps: props => {
    return {
      activitySource: props
    }
  }
})
@filterDataSource({
  action: {
    url: `benefit://rights/list?poolId={query.poolId}`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, ...o } = json
      return {
        ...o,
        currentPage: pageNum
      }
    },
    mapResponse: json => {
      return {
        data: json.list,
        total: json.total
      }
    }
  },
  mapStateToProps: props => {
    return {
      benefitSource: props
    }
  }
})
class BenefitDetail extends Component {
  constructor() {
    super()

    this.state = {
      visible: false,
      resultVisible: false,
      resultConfirm: false,
      createBenefitLoading: false,
      file: [], // 上传文件
    }
  }

  setVisible = v => {
    this.setState({
      visible: v,
    })
  }

  updateFile = v => {
    this.setState({
      file: v
    })
  }

  checkFile = (fileName) => {
    request(
      {
        url: `benefit://rights/add/file/download/check`,
        method: 'GET'
      },
      {
        query: {
          fileName,
        }
      }
    ).then((res) => {
      if (res.code !== '200') {
        Message.error(res.message)
      } else {
        // Message.success('操作成功')
        window.open(`${config.BENEFIT_PATH}/rights/add/file/download?fileName=${fileName}`)
      }


    })
  }

  setResultShow = v => {
    this.setState({
      resultVisible: v
    })
  }

  setResultConfirm = v => {
    this.setState({
      resultConfirm: v
    })
  }

  // handleAddVisible = v => {
  //   this.setState({
  //     addIdVisible: v
  //   })
  // }

  handleDownloadTemp = () => {
    request(
      {
        url: `benefit://rights/add/template/download`,
        method: 'POST'
      },
      {
        body: {}
      }
    ).then((res) => {

      Message.success('操作成功')

    })
  }

  // handleAddId = v => {
  //   console.log('这里才是添加权益')
  //   this.setState({
  //     addIdVisible: true,
  //   })
  // }

  setCreateBenefitLoading = v => {
    this.setState({
      createBenefitLoading: v
    })
  }

  reloadAllList = () => {
    this.props.benefitSource && this.props.benefitSource.tableProps.reload()
    this.props.activitySource && this.props.activitySource.tableProps.reload()
  }

  render() {
    const detailQuery = window.parseUrl(window.location.href).query

    const { activitySource, benefitSource, pool, reloadAllList } = this.props
    return (
      <div className="right-content benefit-detail">
        <Header
          className="bg-fff newplan-title"
          handleClickButton={() => { this.setVisible(true); this.setResultShow(false); this.setResultConfirm(false); this.updateFile([]) }}
          title="权益池详情"
          buttonText="新建权益"
        >
          <Row className="plan-detail-header">
            <Col span={5}>
              <span>权益池名称：</span>
              {pool.name}
            </Col>
            <Col span={5}>
              <span>权益池id：</span>
              {pool.poolId}
            </Col>
            <Col span={5}>
              <span>创建人：</span>
              {pool.creatorName}
            </Col>
            <Col span={6}>
              <span>创建时间：</span>
              {formatDate(pool.createdAt)}
            </Col>
          </Row>
        </Header>

        <div className="bg-fff newplan-title" style={{ marginTop: '10px', padding: '0 20px' }}>
          <Tab size="medium" style={{ fontSize: '20px' }}>
            <Tab.Item title="权益列表" key="1">
              <Filter
                style={{ marginTop: '20px' }}
                {...benefitSource.filterProps}
                uiSource={filterUISource}
              />
              <Table
                {...benefitSource.tableProps}
                columns={benefitColumns}
                hasBorder={false}
                className="benefit-detail-table"
              />
            </Tab.Item>
            <Tab.Item title="已关联活动" key="2">
              <Table
                style={{ marginTop: '10px' }}
                {...activitySource.tableProps}
                columns={relatedColumns}
                hasBorder={false}
                actionFunc={this.reloadAllList}
                className="benefit-detail-table"
              />
            </Tab.Item>
          </Tab>
        </div>

        <CreateBenefit
          reloadAllList={this.reloadAllList}
          visible={this.state.visible}
          file={this.state.file}
          checkFile={this.checkFile}
          updateFile={this.updateFile}
          poolId={pool.poolId}
          setResultShow={this.setResultShow}
          resultVisible={this.state.resultVisible}
          resultConfirm={this.state.resultConfirm}
          setResultConfirm={this.setResultConfirm}
          setVisible={this.setVisible}
          createBenefitLoading={this.state.createBenefitLoading}
          setCreateBenefitLoading={this.setCreateBenefitLoading}
        />

        {/* <AddIdChoice
          visible={this.state.addIdVisible}
          downloadTemp={this.handleDownloadTemp}
          poolId={detailQuery.poolId || ''}
          setVisible={this.handleAddVisible}
        /> */}
      </div>
    )
  }
}

export default BenefitDetail
