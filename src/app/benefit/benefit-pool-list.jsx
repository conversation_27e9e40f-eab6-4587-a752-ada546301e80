import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { Dialog, Input, Form, Message, Button } from '@alifd/next'
import Moment from 'moment'
import filterDataSource from '@/source/filterDataSource'
import { Filter, Table } from '@/packages/table/FilterTable'
import Header from '@/components/header'
import fusionInputFix from '@/utils/fusionInputFix'
import formDataSource from '@/source/formDataSource'
import formatDate from '@/utils/formatDate'
import BoreasDialog from '@/components/dialog'

import { request } from '@/packages/request'

const FormItem = Form.Item
const XInput = fusionInputFix()(Input)

function changePoolStatus(record, type, reload) {
  request(
    {
      url: `benefit://rights/pool/change`,
      method: 'POST'
    },
    {
      body: {
        type,
        poolId: record.poolId
      }
    }
  ).then(() => {
    Message.success('操作成功')
    reload()
  })
}

function handleChangePool(record, type, reload) {
  // type 1上线 2 下线
  if (+record.status === 1) {
    BoreasDialog({
      title: '权益池下线',
      content: '确定要权益池下线吗？',
      onOk: () => changePoolStatus(record, type, reload)
    })
  } else if (+record.status === 2) {
    BoreasDialog({
      title: '权益池重新上线',
      content: '确定重新上线权益池吗？',
      onOk: () => changePoolStatus(record, type, reload)
    })
  }
}

const filterUISource = {
  'x-component': 'Filter',
  labelAlign: 'top',
  children: [
    {
      defaultValue: '',
      label: '权益池名称:',
      name: 'name',
      placeholder: '请输入权益池名称',
      'x-component': 'Input'
    },
    {
      defaultValue: '',
      label: '权益池ID:',
      name: 'rightsPoolId',
      placeholder: '请输入权益池ID',
      'x-component': 'Input',
      'data-type': 'number'
    },
    {
      defaultValue: '',
      label: '创建人:',
      name: 'creatorName',
      placeholder: '请输入创建人',
      'x-component': 'Input'
    }
  ]
}

const columns = [
  {
    title: '权益池ID',
    dataIndex: 'poolId'
  },
  {
    title: '权益池名称',
    dataIndex: 'name'
  },
  {
    title: '状态',
    dataIndex: 'status',
    cell: value => {
      return (
        <span className={`boreas-status boreas-status-${value}`}>
          {value == 1 ? '生效中' : '已下线'}
        </span>
      )
    }
  },
  {
    title: '创建人',
    dataIndex: 'creatorName'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    cell: value => {
      return formatDate(value)
    }
  },
  {
    title: '操作',
    dataIndex: 'ops',
    cell: (value, index, record, { reload }) => {
      return (
        <div className="boreas-cell">
          <Link
            to={{
              pathname: `/benefit/detail`,
              search: `?poolId=${record.poolId}`
            }}
          >
            <span className="pr">详情</span>
          </Link>
          {record.status === 1 && (
            <span onClick={() => handleChangePool(record, 2, reload)} className="pl">
              下线
            </span>
          )}
          {record.status === 2 && (
            <span onClick={() => handleChangePool(record, 1, reload)} className="pl">
              重新上线
            </span>
          )}
        </div>
      )
    }
  }
]

function List(props) {
  const [visible, setVisible] = useState(false)
  const [createDiable, setCreateDisable] = useState(true)
  const { reload } = props.tableProps || {}

  function handleCreateInputChange(name, v) {
    if (v) {
      setCreateDisable(false)
    } else {
      setCreateDisable(true)
    }
  }

  return (
    <div className="right-content">
      <div className="newplan-title">
        <Header title="权益池" buttonText="新建权益池" handleClickButton={() => setVisible(true)} />
      </div>

      <div className="bg-fff" style={{ padding: '20px' }}>
        <Filter uiSource={filterUISource} {...props.filterProps} />
        <Table
          className="boreas-table"
          {...props.tableProps}
          columns={columns}
          hasBorder={false}
          emptyContent="请添加权益~"
        />
        <CreateBenefitPool
          setVisible={setVisible}
          visible={visible}
          // addId={handleAddId}
          createDiable={createDiable}
          onCreated={handleCreateBenefit}
          reload={reload}
          onFormChange={handleCreateInputChange}
        />
      </div>
    </div>
  )
}

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 }
}

function handleCreateBenefit() { }

// function handleAddId() {
//   console.log('点击了添加id')
// }

const CreateBenefitPool = formDataSource({
  submitAction: {
    url: `benefit://rights/pool/add`
  }
})(
  ({
    visible,
    setVisible,
    createDiable,
    onCreated,
    reload,
    field,
    submit,
    handleCreateInputChange
  }) => {
    return (
      <Dialog
        title="新建权益池"
        onOk={() => {
          submit().then(resp => {
            Message.success('新建成功')
            setVisible(false)
            reload()
          })
        }}
        style={{
          width: '576px',
          height: '234px'
        }}
        footerActions={['cancel', 'ok']}
        okProps={{ children: '下一步', disabled: createDiable }}
        onCancel={() => setVisible(false)}
        onClose={() => setVisible(false)}
        visible={visible}
      >
        <div style={{ marginTop: '10px' }} className="bg-fff">
          <Form field={field}>
            <FormItem label="权益池名称:" {...formItemLayout}>
              <XInput
                maxLength={20}
                {...field.init('name')}
                hasLimitHint
                placeholder="请输入权益池名称"
              />
            </FormItem>
          </Form>
        </div>
      </Dialog>
    )
  }
)

export default filterDataSource({
  action: {
    url: `benefit://rights/pool/list`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, ...o } = json
      return {
        ...o,
        currentPage: pageNum
      }
    },
    mapResponse: (json, { props, query }) => {
      return {
        total: json.total,
        data: json.list || []
      }
    }
  }
})(List)
