import React from 'react'
import { Dialog, Icon } from '@alifd/next'
import formDataSource from '@/source/formDataSource'
// import fusionInputFix from '@/utils/fusionInputFix'
// import { request } from '@/packages/request'
import { config } from '@/api/config'

const BatchResult = formDataSource({
  submitAction: {
    url: `benefit://rights/add?poolId={query.poolId}`,
    mapRequest: (query, obj) => {
      return {
        poolId: obj.query.poolId,
        sourceCode: query.sourceCode,
        peopleRangeType: query.peopleRangeType,
        sourceImportId: query.sourceImportId,
        districtRangeType: query.districtRangeType
      }
    }
  }
})(
  ({
    visible,
    setVisible,
    poolId,
    checkFile,
    file,
  }) => {

    return (
      <Dialog
        title="活动添加结果"
        okProps={{
          // loading: createBenefitLoading,
          children: '关闭'
        }}
        footerActions={['cancel', 'ok']}
        onOk={() => {
          setVisible(false)
        }}
        style={{
          width: '776px',
          height: '450px'
        }}
        onCancel={() => {
          setVisible(false)
        }}
        onClose={() => {
          setVisible(false)
        }}
        visible={visible}
      >
        <div style={{ marginTop: '20px' }} className="bg-fff">
          <p style={{ fontSize: '14px', backgroundColor: '#f1f2f45e', lineHeight: '24px' }}> 添加结果和原因请下载Excel查看</p>
          <div>
            {file.name}
            &nbsp;&nbsp;
            <a target="_blank"
              rel="noopener noreferrer"
              onClick={() => checkFile(file.name)}
            // href={`${config.BENEFIT_PATH}/rights/add/file/download?fileName=${file.name}`}
            >
              <Icon type="download" size="small" />下载结果
            </a>
          </div>
        </div>
      </Dialog>
    )
  }
)

export default BatchResult
