import React, { Component } from 'react'
import { Grid, Table } from '@alifd/next'
import "./treasure.scss"

const { Row, Col } = Grid
const boxType = {
  '0': '奖品'
}
const boxTypeList = [

  {
    label: '奖品',
    value: 0,
  }
]

const columns = [
  {
    title: '奖品编号',
    dataIndex: 'boxSerial',
    cell: (value, index, record, { reload }) => {
      return (
        <span>
          {record.boxSerial}
        </span>
      )
    }
  },
  {
    title: '权益投放ID',
    dataIndex: 'prizeId'
  },
  {
    title: '概率',
    dataIndex: 'percent'
  },
  {
    title: '兑换金额',
    dataIndex: 'changeAmount'
  }
]

function GiftRuleTable({ data }) {

  console.log('GiftRuleTable', data)

  return (
    <div>
      {data.length ? (
        <Table key={'boxSerial'} dataSource={data} columns={columns}>
          {columns.map((item, index) => {
            return <Table.Column key={index} title={item.title} dataIndex={item.dataIndex} />
          })}
        </Table>
      ) : (
        '暂无'
      )}
    </div>
  )
}

class GiftRule extends Component {
  generateBox = () => {
    let { dataSource } = this.props
    let smallData = dataSource.filter(item => item.boxType === 0)
    return boxTypeList.map((box, index) => {
      return (
        <div key={index}>
          <Row className="treasure-detail-block">
            <Col span={4}>
              <span>{boxType[box.value]}数量：</span>
            </Col>
            <Col span={12}>
              {smallData.length}
            </Col>
          </Row>
          <Row className="treasure-detail-block">
            <Col span={4}>
              <span>{boxType[box.value]}规则：</span>
            </Col>
            <Col span={12}>
              <GiftRuleTable data={smallData} />
            </Col>
          </Row>
        </div>

      )
    })
  }

  render() {
    return (
      <div>
        {this.generateBox()}
      </div>
    )
  }
}

export default GiftRule
