.treasure-detail-block{
    margin-bottom: 8px;
}


.treasure-detail {
  .rule-table {
    width: 300px;
    margin: 0 auto;
  }

  .newplan-title{
    padding-left:16px;
    padding-top:16px;
    margin-top:16px;
  }
  .info-card{
    border: 1px solid #ebebeb;
    }
    .boreas-form-title {
      width: 900px;
      margin: 0 auto;
      margin:16px 0 0 16px;
      .next-form-item-label {
        text-align: left;
      }
    }
    &-header {
        margin-left:16px;
        padding:16px;
      span {
        color: #999999;
      }
    }
    &-block {
      width: 900px;
      margin: 0 auto;
      font-size: 14px;
      padding: 16px 44px 16px 44px;

      .next-table {
        width: 513px;
        // padding-top: 10px;
        display: inline-block;
      }
      span {
        color: #999999;
        padding-right: 11px;
        vertical-align: top;
      }
    }
  }

  .treasure-detail-content {
    position: relative;
    &-editor {
      position: absolute;
      right: 88px;
      top: 30px;
      font-size: 16px;
    }
  }

  .treasure-log-fitertable {
    width: 900px;
    margin: 0 auto;
    font-size: 16px;
    .next-table-header {
      height: 80px;
    }
    .next-table-row {
      height: 80px;
    }
  }

  .treasure-log {
    .boreas-table {
      width: 900px;
      margin: 0 auto;
    }
  }

.treasure {
  &-wrap {
    margin: 20px;
    padding:20px 200px 20px 50px;
    background: white;
  }
}
