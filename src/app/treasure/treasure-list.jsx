/* eslint-disable radix */



import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { Dialog, Input, Form, Message, Button } from '@alifd/next'
import Moment from 'moment'
import { log } from 'util'
import filterDataSource from '@/source/filterDataSource'
import { Filter, Table } from '@/packages/table/FilterTable'
import Header from '@/components/header'
import fusionInputFix from '@/utils/fusionInputFix'
import formatDate from '@/utils/formatDate'
import BoreasDialog from '@/components/dialog'
import parseUrl from '@/packages/request/parseUrl'
import { request } from '@/packages/request'
import ChooseType from './routes/ChooseType'

const FormItem = Form.Item
const XInput = fusionInputFix()(Input)
const playTypeList = {
  1: '宝箱',
  2: '现金裂变',
  3: '一键抽奖'
}

const copyPathMap = {
  1: `/treasure/copy`,
  2: `/treasure/copyCash`,
  3: `/treasure/copyLuckDraw`
}

const detailPathMap = {
  1: `/treasure/detail`,
  2: `/treasure/cashDetail`,
  3: `/treasure/luckDrawActivityDetail`
}
const statusList = {
  0: '未生效',
  1: '已生效',
  2: '已结束'
}

const switchList = {
  0: '未下线',
  1: '已下线'
}

function changeStatus(record, type, reload) {
  request(
    {
      url: `treasure://playbox/updateState`,
      method: 'POST'
    },
    {
      query: {
        yn: type,
        status: type,
        playType: record.activityPlay.playType,
        activityId: record.activityPlay.id
      }
    }
  ).then(() => {
    Message.success('操作成功')
    reload()
  })
}

function handleChange(record, type, reload) {
  BoreasDialog({
    title: '下线',
    content: '确定要下线吗？',
    onOk: () => changeStatus(record, type, reload)
  })
}


const columns = [
  {
    title: '玩法ID',
    dataIndex: 'activityId',
    cell: (value, index, record, { reload }) => {
      return (
        <span>
          {record.activityPlay.id}
        </span>
      )
    }
  },
  {
    title: '玩法活动名称',
    dataIndex: 'playName',
    cell: (value, index, record, { reload }) => {
      return (
        <span>
          {record.activityPlay.playName}
        </span>
      )
    }
  },
  {
    title: '玩法类型',
    dataIndex: 'type',
    cell: (value, index, record, { reload }) => {
      return (
        <span>
          {playTypeList[record.activityPlay.playType]}
        </span>
      )
    }
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    width: '100px',
    cell: (value, index, record, { reload }) => {
      return (
        <span>
          {record.activityPlay.cname}
        </span>
      )
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '150px',
    cell: (value, index, record, { reload }) => {
      return (
        <span className={`boreas-status boreas-status-${record.activityPlay.status}`}>
          {statusList[record.activityPlay.status]} - {switchList[record.activityPlay.yn ? 1 : 0]}
        </span>
      )
    }
  },
  {
    title: '活动时间',
    dataIndex: 'createdAt',
    cell: (value, index, record, { reload }) => {
      return (
        <span>
          {formatDate(record.activityPlay.activityStartTime)}~{formatDate(record.activityPlay.activityEndTime)}
        </span>
      )

    }
  },
  {
    title: '创建时间',
    dataIndex: 'ctime',
    cell: (value, index, record, { reload }) => {
      return (
        <span>
          {formatDate(record.activityPlay.ctime)}
        </span>
      )

    }
  },
  {
    title: '操作',
    dataIndex: 'ops',
    cell: (value, index, record, { reload }) => {
      return (
        <div className="boreas-cell">
          <Link
            to={{
              pathname: `${detailPathMap[record.activityPlay.playType]}`,
              search: `?id=${record.activityPlay.id}`
            }}
          >
            <span className="pr br">详情</span>
          </Link>
          <Link
            to={{
              pathname: `/treasure/log`,
              search: `?id=${record.activityPlay.id}`
            }}
          >
            <span className="pl">日志</span>
          </Link>

          <Link to={`${copyPathMap[record.activityPlay.playType]}/${record.activityPlay.id}`}>
            <span className="pl">复制</span>
          </Link>
          {(record.activityPlay.status === 1 || record.activityPlay.status === 0) && record.activityPlay.yn !== 1 && (
            <span onClick={() => handleChange(record, 1, reload)} className="pl">
              下线
            </span>
          )}


        </div>
      )
    }
  }
]

function List(props) {

  const searchParmas = parseUrl(window.location.href).query || {}
  console.log(searchParmas)
  const { tableProps = {} } = props
  const [visible, setVisible] = useState(false)

  const handleEvent = (e, values, field) => {
    if (e === 'reset') {
      field.setValues({
        playName: '',
        activityId: '',
        cname: '',
        playType: '',
        status: '',
      })
      props.filterProps.search({
        playName: undefined,
        activityId: undefined,
        cname: undefined,
        playType: undefined,
        status: undefined,

      })
    }
  }


  const filterUISource = {
    'x-component': 'Filter',
    labelAlign: 'top',
    children: [
      {
        defaultValue: searchParmas.playName,
        label: '玩法名称:',
        name: 'playName',
        placeholder: '请输入玩法名称',
        'x-component': 'Input'
      },
      {
        defaultValue: searchParmas.activityId,
        label: '玩法活动ID:',
        name: 'activityId',
        placeholder: '请输入玩法活动ID',
        'x-component': 'Input',
        'data-type': 'number'
      },
      {
        defaultValue: searchParmas.cname,
        label: '创建人:',
        name: 'cname',
        placeholder: '请输入创建人名称',
        'x-component': 'Input'
      },
      {
        defaultValue: searchParmas.playType,
        label: '玩法类型:',
        name: 'playType',
        placeholder: '请选择玩法类型',
        'x-component': 'Select',
        dataSource: [
          {
            value: '',
            label: '全部'
          },
          {
            value: '1',
            label: '宝箱'
          },
          {
            value: '2',
            label: '裂变'
          },
          {
            value: '3',
            label: '一键抽奖'
          }
        ]
      },
      {
        defaultValue: searchParmas.status,
        label: '活动状态:',
        name: 'status',
        placeholder: '请选择玩法活动状态',
        'x-component': 'Select',
        dataSource: [
          {
            value: '',
            label: '全部状态'
          },
          {
            value: '0',
            label: '未生效'
          },
          {
            value: '1',
            label: '已生效'
          },
          {
            value: '2',
            label: '已结束'
          }
        ]
      },

    ]
  }

  const switchChoose = (val) => {
    setVisible(() => {
      return val
    })
  }

  const confirmChoose = (val) => {
    console.log('val', val)
    if (!val) {
      Message.error('请选择要创建的玩法类型')
      return
    }
    if (val === 1) {
      props.history.push({
        pathname: '/treasure/create',
        search: ''
      });
      // window.location.reload();
    } else if (val === 2) {
      props.history.push({
        pathname: '/treasure/createCash',
        search: ''
      });
      // window.location.reload();
    } else if (val === 3) {
      props.history.push({
        pathname: '/treasure/createLuckDraw',
        search: ''
      });
      // window.location.reload();
    } else {
      throw new Error('没有配置此类型的页面')
    }
    /**
     *   props.history.push({
          pathname: '/treasure/create',
          search: ''
        });
        window.location.reload();
     */
  }

  return (
    <div className="right-content">
      <div className="bg-fff" style={{ padding: '20px' }}>
        <div className="newplan-title">
          <Header title="互动玩法" buttonText="新建玩法活动" handleClickButton={() =>
            {
              setVisible(true)
            }
          } />
        </div>
        <Filter resetType="reset" uiSource={filterUISource} onEvent={handleEvent} {...props.filterProps} />
        <Table
          className="boreas-table"
          {...props.tableProps}
          columns={columns}
          hasBorder={false}
          emptyContent="请添加玩法~"
        />
        <ChooseType
          visible={visible}
          setVisible={switchChoose}
          onConfirm={confirmChoose} />
      </div>
    </div>
  )
}

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 }
}

export default filterDataSource({
  action: {
    url: `treasure://playbox/query`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, pageSize, ...o } = json
      return {
        ...o,
        pageNo: pageNum,
        pageSize,
      }
    },
    mapResponse: (json, { props, query }) => {
      return {
        total: json.total,
        data: json.list || []
      }
    }
  }
})(List)
