/**
 * 奖品规则的可编辑table,要支持上传图片啊！！！
 */
import React, { useState, useEffect, useContext, Component } from 'react'
import { Table, Input, Button } from '@alifd/next'
import fusionInputFix from '@/utils/fusionInputFix'
import ComponentMap from '@/packages/ali-form/componentMap'
import { useFormContext } from '@/packages/ali-form/withForm'
import FormContext from '@/packages/ali-form/FormContext'

const map = new ComponentMap({ wrap: false })

// const XInput = fusionInputFix()(Input)

function createEmptyRow(columns) {
  const row = {}

  for (let i = 0; i < columns.length; i++) {
    const col = columns[i]
    if (col.dataIndex) {
      row[col.dataIndex] = ''
    }
  }
  return row
}

function redefineColumns(props, handler, formContext) {
  const { columns, values, idname, title } = props
  return columns.map((x, i) => {
    if (!x.cell) {
      x.cell = (_, i) => {
        const C = map.get(x.xComponent)

        if (!C) {
          throw `[Editable] component ${x.xComponent} not found`
        }
        const xProps = typeof x.props === 'function' ? x.props(props, i) : x.props
        return formContext.componentMap.render(C, xProps, {
          value: formContext.field.getValue(`${idname}.${i}.${x.dataIndex}`) || xProps.defaultValue,
          handleChange: (e) => handler(x.dataIndex, e, i)
        })
      }
    }
    return x
  })
}

export default (props) => {

  const [data, setData] = useState([])
  const [inited, setInited] = useState(false)
  const { formContext } = useContext(FormContext.ctx)
  const [emptyRows, setEmptyRows] = useState(props.emptyRows);
  const columns = redefineColumns(props, handleCellChange, formContext)

  function handleCellChange(name, value, i) {
    setData(list => {
      // console.log('list', list)
      // console.log('formContext',formContext.field.getValue(props.idname))
      // const orignList = formContext.field.getValue(props.idname) || list
      const orignList = list || formContext.field.getValue(props.idname)
      // console.log('orignList', orignList)

      orignList[i][name] = value
      return [...orignList]
    })
  }

  useEffect(() => {
    if (!inited) {
      if (formContext.field.getValue(props.idname) !== undefined) {
        setData(formContext.field.getValue(props.idname));
      }
    }
  }, [])

  useEffect(() => {
    if (inited) {
      // console.log('update,看看数据的长度与emptyRows的长度一致不？')
      updataEmptyRow()
    }
  }, [props.value])

  useEffect(() => {
    if (inited) {
      props.onChange && props.onChange(data)
    }
  }, [data])

  function updataEmptyRow() {
    // console.log('需要更新否？')
    if (data.length === emptyRows) {
      // console.log(data.length)
      // console.log(emptyRows)
      console.log('若更新的emptyRows长度与再有的data行数一致则无需变更')
    } else {

      setData(data => {
        if (data.length < emptyRows) { // 若数量由1变3，则再添加几个
          // console.log('需要更新否若数量由1变3，则再添加几个？')
          for (let i = data.length; i < emptyRows; i++) {
            data.push(createEmptyRow(props.columns))
          }
          return [...data]
        } else { // 若数量由3变1，则移除后面的几位
          // console.log('若数量由3变1，则移除后面的几位')
          // for (let i = data.length; i < emptyRows; i++) {
          data.splice(emptyRows, data.length - emptyRows)
          // }
          return [...data]
        }

      })
      setTimeout(() => {
        setInited(x => true)
      })
    }
  }


  useEffect(() => {
    if (emptyRows) {
      updataEmptyRow();
    }
  }, [emptyRows])

  return <div>
    <Table
      dataSource={data}
    >
      {columns.map(col => {
        return <Table.Column {...col} />
      })}
      {
        (props.deleteButton && emptyRows > (props.minLength || 0)) && <Table.Column cell={(x, i) => {
          return <Button warning size="small" style={{ marginBottom: 16 }} onClick={() => {
            setData(data => {
              data.splice(i, 1)
              return [...data]
            });
            setEmptyRows(emptyRows - 1)
          }}>删除</Button>
        }} />
      }
    </Table>
    {
      props.addButton && <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 10 }}>
        {
          props.maxLength !== undefined &&
          <span style={{ lineHeight: '32px', marginRight: 10, color: 'grey' }}>{emptyRows}/{props.maxLength}</span>
        }
        <Button size="small" disabled={props.maxLength !== undefined && emptyRows >= props.maxLength} onClick={() => {
          if (props.maxLength === undefined || emptyRows < props.maxLength) {
            setEmptyRows(emptyRows + 1)
          }
        }}>添加</Button>
      </div>
    }
  </div>
}

