import React, { Component, Suspense, lazy } from 'react'

import TreasureList from './treasure-list'
import TreasureActivity from './routes/activity'
import CashActivity from './routes/cashActivity'
import luckDrawActivity from './routes/luckDrawActivity';
import TreasureDetailWrap from './treasure-detail'
import CashDetailWrap from './cash-detail'
import luckDrawActivityDetail from './luck_draw_activity-detail'
import TreasureLog from './treasure-log'
import Routers from '@/packages/routers'

function toPath(file) {
  const prts = file.split('.')
  prts.pop()
  return '/' + prts.filter(x => x).join('/')

}

const req = require.context('./routes')
const files = req.keys()

const routerList = [
  {
    // 玩法列表
    subRouterPath: '/treasure',
    routerItems: [
      {
        path: '/',
        component: TreasureList,
        breadcrumbName: '互动玩法'
      },
      {
        path: '/list',
        component: TreasureList,
        breadcrumbName: '互动玩法'
      },
      {
        path: '/copy/:id(\\d+)?',
        component: TreasureActivity,
        breadcrumbName: '玩法复制'
      },
      {
        path: '/copyCash/:id(\\d+)?',
        component: CashActivity,
        breadcrumbName: '玩法复制'
      },
      {
        path: '/copyLuckDraw/:id(\\d+)?',
        component: luckDrawActivity,
        breadcrumbName: '玩法复制'
      },
      {
        path: '/create',
        component: TreasureActivity,
        breadcrumbName: '玩法创建'
      },
      {
        path: '/createCash',
        component: CashActivity,
        breadcrumbName: '玩法创建'
      },
      {
        path: '/createLuckDraw',
        component: luckDrawActivity,
        breadcrumbName: '玩法创建'
      },
      {
        path: '/detail',
        component: TreasureDetailWrap,
        breadcrumbName: '玩法详情'
      },
      {
        path: '/cashDetail',
        component: CashDetailWrap,
        breadcrumbName: '玩法详情'
      },
      {
        path: '/luckDrawActivityDetail',
        component: luckDrawActivityDetail,
        breadcrumbName: '玩法详情'
      },
      {
        path: '/log',
        component: TreasureLog,
        breadcrumbName: '玩法日志'
      }
    ]
  }
]

const breadcrumbMap = {
  '/': '互动玩法',
  '/treasure/list': '互动玩法',
  '/treasure/detail': '玩法详情',
  '/treasure/log': '玩法日志'
}

function App() {
  return <Routers routerList={routerList} redirectPath="/treasure" breadcrumbMap={breadcrumbMap} />
}

export default App
