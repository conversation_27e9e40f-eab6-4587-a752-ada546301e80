import React, { Component } from 'react'
import { Grid, Table } from '@alifd/next'
import bosify from '@/utils/bosify'
import "./treasure.scss"

const { Row, Col } = Grid


const renderCell = (_v) => {
  console.log('-v',_v)
  if (_v && _v.match(/^(http|https):/)) {
    return <img src={bosify(_v)} style={{ maxWidth: '80px' }} />
  }
  return _v
}

const boxType = {
  '0': '奖品'
}
const boxTypeList = [

  {
    label: '奖品',
    value: 0,
  }
]

const columns = [
  {
    title: '奖品编号',
    dataIndex: 'boxSerial',
    cell: (value, index, record, { reload }) => {
      return (
        <span>
          {record.boxSerial}
        </span>
      )
    }
  },
  // {
  //   title: '成团人数',
  //   dataIndex: 'groupNum'
  // },
  {
    title: '权益投放ID',
    dataIndex: 'prizeId'
  },
  {
    title: '权益领取数量',
    dataIndex: 'prizeGetNum'
  },
  {
    title: '兑换金额',
    dataIndex: 'changeAmount'
  },
  {
    title: '奖品名称',
    dataIndex: 'name'
  },
  // {
  //   title: '奖品图标',
  //   dataIndex: 'image',
  //   cell: (value, index, record, { reload }) => {
  //     const url = JSON.parse(record.image)[0].url
  //     return (
  //       <span>
  //         {url}
  //       </span>
  //     )
  //   }
  // }
]

function GiftRuleTable({ data }) {

  console.log('GiftRuleTable', data)

  return (
    <div>
      {data.length ? (
        <Table key={'boxSerial'} dataSource={data} columns={columns}>
          {columns.map((item, index) => {
            return <Table.Column key={index} title={item.title} dataIndex={item.dataIndex} />
          })}
        </Table>
      ) : (
          '暂无'
        )}
    </div>
  )
}

class GiftRule extends Component {

  generateBox = () => {

    let { dataSource } = this.props

    // let bigData = dataSource.filter(item => item.boxType === 1)
    let smallData = dataSource.filter(item => item.boxType === 0)

    let nodes = boxTypeList.map((box, index) => {

      return (
        <div key={index}>
          <Row className="treasure-detail-block">
            <Col span={4}>
              <span>{boxType[box.value]}数量：</span>
            </Col>
            <Col span={12}>
              {smallData.length}
            </Col>
          </Row>
          <Row className="treasure-detail-block">
            <Col span={4}>
              <span>{boxType[box.value]}规则：</span>
            </Col>
            <Col span={12}>
              <GiftRuleTable data={smallData} />
            </Col>
          </Row>
        </div>

      )
    })

    return nodes;

  }

  render() {
    return (
      <div>
        {this.generateBox()}
      </div>
    )
  }
}

export default GiftRule
