import React, { Component } from 'react'
import { But<PERSON>, Message, Dialog } from '@alifd/next'
import moment from 'moment';
import CreateFormHelper from '@/packages/ali-form/CreateFormHelper'
import Render from '@/packages/ali-form'
import Editable from '../component/Editable'
import withForm from '@/packages/ali-form/withForm'
import UploadImger from '@/components/UploadImg'
import './cash.scss'

moment.locale('zh-cn');

const formatDate = (date) => moment(date).format('YYYY-MM-DD HH:mm:ss')
const defaultTimeValues = [moment('00:00:00', 'HH:mm:ss', true), moment('23:59:59', 'HH:mm:ss', true)];
const checkNum = (min, max, name) => {
  return (rule, value, callback) => {
    if (min === undefined || !max) callback()
    if (value !== null || value !== undefined) {
      if (!/\d+/.test(value)) return callback(`${name}必须填入数字`)
      if (min <= value && max >= value) return callback()
      return callback(`${name}不能小于${min}且大于${max}`)
    } else {
      callback()
    }
  }
}
const isValidNum = (formContext) => {

  let isSend = false;
  const everydayAddToNum = formContext.field.getValue('everydayAddToNum')
  if (everydayAddToNum > 9) {
    return `每天初始抽奖次数最大为9`
  }
  // [0].forEach((x, xi) => {
  let list = formContext.field.getValue(`boxType0`)
  console.log(`查看一下奖品规则0`, list)
  const _smallBoxNum = formContext.field.getValue('smallBoxNum')
  if (!list) {
    // console.log(`查看一下宝箱22222${x}`,list)
    isSend = `奖品规则为必填字段`

  } else {
    // eslint-disable-next-line no-nested-ternary
    console.log('_smallBoxNum', _smallBoxNum)
    const initList = []

    list.forEach((n, ni) => {
      if (n.prizeId === '' || n.prizeId == undefined) { // 权益投放id不能为空
        isSend = '权益投放ID不能为空'
        return isSend
      }
      if ((n.percent < 1 || n.percent > 100)) {
        isSend = '概率 必须是1到100'
        return isSend
      }
      if ((n.changeAmount < 1 || n.changeAmount > 999)) {
        isSend = '权益领取数量必须是1到999'
        return isSend
      }
      if (n.name === '') { // 奖品名称为非必填
        isSend = false

      }

    })

  }

  // })
  return isSend
}

const cfh = new CreateFormHelper()
let smallBoxNum = 1
let cashType = false; // 非现金裂变

const basic = cfh.createGroup('基本信息')
basic.addField('playType', 'Select', {
  label: '玩法类型',
  placeholder: '请选择玩法类型',
  defaultValue: 3,
  disabled: true,
  dataSource: [
    { label: '开宝箱', value: 1 },
    { label: '现金裂变', value: 2 },
    { label: '一键抽奖', value: 3 }
  ],
  style: { width: '100%' },
  rules: [{
    required: true,
    message: '玩法类型不能为空'
  }],
  onChange: (val) => {
    console.log(val)

  }
})
basic.addField('playName', 'Input', {
  label: '玩法名称',
  placeholder: '请填写活动名称，不超过20个字',
  maxLength: 20,
  hasLimitHint: true,
  rules: [{
    required: true,
    message: '玩法名称不能为空'
  }]
})
basic.addField('time', 'RangePicker', {
  label: '活动时间',
  style: { width: '100%' },
  showTime: { defaultValue: defaultTimeValues },
  rules: [{
    required: true,
    message: '请选择活动时间'
  }]
})

const actGroup = cfh.createGroup('玩法规则设置')

function timesHelper(label, min, max, defaultValue, innerAfter = '', isRequired = true, precision = 1) {
  return {
    label,
    innerAfter,
    placeholder: min || Number(min) === 0 ? `支持输⼊数字${min}～${max}` : '只支持输⼊数字',
    step: min || 1,
    type: 'number',
    min,
    max,
    precision,
    defaultValue,
    style: { width: '100%' },
    rules: [{
      required: isRequired,
      message: `请填写${label}`
    }, { validator: checkNum(min, max, label) }]
  }
}

actGroup.addField('everyLaunchTotal', 'Input', timesHelper('每人活动期间可抽奖次数', 1, 9999))
actGroup.addField('everydayLaunchNum', 'Input', timesHelper('每人每天可抽奖次数', 1, 9999))
actGroup.addField('everydayAddToNum', 'Input', timesHelper('每天初始抽奖次数', 0, 9))
actGroup.addField('addStatus', 'RadioGroup', {
  label: '追加抽奖次数任务',
  defaultValue: '0',
  dataSource: [
    {
      value: '0',
      label: '无任务'
    },
    {
      value: '1',
      label: '外部任务'
    }
  ]
})

function createTableField(label, emptyRows, idname) {
  return {
    label,
    emptyRows,
    title: label,
    addButton: true,
    deleteButton: true,
    maxLength: 9,
    minLength: 1,
    idname,
    columns: [
      {
        title: '奖品编号',
        cell: (x, i) => {
          return (i + 1) + ""
        }
      },
      {
        key: 'prizeId',
        dataIndex: 'prizeId',
        name: 'prizeId',
        title: '权益定投ID',
        xComponent: 'Input',
        props: timesHelper('')
      }, {
        key: 'percent',
        dataIndex: 'percent',
        title: '概率',
        name: 'percent',
        xComponent: 'Input',
        props: (x, i) => {
          return timesHelper('', 1, 100)
        }
      },
      {
        key: 'changeAmount',
        dataIndex: 'changeAmount',
        title: '权益领取数量',
        name: 'changeAmount',
        xComponent: 'Input',
        props: (x, i) => {
          return timesHelper('', 1, 999)
        }
      }
    ]
  }
}

actGroup.addField('boxType0', 'Editable', createTableField('奖品规则', smallBoxNum, 'boxType0'))


class activity extends Component {

  onCancel = () => {
    Dialog.confirm({
      title: '取消玩法',
      content: '确定要取消该活动玩法吗？',
      // onOk: () => this.props.history.push('/'),
      onOk: () => this.props.history.replace('/treasure'),
      onCancel: () => console.log('cancel')
    });
  }

  onSubmit = () => {
    const { context, submit, formContext, form } = this.props

    formContext.field.validate((errors, data) => {
      console.log(data);
      if (errors) {
        const arr = Object.keys(errors).map(x => errors[x])
        Message.error(arr[0].errors[0])
      } else {
        const isError = isValidNum(formContext)
        if (isError) return Message.error(isError)
        Dialog.confirm({
          title: '创建玩法',
          content: '确定要创建该活动玩法吗？',
          onOk: () => {
            const result = this.props.submit()
            result.then(res => {
              if (String(res.errorCode) === "0" || res.errorDesc === "success") {
                Message.show({
                  type: 'success',
                  content: '创建成功',
                  afterClose: () => this.props.history.push('/')
                });
              } else {
                Message.error(res.errorDesc || '创建失败')
              }
            }).catch(msg => {
              console.log(msg)
              Message.error(msg.error || msg.msg || '提交失败')
            })
          },
          onCancel: () => console.log('cancel')
        });
      }
    })
  }

  render() {
    return <div className="treasure-wrap">
      <Render key={smallBoxNum} />
      <p style={{ textAlign: 'right' }}>
        <Button onClick={this.onCancel} style={{ marginRight: 10 }}>取消</Button>
        <Button onClick={this.onSubmit} type="primary">创建</Button>
      </p>
    </div>
  }
}

export default withForm({
  uiSource: cfh.toJSON(),
  onContextInit: context => {
    // console.log('页面的内容初始化')
    context.componentMap.addComponent('Editable', Editable)
    context.componentMap.addComponent('UploadImger', UploadImger)
  },
  requestAction: {
    url: `treasure://playbox/getDetail?id={props.match.params.id}`,
    isActive: ({ props }) => {
      console.log(props.match.params)
      let isEdit = typeof (props.match.params.id) !== 'undefined'
      return isEdit
    },
    mapResponse: (res) => {
      console.log('res box info', res)

      const { id, activityStartTime, activityEndTime, ...resetProps } = res.activityPlay
      const boxList = res.activityBoxList
      smallBoxNum = res.activityBoxList.length
      actGroup.modifyField('boxType0', 'Editable', createTableField('奖品规则', smallBoxNum, 'boxType0'))
      const filterFunc = (x, n) => {
        if (String(x.boxType) === n) {
          delete x.id

          return true
        }
        return false
      }
      resetProps.addStatus = resetProps.addStatus.toString()

      let newList = []
      boxList.forEach(item => {
        // console.log('整理boxList中的imageList')
        // console.log(item)
        // item.imageList = JSON.parse(item.image)
        newList.push(item)
      })

      return {
        time: [activityStartTime, activityEndTime],
        ...resetProps,
        boxType0: newList

      }
    }
  },
  submitAction: {
    url: `treasure://playbox/create`,
    mapRequest: (query, obj) => {
      let boxList = [];
      const boxType0 = query.boxType0;
      boxList = (boxType0 || []).map((item, index) => {
        item.boxType = 0
        item.boxSerial = index + 1

        return item
      })
      const [activityStartTime, activityEndTime] = query.time
      return {
        activityPlay: {
          activityEndTime: formatDate(activityEndTime),
          activityStartTime: formatDate(activityStartTime),
          everyLaunchTotal: query.everyLaunchTotal,
          limitTime: query.limitTime,
          everydayLaunchNum: query.everydayLaunchNum,
          everydayAddToNum: query.everydayAddToNum,
          addStatus: query.addStatus,
          playName: query.playName,
          playType: query.playType,
          everybodyDayHelpForOnepeopleNum: query.everybodyDayHelpForOnepeopleNum,
          everydayHelpNum: 0,
        },
        boxList
      }
    }
  }
})(activity)
