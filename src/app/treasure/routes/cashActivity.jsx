import React, { Component } from 'react'
import { But<PERSON>, Message, Dialog } from '@alifd/next'
import moment from 'moment';
import CreateFormHelper from '@/packages/ali-form/CreateFormHelper'
import Render from '@/packages/ali-form'
import Editable from '../component/Editable'
import withForm from '@/packages/ali-form/withForm'
import UploadImger from '@/components/UploadImg'
import './cash.scss'

moment.locale('zh-cn');

const formatDate = (date) => moment(date).format('YYYY-MM-DD HH:mm:ss')
const defaultTimeValues = [moment('00:00:00', 'HH:mm:ss', true), moment('23:59:59', 'HH:mm:ss', true)];
const checkNum = (min, max, name) => {
  return (rule, value, callback) => {
    if (min === undefined || !max) callback()
    if (value !== null || value !== undefined) {
      if (!/\d+/.test(value)) return callback(`${name}必须填入数字`)
      if (min <= value && max >= value) return callback()
      return callback(`${name}不能小于${min}且大于${max}`)
    } else {
      callback()
    }
  }
}
const isValidNum = (formContext) => {

  let isSend = false;
  // [0].forEach((x, xi) => {
    let list = formContext.field.getValue(`boxType0`)
    console.log(`查看一下奖品规则0`,list)
    const _smallBoxNum = formContext.field.getValue('smallBoxNum')
    if (!list) {
      // console.log(`查看一下宝箱22222${x}`,list)
        isSend = `奖品规则为必填字段`

    } else {
      // eslint-disable-next-line no-nested-ternary
      console.log('_smallBoxNum', _smallBoxNum)
      const initList = []

      // const mergeList = initList.map((item, itemi) => ({ ...list[itemi], ...item }))
      list.forEach((n, ni) => {
        // const isNull = Object.values(n).some(y => y === '' || y == undefined)

        //  if ((n.groupNum === '' || n.groupNum == undefined) || (n.groupNum < 1 || n.groupNum > 4)) {
        //     isSend = '成团人数必须是1到4'
        //     return isSend
        //   }
          if ((n.prizeGetNum === '' || n.prizeGetNum == undefined) || (n.prizeGetNum < 1 || n.prizeGetNum > 99)) {
            isSend = '权益领取数量必须是1到99'
            return isSend
          }
           if (n.prizeId === '' || n.prizeId == undefined) { // 权益投放id不能为空
            isSend = '权益投放ID不能为空'
            return isSend
          }
           if ((n.prizeId === '' || n.prizeId == undefined) || (n.changeAmount < 1 || n.changeAmount > 999)) {
            isSend = '兑换金额必须是1到999'
            return isSend
          }
           if (n.name === '') { // 奖品名称为非必填
            isSend = false

          }

      })

    }

  // })
  return isSend
}

const cfh = new CreateFormHelper()
let smallBoxNum = 1
let cashType = false; // 非现金裂变

const basic = cfh.createGroup('基本信息')
basic.addField('playType', 'Select', {
  label: '玩法类型',
  placeholder: '请选择玩法类型',
  defaultValue: 2,
  disabled: true,
  dataSource: [
    { label: '开宝箱', value: 1 },
    { label: '现金裂变', value: 2 },
    { label: '一键抽奖', value: 3 }
  ],
  style: { width: '100%' },
  rules: [{
    required: true,
    message: '玩法类型不能为空'
  }],
  onChange: (val) => {
    console.log(val)

  }
})

basic.addField('playName', 'Input', {
  label: '玩法名称',
  placeholder: '请填写活动名称，不超过20个字',
  maxLength: 20,
  hasLimitHint: true,
  rules: [{
    required: true,
    message: '玩法名称不能为空'
  }]
})


basic.addField('time', 'RangePicker', {
  label: '活动时间',
  style: { width: '100%' },
  showTime: { defaultValue: defaultTimeValues },
  rules: [{
    required: true,
    message: '请选择活动时间'
  }]
})

const actGroup = cfh.createGroup('玩法规则设置')

function timesHelper(label, min, max, defaultValue, innerAfter = '', isRequired = true, precision = 1) {
  return {
    label,
    innerAfter,
    placeholder: min || Number(min) === 0 ? `支持输⼊数字${min}～${max}` : '只支持输⼊数字',
    step: min || 1,
    type: 'number',
    min,
    max,
    precision,
    defaultValue,
    style: { width: '100%' },
    rules: [{
      required: isRequired,
      message: `请填写${label}`
    }, { validator: checkNum(min, max, label) }]
  }
}
actGroup.addField('limitTime', 'Input', timesHelper('限制时长', 0.1, 999, null, "小时 "))
actGroup.addField('withdrawalAmount', 'Input', timesHelper('提现金额', 1, 50))

actGroup.addField('everyLaunchTotal', 'Input', timesHelper('每人活动期间可发起次数', 1, 99))
actGroup.addField('everydayLaunchNum', 'Input', timesHelper('每人每天可发起次数', 1, 9, 1))
actGroup.addField('everyHelpTotal', 'Input', timesHelper('每人活动期间可助力次数', 1, 99))
actGroup.addField('everydayHelpNum', 'Input', timesHelper('每人每天可助力次数', 1, 9, 2))
actGroup.addField('everybodyDayHelpForOnepeopleNum', 'Input', timesHelper('每人每天可给同一人助力次数', 1, 9))
actGroup.addField('everydayFirstHelpPrizeId', 'Input', {
  label: '新人助力奖励',
  placeholder: `填写权益投放id`,
  type: 'number',
  rules: [{
    required: true,
    message: `填写权益投放id`
  }]
})

actGroup.addField('smallBoxNum', 'Select', {
  label: '奖品数量',
  style: { width: '100%' },
  defaultValue: 1,
  showSearch: true,
  onChange: (val) => {
    smallBoxNum = val;
    actGroup.modifyField('boxType0', 'Editable', createTableField('奖品规则', val, 'boxType0'))

  },
  dataSource: [
    { label: '1', value: 1 },
    { label: '2', value: 2 },
    { label: '3', value: 3 },
    { label: '4', value: 4 },
    { label: '5', value: 5 },
    { label: '6', value: 6 },
    { label: '7', value: 7 },
    { label: '8', value: 8 },
    { label: '9', value: 9 },
    { label: '10', value: 10 },
    { label: '11', value: 11 },
    { label: '12', value: 12 },
    { label: '13', value: 13 },
    { label: '14', value: 14 },
    { label: '15', value: 15 },
    { label: '16', value: 16 },
    { label: '17', value: 17 },
    { label: '18', value: 18 },
    { label: '19', value: 19 },
    { label: '20', value: 20 },

  ],
  type: 'number',
  rules: [{
    required: true,
    message: `请选择奖品数量`
  }]
})

function createTableField(label, emptyRows, idname) {
  return {
    label,
    emptyRows,
    title: label,
    idname,
    columns: [{
      key: 'boxSerial',
      dataIndex: 'boxSerial',
      title: '奖品编号',
      cell: (x, i) => {
        return (i + 1) + ""
      }
    },
    // {
    //   key: 'groupNum',
    //   dataIndex: 'groupNum',
    //   name: 'groupNum',
    //   title: '成团人数',
    //   xComponent: 'Input',
    //   props: (x, i) => {
    //     return timesHelper('', 1, 4)
    //   }
    // },
    {
      key: 'prizeId',
      dataIndex: 'prizeId',
      name: 'prizeId',
      title: '权益投放ID',
      xComponent: 'Input',
      props: timesHelper('')
    }, {
      key: 'prizeGetNum',
      dataIndex: 'prizeGetNum',
      title: '权益领取数量',
      name: 'prizeGetNum',
      xComponent: 'Input',
      props: (x, i) => {
        return timesHelper('', 1, 99)
      }
    },
    {
      key: 'changeAmount',
      dataIndex: 'changeAmount',
      title: '兑换金额',
      name: 'changeAmount',
      xComponent: 'Input',
      props: (x, i) => {
        return timesHelper('', 1, 999)
      }
    },
    {
      key: 'name',
      dataIndex: 'name',
      name: 'name',
      title: '奖品名称',
      xComponent: 'Input',
      props: () => {
        return {
          placeholder: '请填写奖品名称',
          maxLength: 10,
          hasLimitHint: true,
        }
      }
    },
    // {
    //   key: 'imageList',
    //   dataIndex: 'imageList',
    //   name: 'imageList',
    //   title: '奖品图标',
    //   xComponent: 'UploadImger',
    //   props: () => {
    //     return {
    //       // hints: ['图片上传数量：最多三张', '上传尺寸：440*440px', '上传格式：png'],
    //       maxImages: 1,
    //       actionUrl: 'treasure://activity/image/upload',
    //       actionHeader: { 'X-Requested-With': null },
    //       successResult: (result) => {
    //         console.log(result)
    //         let img = {
    //           url: result.data.data.url || '',
    //           official: '',
    //           type: 0,
    //         }
    //         return img
    //        }
    //     }
    //   }
    // },
  ]
  }
}

actGroup.addField('boxType0', 'Editable', createTableField('奖品规则', smallBoxNum, 'boxType0'))


class activity extends Component {

  onCancel = () => {
    Dialog.confirm({
      title: '取消玩法',
      content: '确定要取消该活动玩法吗？',
      // onOk: () => this.props.history.push('/'),
      onOk: () => this.props.history.replace('/treasure'),
      onCancel: () => console.log('cancel')
    });
  }

  onSubmit =() => {
    const { context, submit, formContext, form } = this.props

    formContext.field.validate((errors, data) => {
      if (errors) {
        const arr = Object.keys(errors).map(x => errors[x])
        Message.error(arr[0].errors[0])
      } else {
        const isError = isValidNum(formContext)
        if (isError) return Message.error(isError)
        Dialog.confirm({
          title: '创建玩法',
          content: '确定要创建该活动玩法吗？',
          onOk: () => {
            const result = this.props.submit()
            result.then(res => {
              if (String(res.errorCode) === "0" || res.errorDesc === "success") {
                Message.show({
                  type: 'success',
                  content: '创建成功',
                  afterClose: () => this.props.history.push('/')
                });
              } else {
                Message.error(res.errorDesc || '创建失败')
              }
            }).catch(msg => {
              console.log(msg)
              Message.error(msg.error || '提交失败')
            })
          },
          onCancel: () => console.log('cancel')
        });
      }
    })
  }

  render() {
    return <div className="treasure-wrap">
    <Render key={smallBoxNum} />
    <p style={{ textAlign: 'right' }}>
      <Button onClick={this.onCancel} style={{ marginRight: 10 }}>取消</Button>
      <Button onClick={this.onSubmit} type="primary">创建</Button>
    </p>
  </div>
  }
}

export default withForm({
  uiSource: cfh.toJSON(),
  onContextInit: context => {
    // console.log('页面的内容初始化')
    context.componentMap.addComponent('Editable', Editable)
    context.componentMap.addComponent('UploadImger',UploadImger)
  },
  requestAction: {
    url: `treasure://playbox/getDetail?id={props.match.params.id}`,
    isActive: ({ props }) => {
      console.log(props.match.params)
      let isEdit = typeof (props.match.params.id) !== 'undefined'
      return isEdit
    },
    mapResponse: (res) => {
      console.log('res box info', res)

      const { id, activityStartTime, activityEndTime, ...resetProps } = res.activityPlay
      const boxList = res.activityBoxList
      smallBoxNum = res.activityPlay.smallBoxNum
      actGroup.modifyField('boxType0', 'Editable', createTableField('奖品规则', res.activityPlay.smallBoxNum, 'boxType0'))
      const filterFunc = (x, n) => {
        if (String(x.boxType) === n) {
          delete x.id

          return true
        }
        return false
      }

      let newList = []
      boxList.forEach(item => {
        // console.log('整理boxList中的imageList')
        // console.log(item)
        // item.imageList = JSON.parse(item.image)
        newList.push(item)
    })

      return {
        time: [activityStartTime, activityEndTime],
        ...resetProps,
        boxType0: newList

      }
    }
  },
  submitAction: {
    url: `treasure://playbox/create`,
    mapRequest: (query, obj) => {
      console.log('submitAction', query)
      let boxList = [];
      const boxType0 = query.boxType0;
      // [0].forEach(xi => {
      //   // eslint-disable-next-line no-nested-ternary
      //   const initList = ([{ boxSerial: 1, groupNum: '0', prizeGetNum: '1' }])

      //   boxList = boxList.concat(initList.map((item, itemi) => ({ ...query[`boxType${xi}`][itemi], ...item, boxType: xi })))
      // })

      boxList = (boxType0 || []).map((item, index) => {
        item.boxType = 0
        item.boxSerial = index + 1

        return item
      })

      console.log(boxList)

      const [activityStartTime, activityEndTime] = query.time
      return {
        activityPlay: {
          activityEndTime: formatDate(activityEndTime),
          activityStartTime: formatDate(activityStartTime),
          everyHelpTotal: query.everyHelpTotal,
          everyLaunchTotal: query.everyLaunchTotal,
          everydayFirstHelpPrizeId: query.everydayFirstHelpPrizeId,
          everydayHelpNum: query.everydayHelpNum,
          everydayLaunchNum: query.everydayLaunchNum,
          limitTime: query.limitTime,
          playName: query.playName,
          playType: query.playType,
          smallBoxNum: query.smallBoxNum,
          withdrawalAmount: query.withdrawalAmount,
          everybodyDayHelpForOnepeopleNum: query.everybodyDayHelpForOnepeopleNum,
        },
        boxList
      }
    }
  }
})(activity)
