import React, { Component } from 'react'
import { But<PERSON>, Message, Dialog } from '@alifd/next'
import moment from 'moment';
import CreateFormHelper from '@/packages/ali-form/CreateFormHelper'
import Render from '@/packages/ali-form'
import Editable from '@/packages/Editable'
import withForm from '@/packages/ali-form/withForm'

moment.locale('zh-cn');

const formatDate = (date) => moment(date).format('YYYY-MM-DD HH:mm:ss')
const defaultTimeValues = [moment('00:00:00', 'HH:mm:ss', true), moment('23:59:59', 'HH:mm:ss', true)];
const checkNum = (min, max, name) => {
  return (rule, value, callback) => {
    if (min === undefined || !max) callback()
    if (value !== null || value !== undefined) {
      if (!/\d+/.test(value)) return callback(`${name}必须填入数字`)
      if (min <= value && max >= value) return callback()
      return callback(`${name}不能小于${min}且大于${max}`)
    } else {
      callback()
    }
  }
}


const isValidNum = (formContext) => {

  let isSend = false;
  let newUserNum = formContext.field.getValue('newUserNum');
  [0, 1].forEach((x, xi) => {
    let list = formContext.field.getValue(`boxType${x}`)
    console.log(`查看一下宝箱${x}`,list)
    const _smallBoxNum = formContext.field.getValue('smallBoxNum')

    if (!list && x === 0) {
      list = [{
        boxSerial: "",
        groupNum: "1",
        prizeGetNum: "1",
        prizeId: ""
      }]
      formContext.field.setValue(`boxType0`, list)
    }

    if (!list) {
      isSend = `宝箱玩法规则都为必填字段`
    } else {
      // eslint-disable-next-line no-nested-ternary
      const initList = xi ? [{ boxSerial: 1 }] :
      (
        _smallBoxNum > 1 ? [{ boxSerial: 1, groupNum: '0', prizeGetNum: '1' }, { boxSerial: 2, prizeGetNum: '1' }, { boxSerial: 3, prizeGetNum: '1' }] :
        [{ boxSerial: 1, groupNum: '0', prizeGetNum: '1' }]
      )
      const mergeList = initList.map((item, itemi) => ({ ...list[itemi], ...item }))
      mergeList.forEach((n, ni) => {
        const isNull = Object.values(n).some(y => y === '' || y == undefined)
        if (_smallBoxNum > 1 || x === 1) { // 小宝箱数量为3或者校验的是大宝箱
          if (isNull) {
            isSend = '玩法规则都为必填字段'
          } else if ((xi !== 0 || ni !== 0) && (n.groupNum < 1 || n.groupNum > 12) && x === 1) {
            isSend = '成团人数必须是1到12'
          } else if ((n.groupNum < newUserNum)) {
            isSend = '新用户数不可超过宝箱成团人数'
          } else if ((xi !== 0 || ni !== 0) && (n.groupNum < 1 || n.groupNum > 4) && x === 0) {
            isSend = '成团人数必须是1到4'
          } else if (n.prizeGetNum < 1 || n.prizeGetNum > 2) {
            isSend = '权益领取数量必须是1到2'
          }
        } else { // 小宝箱数量不大于1时，允许通过
          console.log(`小宝箱数量不大于1,boxType${x}`)
        }
      })
    }

  })
  return isSend
}

const cfh = new CreateFormHelper()
let smallBoxNum = 1
let cashType = false; // 非现金裂变

const basic = cfh.createGroup('基本信息')
basic.addField('playType', 'Select', {
  label: '玩法类型',
  placeholder: '请选择玩法类型',
  defaultValue: 1,
  disabled: true,
  dataSource: [
    { label: '开宝箱', value: 1 },
    { label: '现金裂变', value: 2 },
    { label: '一键抽奖', value: 3 }
  ],
  style: { width: '100%' },
  rules: [{
    required: true,
    message: '玩法类型不能为空'
  }],
  onChange: (val) => {
    console.log(val)

  }
})

basic.addField('playName', 'Input', {
  label: '玩法名称',
  placeholder: '请填写活动名称，不超过20个字',
  maxLength: 20,
  hasLimitHint: true,
  rules: [{
    required: true,
    message: '玩法名称不能为空'
  }]
})


basic.addField('time', 'RangePicker', {
  label: '活动时间',
  style: { width: '100%' },
  showTime: { defaultValue: defaultTimeValues },
  rules: [{
    required: true,
    message: '请选择活动时间'
  }]
})

const actGroup = cfh.createGroup('玩法规则设置')

actGroup.addField('smallBoxNum', 'Select', {
  label: '小宝箱数量',
  style: { width: '100%' },
  defaultValue: 1,
  onChange: (val) => {
    smallBoxNum = val;
    actGroup.modifyField('boxType0', 'Editable', createTableField('小宝箱规则', val, 'boxType0'))
    actGroup.modifyField('boxType1', 'Editable', createTableField('大宝箱规则', 1, 'boxType1'))
  },
  dataSource: [
    { label: '1', value: 1 },
    { label: '3', value: 3 }
  ],
  type: 'number',
  rules: [{
    required: true,
    message: `请选择小宝箱数量`
  }]
})
function timesHelper(label, min, max, defaultValue, innerAfter = '', isRequired = true, precision = 1) {
  return {
    label,
    innerAfter,
    placeholder: min || Number(min) === 0 ? `支持输⼊数字${min}～${max}` : '只支持输⼊数字',
    step: min || 1,
    type: 'number',
    min,
    max,
    precision,
    defaultValue,
    style: { width: '100%' },
    rules: [{
      required: isRequired,
      message: `请填写${label}`
    }, { validator: checkNum(min, max, label) }]
  }
}
actGroup.addField('limitTime', 'Input', timesHelper('限制时长', 0.1, 999, null, "小时 "))


actGroup.addField('everyLaunchTotal', 'Input', timesHelper('每人活动期间可发起次数', 1, 99))
actGroup.addField('everydayLaunchNum', 'Input', timesHelper('每人每天可发起次数', 1, 9, 1))
actGroup.addField('everyHelpTotal', 'Input', timesHelper('每人活动期间可助力次数', 1, 99))
actGroup.addField('everydayHelpNum', 'Input', timesHelper('每人每天可助力次数', 1, 9, 2))
actGroup.addField('everydayFirstHelpPrizeId', 'Input', {
  label: '每天首次助力奖励',
  placeholder: `填写权益投放id`,
  type: 'number',
  style: { width: '100%' },
  rules: [{
    required: true,
    message: `填写权益投放id`
  }]
})
actGroup.addField('newUserNum', 'Input', Object.assign({}, timesHelper('开箱需要新用户数', 0, 99, 1, '', false), {
  placeholder: '支持输入数字0～99，不可超过宝箱成团人数',
  running(formContext) {
    return {
      disabled: formContext.field.getValue('smallBoxNum') !== 1
    }
  }
}))

function createTableField(label, emptyRows, idname) {
  return {
    label,
    emptyRows,
    title: label,
    idname,
    columns: [{
      key: 'boxSerial',
      dataIndex: 'boxSerial',
      title: '宝箱编号',
      cell: (x, i) => {
        return (i + 1) + ""
      }
    }, {
      key: 'groupNum',
      dataIndex: 'groupNum',
      name: 'groupNum',
      title: '成团人数',
      xComponent: 'Input',
      props: (x, i) => {
        if (i === 0 && x.idname !== 'boxType1') { // 小宝箱
          return {
            defaultValue: '0',
            readOnly: true,
            disabled: true
          }
        } else if (x.idname === 'boxType1') { // 大宝箱
          console.log(x.idname)
          return timesHelper('', 1, 12)
        } else {
          return timesHelper('', 1, 4)
        }
      }
    }, {
      key: 'prizeId',
      dataIndex: 'prizeId',
      name: 'prizeId',
      title: '权益投放ID',
      xComponent: 'Input',
      props: timesHelper('')
    }, {
      key: 'prizeGetNum',
      dataIndex: 'prizeGetNum',
      title: '权益领取数量',
      name: 'prizeGetNum',
      xComponent: 'Input',
      props: (x, i) => {
        return x.idname !== 'boxType1' ? {
          defaultValue: '1',
          readOnly: true,
          disabled: true
        } : timesHelper('', 1, 2)
      }
    }]
  }
}

actGroup.addField('boxType0', 'Editable', createTableField('小宝箱规则', smallBoxNum, 'boxType0'))
actGroup.addField('boxType1', 'Editable', createTableField('大宝箱规则', 1, 'boxType1'))

class activity extends Component {

  onCancel = () => {
    Dialog.confirm({
      title: '取消玩法',
      content: '确定要取消该活动玩法吗？',
      // onOk: () => this.props.history.push('/'),
      onOk: () => this.props.history.replace('/treasure'),
      onCancel: () => console.log('cancel')
    });
  }

  onSubmit = () => {
    const { context, submit, formContext, form } = this.props

    formContext.field.validate((errors, data) => {
      if (errors) {
        const arr = Object.keys(errors).map(x => errors[x])
        Message.error(arr[0].errors[0])
      } else {
        const isError = isValidNum(formContext)
        if (isError) return Message.error(isError)
        Dialog.confirm({
          title: '创建玩法',
          content: '确定要创建该活动玩法吗？',
          onOk: () => {
            const result = this.props.submit()
            result.then(res => {
              if (String(res.errorCode) === "0" || res.errorDesc === "success") {
                Message.show({
                  type: 'success',
                  content: '创建成功',
                  afterClose: () => this.props.history.push('/')
                });
              } else {
                console.log(res);
                Message.error(res.errorDesc || '创建失败')
              }
            }).catch(msg => {
              console.log(msg);
              Message.error(msg.error || '提交失败')
            })
          },
          onCancel: () => console.log('cancel')
        });
      }
    })
  }

  render() {
    return <div className="treasure-wrap">
      <Render key={smallBoxNum} />
      <p style={{ textAlign: 'right' }}>
        <Button onClick={this.onCancel} style={{ marginRight: 10 }}>取消</Button>
        <Button onClick={this.onSubmit} type="primary">创建</Button>
      </p>
    </div>
  }
}

export default withForm({
  uiSource: cfh.toJSON(),
  onContextInit: context => {
    // console.log('页面的内容初始化')
    context.componentMap.addComponent('Editable', Editable)
  },
  requestAction: {
    url: `treasure://playbox/getDetail?id={props.match.params.id}`,
    isActive: ({ props }) => {
      console.log(props.match.params)
      let isEdit = typeof (props.match.params.id) !== 'undefined'
      return isEdit
    },
    mapResponse: (res) => {
      console.log('res box info', res)

      const { id, activityStartTime, activityEndTime, ...resetProps } = res.activityPlay
      const boxList = res.activityBoxList
      smallBoxNum = res.activityPlay.smallBoxNum
      actGroup.modifyField('boxType0', 'Editable', createTableField('小宝箱规则', res.activityPlay.smallBoxNum, 'boxType0'))
      const filterFunc = (x, n) => {
        if (String(x.boxType) === n) {
          delete x.id
          return true
        }
        return false
      }

      return {
        time: [activityStartTime, activityEndTime],
        ...resetProps,
        boxType0: boxList.filter(item => filterFunc(item, '0')),
        boxType1: boxList.filter(item => filterFunc(item, '1'))
      }
    }
  },
  submitAction: {
    url: `treasure://playbox/create`,
    mapRequest: (query, obj) => {
      console.log('submitAction', query)
      let boxList = [];
      [0, 1].forEach(xi => {
        // eslint-disable-next-line no-nested-ternary
        const initList = xi ? [{ boxSerial: 1 }] : (
          smallBoxNum > 1 ? ([{ boxSerial: 1, groupNum: '0', prizeGetNum: '1' }, { boxSerial: 2, prizeGetNum: '1' }, { boxSerial: 3, prizeGetNum: '1' }]) :
          ([{ boxSerial: 1, groupNum: '0', prizeGetNum: '1' }])
        )
        boxList = boxList.concat(initList.map((item, itemi) => ({ ...query[`boxType${xi}`][itemi], ...item, boxType: xi })))
      })
      const [activityStartTime, activityEndTime] = query.time

      return {
        activityPlay: {
          activityEndTime: formatDate(activityEndTime),
          activityStartTime: formatDate(activityStartTime),
          everyHelpTotal: query.everyHelpTotal,
          everyLaunchTotal: query.everyLaunchTotal,
          everydayFirstHelpPrizeId: query.everydayFirstHelpPrizeId,
          everydayHelpNum: query.everydayHelpNum,
          newUserNum: query.smallBoxNum === 1 ? query.newUserNum : undefined,
          everydayLaunchNum: query.everydayLaunchNum,
          limitTime: query.limitTime,
          playName: query.playName,
          playType: query.playType,
          smallBoxNum: query.smallBoxNum,
          ext: query.ext
        },
        boxList
        // boxList: [0, 1].reduce((l, c) => [...l, ...query[`boxType${c}`].map((x, i) => ({ ...x, boxType: c, boxSerial: i + 1 }))], [])
      }
    }
  }
})(activity)
