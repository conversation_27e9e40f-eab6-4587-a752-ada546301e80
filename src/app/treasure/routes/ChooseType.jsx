import React, { useState } from 'react'
import { Dialog, Radio, Form, Message } from '@alifd/next'

const RadioGroup = Radio.Group
const FormItem = Form.Item

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 }
}

const ChooseType = ({ visible, setVisible, onConfirm }) => {

  const [playType, setPlayType] = useState('')

  return (<Dialog
    title="选择玩法类型"
    footerActions={['cancel', 'ok']}
    visible={visible}
    onOk={() => {
      console.log('点击选择玩法')
      console.log('playType', playType)
      if (!playType) {
        Message.error('玩法类型不能为空')
        return
      }
      onConfirm(playType)

    }}
    style={{
      width: '676px'
    }}
    onCancel={() => {
      setVisible(false)
    }}
    onClose={() => {
      setVisible(false)
    }}>
    <div style={{ marginTop: '20px' }} className="bg-fff">
      <Form>
        <FormItem label="玩法类型" {...formItemLayout}>
          <RadioGroup
            onChange={(val) => {
              setPlayType(() => {
                return val
              })
            }}
            dataSource={[
              {
                label: '玩法宝箱',
                value: 1
              },
              {
                label: '现金裂变',
                value: 2
              },
              {
                label: '一键抽奖',
                value: 3
              }
            ]}
          />
        </FormItem>
      </Form>

    </div>

  </Dialog>)
}

export default ChooseType
