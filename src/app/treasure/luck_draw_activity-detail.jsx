import React, { Component, useState, useEffect } from 'react'
import { observer, inject } from 'mobx-react'
import { Link } from 'react-router-dom'
import PropTypes from 'prop-types'

import { Breadcrumb, Radio, Message, Form, Button, Dialog, Grid } from '@alifd/next'
import OpPlanHeader from '@/components/header'
import Title from '@/components/title'
import path from '@/api/modules/benefit'
import { request } from '@/packages/request'
import formatDate from '@/utils/formatDate'
import requestDataSource from '@/source/requestDataSource'
import LuckDrawRuleShow from './luckDrawRuleShow'
import './treasure.scss'

const statusList = {
  0: '未生效',
  1: '已生效',
  2: '已结束'
}
const { Row, Col } = Grid
const breads = [
  {
    path: '/treasure',
    name: '互动玩法'
  },
  {
    name: '玩法详情'
  }
]
const playTypeList = {
  1: '宝箱',
  2: '现金裂变',
  3: '一键抽奖'
}

@requestDataSource({
  action: {
    url: `treasure://playbox/getDetail?id={query.id}`,
    method: 'GET'
  },
  mapStateToProps: ({ data }) => {
    return {
      detailData: data
    }
  }
})
class CashDetailWrap extends Component {
  render() {
    const { detailData, history } = this.props
    return <CashDetail detailData={detailData} _history={history} />
  }
}

function CashDetail({ detailData, _history }) {
  console.log(detailData);
  console.log(detailData);
  console.log(detailData);
  console.log(detailData);
  const headerSource = {
    title: '玩法详情',
    renderCell: () => {
      return (
        <span
          className={`boreas-status boreas-status-${detailData.activityPlay.status}`}
          style={{ marginLeft: '20px' }}
        >
          {statusList[+detailData.activityPlay.status]}
        </span>
      )
    }
  }

  return (
    <React.Fragment>
      <Breadcrumb style={{ margin: '20px 0 30px 20px' }}>
        {breads.map(x => <Breadcrumb.Item>
          {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
        </Breadcrumb.Item>)}
      </Breadcrumb>
      <div className="right-content plan-detail treasure-detail">
        <OpPlanHeader {...headerSource} className="bg-fff newplan-title">
          <Row className="plan-detail-header">
            <Col span={5}>
              <span>玩法ID：</span>
              {detailData.activityPlay.id}
            </Col>
            <Col span={5}>
              <span>创建人：</span>
              {detailData.activityPlay.cname}
            </Col>
            <Col span={6}>
              <span>创建时间：</span>
              {formatDate(detailData.activityPlay.ctime)}
            </Col>
          </Row>
        </OpPlanHeader>
        <div
          style={{ marginTop: '10px', padding: '0 20px 30px' }}
          className="bg-fff plan-detail-content"
        >
          {/* 基本信息 */}
          <Title title="基本信息">
            <div className="info-card">
              <Row className="treasure-detail-block">
                <Col>
                  <span>玩法类型：</span>
                  {playTypeList[detailData.activityPlay.playType]}
                </Col>
                <Col>
                  <span>活动时间：</span>
                  {`${formatDate(detailData.activityPlay.activityStartTime) || ''} ~ ${formatDate(detailData.activityPlay.activityEndTime) || ''}`}
                </Col>
              </Row>
              <Row className="treasure-detail-block">
                <Col>
                  <span>玩法名称：</span>
                  {detailData.activityPlay.playName}
                </Col>
              </Row>
            </div>
          </Title>
          {/* 玩法规则配置 */}
          <Title title="玩法规则配置">
            <div className="info-card">
              <Row className="treasure-detail-block">
                <Col>
                  <span>每人活动期间可抽奖次数：</span>
                  {detailData.activityPlay.everyLaunchTotal ? detailData.activityPlay.everyLaunchTotal : 0}
                </Col>
              </Row>
              <Row className="treasure-detail-block">
                <Col>
                  <span>每人每天可抽奖次数：</span>
                  {detailData.activityPlay.everydayLaunchNum ? detailData.activityPlay.everydayLaunchNum : 0}
                </Col>
              </Row>
              <Row className="treasure-detail-block">
                <Col>
                  <span>每天初始抽奖次数：</span>
                  {detailData.activityPlay.everydayAddToNum ? detailData.activityPlay.everydayAddToNum : 0}
                </Col>
              </Row>
              <Row className="treasure-detail-block">
                <Col>
                  <span>追加抽奖次数任务：</span>
                  {detailData.activityPlay.addStatus === 1 ? '外部任务' : '无任务'}
                </Col>
              </Row>
              <LuckDrawRuleShow dataSource={detailData.activityBoxList || []} />
            </div>
          </Title>
        </div>
      </div>
    </React.Fragment>
  )
}

export default CashDetailWrap
