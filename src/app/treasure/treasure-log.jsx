
import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { Breadcrumb, Dialog, Input, Form, Message, Button } from '@alifd/next'
import Moment from 'moment'
import filterDataSource from '@/source/filterDataSource'
import { Filter, Table } from '@/packages/table/FilterTable'
import PlanHeader from '@/components/header/index'
import { request } from '@/packages/request'
import './treasure.scss'


const columns = [
  { title: '操作人', dataIndex: 'uname' },
  { title: '操作内容', dataIndex: 'content' },
  { title: '变更时间', dataIndex: 'utime' }
]
const breads = [
  {
    path: '/treasure',
    name: '互动玩法'
  },
  {
    name: '玩法日志'
  }
]

function List(props) {

  return (
    <div className="right-content treasure-log">
      <Breadcrumb style={{ margin: '20px 0 30px 0' }}>
        {breads.map(x => <Breadcrumb.Item>
          {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
        </Breadcrumb.Item>)}
      </Breadcrumb>
      <PlanHeader title={'日志'} />
      <div className="treasure-log-fitertable">操作记录</div>
      <Table {...props.tableProps} columns={columns} className="treasure-log-fitertable" />
    </div>
  )
}

export default filterDataSource({
  action: {
    url: `treasure://playbox/getLog`,
    method: 'GET',
    mapRequest: json => {
      const { pageNum, pageSize, id } = json
      return {
        id: parseInt(id, 10)
      }
    },
    mapResponse: (json, { props, query }) => {
      return {
        data: json || []
      }
    }
  }
})(List)

