import React, { Component } from 'react'
import { Grid, Table } from '@alifd/next'

import "./treasure.scss"

const { Row, Col } = Grid

const boxType = {
  '1': '大',
  '0': '小'
}
const boxTypeList = [
  {
    label: '大',
    value: 1,
  },
  {
    label: '小',
    value: 0,
  }
]

const columns = [
  {
    title: '编号',
    dataIndex: 'boxSerial',
    cell: (value, index, record, { reload }) => {
      return (
        <span>
          {record.boxSerial}
        </span>
      )
    }
  },
  {
    title: '成团人数',
    dataIndex: 'groupNum'
  },
  {
    title: '权益投放ID',
    dataIndex: 'prizeId'
  },
  {
    title: '权益领取数量',
    dataIndex: 'prizeGetNum'
  }
]

function RuleTable({ data }) {

  return (
    <div>
      {data.length ? (
        <Table dataSource={data} columns={columns}>
          {columns.map((item, index) => {
            return <Table.Column title={item.title} dataIndex={item.dataIndex} />
          })}
        </Table>
      ) : (
          '暂无'
        )}
    </div>
  )
}

class BoxRule extends Component {

  generateBox = () => {

    let { dataSource } = this.props

    let bigData = dataSource.filter(item => item.boxType === 1)
    let smallData = dataSource.filter(item => item.boxType === 0)

    let nodes = boxTypeList.map((box, index) => {

      return (
        <div key={index}>
          <Row className="treasure-detail-block">
            <Col span={4}>
              <span>{boxType[box.value]}宝箱数量：</span>
            </Col>
            <Col span={12}>
              {box.value === 1 ? bigData.length : smallData.length}
            </Col>
          </Row>
          <Row className="treasure-detail-block">
            <Col span={4}>
              <span>{boxType[box.value]}宝箱规则：</span>
            </Col>
            <Col span={12}>
              <RuleTable data={box.value === 1 ? bigData : smallData} />
            </Col>
          </Row>
        </div>

      )
    })

    return nodes;

  }

  render() {
    return (
      <div>
        {this.generateBox()}
      </div>
    )
  }
}

export default BoxRule
