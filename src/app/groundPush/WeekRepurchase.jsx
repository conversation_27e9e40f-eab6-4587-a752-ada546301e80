import * as React from 'react';
import { useEffect, useState } from 'react';
import { request } from '@/packages/request'
import { Message, Input, Dialog, NumberPicker, Button, Icon } from '@alifd/next';

let localId = 1000;

const WeekRepurchase = props => {
  const [data, setData] = useState([]);
  const [editable, setEditable] = useState(false);
  const [ready, setReady] = useState();
  const [mode, setMode] = useState('detail')
  const [version, setVersion] = useState(0)

  useEffect(() => {
    request('crm://repurchase/queryRules', {
      method: 'GET'
    }).then(res => {
      const { budgetId, canEdit, rePurchaseLevels = [] } = res.data
      setData({
        budgetId,
        rePurchaseLevels: rePurchaseLevels.map(item => {
          return {
            ...item,
            _id: ++localId
          }
        })
      })
      setEditable(canEdit)
      setReady(true)
    })
  }, [version])

  if (!ready) {
    return null;
  }

  return (
    <div style={{ width: 960, margin: '0 auto' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20, marginTop: 20 }}>
        <div style={{ color: '#999' }}>
          周新客复购任务-额外返佣（注：仅统计纯新和半新客成功返佣的第2单）
        </div>
        {editable && mode === 'edit' && data.rePurchaseLevels.length < 5 &&
          <Button type="primary" text onClick={() => {
            const list = data.rePurchaseLevels.slice()
            list.push({ _id: ++localId })
            setData(data => ({ ...data, rePurchaseLevels: list }))
          }}><Icon type="add" size="xs" />新增一个</Button>}
      </div>
      <div
        style={{
          background: 'rgb(246, 246, 246)',
          padding: '30px 30px 19px',
          marginBottom: 10
        }}>
        <div style={{ marginBottom: 20, padding: '20px 20px 10px', background: '#fff' }}>
          {data.rePurchaseLevels.length <= 0 ?
            <div style={{ marginBottom: 10, textAlign: 'center', color: '#999' }}>暂无配置</div> :
            null}
          {data.rePurchaseLevels.map(item => {
            function updateValue(key, value) {
              const index = data.rePurchaseLevels.findIndex(p => p._id === item._id)
              if (index !== -1) {
                let list = data.rePurchaseLevels.slice()
                list[index] = {
                  ...list[index],
                  [key]: value
                }
                setData(v => {
                  return { ...v, rePurchaseLevels: list }
                })
              }
            }

            const OrderInput = (
              <NumberPicker
                disabled={!editable || mode === 'detail'}
                value={item.minRepurchaseNum}
                min={1}
                max={9999}
                onChange={value => {
                  updateValue('minRepurchaseNum', value)
                }}
              />
            )
            const BonusInput = (
              <NumberPicker
                disabled={!editable || mode === 'detail'}
                value={item.rePurchaseBonus}
                min={1}
                max={9999}
                onChange={value => {
                  updateValue('rePurchaseBonus', value)
                }}
              />
            )

            return (
              <div
                key={item._id}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: 10
                }}>
                <div style={{ display: 'flex', alignItems: 'center', fontWeight: 'bold' }}>
                  周新客复购成功&nbsp;&nbsp;&nbsp;{OrderInput}&nbsp;&nbsp;&nbsp;单，
                  额外返佣&nbsp;&nbsp;&nbsp;{BonusInput}&nbsp;&nbsp;&nbsp;元
                </div>
                <div>
                  {editable && mode === 'edit' &&
                    <Button type="primary" text onClick={() => {
                      Dialog.confirm({
                        title: '提示',
                        content: '确认要删除吗？',
                        onOk: () => {
                          const index = data.rePurchaseLevels.findIndex(p => p._id === item._id)
                          if (index !== -1) {
                            let list = data.rePurchaseLevels.slice()
                            list = [...list.slice(0, index), ...list.slice(index + 1)]
                            setData(v => {
                              return { ...v, rePurchaseLevels: list }
                            })
                          }
                        }
                      })
                    }}>删除</Button>}
                </div>
              </div>
            )
          })}
        </div>
        <div style={{ marginBottom: 20, padding: '20px 20px 10px', background: '#fff' }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}>
            <label style={{ display: 'inline-block', width: 105, fontWeight: 'bold', textAlign: 'right' }}>预算 ID</label>
            &nbsp;&nbsp;&nbsp;
            <Input
              style={{ width: 300 }}
              disabled={!editable || mode === 'detail'}
              value={data.budgetId}
              onChange={budgetId => setData(data => ({ ...data, budgetId }))}
            />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 10, lineHeight: '34px' }}>
            <label style={{ display: 'inline-block', width: 105, fontWeight: 'bold', textAlign: 'right' }}>结算周期</label>
            &nbsp;&nbsp;&nbsp;
            <div style={{ color: '#999' }}>
              每周一统计上周 7 天复购成功的订单，按照配置中最高返佣金额进行计算。
            </div>
          </div>
        </div>
        {editable && mode === 'detail' &&
          <div style={{ textAlign: 'right' }}>
            <Button onClick={() => setMode('edit')}>编辑</Button>
          </div>}
        {editable && mode === 'edit' &&
          <div style={{ textAlign: 'right' }}>
            <Button onClick={() => {
              setMode('detail')
            }}>取消</Button>
            &nbsp;&nbsp;
            <Button type="primary"
              async onClick={() => {
                for (let i = 0; i < data.rePurchaseLevels.length; i++) {
                  const item = data.rePurchaseLevels[i]
                  if (!item.minRepurchaseNum) {
                    Message.error('请填写复购成功单数')
                    return
                  }
                  if (!item.rePurchaseBonus) {
                    Message.error('请填写额外返佣金额')
                    return
                  }
                }
                if (!data.budgetId && data.rePurchaseLevels.length > 0) {
                  Message.error('请填写预算 ID')
                }
                request('crm://repurchase/updateRules', {
                  method: 'POST',
                  body: data
                }).then(res => {
                  if (res.errorCode === '0') {
                    Message.success('修改成功')
                    setMode('detail')
                    setVersion(v => v + 1)
                  } else {
                    Message.error('修改失败，请稍后再试')
                  }
                })
              }}>
              保存
            </Button>
          </div>}
      </div>
    </div>
  )
}

export default WeekRepurchase
