import React from 'react'
import { Icon, Checkbox, Button, Dialog, Field, Form, Input, NumberPicker, Select, Message } from '@alifd/next'
import { request } from '@/packages/request'

function isEmpty(v) {
  return v === '' || v === null || v === undefined
}

const formItemLayout = {
  labelCol: {
    fixedSpan: 6
  },
  wrapperCol: {
    span: 18
  }
};

const BonusRule = ({ levelDefinition, value, onChange, disabled }) => {
  return (
    <div>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Checkbox
          disabled={disabled}
          checked={value.rewardConsistent}
          onChange={checked => {
            value = {
              ...value,
              rewardConsistent: checked,
              levelBonus: value.levelBonus.map(item => {
                return {
                  ...item,
                  bonusNotDirectCity: item.bonusDirectCity
                }
              })
            }
            onChange(value)
          }}
        />
        &nbsp;城代与直营保持一致&nbsp;仅针对单人模式生效，代理模式不生效(线下结算)
      </div>
      <div>
        {value.levelBonus.map((bonus, index) => {
          function triggerChange(modified) {
            onChange({
              ...value,
              levelBonus: [
                ...value.levelBonus.slice(0, index),
                modified,
                ...value.levelBonus.slice(index + 1)
              ]
            })
          }
          const bonusDirectCityInput = (
            <NumberPicker
              disabled={disabled}
              placeholder="支持两位小数"
              min={0}
              precision={2}
              value={bonus.bonusDirectCity}
              onChange={v => {
                bonus = { ...bonus, bonusDirectCity: v }
                if (value.rewardConsistent) {
                  bonus = { ...bonus, bonusNotDirectCity: v }
                }
                triggerChange(bonus)
              }}
            />
          )
          const bonusNotDirectCityInput = (
            <NumberPicker
              placeholder="支持两位小数"
              min={0}
              precision={2}
              disabled={disabled}
              value={bonus.bonusNotDirectCity}
              onChange={value => {
                bonus = { ...bonus, bonusNotDirectCity: value }
                triggerChange(bonus)
              }}
            />
          )
          return (
            <div key={bonus.levelNum} style={{ marginBottom: 3 }}>
              {levelDefinition[bonus.levelNum - 1].levelName}等级&nbsp;{bonusDirectCityInput}&nbsp;元/人，代理商城市&nbsp;
              {value.rewardConsistent ? bonus.bonusNotDirectCity : bonusNotDirectCityInput}&nbsp;元/人
            </div>
          )
        })}
      </div>
    </div>
  )
}

const CloseRule = ({ value, onChange, disabled }) => {
  const daysInput = (
    <NumberPicker
      disabled={disabled}
      style={{ width: 100 }}
      max={7}
      min={0}
      value={value}
      onChange={onChange}
      placeholder="0~7的整数"
    />
  )

  return (
    <div>
      <div>
        订单完成&nbsp;{daysInput}&nbsp;天有结算
      </div>
      <div style={{ color: '#999', marginTop: 5 }}>
        若填写“0”表示订单完成即结算。“1”表示订单完成后次日结算，以此类推
      </div>
    </div>
  )
}

const Condition = ({ disabled, value, onChange }) => {
  const daysInput = (
    <NumberPicker
      style={{ width: 100 }}
      min={0}
      max={7}
      step={1}
      precision={0}
      disabled={disabled}
      placeholder="0~7的整数"
      value={value.limitDay}
      onChange={limitDay => onChange({ ...value, limitDay })}
    />
  )
  const priceInput = (
    <NumberPicker
      disabled={disabled}
      min={0}
      precision={2}
      step={1}
      style={{ width: 100 }}
      value={value.limitPrice}
      placeholder="支持两位小数"
      onChange={limitPrice => onChange({ ...value, limitPrice })}
    />
  )
  return (
    <div>
      <div>
        识别登录后&nbsp;{daysInput}&nbsp;日内下单，且订单实付金额大于&nbsp;{priceInput}&nbsp;元
      </div>
      <div style={{ color: '#999', marginTop: 5 }}>天数设置为“1”表示用户打开推广链接24小时内</div>
    </div>
  )
}

const SettingItem = ({
  field,
  onDelete,
  mode,
  levelDefinition,
  editable,
  onSave,
  onCancel,
  onEdit,
}) => {
  return (
    <div
      style={{
        background: '#f6f6f6',
        padding: '30px 30px 19px',
        marginBottom: 10
      }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 16
        }}>
        <div style={{ fontSize: 16 }}>{field.getValue('name') || '活动规则名称'}</div>
        {editable &&
          <div className="ug-header-buttons">
            {field.getValue('id') &&
              <React.Fragment>
                <Button
                  text
                  type="primary"
                  onClick={onDelete}>
                  删除
                </Button>
                &nbsp;&nbsp;&nbsp;
              </React.Fragment>}
            {mode === 'readonly' &&
              <Button
                text
                type="primary"
                onClick={onEdit}>
                编辑
              </Button>}
          </div>}
      </div>
      <div style={{ background: '#fff', padding: '20px 0px' }}>
        <Form {...formItemLayout} field={field}>
          <Form.Item label="规则名称">
            <Input
              disabled={mode === 'readonly'}
              placeholder="请输入"
              maxLength={5}
              style={{ width: '400px' }}
              {...field.init('name', {
                rules: [{
                  required: true,
                  message: '请填写规则名称'
                }]
              })} />
          </Form.Item>
          <Form.Item label="业务身份">
            <Select
              disabled={mode === 'readonly'}
              placeholder="请选择业务身份"
              style={{ width: '400px' }}
              multiple
              {...field.init('bizTypes', {
                rules: [{
                  required: true,
                  message: '请选择业务身份'
                }]
              })}>
              <Select.Option value="NEW_RETAIL" label="新零售C4" />
              <Select.Option value="MEDICINE" label="医药" />
              <Select.Option value="NEW_RETAIL_C5" label="新零售C5" />
            </Select>
          </Form.Item>
          <Form.Item label="优先级">
            <NumberPicker
              disabled={mode === 'readonly'}
              style={{ width: '400px' }}
              {...field.init('priority', {
                rules: [{ required: true, message: '请填写优先级' }]
              })}
              min={1}
              hasTrigger={false}
              placeholder='请输入正整数（通用人群优先，圈选人群按优先级生效）'
            />
          </Form.Item>
          <Form.Item label="投放人群">
            <Input.TextArea
              disabled={mode === 'readonly'}
              placeholder="请输入或选择用户群组ID（最多输入5个，用逗号间隔）"
              style={{ width: '400px' }}
              rows={2}
              {...field.init('groupIds', {
                rules: [
                  { required: true, message: '请填写用户群组' },
                  {
                    validator: (rule, value, callback) => {
                      if (!/^(\d+,){0,4}\d+$/.test(value)) {
                        if (callback) {
                          callback('用户群组格式错误')
                        } else {
                          throw new Error('用户群组格式错误')
                        }
                      } else {
                        callback && callback()
                      }
                    }
                  }
                ]
              })}
            />
            {mode !== 'readonly' &&
              <Button text type="primary" onClick={() => {
                window.open(`https://${window.location.host}${window.location.pathname}#/crm/crowedSelect`, '_blank')
              }}>去创建</Button>}
          </Form.Item>
          <Form.Item label="满足规则">
            <Condition
              disabled={mode === 'readonly'}
              {...field.init('condition', {
                rules: [
                  { required: true, message: '请配置满足规则' },
                  {
                    validator: (rule, value, callback) => {
                      if (isEmpty(value.limitDay)) {
                        throw new Error('请填写有效天数')
                      } else if (isEmpty(value.limitPrice)) {
                        throw new Error('请填写订单金额')
                      }
                      callback && callback()
                    }
                  },
                ]
              })}
            />
          </Form.Item>
          <Form.Item label="激励金额">
            <BonusRule
              disabled={mode === 'readonly'}
              levelDefinition={levelDefinition}
              {...field.init('bonus', {
                rules: [
                  { required: true, message: '请填写激励金额' },
                  {
                    validator: (rule, value, callback) => {
                      for (let i = 0; i < value.levelBonus.length; i++) {
                        const item = value.levelBonus[i]
                        if (
                          isEmpty(item.bonusDirectCity) ||
                          isEmpty(item.bonusNotDirectCity)
                        ) {
                          throw new Error('请完整填写激励金额')
                        }
                      }
                      callback && callback()
                    }
                  }
                ]
              })}
            />
          </Form.Item>
          <Form.Item label="结算规则">
            <CloseRule
              disabled={mode === 'readonly'}
              {...field.init('receivedBonusDay', { rules: [{ required: true, message: '请填写结算规则' }] })}
            />
          </Form.Item>
          <Form.Item label="预算ID">
            <Input
              disabled={mode === 'readonly'}
              style={{ width: 400 }}
              placeholder=""
              hasTrigger={false}
              {...field.init('budgetId', { rules: [{ required: true, message: '请填写预算ID' }] })}
            />
          </Form.Item>
          {mode !== 'readonly' &&
            <Form.Item label=" ">
              <Button type="primary" onClick={onSave}>保存</Button>
              &nbsp;
              <Button onClick={onCancel}>取消</Button>
            </Form.Item>}
        </Form>
      </div>
    </div>
  )
}

let counter = 0;

class RewardByUserGroup extends React.Component {
  constructor(options) {
    super(options)

    this.state = {
      canEdit: false,
      items: []
    }
  }

  createSettingItem() {
    const { levelDefinition } = this.props
    return new Field(this, {
      values: {
        _id: counter++,
        mode: 'create',
        condition: { limitDay: 1, limitPrice: '' },
        closeRule: 2,
        bonus: {
          rewardConsistent: false,
          levelBonus: levelDefinition.map((level) => {
            return {
              levelNum: level.levelNum,
              bonusDirectCity: 10,
              bonusNotDirectCity: 0
            }
          })
        }
      }
    })
  }

  ruleToFormData(rule) {
    return {
      _id: counter++,
      id: rule.id,
      mode: 'readonly',
      name: rule.name,
      priority: rule.priority,
      bizTypes: rule.bizTypes.split(','),
      groupIds: rule.groupIds.trim(),
      budgetId: rule.budgetId,
      condition: {
        limitDay: rule.bonusInfo.limitDay,
        maxLimitDay: rule.bonusInfo.maxLimitDay,
        limitPrice: rule.bonusInfo.limitPrice
      },
      receivedBonusDay: rule.bonusInfo.receivedBonusDay,
      bonus: {
        rewardConsistent: rule.bonusInfo.rewardConsistent,
        levelBonus: rule.bonusInfo.levelBonus
      }
    }
  }

  formDataToRule(values) {
    return {
      id: values.id,
      name: values.name,
      priority: values.priority,
      bizTypes: values.bizTypes.join(','),
      groupIds: values.groupIds,
      budgetId: `${values.budgetId}`,
      bonusInfo: {
        bonusType: 6,
        status: 1,
        limitDay: `${values.condition.limitDay}`,
        maxLimitDay: `${values.condition.limitDay}`,
        limitPrice: `${values.condition.limitPrice}`,
        rewardConsistent: values.bonus.rewardConsistent,
        receivedBonusDay: `${values.receivedBonusDay}`,
        levelBonus: values.bonus.levelBonus.map((item => {
          return {
            ...item,
            bonusDirectCity: `${item.bonusDirectCity}`,
            bonusNotDirectCity: `${item.bonusNotDirectCity}`
          }
        }))
      }
    }
  }

  async refresh() {
    const res = await request('crm://bonus/crm/queryRules', {
      method: 'GET'
    })
    console.log('queryRules', res)
    const { rules, canEdit } = res.data
    this.setState({
      items: rules.map(r => {
        return new Field(this, {
          values: this.ruleToFormData(r)
        })
      }),
      canEdit
    })
  }

  componentDidMount() {
    this.refresh()
  }

  render() {
    const { levelDefinition } = this.props
    const { items, canEdit } = this.state

    console.log('levelDefinition', levelDefinition)

    return (
      <div style={{ width: 960, margin: '40px auto', position: 'relative' }}>
        <div style={{ color: '#999', display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
          <div style={{ fontSize: 16 }}>圈定人群返佣配置</div>
          {canEdit &&
            <Button
              type="primary"
              text
              onClick={() => {
                if (items.length >= 10) {
                  Dialog.alert({
                    title: '提示',
                    content: '最多创建 10 个圈人返佣配置'
                  })
                  return
                }
                let _items = items.slice()
                _items = [this.createSettingItem(), ..._items]
                this.setState({ items: _items })
              }}>
              <Icon type="add" size="xs" />新增一个
            </Button>}
        </div>

        {items.length > 0 ?
          items.map(item => (
            <SettingItem
              key={item.getValue('_id')}
              levelDefinition={levelDefinition}
              mode={item.getValue('mode')}
              field={item}
              editable={canEdit}
              onEdit={() => item.setValue('mode', 'edit')}
              onCancel={() => {
                if (item.getValue('id')) {
                  item.setValue('mode', 'readonly')
                } else {
                  const index = items.find(it => it.getValue('_id') === item.getValue('_id'))
                  if (index !== -1) {
                    const _items = items.slice()
                    _items.splice(index, 1)
                    this.setState({ items: _items })
                  }
                }
              }}
              onSave={async () => {
                const field = item
                const res = await field.validatePromise()
                console.log('validate result', res)
                if (res.errors) {
                  const key = Object.keys(res.errors)[0]
                  const msg = res.errors[key].errors[0]
                  Message.error(msg);
                  return
                }

                const values = res.values
                const payload = this.formDataToRule(values)
                let result
                let message
                if (field.getValue('id')) {
                  result = await request('crm://bonus/crm/modifyRule', {
                    method: 'POST',
                    body: payload
                  })
                  message = '修改成功'
                } else {
                  result = await request('crm://bonus/crm/createRule', {
                    method: 'POST',
                    body: payload
                  })
                  message = '创建成功'
                }
                if (result.success) {
                  Message.success(message)
                  await this.refresh()
                } else {
                  Message.error(result.errorDesc || '创建失败，请稍后重试')
                }
              }}
              onDelete={() => {
                Dialog.show({
                  title: '提示',
                  content: '删除后无法恢复，且进行中的订单将无法返佣，是否确认删除？（请提前下沉推广员）',
                  onOk: async () => {
                    await request('crm://bonus/crm/deleteRule', {
                      method: 'POST',
                      body: {
                        id: item.getValue('id')
                      }
                    })
                    Message.success('删除成功')
                    await this.refresh()
                  }
                })
              }}
            />
          )) :
          <div style={{ color: '#999', marginTop: 40, marginBottom: 80, textAlign: 'center' }}>
            暂无数据
          </div>}
      </div>
    )
  }
}

export default RewardByUserGroup
