import React, { useState, useMemo, useRef } from 'react'
import { Breadcrumb, Table, Button, Form, Radio, Message, Dialog, Input, Checkbox, Pagination } from '@alifd/next'
import { Link } from 'react-router-dom'
import { request } from '@/packages/request'
import requestDataSource from '@/source/requestDataSource'
import './quanyi.less'

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const breads = [
  {
    name: '线下拉新'
  },
  {
    name: '拉新权益配置'
  }
]

const formItemLayout = {
  labelCol: {
    fixedSpan: 5
  },
  wrapperCol: {
    span: 18
  }
};

function IconAdd() {
  return <svg
    viewBox="0 0 1024 1024"
    width="12"
    height="12"
  >
    <path
      d="M874.0352 149.956267C777.335467 53.256533 648.7552 0 512 0 375.236267 0 246.664533 53.248 149.956267 149.956267 53.256533 246.664533 0 375.236267 0 512s53.248 265.335467 149.956267 362.043733C246.664533 970.743467 375.236267 1024 512 1024s265.335467-53.248 362.043733-149.956267C970.743467 777.335467 1024 648.763733 1024 512s-53.256533-265.335467-149.9648-362.043733zM554.666667 469.333333V315.682133A42.606933 42.606933 0 0 0 512 273.066667a42.666667 42.666667 0 0 0-42.666667 42.615466V469.333333H315.682133A42.606933 42.606933 0 0 0 273.066667 512a42.666667 42.666667 0 0 0 42.615466 42.666667H469.333333v153.6512A42.606933 42.606933 0 0 0 512 750.933333a42.666667 42.666667 0 0 0 42.666667-42.615466V554.666667h153.6512A42.606933 42.606933 0 0 0 750.933333 512a42.666667 42.666667 0 0 0-42.615466-42.666667H554.666667z m259.029333 344.362667C733.115733 894.293333 625.962667 938.666667 512 938.666667c-113.962667 0-221.115733-44.381867-301.696-124.970667C129.706667 733.1072 85.333333 625.962667 85.333333 512c0-113.962667 44.381867-221.115733 124.970667-301.696C290.8928 129.706667 398.037333 85.333333 512 85.333333c113.962667 0 221.115733 44.381867 301.696 124.970667C894.293333 290.884267 938.666667 398.037333 938.666667 512c0 113.9712-44.381867 221.115733-124.970667 301.696z"
      fill="#FF7C4D"
      p-id="1948"
    ></path>
  </svg>
}


function SelectDiscountInfo({ showList, onCancel, onSelect }) {
  let [list, SetList] = useState([]);
  let [selectCheckbox, SetSelectCheckbox] = useState([]);
  let [searchDiscountId, SetSearchDiscountId] = useState(null);
  let [total, SetTotal] = useState(0);
  let [page, SetPage] = useState(1);
  let [loading, SetLoading] = useState(false);
  const numRef = useRef(searchDiscountId);
  numRef.current = searchDiscountId;

  function update() {
    SetLoading(true);
    request('crm://discount/getDiscountInfo', {
      method: 'GET',
      query: {
        rn: page,
        discountId: numRef.current,
        pn: 10
      }
    }).then((response) => {
      SetLoading(false);
      if (response.data && response.data.discountDtoList) {
        SetList(response.data.discountDtoList);
        SetTotal(response.data.totalNum);
      } else {
        SetList([]);
        SetTotal(0);
      }
    });
  }


  useMemo(() => {
    update();
  }, [page])

  return <Dialog
    title="添加邀请人权益"
    visible={showList}
    onCancel={() => {
      onCancel()
    }}
    onClose={() => {
      onCancel()
    }}
    onOk={() => {
      onSelect(list.find(item => {
        return item.discountId === selectCheckbox;
      }))
    }}
  >
    <div className="dialogList">
      <div className="search">
        投放计划ID
        <Input className="searchInput" placeholder="Large" value={searchDiscountId} onChange={(value) => {
          SetSearchDiscountId(value)
        }} aria-label="Large" />
        <Button type="normal" warning onClick={() => {
          update();
        }}>查询</Button> &nbsp;&nbsp;
        <Button type="normal" onClick={() => {
          SetSearchDiscountId('')
          setTimeout(() => {
            update();
          }, 0)
        }}>重置</Button> &nbsp;&nbsp;
      </div>
      <Table className="list" dataSource={list} loading={loading}>
        <Table.Column title="选择" cell={(value, index, record) => {
          return <Checkbox checked={selectCheckbox === record.discountId} onChange={() => {
            SetSelectCheckbox(record.discountId)
          }
          } />
        }} />
        <Table.Column title="投放计划" dataIndex="discountId" />
        <Table.Column title="投放计划名称" dataIndex="discountName" />
        <Table.Column title="创建人" dataIndex="creatName" />
      </Table>
      <Pagination total={total} onChange={(page) => {
        console.log(page);
        SetPage(page)
      }} className="page-demo" />
    </div>
  </Dialog>;
}

const postData = {}
export default function () {
  let [editType, setEditType] = useState(false);
  let [discountInfo, SetDiscountInfo] = useState(null);
  let [showList, SetShowList] = useState(false);
  let [showYiyaoList, SetYiyaoShowList] = useState(false);

  useMemo(() => {
    request('crm://discount/queryHeadDiscount', {
      method: 'GET'
    }, {
      shopId: 1,
      type: 1
    }).then((response) => {
      SetDiscountInfo(response.data)
      Object.assign(postData, response.data);
    });
  }, [])
  if (discountInfo === null) {
    return null;

  }
  return <div className="quanyi">
    <Breadcrumb className='quanyi_nav'>
      {breads.map((x, index) => <Breadcrumb.Item key={index}>
        {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
      </Breadcrumb.Item>)}
    </Breadcrumb>
    <div className='quanyi_content'>
      <div className="title">拉新权益配置</div>
      {
        editType ?
          <div className='Edit'>
            <div className='info'>
              <div className='info_title'>平台券权益</div>
              <Form {...formItemLayout}>
                <FormItem label="新零售">
                  <Table dataSource={[discountInfo.discountInfo]}>
                    <Table.Column title="序号" dataIndex="id" cell={(value, index) => {
                      return index + 1;
                    }} />
                    <Table.Column title="投放计划" dataIndex="discountId" />
                    <Table.Column title="投放计划名称" dataIndex="discountName" />
                    <Table.Column title="创建人" dataIndex="creatName" />
                  </Table>
                  <p className="add-row">
                    <Button className="button" text data-test-id="add-row" onClick={() => {
                      SetShowList(true)
                    }}>
                      <IconAdd />
                      <span className="btn-text">添加邀请人权益</span>
                    </Button>
                    <span className="desc">最多可添加1个</span>
                    <div style={{ flex: 1 }}></div>
                    <span className="desc">无可用权益?</span>
                    <Button className="button" text style={{ width: 80 }} onClick={() => {
                      Dialog.confirm({
                        title: '去创建投放计划',
                        content: '创建的内容需要在权益平台里操作，确认要去创建吗？',
                        onOk() {
                          window.open('https://kunlun.ele.me/home/<USER>/https://boreas.ele.me/page/benefit-kunlun/index.html#/plan?pageNum=1&pageSize=10');
                        }
                      })
                    }}>点击创建</Button>
                  </p>
                  <SelectDiscountInfo
                    showList={showList}
                    onCancel={() => {
                      SetShowList(false)
                    }}
                    onSelect={(val) => {
                      if (!val) {
                        return
                      }
                      SetDiscountInfo(Object.assign({}, discountInfo, {
                        discountInfo: val
                      }))
                      Object.assign(postData, {
                        discountInfo: val
                      });
                      SetShowList(false)
                    }}
                  />
                </FormItem>
                <FormItem label="医药">
                  <Table dataSource={[discountInfo.medicineDiscountInfo]}>
                    <Table.Column title="序号" dataIndex="id" cell={(value, index) => {
                      return index + 1;
                    }} />
                    <Table.Column title="投放计划" dataIndex="discountId" />
                    <Table.Column title="投放计划名称" dataIndex="discountName" />
                    <Table.Column title="创建人" dataIndex="creatName" />
                  </Table>
                  <p className="add-row">
                    <Button className="button" text data-test-id="add-row" onClick={() => {
                      SetYiyaoShowList(true)
                    }}>
                      <IconAdd />
                      <span className="btn-text">添加邀请人权益</span>
                    </Button>
                    <span className="desc">最多可添加1个</span>
                    <div style={{ flex: 1 }}></div>
                    <span className="desc">无可用权益?</span>
                    <Button className="button" text style={{ width: 80 }} onClick={() => {
                      Dialog.confirm({
                        title: '去创建投放计划',
                        content: '创建的内容需要在权益平台里操作，确认要去创建吗？',
                        onOk() {
                          window.open('https://kunlun.ele.me/home/<USER>/https://boreas.ele.me/page/benefit-kunlun/index.html#/plan?pageNum=1&pageSize=10');
                        }
                      })
                    }}>点击创建</Button>
                  </p>
                  <SelectDiscountInfo
                    showList={showYiyaoList}
                    onCancel={() => {
                      SetYiyaoShowList(false)
                    }}
                    onSelect={(val) => {
                      if (!val) {
                        return
                      }
                      SetDiscountInfo(Object.assign({}, discountInfo, {
                        medicineDiscountInfo: val
                      }))
                      Object.assign(postData, {
                        medicineDiscountInfo: val
                      });
                      SetYiyaoShowList(false)
                    }}
                  />
                </FormItem>
                <FormItem label="投放规则">
                  <div style={{ lineHeight: '36px' }}>默认生效</div>
                </FormItem>
              </Form>
            </div>
            <div className='info'>
              <div className='info_title'>N元购权益</div>
              <Form {...formItemLayout}>
                <FormItem label="是否添加权益">
                  <div>
                    <RadioGroup
                      defaultValue={discountInfo.buyOutFlag}
                      onChange={(v) => {
                        postData.buyOutFlag = v;
                        SetDiscountInfo(Object.assign({}, discountInfo, {
                          buyOutFlag: v
                        }))
                      }}>
                      <Radio value={1}>添加</Radio>
                      <Radio value={0}>不添加</Radio>
                    </RadioGroup>
                  </div>
                </FormItem>
                <FormItem label="选择权益">
                  <div
                    style={{ lineHeight: '36px' }}>
                    {discountInfo.buyOutFlag ? '将自动拉取商家生效中的N元购活动信息(不含平台新客1元购活动)' : '无'}
                  </div>
                </FormItem>
                <FormItem label="活动规则">
                  <div style={{ lineHeight: '36px' }}>
                    {discountInfo.buyOutFlag ? '默认生效' : '无'}
                  </div>
                </FormItem>
                <FormItem label=" ">
                  <Button type="normal" onClick={() => {
                    setEditType(false)
                  }}>取消</Button> &nbsp;&nbsp;
                  <Button className='saveButton' type="primary" onClick={() => {
                    request('crm://discount/creatDiscount', {
                      method: 'POST',
                      body: {
                        discountId: postData.discountInfo.discountId,
                        discountName: postData.discountInfo.discountName,
                        discountCreator: postData.discountInfo.creatName,
                        medicineDiscountId: postData.medicineDiscountInfo.discountId,
                        medicineDiscountName: postData.medicineDiscountInfo.discountName,
                        medicineDiscountCreator: postData.medicineDiscountInfo.creatName,
                        buyOutFlag: postData.buyOutFlag,
                        bugOutFlag: postData.buyOutFlag
                      }
                    }).then((response) => {
                      Message.success(response.errorDesc)
                      if (response.success) {
                        setEditType(false);
                        request('crm://discount/queryHeadDiscount', {
                          method: 'GET'
                        }, {
                          shopId: 1,
                          type: 1
                        }).then((response) => {
                          SetDiscountInfo(response.data)
                          Object.assign(postData, response.data);
                        });
                      }
                    });
                  }}>保存</Button>
                </FormItem>
              </Form>
            </div>
          </div>
          :
          <div className='Info'>
            {
              discountInfo.canEdit && <Button className="editButton" type="normal" warning onClick={() => {
                setEditType(true)
              }}>修改配置</Button>
            }
            <div className="info">
              <div className='info_title'>平台券</div>
              <table>
                <tbody>
                  <tr>
                    <td className="nav">客态权益投放计划</td>
                    <td>
                      <Table dataSource={[discountInfo.discountInfo]}>
                        <Table.Column title="投放计划" dataIndex="discountId" />
                        <Table.Column title="投放计划名称" dataIndex="discountName" />
                        <Table.Column title="创建人" dataIndex="creatName" />
                      </Table>
                    </td>
                  </tr>
                  <tr>
                    <td className="nav">医药权益</td>
                    <td>
                      <Table dataSource={[discountInfo.medicineDiscountInfo]}>
                        <Table.Column title="投放计划" dataIndex="discountId" />
                        <Table.Column title="投放计划名称" dataIndex="discountName" />
                        <Table.Column title="创建人" dataIndex="creatName" />
                      </Table>
                    </td>
                  </tr>
                  <tr>
                    <td className="nav">投放规则</td>
                    <td>默认生效</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div className="info">
              <div className='info_title'>N元购</div>
              <table>
                <tbody>
                  <tr>
                    <td className="nav">权益</td>
                    <td>{discountInfo.buyOutFlag ? '将自动拉取商家生效中的N元购活动信息(不含平台新客1元购活动)' : '无'}</td>
                  </tr>
                  <tr>
                    <td className="nav">投放规则</td>
                    <td>{discountInfo.buyOutFlag ? '默认生效' : '无'}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
      }
    </div>
  </div>
}
