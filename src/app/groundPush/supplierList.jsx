import React, { useState, useEffect } from 'react'
import { Breadcrumb, Form, Input, Select, Grid, Table, Button, Dialog, Pagination, Message } from '@alifd/next'
import { Link } from 'react-router-dom'
import { request } from '@/packages/request'
import DialogInput from './supplierInput'

const FormItem = Form.Item;
const { Row, Col } = Grid;

const formItemLayout = {
  wrapperCol: { span: 24 }
};

const insetLayout = {
  labelCol: { fixedSpan: 5 },
  wrapperCol: {
    span: 16
  }
};
let time = (time) => {
  let date = new Date(time);
  let YY = date.getFullYear() + '-';
  let MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  let DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
  let hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  let mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
  let ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
  return YY + MM + DD + " " + hh + mm + ss;
}
const breads = [
  {
    name: '地推拉新'
  },
  {
    name: '供应商管理列表'
  }
]
const bizTypeConfig = {
  NEW_RETAIL: "新零售C4",
  MEDICINE: "医药",
  NEW_RETAIL_C5: "新零售C5",
}
function Demo(props) {
  const { massige, flag, setFlag } = props
  return (
    <Dialog
      title="Welcome to Alibaba.com"
      visible={flag}
      autoFocus
      onOk={() => {
        setFlag(false)
      }}
      onCancel={() => {
        setFlag(false)
      }}
      cancelProps={{ 'aria-label': 'cancel' }}
      okProps={{ 'aria-label': 'ok' }}>
      <p>{massige}</p>
    </Dialog>
  )
}
export default function (props) {
  let [sellerInfoList, setSellerInfoList] = useState(null);
  let [sellerCount, setSellerCount] = useState(null);
  let [visible, setVisible] = useState(false);
  let [phoneValue, setPhoneValue] = useState('');
  let [nameValue, setNameValue] = useState('')
  let [bizType, setBizType] = useState('')
  let [searchObj, setSearchObj] = useState({
    pageNum: 1,
    pageSize: 10
  });
  const [flag, setFlag] = useState(false)
  const [massige, setMassige] = useState('')
  let [num, setNum] = useState(0)
  const valueObj = {
    phone: '',
    name: '',
    supplierType: 'NEW_RETAIL',
    storeId: ''
  }
  useEffect(() => {
    request('crm://seller/getSellerInfo?pageNum=' + searchObj.pageNum + '&pageSize=' + searchObj.pageSize, {
      method: 'GET',
      query: searchObj
    }).then((res) => {
      if (res.data.sellerList) {
        const newData = res.data.sellerList.map(item => {
          return {
            ...item,
            'updateTime': time(item.updateTime)
          }
        })
        setSellerInfoList(newData);
        setSellerCount(res.data.totalNum);
      } else {
        setSellerInfoList([])
        setSellerCount(0)
      }
    });
  }, [searchObj, num])
  if (sellerInfoList === null || sellerCount === null) {
    return null
  }
  function addSupplier(values) {
    const regPhone = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
    console.log(regPhone.test(values.phone))
    if (!regPhone.test(values.phone)) {
      Message.error('请输入合法手机号格式')
      return false
    }
    if (values.name.length <= 0) {
      Message.error('输入内容不能为空')
      return false
    }
    if (values.bizType === 'MEDICINE' && !values.storeId) {
      Message.error('请输入门店ID')
      return false
    }
    request('crm://seller/createSeller', {
      method: 'POST',
      body: {
        sellerMobile: values.phone,
        sellerName: values.name,
        bizType: values.bizType,
        storeId: values.storeId
      }
    }).then(res => {
      console.log(res)
      if (res.data === '800001') {
        Message.error('该手机号已注册供应商')
      } else if (res.data === '800002') {
        Message.success('添加成功');
        setVisible(false)
        setNum(num => {
          return num + 1
        })
      } else if (res.data === '800003') {
        setMassige(res.data)
        setVisible(false)
        setFlag(true)
      } else if (res.data === '800004') {
        Message.error('未找到该门店，请核对门店ID')
      }
    })
  }
  return <div className='shopList'>
    <Breadcrumb className='shopList_nav'>
      {breads.map((x, index) => <Breadcrumb.Item key={index}>
        {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
      </Breadcrumb.Item>)}
    </Breadcrumb>
    <div className='shopList_content'>
      <div className="title">供应商管理列表</div>
      <Button className='addButton' warning onClick={() => {
        setVisible(true);
      }}>添加供应商</Button>
      {
        visible ?
          <DialogInput
            valueObj={valueObj}
            title='新增供应商'
            setVisible={setVisible}
            addSupplier={addSupplier}
            visible={visible} /> :
          null
      }
      {
        Demo({ massige, flag, setFlag })
      }
      {/* 供应商筛选 */}
      <Form {...formItemLayout}>
        <FormItem style={{ marginBottom: 0 }}>
          <Row gutter="4">
            <Col fixedSpan={10}></Col>
          </Row>
        </FormItem>
        <FormItem style={{ marginBottom: 0 }}>
          <Row gutter="4">
            <Col>
              <FormItem label="供应商手机号" {...insetLayout} required asterisk={false}>
                <Input value={phoneValue} onChange={(val) => {
                  setPhoneValue(val)
                }} />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="供应商名称" {...insetLayout} required asterisk={false}>
                <Input value={nameValue} onChange={(val) => {
                  setNameValue(val)
                }} />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="业务身份" {...insetLayout} required asterisk={false}>
                <Select value={bizType}
                  style={{ width: 140 }}
                  hasClear
                  onChange={(val) => {
                    setBizType(val)
                  }}>
                  <Select.Option value="NEW_RETAIL" label="新零售C4" />
                  <Select.Option value="MEDICINE" label="医药" />
                  <Select.Option value="NEW_RETAIL_C5" label="新零售C5" />
                </Select>
              </FormItem>
            </Col>
            <Col fixedSpan={10}>
              <Form.Submit onClick={() => {
                setSearchObj({
                  ...searchObj,
                  sellerMobile: phoneValue,
                  sellerName: nameValue,
                  bizType,
                  pageNum: 1
                })
              }}>查询</Form.Submit>
              <Form.Submit style={{ marginLeft: '20px' }} onClick={(v) => {
                setPhoneValue('')
                setNameValue('')
                setBizType('')
                setSearchObj({
                  pageNum: 1,
                  pageSize: 10
                })
              }}>重置</Form.Submit>
            </Col>
          </Row>
        </FormItem>
      </Form>

      <div className="count">共有{sellerCount}个相关内容</div>
      <Table dataSource={sellerInfoList}>
        <Table.Column title="供应商手机号" htmlTitle="sellerId" dataIndex="sellerMobile" />
        <Table.Column title="供应商名称" dataIndex="sellerName" />
        <Table.Column title="业务身份" dataIndex="bizType" cell={value => {
          return <div>{bizTypeConfig[value]}</div>
        }} />
        <Table.Column title="维护人" dataIndex="creator" />
        <Table.Column title="淘系门店ID" dataIndex="storeId" />
        <Table.Column title="添加时间" dataIndex="updateTime" />
        <Table.Column title="操作" width={60} cell={(value, index, record) => {
          return <Button.Group size='medium'>
            <Button onClick={() => {
              Dialog.confirm({
                title: '删除供应商',
                content: '确定要删除该供应商吗？',
                onOk: () => {
                  return new Promise((res) => {
                    const formData = new FormData()
                    formData.append('sellerId', record.sellerId);
                    request('crm://seller/deleteSeller', {
                      method: 'POST',
                      body: formData
                    }).then((response) => {
                      if (!response.data) {
                        Message.error('删除失败');
                      } else {
                        setTimeout(() => {
                          request('crm://seller/getSellerInfo?pageNum=' + searchObj.pageNum + '&pageSize=' + searchObj.pageSize, {
                            method: 'GET',
                            query: searchObj
                          }).then((res) => {
                            if (res.data.sellerList) {
                              const newData = res.data.sellerList.map(item => {
                                return {
                                  ...item,
                                  'updateTime': time(item.updateTime)
                                }
                              })
                              setSellerInfoList(newData);
                              setSellerCount(res.data.totalNum);
                            } else if (searchObj.pageNum > 1) {
                              setSearchObj(searchObj => {
                                return {
                                  ...searchObj,
                                  pageNum: searchObj.pageNum - 1
                                }
                              })
                            } else {
                              setSellerInfoList([])
                              setSellerCount(0)
                            }
                            Message.success('删除成功');
                          });
                        });
                      }
                      res(response);
                    });
                  })
                }
              });
            }}>删除</Button>
          </Button.Group>
        }} />
      </Table>
      <Pagination
        current={searchObj.pageNum}
        defaultCurrent={searchObj.pageNum}
        total={sellerCount}
        pageSize={searchObj.pageSize}
        onChange={(page) => {
          setSearchObj(Object.assign({}, searchObj, {
            pageNum: page
          }))
        }} />
    </div>
  </div>
}
