import React, { useState, useMemo } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Form,
  Input,
  DatePicker,
  TimePicker,
  Balloon,
  Icon,
  Message,
  NumberPicker,
  Checkbox,
  Upload
} from '@alifd/next'
import { Link } from 'react-router-dom'
import './informationList.less'
import { request } from '@/packages/request'
import DomainConfigure from "../../packages/request/DomainConfigure";
import { buildEnv } from '@/packages/env'

const env = buildEnv()

const FormItem = Form.Item;
const breads = [
  {
    name: '线下拉新'
  },
  {
    name: '内容管理'
  }
]

const formItemLayout = {
  labelCol: {
    fixedSpan: 7
  },
  wrapperCol: {
    span: 18
  }
};
const postData = {}

function DateTime({ defaultValue, onChange }) {
  return <div>
    <DatePicker defaultValue={defaultValue.split(' ')[0]} onChange={(value) => {
      onChange(value + ' ' + defaultValue.split(' ')[1])
    }} />
    <TimePicker defaultValue={defaultValue.split(' ')[1]} onChange={(value) => {
      onChange(defaultValue.split(' ')[0] + ' ' + value);
    }} />
  </div>
}

function inforMationList() {
  let [editType, setEditType] = useState(false);
  let [queryRuleInfo, SetQueryRuleInfo] = useState(null);

  useMemo(() => {
    request('crm://bonus/queryRuleInfo', {
      method: 'GET'
    }).then((response) => {
      SetQueryRuleInfo(response.data)
      Object.assign(postData, response.data);
    });
  }, [])
  if (queryRuleInfo === null) return null;

  function EditItem(sort, userType) {
    const index = queryRuleInfo.list.findIndex(item => {
      return item.sort === sort && item.userType === userType;
    })
    return index > -1 && <div className='rewardInfo'>
      <p>{userType === 1 ? '老客' : '新客'}</p>
      <div className='rewardInfo_content'>
        <div className='item'>
          <div className='name'>URL链接：</div>
          <Input onChange={(value) => {
            let newList = Array.from(queryRuleInfo.list)
            newList[index] = Object.assign({}, queryRuleInfo.list[index], {
              jumpUrl: value
            })
            SetQueryRuleInfo(Object.assign({}, queryRuleInfo, {
              list: newList
            }));
            postData.list = newList;
          }} value={queryRuleInfo.list[index].jumpUrl} />
        </div>
        <div className='item'>
          <div className='name'>图片：</div>
          <Upload
            limit={1}
            listType="card"
            accept="image/png, image/jpg, image/jpeg, image/gif, image/bmp"
            timeout={20000}
            headers={{ 'X-Requested-With': null }}
            action={`//${DomainConfigure.crm[env]}/activity/image/upload`}
            onSuccess={(params) => {
              let newList = Array.from(queryRuleInfo.list)
              newList[index] = Object.assign({}, queryRuleInfo.list[index], {
                imageUrl: params.response.data.data.url
              })
              SetQueryRuleInfo(Object.assign({}, queryRuleInfo, {
                list: newList
              }));
              postData.list = newList;
            }}
            value={[{ url: queryRuleInfo.list[index].imageUrl }]}
            shape="card"
            style={{ display: "inline-block" }}
          />
        </div>
      </div>
    </div>
  }

  function switchCheckBox(sort, userType) {
    const index = queryRuleInfo.list.findIndex(item => {
      return item.sort === sort && item.userType === userType;
    })
    return <Checkbox checked={index > -1} onChange={(value) => {

      let newList = Array.from(queryRuleInfo.list)
      if (index > -1) {
        newList.splice(index, 1)
      } else {
        newList.push({
          imageUrl: "",
          jumpUrl: "",
          sort,
          userType
        })
      }
      SetQueryRuleInfo(Object.assign({}, queryRuleInfo, {
        list: newList
      }));
      postData.list = newList;
    }}>{userType === 1 ? '老客' : '新客'}</Checkbox>
  }

  function getConfigItem(sort, userType) {
    const config = queryRuleInfo.list.find(item => {
      return item.sort === sort && item.userType === userType;
    });
    return config && <tr>
      <td className="nav">新客</td>
      <td>
        <table>
          <tbody>
          <tr>
            <td>跳转链接</td>
            <td>{config.jumpUrl}</td>
          </tr>
          <tr>
            <td className="nav">图片</td>
            <td><img src={config.imageUrl} height={100} style={{ border: 'solid 1px #dedede' }} /></td>
          </tr>
          </tbody>
        </table>
      </td>
    </tr>
  }

  return <div className="informationList">
    <Breadcrumb className='informationList_nav'>
      {breads.map((x, index) => <Breadcrumb.Item key={index}>
        {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
      </Breadcrumb.Item>)}
    </Breadcrumb>
    <div className='informationList_content'>
      <div className='Edit'>
        <div className='info'>
          <Form {...formItemLayout}>
            <FormItem label="拉新规则说明">
              {
                editType ?
                  <Input.TextArea
                    onChange={(v) => {
                      postData.ruleContent = v;
                    }}
                    autoHeight
                    defaultValue={queryRuleInfo.ruleContent}
                    placeholder="不超过1000字，会展示在用户端规则说明模块" />
                  : <div>
                    {
                      queryRuleInfo.ruleContent && queryRuleInfo.ruleContent.split('\n').map(item => {
                        return <p>{item}</p>
                      })
                    }</div>
              }
            </FormItem>
            <FormItem label="新人可领取权益范围">
              <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                {
                  editType ?
                    <NumberPicker min={0} precision={2} defaultValue={queryRuleInfo.minDiscount} onChange={(v) => {
                      postData.minDiscount = v;
                    }} style={{ width: 100 }} />
                    : <span>{queryRuleInfo.minDiscount}</span>
                }
                <span>&nbsp;&nbsp;-&nbsp;&nbsp;</span>
                {
                  editType ?
                    <NumberPicker min={0} precision={2} defaultValue={queryRuleInfo.maxDiscount} onChange={(v) => {
                      postData.maxDiscount = v;
                    }} style={{ width: 100 }} /> : <span>{queryRuleInfo.maxDiscount}</span>
                }
                <span>&nbsp;&nbsp;元&nbsp;&nbsp;</span>
                <Balloon trigger={<Icon type="help" style={{ color: 'grey' }} />} triggerType="hover" align="r">
                  <div>提示：价格区间将展示在客态页面，请对齐权益活动配置</div>
                </Balloon>
              </div>
            </FormItem>
            <FormItem label="消息推送">
              <table>
                <tbody>
                <tr>
                  <td className="nav">开始时间</td>
                  <td>
                    {
                      editType ?
                        <DateTime defaultValue={queryRuleInfo.messageDTO.startTime} onChange={(v) => {
                          postData.messageDTO.startTime = v;
                        }} /> : <span>{queryRuleInfo.messageDTO.startTime}</span>
                    }
                  </td>
                </tr>
                <tr>
                  <td className="nav">结束时间</td>
                  <td>
                    {
                      editType ?
                        <DateTime defaultValue={queryRuleInfo.messageDTO.endTime} onChange={(v) => {
                          postData.messageDTO.endTime = v;
                        }} /> : <span>{queryRuleInfo.messageDTO.endTime}</span>
                    }
                  </td>
                </tr>
                <tr>
                  <td className="nav">消息标题</td>
                  <td>
                    {
                      editType ?
                        <Input defaultValue={queryRuleInfo.messageDTO.title} onChange={(v) => {
                          postData.messageDTO.title = v;
                        }} /> : <span>{queryRuleInfo.messageDTO.title}</span>
                    }
                  </td>
                </tr>
                <tr>
                  <td className="nav">跳转链接</td>
                  <td>
                    {
                      editType ?
                        <Input defaultValue={queryRuleInfo.messageDTO.jumpUrl} onChange={(v) => {
                          postData.messageDTO.jumpUrl = v;
                        }} /> : <span>{queryRuleInfo.messageDTO.jumpUrl}</span>
                    }
                  </td>
                </tr>
                </tbody>
              </table>
            </FormItem>
            <FormItem label={"客态底部banner1"}>
              <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                {
                  editType ?
                    <div>
                      <div>
                        {switchCheckBox(0, 0)}
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        {switchCheckBox(0, 1)}
                      </div>
                      {EditItem(0, 0)}
                      {EditItem(0, 1)}
                    </div>
                    :
                    <div>
                      {
                        (queryRuleInfo.list.find(item => item.sort === 0)) && <table>
                          <tbody>
                          {getConfigItem(0, 0)}
                          {getConfigItem(0, 1)}
                          </tbody>
                        </table>
                      }
                    </div>
                }
              </div>
            </FormItem>
            <FormItem label={"客态底部banner2"}>
              <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                {
                  editType ?
                    <div>
                      <div>
                        {switchCheckBox(1, 0)}
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        {switchCheckBox(1, 1)}
                      </div>
                      {EditItem(1, 0)}
                      {EditItem(1, 1)}
                    </div>
                    :
                    <div>
                      {
                        (queryRuleInfo.list.find(item => item.sort === 1)) && <table>
                          <tbody>
                          {getConfigItem(1, 0)}
                          {getConfigItem(1, 1)}
                          </tbody>
                        </table>
                      }
                    </div>
                }
              </div>
            </FormItem>
          </Form>
        </div>
      </div>
      <div className='Info'>
        {
          editType ?
            <Button type="normal" warning onClick={() => {
              if (+postData.maxDiscount <= +postData.minDiscount) {
                Message.error('领取范围最大值应大于最小值')
              } else {
                request('crm://rule/creatRule', {
                  method: 'POST',
                  body: postData
                }, postData).then((response) => {
                  if (response.success) {
                    Message.success('保存成功')
                    request('crm://bonus/queryRuleInfo', {
                      method: 'GET'
                    }).then((response) => {
                      SetQueryRuleInfo(response.data)
                      Object.assign(postData, response.data);
                      setEditType(false);
                    });
                  } else {
                    Message.error(response.errorDesc)
                  }
                });
              }
            }}>保存</Button>
            :
            (queryRuleInfo.canEdit && <Button className="editButton" warning onClick={() => {
              setEditType(true)
            }}>修改配置</Button>)
        }
      </div>
    </div>
  </div>
}

export default inforMationList;
