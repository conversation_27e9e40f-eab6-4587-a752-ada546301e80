import React, { Component, useMemo, useState } from 'react'
import { Tab, <PERSON><PERSON><PERSON><PERSON>b, Button, Form, Message, Checkbox, NumberPicker, Select, Input } from '@alifd/next'
import { Link } from 'react-router-dom'
import { request } from '@/packages/request'
import RewardByUsrGroup from './RewardByUserGroup';
import WeekRepurchase from './WeekRepurchase';
import './newUserReward.less'

const FormItem = Form.Item;
const breads = [
  {
    name: '线下拉新'
  },
  {
    name: '拉新激励配置'
  }
]

const formItemLayout = {
  labelCol: {
    fixedSpan: 5
  },
  wrapperCol: {
    span: 18
  }
};
const updateModel = {
  type1: {},
  type2: {},
  type3: {},
  type4: {},
  type5: {}
}

function Number({ model, index, min, max, precision, placeholder, disabled, onChange }) {
  return <NumberPicker
    defaultValue={model[index]}
    min={min}
    max={max}
    style={{ width: 120 }}
    placeholder={placeholder}
    disabled={disabled}
    precision={precision}
    onChange={(value) => {
      model[index] = value;
      if (onChange) {
        onChange(value)
      }
    }}
  />
}

export default function () {
  let [editType, setEditType] = useState(false);
  let [queryHeadDiscount, SetQueryHeadDiscount] = useState(null);
  let [levelDefinition, SetLevelDefinition] = useState(null);
  let [budgetInfos, setBudgetInfos] = useState(null);
  let [user, SetUser] = useState(null);
  let [canEdit, SetCanEdit] = useState(false);
  let [selectDownList, SetSelectDownList] = useState(null);
  const [activeTab, setActiveTab] = useState('common');

  useMemo(async () => {
    let userInfo = await (fetch('https://kunlun.alibaba-inc.com/user/info', {
      credentials: 'include'
    })
      .then((response) => {
        return response.json();
      })
      .then((myJson) => {
        return myJson.data;
      }));
    SetUser(userInfo)


    let downListRes = await request('crm://bonus/queryBudget', {
      method: 'GET',
      query: {
        shopId: 1,
        type: 1
      }
    })
    if (downListRes.data) {
      SetSelectDownList(downListRes.data.budgets);
    }


    request('crm://bonus/query', {
      method: 'GET',
      query: {
        shopId: 1,
        type: 1
      }
    }).then((response) => {
      setBudgetInfos(response.data.budgetInfos)
      SetQueryHeadDiscount(response.data.bonusInfos)
      SetLevelDefinition(response.data.levelDefinition)
      SetCanEdit(response.data.canEdit)
    });
  }, [])

  if (queryHeadDiscount === null || queryHeadDiscount.length === 0 || levelDefinition === null) {
    return null;
  }

  updateModel.type1 = queryHeadDiscount.find(item => (item.bonusType === 1));
  updateModel.type2 = queryHeadDiscount.find(item => (item.bonusType === 2));
  updateModel.type3 = queryHeadDiscount.find(item => (item.bonusType === 3));
  updateModel.type4 = queryHeadDiscount.find(item => (item.bonusType === 4));
  updateModel.type5 = queryHeadDiscount.find(item => (item.bonusType === 5));
  updateModel.directCityId = budgetInfos.find(item => {
    return item.type === 1;
  });
  updateModel.notDirectCityId = budgetInfos.find(item => {
    return item.type === 0;
  });
  updateModel.medicineCityId = budgetInfos.find(item => {
    return item.type === 2;
  });
  updateModel.levelDefinition = levelDefinition;

  return <div className="newUserReward">
    <Breadcrumb className='newUserReward_nav'>
      {breads.map((x, index) => <Breadcrumb.Item key={index}>
        {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
      </Breadcrumb.Item>)}
    </Breadcrumb>
    <div className='newUserReward_content'>
      <div className="title" style={{ marginBottom: 20 }}>拉新激励配置</div>
      <Tab activeKey={activeTab} onChange={tab => setActiveTab(tab)}>
        <Tab.Item key="common" title="通用激励配置" lazyLoad={false}>
          <div style={{ position: 'relative' }}>
            {editType ?
              <div className='Edit'>
                <div className='info'>
                  <div className='info_title'>推广员等级配置</div>
                  <Form {...formItemLayout}>
                    <FormItem label="推广员等级">
                      <table>
                        <tbody>
                          <tr>
                            <td className="nav">等级名称</td>
                            <td className="nav">周复购新客人数</td>
                            <td className="nav">推广上限人数</td>
                          </tr>
                          {
                            updateModel.levelDefinition.map((item, index) => {
                              return <tr>
                                <td>
                                  <Input defaultValue={item.levelName} onChange={(v) => {
                                    SetLevelDefinition(levelDefinition.map((itemLevel, keyLevel) => {
                                      if (keyLevel === index) {
                                        return {
                                          ...itemLevel, levelName: v
                                        }
                                      } else {
                                        return itemLevel;
                                      }
                                    }))
                                  }} />
                                </td>
                                <td style={{ display: 'flex' }}>
                                  <div
                                    style={{
                                      lineHeight: '34px',
                                      width: 50,
                                      color: '#CFCFCF',
                                      border: 'solid 1px #EBEBEB',
                                      borderRadius: 4,
                                      background: '#FAFAFA',
                                      marginRight: 4,
                                      paddingLeft: 10
                                    }}>
                                    {item.minRepurchaseNum}
                                  </div>
                                  {
                                    index === updateModel.levelDefinition.length - 1 ?
                                      <div style={{ lineHeight: '34px' }}>及以上</div> :
                                      <Number
                                        model={item}
                                        index='maxRepurchaseNum'
                                        min={item.minRepurchaseNum}
                                        disabled={index === updateModel.levelDefinition.length - 1}
                                        onChange={(value) => {
                                          item.maxRepurchaseNum = value;
                                          SetLevelDefinition(levelDefinition.map((itemLevel, keyLevel) => {
                                            if (keyLevel === index + 1) {
                                              return Object.assign({}, itemLevel, {
                                                minRepurchaseNum: item.maxRepurchaseNum + 1
                                              })
                                            } else {
                                              return itemLevel;
                                            }
                                          }))
                                        }} />
                                  }
                                </td>
                                <td>
                                  <Number
                                    model={item}
                                    index='newGuestUpLimit'
                                    onChange={(value) => {
                                      item.newGuestUpLimit = value;
                                    }} />
                                </td>
                              </tr>
                            })
                          }
                        </tbody>
                      </table>
                    </FormItem>
                  </Form>
                  <div className='info_title'>纯新客配置</div>
                  <Form {...formItemLayout}>
                    <FormItem label="订单类型">
                      <div>
                        <Checkbox disabled checked>首单(必选，不可取消)</Checkbox>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <Checkbox defaultChecked={updateModel.type2.status} onChange={(value) => {
                          let arr = Array.from(queryHeadDiscount);
                          let index = queryHeadDiscount.findIndex(item => {
                            return item.bonusType === 2;
                          });
                          arr[index] = Object.assign({}, arr[index], {
                            status: value ? 1 : 0
                          })
                          SetQueryHeadDiscount(arr)
                        }}>二单</Checkbox>
                      </div>
                      <div className='rewardInfo'>
                        <p>纯新客首单激励</p>
                        <div className='rewardInfo_content'>
                          <p>
                            满足规则&nbsp;&nbsp;&nbsp;识别登陆后
                            <Number
                              model={updateModel.type1}
                              min={0}
                              max={7}
                              index='limitDay'
                              placeholder='0～7的整数'
                              onChange={(value) => {
                                let arr = Array.from(queryHeadDiscount);
                                let index = queryHeadDiscount.findIndex(item => {
                                  return item.bonusType === 3;
                                });
                                arr[index] = Object.assign({}, arr[index], {
                                  limitDay: value
                                })
                                SetQueryHeadDiscount(arr)
                                updateModel.type3.limitDay = value;
                              }} />
                            日内下单，且订单实付金额大于
                            <Number
                              model={updateModel.type1} min={0} precision={2} index='limitPrice'
                              placeholder='支持两位小数' />
                            元
                          </p>
                          <div className='desc'>天数设置为“1”表示用户打开推广链接24小时内</div>
                          <div style={{ display: 'flex', margin: '10px 0' }}>
                            <div style={{ lineHeight: '37px' }}>激励金额&nbsp;&nbsp;&nbsp;</div>
                            <div>
                              <div>
                                <Checkbox defaultChecked={updateModel.type1.rewardConsistent} onChange={(value) => {
                                  SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                    if (item.bonusType === 1) {
                                      return {
                                        ...item,
                                        rewardConsistent: value,
                                        levelBonus: item.levelBonus.map(item2 => {
                                          return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                        })
                                      }
                                    } else {
                                      return item;
                                    }
                                  }));
                                }}>城代与直营保持一致</Checkbox>

                                <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>&nbsp;&nbsp;仅针对单人模式生效，代理模式不生效(线下结算)</span>
                              </div>
                              {
                                updateModel.type1.levelBonus.map((level, index) => {
                                  return <p>
                                    {levelDefinition[level.levelNum - 1].levelName}等级&nbsp;
                                    <Number
                                      model={updateModel.type1.levelBonus[index]} min={0} precision={2}
                                      index='bonusDirectCity'
                                      placeholder='支持两位小数'
                                      onChange={() => {
                                        if (updateModel.type1.rewardConsistent) {
                                          SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                            if (item.bonusType === 1) {
                                              return {
                                                ...item,
                                                levelBonus: item.levelBonus.map(item2 => {
                                                  return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                                })
                                              }
                                            } else {
                                              return item;
                                            }
                                          }));
                                        }
                                      }}
                                    />
                                    &nbsp;元/人，代理城市&nbsp;
                                    {
                                      updateModel.type1.rewardConsistent ?
                                        level.bonusNotDirectCity :
                                        <Number
                                          disabled={updateModel.type1.rewardConsistent}
                                          model={level} min={0} precision={2}
                                          index='bonusNotDirectCity'
                                          placeholder='支持两位小数'
                                        />
                                    }
                                    &nbsp;元/人
                                  </p>
                                })
                              }
                            </div>
                          </div>
                          <p>结算规则&nbsp;&nbsp;&nbsp;订单完成
                            <Number
                              model={updateModel.type1} min={0} max={7} index='receivedBonusDay'
                              placeholder='0～7的整数' />天后实时结算
                          </p>
                          <div className='desc'>若填写“0”表示订单完成即结算。“1”表示订单完成后次日结算，以此类推</div>
                        </div>
                      </div>
                      {
                        updateModel.type2.status ? <div className='rewardInfo'>
                          <p>纯新客二单激励</p>
                          <div
                            className='desc'
                            style={{ position: 'absolute', top: 20, right: 20, color: '#ababab' }}
                          >生鲜自提门店推广无新客二单激励
                          </div>
                          <div className='rewardInfo_content'>
                            <div>
                              <div>
                                满足规则&nbsp;&nbsp;&nbsp;首单完成后&nbsp;&nbsp;
                                <Number model={updateModel.type2} min={0} max={7} index='limitDay' placeholder='0～7的整数' />&nbsp;-&nbsp;
                                <Number model={updateModel.type2} min={0} max={7} index='maxLimitDay' placeholder='0～7的整数' />&nbsp;&nbsp;
                                日内下单
                              </div>
                              <div className='desc' style={{ margin: '5px 0' }}>若填写“1”表示首单完成当天24:00前。“2”表示订单完成后次日后，以此类推</div>
                              <div style={{ paddingLeft: 67 }}>
                                且订单实付金额大于
                                <Number
                                  model={updateModel.type2} min={0} precision={2} index='limitPrice'
                                  placeholder='支持两位小数' />
                                元
                              </div>
                            </div>
                            <div style={{ display: 'flex', margin: '10px 0' }}>
                              <div style={{ lineHeight: '37px' }}>激励金额&nbsp;&nbsp;&nbsp;</div>
                              <div>
                                <div>
                                  <Checkbox defaultChecked={updateModel.type2.rewardConsistent} onChange={(value) => {
                                    SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                      if (item.bonusType === 2) {
                                        return {
                                          ...item,
                                          rewardConsistent: value,
                                          levelBonus: item.levelBonus.map(item2 => {
                                            return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                          })
                                        }
                                      } else {
                                        return item;
                                      }
                                    }));
                                  }}>城代与直营保持一致</Checkbox>

                                  <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>&nbsp;&nbsp;仅针对单人模式生效，代理模式不生效(线下结算)</span>
                                </div>
                                {
                                  updateModel.type2.levelBonus.map((level, index) => {
                                    return <p>
                                      {levelDefinition[level.levelNum - 1].levelName}等级&nbsp;
                                      <Number
                                        model={updateModel.type2.levelBonus[index]} min={0} precision={2}
                                        index='bonusDirectCity'
                                        placeholder='支持两位小数'
                                        onChange={() => {
                                          if (updateModel.type1.rewardConsistent) {
                                            SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                              if (item.bonusType === 2) {
                                                return {
                                                  ...item,
                                                  levelBonus: item.levelBonus.map(item2 => {
                                                    return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                                  })
                                                }
                                              } else {
                                                return item;
                                              }
                                            }));
                                          }
                                        }}
                                      />
                                      &nbsp;元/人，代理城市&nbsp;
                                      {
                                        updateModel.type2.rewardConsistent ?
                                          level.bonusNotDirectCity :
                                          <Number
                                            disabled={updateModel.type2.rewardConsistent}
                                            model={level} min={0} precision={2}
                                            index='bonusNotDirectCity'
                                            placeholder='支持两位小数'
                                          />
                                      }
                                      &nbsp;元/人
                                    </p>
                                  })
                                }
                              </div>
                            </div>
                            <p>结算规则&nbsp;&nbsp;&nbsp;订单完成
                              <Number
                                model={updateModel.type2} min={0} max={7} index='receivedBonusDay'
                                placeholder='0～7的整数' />天后实时结算
                            </p>
                            <div className='desc'>若填写“0”表示订单完成即结算。“1”表示订单完成后次日结算，以此类推</div>
                          </div>
                        </div> : null
                      }
                    </FormItem>
                  </Form>
                </div>
                <div className='info'>
                  <div className='info_title'>半新客配置</div>
                  <Form {...formItemLayout}>
                    <FormItem label="订单类型">
                      <div>
                        <Checkbox defaultChecked={updateModel.type3.status} onChange={(value) => {
                          let arr = Array.from(queryHeadDiscount);
                          let index = queryHeadDiscount.findIndex(item => {
                            return item.bonusType === 3;
                          });
                          arr[index] = Object.assign({}, arr[index], {
                            status: value ? 1 : 0
                          })
                          SetQueryHeadDiscount(arr)
                        }}>首单</Checkbox>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <Checkbox defaultChecked={updateModel.type4.status} onChange={(value) => {
                          let arr = Array.from(queryHeadDiscount);
                          let index = queryHeadDiscount.findIndex(item => {
                            return item.bonusType === 4;
                          });
                          arr[index] = Object.assign({}, arr[index], {
                            status: value ? 1 : 0
                          })
                          SetQueryHeadDiscount(arr)
                        }}>二单</Checkbox>
                      </div>
                      {
                        updateModel.type3.status ? <div className='rewardInfo'>
                          <p>半新客首单激励</p>
                          <div className='rewardInfo_content'>
                            <p>
                              满足规则&nbsp;&nbsp;&nbsp;识别登陆后
                              <NumberPicker
                                value={updateModel.type3.limitDay}
                                min={0}
                                max={7}
                                style={{ width: 120 }}
                                placeholder='0～7的整数'
                                disabled
                              />
                              日内下单，且订单实付金额大于
                              <Number
                                model={updateModel.type3} min={0} precision={2} index='limitPrice'
                                placeholder='支持两位小数' />
                              元
                            </p>
                            <div className='desc'>天数设置为“1”表示用户打开推广链接24小时内</div>
                            <div style={{ display: 'flex', margin: '10px 0' }}>
                              <div style={{ lineHeight: '37px' }}>激励金额&nbsp;&nbsp;&nbsp;</div>
                              <div>
                                <div>
                                  <Checkbox defaultChecked={updateModel.type3.rewardConsistent} onChange={(value) => {
                                    SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                      if (item.bonusType === 3) {
                                        return {
                                          ...item,
                                          rewardConsistent: value,
                                          levelBonus: item.levelBonus.map(item2 => {
                                            return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                          })
                                        }
                                      } else {
                                        return item;
                                      }
                                    }));
                                  }}>城代与直营保持一致</Checkbox>

                                  <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>&nbsp;&nbsp;仅针对单人模式生效，代理模式不生效(线下结算)</span>
                                </div>
                                {
                                  updateModel.type3.levelBonus.map((level, index) => {
                                    return <p>
                                      {levelDefinition[level.levelNum - 1].levelName}等级&nbsp;
                                      <Number
                                        model={updateModel.type3.levelBonus[index]} min={0} precision={2}
                                        index='bonusDirectCity'
                                        placeholder='支持两位小数'
                                        onChange={() => {
                                          if (updateModel.type1.rewardConsistent) {
                                            SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                              if (item.bonusType === 3) {
                                                return {
                                                  ...item,
                                                  levelBonus: item.levelBonus.map(item2 => {
                                                    return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                                  })
                                                }
                                              } else {
                                                return item;
                                              }
                                            }));
                                          }
                                        }}
                                      />
                                      &nbsp;元/人，代理城市&nbsp;
                                      {
                                        updateModel.type3.rewardConsistent ?
                                          level.bonusNotDirectCity :
                                          <Number
                                            disabled={updateModel.type3.rewardConsistent}
                                            model={level} min={0} precision={2}
                                            index='bonusNotDirectCity'
                                            placeholder='支持两位小数'
                                          />
                                      }
                                      &nbsp;元/人
                                    </p>
                                  })
                                }
                              </div>
                            </div>
                            <p>结算规则&nbsp;&nbsp;&nbsp;订单完成
                              <Number
                                model={updateModel.type3} min={0} max={7} index='receivedBonusDay'
                                placeholder='0～7的整数' />天后实时结算
                            </p>
                            <div className='desc'>若填写“0”表示订单完成即结算。“1”表示订单完成后次日结算，以此类推</div>
                          </div>
                        </div> : null
                      }
                      {
                        updateModel.type4.status ? <div className='rewardInfo'>
                          <p>半新客二单激励</p>
                          <div
                            className='desc'
                            style={{ position: 'absolute', top: 20, right: 20, color: '#ababab' }}
                          >生鲜自提门店推广无新客二单激励
                          </div>
                          <div className='rewardInfo_content'>
                            <div>
                              <div>
                                满足规则&nbsp;&nbsp;&nbsp;首单完成后&nbsp;&nbsp;
                                <Number model={updateModel.type4} min={0} max={7} index='limitDay' placeholder='0～7的整数' />&nbsp;-&nbsp;
                                <Number model={updateModel.type4} min={0} max={7} index='maxLimitDay' placeholder='0～7的整数' />&nbsp;&nbsp;
                                日内下单
                              </div>
                              <div className='desc' style={{ margin: '5px 0' }}>若填写“1”表示首单完成当天24:00前。“2”表示订单完成后次日后，以此类推</div>
                              <div style={{ paddingLeft: 67 }}>
                                且订单实付金额大于
                                <Number
                                  model={updateModel.type4} min={0} precision={2} index='limitPrice'
                                  placeholder='支持两位小数' />
                                元
                              </div>
                            </div>
                            <div style={{ display: 'flex', margin: '10px 0' }}>
                              <div style={{ lineHeight: '37px' }}>激励金额&nbsp;&nbsp;&nbsp;</div>
                              <div>
                                <div>
                                  <Checkbox defaultChecked={updateModel.type4.rewardConsistent} onChange={(value) => {
                                    SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                      if (item.bonusType === 4) {
                                        return {
                                          ...item,
                                          rewardConsistent: value,
                                          levelBonus: item.levelBonus.map(item2 => {
                                            return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                          })
                                        }
                                      } else {
                                        return item;
                                      }
                                    }));
                                  }}>城代与直营保持一致</Checkbox>
                                  <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>&nbsp;&nbsp;仅针对单人模式生效，代理模式不生效(线下结算)</span>
                                </div>
                                {
                                  updateModel.type4.levelBonus.map((level, index) => {
                                    return <p>
                                      {levelDefinition[level.levelNum - 1].levelName}等级&nbsp;
                                      <Number
                                        model={updateModel.type4.levelBonus[index]} min={0} precision={2}
                                        index='bonusDirectCity'
                                        placeholder='支持两位小数'
                                        onChange={() => {
                                          if (updateModel.type1.rewardConsistent) {
                                            SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                              if (item.bonusType === 2) {
                                                return {
                                                  ...item,
                                                  levelBonus: item.levelBonus.map(item2 => {
                                                    return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                                  })
                                                }
                                              } else {
                                                return item;
                                              }
                                            }));
                                          }
                                        }}
                                      />
                                      &nbsp;元/人，代理城市&nbsp;
                                      {
                                        updateModel.type4.rewardConsistent ?
                                          level.bonusNotDirectCity :
                                          <Number
                                            disabled={updateModel.type4.rewardConsistent}
                                            model={level} min={0} precision={2}
                                            index='bonusNotDirectCity'
                                            placeholder='支持两位小数'
                                          />
                                      }
                                      &nbsp;元/人
                                    </p>
                                  })
                                }
                              </div>
                            </div>
                            <p>结算规则&nbsp;&nbsp;&nbsp;订单完成
                              <Number
                                model={updateModel.type4} min={0} max={7} index='receivedBonusDay'
                                placeholder='0～7的整数' />天后实时结算
                            </p>
                            <div className='desc'>若填写“0”表示订单完成即结算。“1”表示订单完成后次日结算，以此类推</div>
                          </div>
                        </div> : null
                      }
                    </FormItem>
                  </Form>
                </div>
                <div className='info'>
                  <div className='info_title'>医药用户配置</div>
                  <Form {...formItemLayout}>
                    <FormItem label="订单类型">
                      <div>
                        <Checkbox defaultChecked={updateModel.type5.status} onChange={(value) => {
                          let arr = Array.from(queryHeadDiscount);
                          let index = queryHeadDiscount.findIndex(item => {
                            return item.bonusType === 5;
                          });
                          arr[index] = Object.assign({}, arr[index], {
                            status: value ? 1 : 0
                          })
                          SetQueryHeadDiscount(arr)
                        }}>首单</Checkbox>
                      </div>
                      {
                        updateModel.type5.status ?
                          <div className='rewardInfo'>
                            <p>医药首单奖励</p>
                            <div className='rewardInfo_content'>
                              <p>
                                满足规则&nbsp;&nbsp;&nbsp;识别登陆后
                                <Number
                                  model={updateModel.type5}
                                  min={0}
                                  max={7}
                                  index='limitDay'
                                  placeholder='0～7的整数' />
                                日内下单，且订单实付金额大于
                                <Number
                                  model={updateModel.type5} min={0} precision={2} index='limitPrice'
                                  placeholder='支持两位小数' />
                                元
                              </p>
                              <div className='desc'>天数设置为“1”表示用户打开推广链接24小时内</div>
                              <div style={{ display: 'flex', margin: '10px 0' }}>
                                <div style={{ lineHeight: '37px' }}>激励金额&nbsp;&nbsp;&nbsp;</div>
                                <div>
                                  <div>
                                    <Checkbox defaultChecked={updateModel.type5.rewardConsistent} onChange={(value) => {
                                      SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                        if (item.bonusType === 5) {
                                          return {
                                            ...item,
                                            rewardConsistent: value,
                                            levelBonus: item.levelBonus.map(item2 => {
                                              return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                            })
                                          }
                                        } else {
                                          return item;
                                        }
                                      }));
                                    }}>城代与直营保持一致</Checkbox>

                                    <span style={{ display: 'inline-block', verticalAlign: 'middle' }}>&nbsp;&nbsp;仅针对单人模式生效，代理模式不生效(线下结算)</span>
                                  </div>
                                  {
                                    updateModel.type5.levelBonus.map((level, index) => {
                                      return <p>
                                        {levelDefinition[level.levelNum - 1].levelName}等级&nbsp;
                                        <Number
                                          model={updateModel.type5.levelBonus[index]} min={0} precision={2}
                                          index='bonusDirectCity'
                                          placeholder='支持两位小数'
                                          onChange={() => {
                                            if (updateModel.type5.rewardConsistent) {
                                              SetQueryHeadDiscount(queryHeadDiscount.map((item) => {
                                                if (item.bonusType === 5) {
                                                  return {
                                                    ...item,
                                                    levelBonus: item.levelBonus.map(item2 => {
                                                      return { ...item2, bonusNotDirectCity: item2.bonusDirectCity }
                                                    })
                                                  }
                                                } else {
                                                  return item;
                                                }
                                              }));
                                            }
                                          }}
                                        />
                                        &nbsp;元/人，代理城市&nbsp;
                                        {
                                          updateModel.type5.rewardConsistent ?
                                            level.bonusNotDirectCity :
                                            <Number
                                              disabled={updateModel.type5.rewardConsistent}
                                              model={level} min={0} precision={2}
                                              index='bonusNotDirectCity'
                                              placeholder='支持两位小数'
                                            />
                                        }
                                        &nbsp;元/人
                                      </p>
                                    })
                                  }
                                </div>
                              </div>
                              <p>结算规则&nbsp;&nbsp;&nbsp;订单完成
                                <Number
                                  model={updateModel.type5} min={0} max={7} index='receivedBonusDay'
                                  placeholder='0～7的整数' />天后实时结算
                              </p>
                              <div className='desc'>若填写“0”表示订单完成即结算。“1”表示订单完成后次日结算，以此类推</div>
                            </div>
                          </div> :
                          null}
                    </FormItem>
                  </Form>
                </div>
                <div className='info'>
                  <div className='info_title'>关联平台预算</div>
                  <Form {...formItemLayout}>
                    <FormItem label="纯新客预算ID">
                      <Select
                        defaultValue={updateModel.directCityId.budgetId}
                        disabled={updateModel.directCityId.creatorId !== parseInt(user.workid, 10) && updateModel.directCityId.creatorId !== 0}
                        onChange={(value) => {
                          // if (updateModel.directCityId.creatorId === 0) {
                          updateModel.directCityId.creatorId = parseInt(user.workid, 10)
                          // }
                          updateModel.directCityId.budgetId = value;
                        }}
                      >
                        {
                          selectDownList && selectDownList.map(item => {
                            return <Select.Option value={item.budgetId}>{item.budgetName}</Select.Option>;
                          })
                        }
                      </Select>
                      {
                        updateModel.directCityId.creatorId === parseInt(user.workid, 10) &&
                        <Button type="normal" text warning onClick={() => {
                          updateModel.directCityId.creatorId = 0;
                          Message.success('保存后，可完成释放')
                        }}>释放</Button>
                      }
                    </FormItem>
                    <FormItem label="半新客预算ID">
                      <Select
                        defaultValue={updateModel.notDirectCityId.budgetId}
                        disabled={updateModel.notDirectCityId.creatorId !== parseInt(user.workid, 10) && updateModel.notDirectCityId.creatorId !== 0}
                        onChange={(value) => {
                          updateModel.notDirectCityId.creatorId = parseInt(user.workid, 10)
                          updateModel.notDirectCityId.budgetId = value;
                        }}
                      >
                        {
                          selectDownList && selectDownList.map(item => {
                            return <Select.Option value={item.budgetId}>{item.budgetName}</Select.Option>;
                          })
                        }
                      </Select>
                      {
                        updateModel.notDirectCityId.creatorId === parseInt(user.workid, 10) &&
                        <Button type="normal" text warning onClick={() => {
                          updateModel.notDirectCityId.creatorId = 0;
                          Message.success('保存后，可完成释放')
                        }}>释放</Button>
                      }
                    </FormItem>
                    <FormItem label="医药预算ID">
                      <Select
                        defaultValue={updateModel.medicineCityId.budgetId}
                        disabled={updateModel.medicineCityId.creatorId !== parseInt(user.workid, 10) && updateModel.medicineCityId.creatorId !== 0}
                        onChange={(value) => {
                          updateModel.medicineCityId.creatorId = parseInt(user.workid, 10)
                          updateModel.medicineCityId.budgetId = value;
                        }}
                      >
                        {
                          selectDownList && selectDownList.map(item => {
                            return <Select.Option value={item.budgetId}>{item.budgetName}</Select.Option>;
                          })
                        }
                      </Select>
                      {
                        updateModel.medicineCityId.creatorId === parseInt(user.workid, 10) &&
                        <Button type="normal" text warning onClick={() => {
                          updateModel.medicineCityId.creatorId = 0;
                          Message.success('保存后，可完成释放')
                        }}>释放</Button>
                      }
                    </FormItem>
                  </Form>
                </div>
                <div className='tools'>
                  <Button onClick={() => {
                    setEditType(false)
                  }}>取消</Button>&nbsp;&nbsp;&nbsp;&nbsp;
                  <Button type="primary" warning onClick={() => {
                    if (levelDefinition.find((item) => {
                      return item.minRepurchaseNum >= item.maxRepurchaseNum
                    })) {
                      Message.error('周复购新客人数，结束应大于开始')
                    } else {
                      request('crm://bonus/creat', {
                        method: 'POST',
                        body: {
                          bonusInfos: [updateModel.type1, updateModel.type2, updateModel.type3, updateModel.type4, updateModel.type5],
                          budgetInfos: [updateModel.directCityId, updateModel.notDirectCityId, updateModel.medicineCityId],
                          levelDefinition
                        }
                      }).then((response) => {
                        if (response.success) {
                          Message.success('保存成功')
                          setEditType(false)
                          SetQueryHeadDiscount(null)
                          request('crm://bonus/query', {
                            method: 'GET',
                            query: {
                              shopId: 1,
                              type: 1
                            }
                          }).then((response) => {
                            setBudgetInfos(response.data.budgetInfos)
                            SetQueryHeadDiscount(response.data.bonusInfos)
                          });
                        } else {
                          Message.error(response.errorDesc)
                        }
                      });
                      setEditType(true)
                    }
                  }}>保存</Button>
                </div>
              </div>
              :
              <div className='Info'>
                {
                  canEdit && <Button className="editButton" type="normal" warning onClick={() => {
                    setEditType(true)
                  }}>修改配置</Button>
                }
                <div className="info">
                  <div className='info_title'>推广员等级配置</div>
                  <table>
                    <tbody>
                      <tr>
                        <td className="nav">等级名称</td>
                        <td className="nav">周复购新客人数</td>
                        <td className="nav">推广上限人数</td>
                      </tr>
                      {
                        levelDefinition.map((item, index) => {
                          return <tr>
                            <td>{item.levelName}</td>
                            <td>{item.minRepurchaseNum}{index === levelDefinition.length - 1 ? '及以上' : (' - ' + item.maxRepurchaseNum)}</td>
                            <td>{item.newGuestUpLimit}</td>
                          </tr>
                        })
                      }
                    </tbody>
                  </table>
                </div>
                <div className="info">
                  <div className='info_title'>激励配置</div>
                  <table>
                    <tbody>
                      {
                        queryHeadDiscount.sort((a, b) => {
                          return a.bonusType - b.bonusType;
                        }).map(item => {
                          return item.status === 1 ?
                            <React.Fragment>
                              <tr>
                                <td className="nav" rowSpan={3}>{{ 1: '纯新首单', 2: '纯新二单', 3: '半新首单', 4: '半新二单', 5: '医药首单' }[item.bonusType]}</td>
                                <td>
                                  <p>满足规则：{
                                    (item.bonusType === 2 || item.bonusType === 4) ?
                                      <span>首单完成后{item.limitDay}～{item.maxLimitDay}日内下单</span> :
                                      <span>识别登录后{item.limitDay}日内下单</span>
                                  }，且订单金额大于{item.limitPrice}元;</p>
                                </td>
                              </tr>
                              <tr>
                                <td style={{ display: 'flex' }}>
                                  <div>激励金额：</div>
                                  <div>
                                    <p>城代与直营保持一致：{item.rewardConsistent ? '是' : '否'}</p>
                                    {
                                      item.levelBonus.map(level => {
                                        return <p>{levelDefinition[level.levelNum - 1].levelName} -
                                          直营城市 {level.bonusDirectCity} 元/人，代理{level.bonusNotDirectCity}元/人</p>
                                      })
                                    }
                                  </div>
                                </td>
                              </tr>
                              <tr>
                                <td>
                                  <p>结算规则：订单完成{item.receivedBonusDay}天后实时结算;</p>
                                </td>
                              </tr>
                            </React.Fragment>
                            : null
                        })
                      }
                    </tbody>
                  </table>
                </div>
                <div className="info">
                  <div className='info_title'>预算信息</div>
                  <table>
                    <tbody>
                      <tr>
                        <td className="nav">纯新客预算ID</td>
                        <td>
                          {updateModel.directCityId.budgetId}
                        </td>
                      </tr>
                      <tr>
                        <td className="nav">半新客预算ID</td>
                        <td>
                          {updateModel.notDirectCityId.budgetId}
                        </td>
                      </tr>
                      <tr>
                        <td className="nav">医药预算ID</td>
                        <td>
                          {updateModel.medicineCityId.budgetId}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>}
          </div>
        </Tab.Item>
        <Tab.Item key="by-user-group" title="圈人群激励配置">
          {levelDefinition ? <RewardByUsrGroup levelDefinition={levelDefinition} /> : null}
        </Tab.Item>
        <Tab.Item key="week-repurchase" title="周复购激励配置">
          <WeekRepurchase />
        </Tab.Item>
      </Tab>
    </div>
  </div>
}
