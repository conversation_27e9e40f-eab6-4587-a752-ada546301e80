.newUserReward {
  padding: 20px;

  &_nav {
    margin-bottom: 20px;
  }

  &_content {
    background-color: white;
    padding: 20px;
    position: relative;

    .Edit {
      .info_title {
        color: #9a9a9a;
      }

      .tools {
        padding-left: 160px;
      }
    }

    .editButton {
      position: absolute;
      top: 20px;
      right: 20px;
    }

    .title {
      font-size: 20px;
    }

    .info {
      padding: 30px 60px;

      .info_title {
        margin-bottom: 20px;
      }

      .rewardInfo {
        margin-top: 10px;
        background-color: #f6f6f6;
        padding: 20px;
        position: relative;

        > p {
          line-height: 20px;
          font-size: 14px;
          margin-bottom: 5px;
        }

        &_content {
          padding: 18px;
          background-color: white;

          .desc {
            padding-left: 67px;
            color: #ababab;
          }

          > p {
            line-height: 50px;

            .next-number-picker {
              margin: 0 7px;
            }
          }
        }
      }

      table {
        width: 100%;
        border-spacing: 0;
        border-top: 1px solid #EBEBEB;
        border-left: 1px solid #EBEBEB;

        > tbody > tr {
          > td {
            border-bottom: 1px solid #EBEBEB;
            border-right: 1px solid #EBEBEB;
            padding: 20px;
          }

        }
      }

      .nav {
        background-color: #FAFAFA;
        width: 200px;
      }
    }
  }
}

.next-number-picker .next-input.next-medium .next-input-control {
  padding-right: 0 !important;
}

.next-number-picker-normal .next-number-picker-handler {
  height: 34px;
}
