import React, { Component } from 'react'
import { Breadcrumb, Form, Input, Grid, Table, Button } from '@alifd/next'
import { Link } from 'react-router-dom'
import './shopDetail.less';

const FormItem = Form.Item;
const { Row, Col } = Grid;

const formItemLayout = {
  wrapperCol: { span: 24 }
};

const insetLayout = {
  labelCol: { fixedSpan: 5 },
  wrapperCol: {
    span: 17
  }
};

const breads = [
  {
    name: '地推拉新'
  },
  {
    name: '商户管理列表',
    path: '/groundPush/shopList'
  },
  {
    name: '商户详情'
  }
]
export default function () {
  return <div className='shopDetail'>
    <Breadcrumb className='shopDetail_nav'>
      {breads.map((x, index) => <Breadcrumb.Item key={index}>
        {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
      </Breadcrumb.Item>)}
    </Breadcrumb>
    <div className='shopDetail_content'>
      <div className="title">商户详情</div>
      <div className='Info'>
        <div className="info">
          <div className='info_title'>商户信息</div>
          <table>
            <tbody>
            <tr>
              <td className="nav">门店</td>
              <td>XXXXX</td>
            </tr>
            <tr>
              <td className="nav">供应商</td>
              <td>XXXXX</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div className="info">
          <div className='info_title'>券权益</div>
          <table>
            <tbody>
            <tr>
              <td className="nav">权益投放计划</td>
              <td>XXXXX</td>
            </tr>
            <tr>
              <td className="nav">创建人</td>
              <td>XXXXX</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div className="info">
          <div className='info_title'>N元购</div>
          <Table dataSource={[]}>
            <Table.Column title="商品" htmlTitle="Unique Id" dataIndex="id" />
            <Table.Column title="原价(元)" dataIndex="title.name" />
            <Table.Column title="活动价(元)" dataIndex="title.name" />
            <Table.Column title="折扣" dataIndex="title.name" />
            <Table.Column title="活动库存" dataIndex="title.name" />
            <Table.Column title="平台补贴(元)" dataIndex="title.name" />
            <Table.Column title="商家补贴(元)" dataIndex="time" />
          </Table>
        </div>
      </div>
    </div>
  </div>
}
