import React, { useState } from 'react'
import { Select, Input, Dialog, Form, Radio, Field, Message, NumberPicker } from '@alifd/next'

function supplierInput(props) {
    const { valueObj, title, setVisible, addSupplier, visible } = props;
    const [storeOptions, setStoreOptions] = useState([]);
    const field = Field.useField();
    const { init, getError } = field
    const FormItem = Form.Item;
    const inputObj = {
        phone: {
            name: '手机号',
            options: {
                initValue: valueObj.phone,
                rules: [{
                    required: true,
                    pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
                    message: '请输入合法的手机号'
                }],
                onChange: (name, value) => {
                    console.log(value)
                }
            },
            legnth: 11
        },
        name: {
            name: '名称',
            options: {
                initValue: valueObj.name,
                rules: [{
                    required: true
                }],
                onChange: (name, value) => {
                }
            },
            legnth: 25
        },
        supplierType: {
            name: '业务身份',
            options: {
                initValue: valueObj.supplierType,
                rules: [{
                    required: true
                }]
            }
        },
        storeId: {
            name: '绑定门店ID',
            options: {
                initValue: valueObj.storeId,
            }
        }
    }
    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 }
    };
    return (
        <Dialog
            visible={visible}
            title={<div>{title}<span style={{ color: '#ccc', fontSize: 12 }}>&nbsp;&nbsp;注：该供应商为第三方合作公司/个人，非地推门店所属商户供应商</span></div>}
            footerActions={['cancel', 'ok']}
            onOk={() => {
                addSupplier(field.getValues())
            }}
            style={{
                width: '700px'
            }}
            onCancel={() => {
                setVisible(false)
            }}
            onClose={() => {
                setVisible(false)
            }}
        >
            <div style={{ marginTop: '20px' }} className="dialog">
                <Form {...formItemLayout}>
                    <FormItem label='供应商手机号'>
                        <Input {...init('phone', {
                            ...inputObj.phone.options
                        })}
                            maxLength={inputObj.phone.legnth}
                        />
                        {
                            getError('phone') ?
                                <span style={{ color: 'red' }}>
                                    {getError('phone').join('')}
                                </span>
                                :
                                null
                        }
                    </FormItem>
                    <FormItem label='供应商名称'>
                        <Input {...init('name', {
                            ...inputObj.name.options
                        })}
                            maxLength={inputObj.name.legnth}
                        />
                        {
                            getError('name') ?
                                <span style={{ color: 'red' }}>
                                    {'供应商名称' + getError('name').join('').slice(4)}
                                </span>
                                :
                                null
                        }
                    </FormItem>
                    <FormItem label='业务身份'>
                        <Radio.Group {...init('bizType', {
                            ...inputObj.supplierType.options
                        })}>
                            <Radio value="NEW_RETAIL" label="新零售C4" />
                            <Radio value="MEDICINE" label="医药" />
                            <Radio value="NEW_RETAIL_C5" label="新零售C5" />
                        </Radio.Group>
                    </FormItem>
                    {field.getValue('bizType') === 'MEDICINE' &&
                        <FormItem label='绑定门店ID'>
                            <NumberPicker
                                style={{ width: '100%' }}
                                {...init('storeId', {
                                    ...inputObj.storeId.options
                                })}
                                min={1}
                                precision={0}
                                hasTrigger={false}
                                placeholder="请输入淘系门店ID"
                            />
                        </FormItem>}
                </Form>
            </div>
        </Dialog>
    )
}
export default supplierInput
