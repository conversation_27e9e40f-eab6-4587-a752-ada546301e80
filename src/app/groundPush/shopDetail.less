.shopDetail {
  padding: 20px;

  .next-table-cell {
    padding: 0 !important;
  }

  &_nav {
    margin-bottom: 20px;
  }

  &_content {
    background-color: white;
    padding: 20px;
    position: relative;

    .Edit {
      .info_title {
        color: #9a9a9a;
      }

      .add-row {
        display: flex;
        flex-direction: row;
        align-items: center;

        .button {
          width: 120px;
          color: #FF7C4D;
        }

        .desc {
          margin-left: 30px;
          color: #a4a4a4;
        }
      }
    }

    .editButton {
      position: absolute;
      top: 20px;
      right: 20px;
    }

    .title {
      font-size: 20px;
    }

    .info {
      padding: 30px 60px;

      .info_title {
        margin-bottom: 20px;
      }

      .rewardInfo {
        margin-top: 10px;
        background-color: #f6f6f6;
        padding: 20px;

        &_content {
          padding: 18px;
          background-color: white;

          > p {
            line-height: 50px;

            .next-number-picker {
              margin: 0 7px;
            }
          }
        }
      }

      .saveButton {
        background-color: #FF7C4D
      }

      table {
        width: 100%;
        border-spacing: 0;
        border-top: 1px solid #EBEBEB;
        border-left: 1px solid #EBEBEB;

        tr {
          td {
            padding: 20px;
            border-bottom: 1px solid #EBEBEB;
            border-right: 1px solid #EBEBEB;
          }

        }
      }

      .nav {
        background-color: #FAFAFA;
        width: 200px;
      }
    }
  }
}

.dialogList {

  .search {
    .searchInput {
      margin: 0 10px;
    }
  }

  .list {
    margin-top: 20px;
  }
}
