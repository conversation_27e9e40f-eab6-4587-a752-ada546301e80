import React, { useState } from 'react'
import { Breadcrumb, Form, Input, Grid, Table, Button, Upload, Icon, Dialog, Message, Loading } from '@alifd/next'
import { Link } from 'react-router-dom'
import { config } from '../../api/config';
import './addShop.less';

const breads = [
  {
    name: '地推拉新'
  },
  {
    name: '商户管理列表',
    path: '/groundPush/shopList'
  },
  {
    name: '添加商户'
  }
]
export default function (props) {
  let [visible, setVisible] = useState(false)
  let [wrongList, setWrongList] = useState([])
  let [repeatList, setRepeatList] = useState([])
  let [noUsedList, setNoUsedList] = useState([])
  let [uploading, setUploading] = useState(false)
  return <div className='addShop'>
    <Breadcrumb className='addShop_nav'>
      {breads.map((x, index) => <Breadcrumb.Item key={index}>
        {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
      </Breadcrumb.Item>)}
    </Breadcrumb>
    <div className='addShop_content'>
      <div className="title">添加商户</div>
      <div className="info">
        <div className='info_title'>上传文件</div>
        <div>
          <p>请先下载<a
            className='downloadTemplate'
            href={config.API_ROOT + "/shop/download"}
          >商户导入模板</a>，按照要求填写完成，上传到系统</p>
          <Upload
            className="upload"
            disabled={uploading}
            action={config.API_ROOT + "/shop/upload"}
            beforeUpload={() => {
              setUploading(true)
            }}
            onSuccess={(res) => {
              let { data } = res.response.data;
              let hasError = false;
              if (data.repetitiveShops && data.repetitiveShops.length > 0) {
                hasError = true;
                setRepeatList(res.response.data.data.repetitiveShops.map(item => {
                  return Object.assign({
                    reason: '重复数据'
                  }, item)
                }));
              }
              if (data.paramErrorShops && data.paramErrorShops.length > 0) {
                hasError = true;
                setWrongList(res.response.data.data.paramErrorShops.map(item => {
                  return Object.assign({
                    reason: '参数错误'
                  }, item)
                }));
              }
              if (data.noUsedList && data.noUsedList.length) {
                hasError = true;
                setNoUsedList(res.response.data.data.noUsedList.map(item => {
                  return Object.assign({
                    reason: '无效的店铺'
                  }, item)
                }));
              }
              if (hasError) {
                setVisible(true)
              } else {
                Message.success('全部添加成功');
                setTimeout(() => {
                  props.history.replace('/groundPush/shopList');
                }, 1600)
              }
              setUploading(false)
            }}
          >
            {uploading && <Loading style={{ display: 'block', top: 50 }} />}
            <div className="next-upload-card">
              <Icon type="upload" size="large" />
              <div className="next-upload-text">
                点击上传文件
              </div>
            </div>
          </Upload>
        </div>
      </div>
    </div>
    <Dialog
      title="添加失败数据"
      visible={visible}
      onClose={() => {
        setVisible(false)
      }}
      footer={<div></div>}
    >
      <div style={{ height: 500, width: 500, overflow: 'auto' }}>
        <Table dataSource={[...wrongList, ...repeatList, ...noUsedList]}>
          <Table.Column title="店铺ID" dataIndex="shopId" />
          <Table.Column title="失败原因" dataIndex="reason" />
        </Table>
      </div>
    </Dialog>
  </div>
}
