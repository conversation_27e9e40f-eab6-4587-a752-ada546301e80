import React, { useState, useMemo } from 'react'
import { Breadcrumb, Form, Input, Select, Grid, Table, Button, Dialog, Pagination, Message } from '@alifd/next'
import { Link } from 'react-router-dom'
import { request } from '@/packages/request'
import './shopList.less';

const FormItem = Form.Item;
const { Row, Col } = Grid;

const formItemLayout = {
  wrapperCol: { span: 24 }
};

const insetLayout = {
  labelCol: { fixedSpan: 5 },
  wrapperCol: {
    span: 16
  }
};

const breads = [
  {
    name: '地推拉新'
  },
  {
    name: '商户管理列表'
  }
]

export default function (props) {
  let [shopInfoList, setShopInfoList] = useState(null);
  let [shopCount, setShopCount] = useState(null);
  let [searchObj, setSearchObj] = useState({
    rn: 1,
    pn: 10
  });
  useMemo(() => {
    request('crm://shop/getShopInfo?rn=' + searchObj.rn + '&pn=' + searchObj.pn, {
      method: 'GET',
      query: searchObj
    }).then((res) => {
      if (res.errorDesc === 'success') {
        setShopInfoList(res.data.shopInfoList);
        setShopCount(res.data.totalNum);
      }
    });
  }, [])
  if (shopInfoList === null || shopCount === null) {
    return null
  }

  function SearchInput({ searchKey }) {
    let [val, setVal] = useState(searchObj[searchKey]);
    return <Input
      value={val}
      name={searchKey}
      onChange={(val) => {
        setVal(val);
      }}
      onBlur={() => {
        setSearchObj(Object.assign({}, searchObj, {
          [searchKey]: val
        }))
      }}
    />;
  }

  function SearchSelect({ searchKey, children }) {
    let [val, setVal] = useState(searchObj[searchKey]);
    return <Select
      style={{ width: '100%' }}
      value={val}
      name={searchKey}
      onChange={(val) => {
        setVal(val);
      }}
      onBlur={() => {
        setSearchObj(Object.assign({}, searchObj, {
          [searchKey]: val
        }))
      }}
    >{children}</Select>;
  }

  return <div className='shopList'>
    <Breadcrumb className='shopList_nav'>
      {breads.map((x, index) => <Breadcrumb.Item key={index}>
        {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
      </Breadcrumb.Item>)}
    </Breadcrumb>
    <div className='shopList_content'>
      <div className="title">商户管理列表</div>
      <Button className='addButton' warning onClick={() => {
        props.history.push('/groundPush/addShop');
      }}>添加商户</Button>
      <Form {...formItemLayout}>
        <FormItem style={{ marginBottom: 0 }}>
          <Row gutter="4">
            <Col>
              <FormItem
                label="门店ID：" {...insetLayout} required requiredTrigger="onBlur"
                asterisk={false}>
                <SearchInput searchKey='shopId' />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="门店名称" {...insetLayout} required asterisk={false}>
                <SearchInput searchKey='shopName' />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="维护人" {...insetLayout} required asterisk={false}>
                <SearchInput searchKey='creatorName' />
              </FormItem>
            </Col>
            <Col fixedSpan={10}></Col>
          </Row>
        </FormItem>
        <FormItem style={{ marginBottom: 0 }}>
          <Row gutter="4">
            <Col>
              <FormItem
                label="供应商ID：" {...insetLayout} required requiredTrigger="onBlur"
                asterisk={false}>
                <SearchInput searchKey='shopSupplierId' />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="供应商名称" {...insetLayout} required asterisk={false}>
                <SearchInput searchKey='shopSupplierName' />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="门店状态" {...insetLayout} required asterisk={false}>
                <SearchSelect searchKey='shopStatus'>
                  <Select.Option value={1}>有效</Select.Option>
                  <Select.Option value={0}>无效</Select.Option>
                </SearchSelect>
              </FormItem>
            </Col>
            <Col fixedSpan={10}>
              <Form.Submit onClick={(v) => {
                setSearchObj(Object.assign({}, searchObj, {
                  rn: 1
                }))
                request('crm://shop/getShopInfo', {
                  method: 'GET',
                  query: Object.assign({}, searchObj, {
                    rn: 1
                  })
                }).then((res) => {
                  if (res.errorDesc === 'success') {
                    setShopInfoList(res.data.shopInfoList);
                    setShopCount(res.data.totalNum);
                  }
                });
              }}>查询</Form.Submit>
              <Form.Submit onClick={(v) => {
                setSearchObj({
                  rn: 1,
                  pn: 10
                })
                request('crm://shop/getShopInfo', {
                  method: 'GET',
                  query: {
                    rn: 1,
                    pn: 10
                  }
                }).then((res) => {
                  if (res.errorDesc === 'success') {
                    setShopInfoList(res.data.shopInfoList);
                    setShopCount(res.data.totalNum);
                  }
                });
              }}>重置</Form.Submit>
            </Col>
          </Row>
        </FormItem>
      </Form>
      <div className="count">共有{shopCount}个相关内容</div>
      <Table dataSource={shopInfoList}>
        <Table.Column title="门店ID" htmlTitle="Unique Id" dataIndex="shopId" />
        <Table.Column title="门店名称" dataIndex="shopName" />
        <Table.Column title="供应商名称" dataIndex="shopSupplierName" />
        <Table.Column title="店铺状态" dataIndex="shopStatus" cell={(value, index, record) => {
          return <div>{value === 1 ? '有效' : '无效'}</div>
        }} />
        <Table.Column title="维护人" dataIndex="creatorName" />
        <Table.Column title="添加时间" dataIndex="gmtCreate" />
        <Table.Column title="操作" width={60} cell={(value, index, record) => {
          return <Button.Group size='size'>
            <Button onClick={() => {
              Dialog.confirm({
                title: '删除商户',
                content: '确定要删除该商户么？',
                onOk: () => {
                  return new Promise((res) => {
                    request('crm://shop/deleteShopInfo', {
                      method: 'POST',
                      body: {
                        shopId: record.shopId
                      }
                    }).then((response) => {
                      console.log(1, response);
                      if (response.errorCode !== '0') {
                        Message.error(response.errorDesc);
                      } else {
                        Message.success('删除成功');
                        setTimeout(() => {
                          request('crm://shop/getShopInfo?rn=' + searchObj.rn + '&pn=' + searchObj.pn, {
                            method: 'GET',
                            query: searchObj
                          }).then((res) => {
                            if (res.errorDesc === 'success') {
                              setShopInfoList(res.data.shopInfoList);
                              setShopCount(res.data.totalNum);
                            }
                          });
                        }, 10);
                      }
                      res(response);
                      // SetQueryRuleInfo(response.data)
                      // Object.assign(postData, response.data);
                    });
                  })
                }
              });
            }}>删除</Button>
          </Button.Group>
        }} />
      </Table>
      <Pagination
        current={searchObj.rn}
        defaultCurrent={searchObj.rn}
        total={shopCount}
        pageSize={searchObj.pn}
        onChange={(page) => {
          setSearchObj(Object.assign({}, searchObj, {
            rn: page
          }));
          request('crm://shop/getShopInfo?rn=' + searchObj.rn + '&pn=' + searchObj.pn, {
            method: 'GET',
            query: Object.assign({}, searchObj, {
              rn: page
            })
          }).then((res) => {
            if (res.errorDesc === 'success') {
              setShopInfoList(res.data.shopInfoList);
            }
          });
        }} />
    </div>
  </div>
}
