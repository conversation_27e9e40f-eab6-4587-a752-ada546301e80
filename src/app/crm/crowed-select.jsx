import React, { Component } from 'react';
import "./crm.scss"
import { buildEnv } from '@/packages/env'
import { config } from '@/api/config';

export default class crowedSelect extends Component {

  // resolveIframePath = () => {
  //   let path = 'https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/select'
  //   if (buildEnv === 'prod') {
  //     path = `https://market.m.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/select`
  //   } else if (buildEnv === 'pre') {
  //     path = `https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/select`
  //   } else {
  //     path = `https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/select`
  //   }

  //   return path
  // }


  render() {
    return (
      <div className="iframe-container">
        <iframe
          title="人群圈选"
          style={{ width: '100%', overflow: 'visible', minHeight: "calc(100vh)" }}
          // scrolling="no"
          src={config.CROWED_SELECT_URL}
          width="100%"
          scrolling="y"
          frameBorder="0"
        />
      </div>

    );
  }
}
