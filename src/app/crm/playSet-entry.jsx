import React, { Component } from 'react';
import { buildEnv } from '@/packages/env'
import { config } from '@/api/config';


export default class playSetEntry extends Component {

  // resolveIframePath = () => {
  //   let path = 'https://boreas.kunlun.alibaba-inc.com/page/crm-user/index.html#/'
  //   if (buildEnv === 'prod') {
  //     path = `https://boreas.kunlun.alibaba-inc.com/page/crm-user/index.html#/`
  //   } else if (buildEnv === 'pre') {
  //     path = `https://kun.eleme.test/app/eleme-b-newretail/new_kun/crm-user/index.html`
  //   } else {
  //     path = `https://market.wapa.taobao.com/app/eleme-b-newretail/new_kun/crm-user/index.html#/crm_user/entry`
  //   }

  //   return path
  // }

  render() {
    return (
      <iframe
        title="11"
        style={{ width: '100%', height: '100%', overflow: 'visible' }}
        src={config.PLAYSET_URL}
        width="100%"
        scrolling="y"
        frameBorder="0"
      />
    );
  }
}
