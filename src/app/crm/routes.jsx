import React, { Component, Suspense, lazy } from 'react'
import { buildEnv } from '@/packages/env'
import Routers from '@/packages/routers'

import crowedList from './crowed-list'
import crowedSelect from './crowed-select'
import playSetList from './playSet-list'
import playSetEntry from './playSet-entry'
import whiteList from './white-list'
import shopList from "../groundPush/shopList";
import supplierList from "../groundPush/supplierList";
import shopDetail from "../groundPush/shopDetail";
import addShop from '../groundPush/addShop';
import newUserReward from "../groundPush/newUserReward";
import quanyi from "../groundPush/quanyi";
import informationList from "../groundPush/informationList";


function toPath(file) {
  const prts = file.split('.')
  prts.pop()
  return '/' + prts.filter(x => x).join('/')

}


function iFrameWidget(urlObj) {
  return () => {
    const env = buildEnv()
    console.log(env);
    return <div className="iframe-container">
      <iframe
        title="人群列表"
        style={{ width: '100%', overflow: 'visible', minHeight: 'calc(100vh - 60px)' }}
        onLoad={(e) => {
          console.log('iframe--onload-----------')
        }}
        src={urlObj[env]}
        className="iframe"
        scrolling="y"
        frameBorder="0"
      />
    </div>
  }
}


const req = require.context('./routes')

const files = req.keys()

const routerList = [
  {
    // 玩法列表
    subRouterPath: '/crm',
    routerItems: [
      {
        path: '/',
        component: playSetEntry,
        breadcrumbName: '用户成长'
      },
      {
        path: '/userGrowUp',
        component: playSetEntry,
        breadcrumbName: '用户成长'
      },
      {
        path: '/userGrowList',
        component: playSetList,
        breadcrumbName: '玩法列表'
      },
      {
        path: '/whiteList',
        component: whiteList,
        breadcrumbName: '白名单列表'
      },
      {
        path: '/crowedList',
        component: crowedList,
        breadcrumbName: '人群列表'
      },
      {
        path: '/playset-v2',
        component: iFrameWidget({
          daily: 'https://nr.alibaba.net/op-fe/interactive-webapp/index.html#/playset/list',
          pre: 'https://pre-nr.alibaba-inc.com/op-fe/interactive-webapp/index.html#/playset/list',
          prod: 'https://nr.alibaba-inc.com/op-fe/interactive-webapp/index.html#/playset/list'
        }),
        breadcrumbName: '玩法列表(新)'
      },
      {
        path: '/source-list',
        component: iFrameWidget({
          daily: 'https://nr.alibaba.net/op-fe/interactive-webapp/index.html#/source/list',
          pre: 'https://pre-nr.alibaba-inc.com/op-fe/interactive-webapp/index.html#/source/list',
          prod: 'https://nr.alibaba-inc.com/op-fe/interactive-webapp/index.html#/source/list'
        }),
        breadcrumbName: '素材库'
      },
      {
        path: '/crowdInsight',
        component: iFrameWidget({
          daily: 'https://market.wapa.taobao.com/app/eleme-xcy-fed/crowd-insight/index.html#/crowd/map/ta-concentration',
          pre: 'https://market.wapa.taobao.com/app/eleme-xcy-fed/crowd-insight/index.html#/crowd/map/ta-concentration',
          prod: 'https://market.m.taobao.com/app/eleme-xcy-fed/crowd-insight/index.html#/crowd/map/ta-concentration'
        }),
        breadcrumbName: 'TA浓度测算'
      },
      {
        path: '/ebCrowedList',
        component: iFrameWidget({
          daily: 'https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/template/list',
          pre: 'https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/template/list',
          prod: 'https://market.m.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/template/list'
        }),
        breadcrumbName: '人群列表'
      },
      {
        path: '/crowedSelect',
        component: crowedSelect,
        breadcrumbName: '圈选人群'
      },
      {
        path: '/playSet',
        component: playSetList,
        breadcrumbName: '玩法配置'
      },
      {
        path: '/strategyicLab',
        component: iFrameWidget({
          daily: 'https://market.waptest.taobao.com/app/eleme-xcy-fed/strategic-lab/index.html#/strategy/list',
          pre: 'https://market.wapa.taobao.com/app/eleme-xcy-fed/strategic-lab/index.html#/strategy/list',
          prod: 'https://market.m.taobao.com/app/eleme-xcy-fed/strategic-lab/index.html#/strategy/list'
        }),
        breadcrumbName: '策略实验室'
      }
    ]
  },
  {
    subRouterPath: '/promotion',
    routerItems: [{
      path: "/tools/qrcode",
      component: iFrameWidget({
        pre: 'https://market.wapa.taobao.com/app/op-fe/kunlun-promotion-webapp/index.html?env=pre#/promotion/tools/qrcode',
        prod: 'https://market.m.taobao.com/app/op-fe/kunlun-promotion-webapp/index.html#/promotion/tools/qrcode'
      }),
      breadcrumbName: '私域推广'
    }]
  },
  {
    // 玩法列表
    subRouterPath: '/groundPush',
    routerItems: [
      {
        path: '/shopList',
        component: shopList,
        breadcrumbName: '商户活动管理'
      },
      {
        path: '/supplierList',
        component: supplierList,
        breadcrumbName: '供应商活动管理'
      },
      {
        path: '/shopDetail/:id',
        component: shopDetail,
        breadcrumbName: '商户活动管理'
      },
      {
        path: '/approachList',
        component: iFrameWidget({
          daily: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/planList',
          pre: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html?env=pre#/groundPush/planList',
          prod: 'https://market.m.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/planList'
        }),
        breadcrumbName: '地推方案管理'
      },
      {
        path: '/merchantPromotionCodeList',
        component: iFrameWidget({
          daily: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/merchantPromotionCodeList',
          pre: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html?env=pre#/groundPush/merchantPromotionCodeList',
          prod: 'https://market.m.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/merchantPromotionCodeList'
        }),
        breadcrumbName: '推广码管理'
      },
      {
        path: '/organizationList',
        component: iFrameWidget({
          daily: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/organizationList',
          pre: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html?env=pre#/groundPush/organizationList',
          prod: 'https://market.m.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/organizationList'
        }),
        breadcrumbName: '地推组织管理'
      },
      {
        path: '/activityList',
        component: iFrameWidget({
          daily: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/activityList',
          pre: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html?env=pre#/groundPush/activityList',
          prod: 'https://market.m.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/activityList'
        }),
        breadcrumbName: '地推活动管理'
      },
      {
        path: '/couponPlanList',
        component: iFrameWidget({
          daily: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/couponPlanList',
          pre: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html?env=pre#/groundPush/couponPlanList',
          prod: 'https://market.m.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/couponPlanList'
        }),
        breadcrumbName: '地推活动管理'
      },
      {
        path: '/commissionList',
        component: iFrameWidget({
          daily: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/commissionList',
          pre: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html?env=pre#/groundPush/commissionList',
          prod: 'https://market.m.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/commissionList'
        }),
        breadcrumbName: '佣金活动管理'
      },
      {
        path: '/addShop',
        component: addShop,
        breadcrumbName: '添加商户'
      },
      {
        path: '/newUserReward',
        component: newUserReward,
        breadcrumbName: '拉新激励配置'
      },
      {
        path: '/quanyi',
        component: quanyi,
        breadcrumbName: '拉新权益配置'
      }, {
        path: '/informationList',
        component: informationList,
        breadcrumbName: '内容管理'
      }, {
        path: '/blacklist',
        component: iFrameWidget({
          daily: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/blacklist',
          pre: 'https://market.wapa.taobao.com/app/op-fe/ground-push-webapp/index.html?env=pre#/groundPush/blacklist',
          prod: 'https://market.m.taobao.com/app/op-fe/ground-push-webapp/index.html#/groundPush/blacklist'
        }),
        breadcrumbName: '推广员黑名单'
      }
    ]
  }
]

const breadcrumbMap = {
  '/': 'CRM',
  '/crm/userGrowUp': '用户成长',
  '/crm/crowedList': '人群列表',
  '/crm/crowedSelect': '圈选人群',
  '/crm/userWhiteList': '白名单'

}

function App() {
  return <Routers routerList={routerList} redirectPath="/crm" breadcrumbMap={breadcrumbMap} />
}

export default App
