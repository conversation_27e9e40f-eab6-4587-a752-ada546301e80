import React, { Component } from 'react';
import ReactDOM from 'react-dom'
import "./crm.scss"
import { buildEnv } from '@/packages/env'
import { config } from '@/api/config';

export default class crowedList extends Component {
  constructor(props) {
    super(props)

    this.state = {
      iFrameHeight: '0px'
    }
  }

  // resolveIframePath = () => {
  //   let path = 'https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/list'
  //   if (buildEnv === 'prod') {
  //     path = `https://market.m.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/list`
  //   } else if (buildEnv === 'pre') {
  //     path = `https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/list`
  //   } else {
  //     path = `https://market.wapa.taobao.com/app/eleme-xcy-fed/crm-label/index.html#/crowd/list`
  //   }

  //   return path
  // }

  // componentWillUpdate() {
  //   console.log('update')
  // }

  // componentDidMount() {
  //   console.log('iframe-------------')

  //   // eslint-disable-next-line react/no-find-dom-node
  //   let content = ReactDOM.findDOMNode(this.iframe_crowed)
  //   let outerHeight = content.contentWindow.top.outerHeight
  //   this.setState({
  //     iFrameHeight: `${outerHeight}px`
  //   })
  // }

  handleOnload = (e) => {
    console.log('iframe--onload-----------')
  }

  render() {
    return (
      <div className="iframe-container">
        <iframe
          title="人群列表"
          style={{ width: '100%', overflow: 'visible', minHeight: "calc(100vh)" }}
          onLoad={(e) => this.handleOnload(e)}
          src={config.CROWED_URL}
          className="iframe"
          ref={c => { this.iframe_crowed = c }}
          scrolling="y"
          frameBorder="0"
        />
      </div>

    );
  }
}
