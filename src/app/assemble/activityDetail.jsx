import React, { useState, useEffect } from 'react'
import { Tab } from '@alifd/next';
import iconQuan from '@/assets/icon_quan.png'
import Forms from './components/Forms'
import Detail from './components/Detail'
import { request } from '@/packages/request'
import './activityDetail.less'

const Layer = ({ Render, ...reset }) => <section className="detail-wrap"><Render {...reset} /></section>
const Header = ({ name, playId }) => {
  return (<div className="detail-head">
    <div className="pin">拼</div>
    <div>
      <h1 className="detail-title">{name}</h1>
      <p>ID:{playId}</p>
    </div>
  </div>)
}
const DetailTab = ({ actived, id, ...reset }) => {
  const [active, setActive] = useState(actived)
  return (<Tab activeKey={active} onChange={(val) => {
    setActive(val)
  }}>
    <Tab.Item title='活动信息' key="info">
      <Forms view='detail' id={id} />
    </Tab.Item>
    <Tab.Item title='活动商品' key='goods'>
      <Detail id={id} />
    </Tab.Item>
  </Tab>)
}

const ActivityDetail = ({ history }) => {
  let [playName, setPlayName] = useState('')
  const list = history && new URLSearchParams(history.location.search)
  const id = list && list.get('id')
  const key = list && list.get('key')
  const playId = list && list.get('playId')
  useEffect(() => {
    request('mtop://mtop.ele.newretail.play.platform.query.act', {
      query: Object.assign({
        playId,
        currentPage: 0,
        pageSize: 1
      })
    }).then(res => {
      if (res.data.data) {
        let { playInfoDTOList } = res.data.data
        if (playInfoDTOList.length > 0) {
          setPlayName(playInfoDTOList[0].name)
        }
      }
    })
  }, [])
  return <div className="activityDetail">
    <Layer Render={Header} name={playName} playId={playId} />
    <Layer Render={DetailTab} actived={key} id={id} />
  </div>
}

export default ActivityDetail
