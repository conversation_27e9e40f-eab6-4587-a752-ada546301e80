/**
 * 玩法配置-列表
 */
/* eslint-disable radix */

import React, { Component, useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Dialog, Input, Form, Button, Message, RangePicker } from '@alifd/next'
import _ from 'lodash'

import filterDataSource from '@/source/filterDataSource'
import requestDataSource from '@/source/requestDataSource'

import { Filter, Table } from '@/packages/table/FilterTable'
import Header from '@/components/header'
import parseUrl from '@/packages/request/parseUrl'

import formatDate from '@/utils/formatDate'
import BoreasDialog from '@/components/dialog'
import { request } from '@/packages/request'

const statusList = {
  1: "未开始",
  2: "活动中",
  3: "活动正常结束",
  4: "已下线"
}

export default function (props) {
  const [dataSource, setDataSource] = useState([])
  let [total, setTotal] = useState(0)
  let [currentPage, setCurrentPage] = useState(0)
  let [pageSize, setPageSize] = useState(10)
  let [searchParmas, setSearchParmas] = useState(parseUrl(window.location.href).query || {
    playId: '',
    name: '',
    queryActInfoState: '',
    creator: '',
    storeId: '',
    date: '',
  })
  const handleEvent = (e, values, field) => {
    if (e === 'reset') {
      let newParams = {
        userId: null,
        phone: null,
        queryState: null,
        creator: null,
        storeId: null,
        date: null
      }
      setSearchParmas(newParams)
      setCurrentPage(0)
      updateList(newParams);
    } else if (e === 'query') {
      if (currentPage !== 0) {
        setCurrentPage(0)// 因为回到0，会自动触发updateList。但是如果当前已经是0，则无法触发state事件
      } else {
        updateList(searchParmas)
      }
    } else if (e === 'change') {
      setSearchParmas(val => {
        val[values] = field;
        return val;
      })
    }
  }

  function updateList(searchParmas) {
    setDataSource([]);
    // setTotal(0);
    request('mtop://mtop.ele.newretail.play.platform.query.act', {
      query: Object.assign({
        playId: searchParmas.playId !== '' ? searchParmas.playId : null,
        name: searchParmas.name !== '' ? searchParmas.name : null,
        queryActInfoState: searchParmas.queryActInfoState !== '' ? searchParmas.queryActInfoState : null,
        creator: searchParmas.creator !== '' ? searchParmas.creator : null,
        startTime: (searchParmas.date && searchParmas.date[0]) ? searchParmas.date[0].format('YYYY-MM-DD 00:00:00') : null,
        endTime: (searchParmas.date && searchParmas.date[1]) ? searchParmas.date[1].format('YYYY-MM-DD 23:59:59') : null,
        currentPage,
        pageSize
      })
    }).then(res => {
      let data = res.data.data;
      setDataSource(data.playInfoDTOList)
      setTotal(data.total)
    })
  }

  useEffect(() => {
    updateList(searchParmas);
  }, [currentPage, pageSize])


  const filterUISource = {
    'x-component': 'Filter',
    labelAlign: 'top',
    children: [
      {
        defaultValue: searchParmas.playId,
        label: '玩法ID:',
        name: 'playId',
        placeholder: '请输入活动ID',
        'x-component': 'Input',
        'data-type': 'number'
      },
      {
        defaultValue: searchParmas.name,
        label: '玩法名称:',
        name: 'name',
        placeholder: '请输入活动名称',
        'x-component': 'Input'
      },
      {
        defaultValue: searchParmas.queryActInfoState,
        label: '活动状态:',
        name: 'queryActInfoState',
        placeholder: '请选择玩法活动状态',
        'x-component': 'Select',
        dataSource: [{
          value: '',
          label: '全部状态'
        }, ...Object.keys(statusList).map(x => ({
          value: x,
          label: statusList[x]
        }))]
      },
      {
        defaultValue: searchParmas.creator,
        label: '创建人:',
        name: 'creator',
        placeholder: '请输入创建人名称',
        'x-component': 'Input'
      },
      {
        label: "活动日期:",
        defaultValue: searchParmas.date,
        name: "date",
        showTime: true,
        "data-type": "string",
        "x-component": "RangePicker",
        required: true,
        rules: {
          required: true,
          message: "活动日期不能为空"
        },
        disabledDate: true,
        initValue: []
      }
    ]
  }

  const columns = [
    {
      title: '玩法ID',
      dataIndex: 'playId'
    },
    {
      title: '玩法名称',
      dataIndex: 'name'
    },
    {
      title: '活动时间',
      dataIndex: 'timeRange',
      cell: (value, index, record) => {
        return <span>{formatDate(record.startDate, 'YYYY-MM-DD')}~{formatDate(record.endDate, 'YYYY-MM-DD')}</span>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate'
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      width: '120px',
    },
    // {
    //   title: '商户数',
    //   dataIndex: 'storeCount'
    // },
    {
      title: '商品数',
      dataIndex: 'itemCount'
    },
    {
      title: '状态',
      dataIndex: 'showStatus',
      width: '90px',
      cell: value => {
        const colors = {
          1: "#FFBB33",// 未开始
          2: "#00CCAA",// 活动中
          3: "#CCCCCC",// 活动正常结束
          4: "#FF3000"// 已下线
        }
        return (
          <div className={`crm-status hasStatueIcon crm-status-${value}`}>
            <div className={'icon'} style={{ backgroundColor: colors[value] }}></div>
            {statusList[value]}
          </div>
        )
      }
    },
    {
      title: '操作',
      dataIndex: 'ops',
      width: '240px',
      cell: (value, index, record, { reload }) => {
        return (
          <div className="boreas-cell">
            <Link to={{
              pathname: `/assemble/detail`,
              search: `?id=${record.instanceId}&key=info&playId=${record.playId}`
            }} className="pl">
              <span className="pr">详情</span>
            </Link>
            {record.status !== 2 && (<React.Fragment>
              {
                record.showStatus !== 3 && <Link to={{
                  pathname: `/assemble/detail`,
                  search: `?id=${record.instanceId}&key=goods&playId=${record.playId}`
                }} className="pl">
                  <span className="pr">管理商品</span>
                </Link>
              }
              {
                ![3, 4].includes(record.showStatus) && <Link
                  onClick={() => {
                    const text = record.status === 3 ? '上' : '下'
                    BoreasDialog({
                      title: `活动${text}线确认`,
                      content: `确认${text}线【${record.name}】吗？`,
                      onOk: () => {
                        request(`mtop://mtop.ele.newretail.play.platform.offline.act`,
                          {
                            body: {
                              playIds: JSON.stringify([record.playId])
                            }
                          }).then((res) => {
                          if (res !== null) {
                            Message.success('操作成功')
                            updateList(searchParmas)
                          }
                          reload()
                        })
                      }
                    })
                  }}
                  className="pl"
                >
                  <span className="pr">{record.status === 3 ? '上线' : '下线'}</span>
                </Link>
              }
            </React.Fragment>)}
            <Link to={{
              pathname: `/assemble/edit`,
              search: `?id=${record.instanceId}`
            }} className="pl">
              <span className="pr">复制</span>
            </Link>
          </div>
        )
      }
    }
  ]

  return (
    <div className="right-content" style={{ paddingTop: 10 }}>
      <div className="newplan-title">
        <Header title="活动管理" />
      </div>
      <div className="bg-fff crm-list-wrap" style={{ padding: '20px' }}>
        <Filter resetType="reset" uiSource={filterUISource} onEvent={handleEvent} {...props.filterProps} />
        <p className="colonel-btn"><Button onClick={() => {
          props.history.push('/assemble/create')
        }} type="primary">+ 创建活动</Button></p>
        <Table
          className="boreas-table"
          total={total}
          dataSource={dataSource}
          columns={columns}
          hasBorder={false}
          defaultCurrent={currentPage}
          pageSize={pageSize}
          changePageSize={(val) => {
            setPageSize(val)
          }}
          search={({ pageNum }) => {
            setCurrentPage(pageNum)
          }}
          emptyContent="请添加活动~"
        />
      </div>
    </div>
  )
}


@filterDataSource({
  action: {
    url: `mtop://activity/list`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, ...o } = json

      return {
        ...o,
        pageNum
      }
    },
    mapResponse: (json, { props, query }) => {
      return {
        total: json.total,
        data: json.data || []
      }
    }
  }
})

class List extends Component {
  changeStatus = (record, type, reload) => {
    request(
      {
        url: `assemble://activity/status`,
        method: 'POST'
      },
      {
        body: {
          status: type,
          id: record.id
        }
      }
    ).then(() => {
      Message.success('操作成功')
      reload()
    })
  }

  handleChange = (record, reload) => {
    const text = record.status === 3 ? '上' : '下'
    BoreasDialog({
      title: `活动${text}线确认`,
      content: `确认${text}线【${record.name}】吗？`,
      onOk: () => this.changeStatus(record, reload)
    })
  }

  render() {
    return <div></div>
  }
}

