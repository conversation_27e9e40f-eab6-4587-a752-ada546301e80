.activityDetail {

  .detail-wrap {
    margin-bottom: 10px;
    background: white;
  }

  .detail-head {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 15px;

    .detail-img {
      width: 20px;
      height: 20px;
      margin-right: 5px;
      vertical-align: text-top;
    }

    p {
      margin: 6px 0 0;
      padding: 0;
      font-size: 14px;
      line-height: 14px;
      color: #99A0AD;
    }

    .detail-title {
      font-size: 20px;
      line-height: 20px;
      margin: 0;
      padding: 0;
    }
  }

  padding: 0 20px;

  .pin {
    background: #f19938;
    color: white;
    padding: 2px;
    width: 20px;
    height: 20px;
    font-size: 14px;
    text-align: center;
    border-radius: 3px;
    margin-right: 4px;
  }

  .next-tabs {
    .next-tabs-bar {
      .next-tabs-nav-container {
        margin-left: 7px;
      }
    }
  }

  // 定制tab切换样式
  .next-tabs-tab {
    margin: 0 12px;
  }

  .next-tabs.next-medium .next-tabs-tab-inner {
    font-size: 16px;
    padding: 14px 0;
  }
}
