/**
 * 玩法配置-列表
 */
/* eslint-disable radix */

import React, { Component, useState, useEffect, useMemo } from 'react'
import { Dialog, Input, Form, Message, Button, Field, Select, DatePicker, Radio } from '@alifd/next'
import { request } from '@/packages/request'

import fusionInputFix from '@/utils/fusionInputFix'

import { Filter, Table } from '@/packages/table/FilterTable'
import Header from '@/components/header'
import parseUrl from '@/packages/request/parseUrl'
import moment from 'moment';
import './colonelList.less'

const { RangePicker } = DatePicker;

const { Option } = Select;
const FormItem = Form.Item;
const XInput = fusionInputFix()(Input)
const statusList = {
  1: "上线中",
  [-1]: "已下线"
}

const formItemLayout = {
  labelCol: {
    fixedSpan: 6
  },
  wrapperCol: {
    span: 18
  }
}

export default function () {
  let [list, setList] = useState([])
  let [selectList, setSelectList] = useState([])
  let [currentPage, setCurrentPage] = useState(0)
  let [pageSize, setPageSize] = useState(10)
  let [total, setTotal] = useState(0)
  let [visible, setVisible] = useState(false)
  let [visibleZD, setVisibleZD] = useState(false)
  let [insertData, setInsertData] = useState({})
  let [storeList, setStoreList] = useState([])
  let [billStatus, setBillStatus] = useState([]);
  let [selectZDDate, setSelectZDDate] = useState([null, null]);
  let [searchParmas, setSearchParmas] = useState(parseUrl(window.location.href).query || {
    userId: null,
    phone: null,
    queryState: null,
    ownerType: 0,
    creator: null,
    storeId: null
  })

  let [typeList, setTypeList] = useState([]);

  function updateList(searchParmas) {
    setList([]);
    setTotal(0);
    request('mtop://mtop.ele.newretail.play.platform.guser.query', {
      query: Object.assign({
        currentPage,
        pageSize,
        userId: searchParmas.userId !== '' ? searchParmas.userId : null,
        phone: searchParmas.phone !== '' ? searchParmas.phone : null,
        queryState: parseInt(searchParmas.queryState),
        ownerType: searchParmas.ownerType !== 0 ? searchParmas.ownerType : null,
        creator: searchParmas.creator !== '' ? searchParmas.creator : null,
        storeId: searchParmas.storeId !== '' ? searchParmas.storeId : null
      })
    }).then(res => {
      let data = res.data.data;
      setList(data.groupUserDTOList)
      setTotal(data.total)
    })
  }

  const handleEvent = (e, values, field) => {
    console.log(values);
    if (e === 'reset') {
      let newParams = {
        userId: null,
        phone: null,
        queryState: null,
        ownerType: 0,
        creator: null,
        storeId: null
      }
      setSearchParmas(newParams)
      setSelectList([]);
      setCurrentPage(0)
      updateList(newParams);
    } else if (e === 'query') {
      setSelectList([]);
      setCurrentPage(0)
      updateList(searchParmas);
    } else if (e === 'change') {
      setSearchParmas(val => {
        val[values] = field;
        return val;
      })
    }
  }
  useEffect(() => {
    request('mtop://mtop.ele.newretail.play.platform.owner.type.query').then(res => {
      setTypeList(res.data.data.map(item => {
        return {
          value: item.type,
          label: item.desc
        };
      }))
    })
    updateList(searchParmas);
  }, [pageSize, currentPage])

  const field = new Field(this);

  const filterUISource = {
    'x-component': 'Filter',
    labelAlign: 'top',
    children: [
      {
        defaultValue: searchParmas.userId,
        label: 'ele-ID:',
        name: 'userId',
        noArrow: true,
        placeholder: '请输入 ele-ID',
        'x-component': 'Input',
        'data-type': 'number'
      },
      {
        defaultValue: searchParmas.phone,
        label: '用户手机号:',
        name: 'phone',
        noArrow: true,
        placeholder: '请输入用户手机号',
        'x-component': 'Input',
        'data-type': 'number'
      },
      {
        defaultValue: searchParmas.status,
        label: '团长状态:',
        name: 'queryState',
        placeholder: '请选择团长状态',
        'x-component': 'Select',
        dataSource: [{
          value: '',
          label: '全部状态'
        }, ...Object.keys(statusList).map(x => ({
          value: x,
          label: statusList[x]
        }))]
      },
      {
        defaultValue: searchParmas.creator,
        label: '创建人:',
        name: 'creator',
        placeholder: '请输入创建人名称',
        'x-component': 'Input'
      },
      {
        defaultValue: searchParmas.storeId,
        label: '门店:',
        name: 'storeId',
        placeholder: '请输入门店名称／ID',
        'x-component': 'Select',
        showSearch: true,
        dataSource: storeList,
        hasClear: true,
        filterLocal: false,
        onSearch: (val) => {
          if (val === '') {
            setStoreList([])
          } else {
            let params = {
              query: {
                currentPage: 0,
                pageSize,
                // storeId: val,
                storeName: val
              }
            }
            if (val.match(/^\d{8,}$/)) {
              params = {
                query: {
                  currentPage: 0,
                  pageSize,
                  storeId: parseInt(val)
                }
              }
            }
            request('mtop://mtop.ele.newretail.play.platform.store.query', params).then(res => {
              let result = res.data.data
              setStoreList(result.storeDTOList.map(item => {
                return {
                  value: item.storeId,
                  label: item.storeName
                }
              }))
            })
          }
        }
      },
      {
        defaultValue: searchParmas.status,
        label: '团长类型:',
        name: 'ownerType',
        placeholder: '请选择团长类型',
        'x-component': 'Select',
        dataSource: [{
          value: 0,
          label: '全部类型'
        }, ...typeList]
      }
    ]
  }

  const columns = [
    {
      title: 'ele-ID',
      width: '100px',
      dataIndex: 'userId'
    },
    {
      title: '用户手机号',
      width: '150px',
      dataIndex: 'phone'
    },
    {
      title: '姓名',
      width: '100px',
      dataIndex: 'name'
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'gmtCreate'
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      width: '100px',
    },
    {
      title: '团长类型',
      dataIndex: 'ownerType',
      width: '130px',
      cell: value => {
        const match = typeList.find(item => {
          return item.value === value
        });
        if (match) {
          return match.label
        } else {
          return value;
        }
      }
    },
    {
      title: '门店',
      dataIndex: 'storeId',
      width: '200px',
      cell: value => {
        return <div>
          {
            JSON.parse(value).map(item => {
              return <span>{item.storeName}</span>
            })
          }</div>
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: '120px',
      cell: value => {
        const colors = {
          1: "#00CCAA",// 上线中
          [-1]: "#FF3000",// 已下线
        }
        return (
          <div className={`crm-status hasStatueIcon crm-status-${value}`}>
            <div className={'icon'} style={{ backgroundColor: colors[value] }}></div>
            <div>{statusList[value]}</div>
          </div>
        )
      }
    },
    {
      title: '账户余额',
      dataIndex: 'balance',
      width: '100px',
    },
    {
      title: '已提现',
      dataIndex: 'cashOut',
      width: '100px',
    },
    {
      title: '操作',
      dataIndex: 'ops',
      width: '140px',
      cell: (value, index, record) => {
        return (
          <div className="boreas-cell">
            <span onClick={() => {
              setVisibleZD(true)
              setSelectList([record.userId])
            }}>导出账单</span>
            <span
              onClick={() => {
                Dialog.confirm({
                  title: '提示',
                  content: '是否要' + (record.status === -1 ? '上线' : '下线') + '?',
                  onOk: () => {
                    let storeIds = JSON.parse(record.storeId).map(item => {
                      return item.storeId;
                    })
                    if (record.status === 1) {
                      request('mtop://mtop.ele.newretail.play.platform.guser.offline', {
                        query: {
                          userId: record.userId,
                          storeIds: JSON.stringify(storeIds),
                        }
                      }).then(res => {
                        Message.success('下线成功')
                        updateList(searchParmas);
                      })
                    } else {
                      request('mtop://mtop.ele.newretail.play.platform.guser.online', {
                        method: 'POST',
                        query: {
                          userId: record.userId,
                          storeIds: JSON.stringify(storeIds),
                        }
                      }).then(res => {
                        Message.success('上线成功')
                        updateList(searchParmas);
                      })
                    }
                  }
                });
              }}
              className="pl"
            >
              {record.status === -1 ? '上线' : '下线'}
            </span>
          </div>
        )
      }
    }
  ]
  const billStatusList = useMemo(() => {
    return [
      { value: 1, label: '顾客待付款' },
      { value: 2, label: '取消订单，分佣失败' },
      { value: 3, label: '顾客已付款' },
      { value: 4, label: '已退款，分佣失败' },
      { value: 5, label: '结算中，等待分佣' },
      { value: 6, label: '已分佣' }
    ]
  }, [])

  return (
    <div className="right-content" style={{ paddingTop: 10 }}>
      <div className="newplan-title">
        <Header title="团长管理" />
      </div>
      <div className="bg-fff crm-list-wrap" style={{ padding: '20px' }}>
        <Filter resetType="reset" uiSource={filterUISource} onEvent={handleEvent} />
        <p className="colonel-btn"><Button onClick={() => {
          setVisible(true)
        }} type="primary">+ 新增团长</Button></p>
        <Message
          title={
            <p className="colonel-tips">已选择 <i> {selectList.length} </i> 项
              <Button disabled={selectList.length === 0} type="primary" text onClick={() => {
                setVisibleZD(true)
              }}>批量导出账单</Button>
            </p>}
          type="notice"
        />
        <Table
          className="boreas-table"
          primaryKey="userId"
          total={total}
          dataSource={list}
          defaultCurrent={currentPage}
          pageSize={pageSize}
          search={({ pageNum }) => {
            setCurrentPage(pageNum)
          }}
          changePageSize={(val) => {
            setPageSize(val)
          }}
          rowSelection={{
            selectedRowKeys: selectList,
            onChange: (e) => {
              setSelectList(e)
            },
            getProps: (record) => {
              return {
                disabled: record.status === 3
              };
            }
          }}
          columns={columns}
          hasBorder={false}
          emptyContent="请添加~"
        />
        <Dialog
          title="新增团长"
          className="colonel-dialog"
          visible={visible}
          onOk={() => {
            if (insertData.phone && !insertData.phone.match(/^\d{11}$/)) {
              Message.error('请输入正确的用户手机号');
            } else if (insertData.userId === '') {
              Message.error('请匹配正确的ele-ID');
            } else if (!insertData.name) {
              Message.error('请输入用户姓名');
            } else if (!insertData.storeId) {
              Message.error('请输入门店');
            } else if (!insertData.ownerType) {
              Message.error('请选择团长类型');
            } else {
              request('mtop://mtop.ele.newretail.play.platform.guser.add', {
                method: 'POST',
                query: insertData
              }).then(res => {
                if (res.data.errorCode === '0') {
                  Message.success('新增成功')
                  setInsertData({})
                  updateList(searchParmas);
                  setVisible(false)
                }
              })
            }
          }}
          onCancel={() => {
            setVisible(false)
          }}
          onClose={() => {
            setVisible(false)
          }}>
          <Form labelAlign="left" {...formItemLayout}>
            <FormItem label="用户手机号:" format="tel" required>
              <XInput
                type="Number"
                className="phoneInput"
                placeholder="请输入用户手机号"
                {...field.init('sourceImportId', {
                  rules: {
                    required: true,
                    message: '请填写用户手机号'
                  }
                })}
                onChange={(val) => {
                  if (val.length === 11) {
                    request('mtop://mtop.ele.newretail.play.platform.phone.user.query', {
                      query: {
                        phone: val
                      }
                    }).then(res => {
                      setInsertData(Object.assign({}, insertData, {
                        phone: val,
                        userId: (res && res.data) ? res.data.data : ''
                      }))
                    })
                  } else {
                    setInsertData(Object.assign({}, insertData, {
                      phone: val,
                      userId: ''
                    }))
                  }
                }}
              />
            </FormItem>
            <FormItem label="ele-ID:">
              <p>{insertData.userId}</p>
            </FormItem>
            <FormItem label="用户姓名:" required>
              <XInput
                placeholder="请输入用户姓名"
                {...field.init('sourceImportId', {
                  rules: {
                    required: true,
                    message: '请填写用户姓名'
                  }
                })}
                onChange={(val) => {
                  setInsertData(data => {
                    data.name = val;
                    return data;
                  })
                }}
              />
            </FormItem>
            <FormItem label="门店:" required>
              <Select
                showSearch
                placeholder="请输入门店"
                filterLocal={false}
                dataSource={storeList}
                onSearch={(val) => {
                  if (val === '') {
                    setStoreList([])
                  } else {
                    let params = {
                      query: {
                        currentPage: 0,
                        pageSize,
                        // storeId: val,
                        storeName: val
                      }
                    }
                    if (val.match(/^\d{8,}$/)) {
                      params = {
                        query: {
                          currentPage: 0,
                          pageSize,
                          storeId: parseInt(val)
                        }
                      }
                    }
                    request('mtop://mtop.ele.newretail.play.platform.store.query', params).then(res => {
                      let result = res.data.data
                      setStoreList(result.storeDTOList.map(item => {
                        return {
                          value: item.storeId,
                          label: item.storeName
                        }
                      }))
                    })
                  }
                }}
                onChange={(val) => {
                  let find = storeList.find(item => {
                    return item.value === val;
                  })
                  setInsertData(data => {
                    data.storeId = val;
                    data.storeName = find.label;
                    return data;
                  })
                }}
                style={{ width: 200 }} />
            </FormItem>
            <FormItem label="团长类型" required>
              <Radio.Group dataSource={typeList} onChange={(val) => {
                setInsertData(data => {
                  data.ownerType = val;
                  return data;
                })
              }} />
            </FormItem>
          </Form>
        </Dialog>
        <Dialog
          title="导出账单"
          okText="确认导出"
          footerActions={['cancel', 'ok']}
          okProps={{ children: '确认导出' }}
          onOk={() => {
            if (!selectZDDate[0] || !selectZDDate[1]) {
              Message.error('请选择时间');
              return;
            }
            if (selectZDDate[1].diff(selectZDDate[0], 'day') > 30) {
              Message.error('选择天数不能大于31天');
              return;
            }
            request('mtop://mtop.ele.newretail.play.platform.bill.export', {
              query: {
                userIds: JSON.stringify(selectList),
                gmtStart: selectZDDate[0].format('YYYY-MM-DD 00:00:00'),
                gmtEnd: selectZDDate[1].format('YYYY-MM-DD 23:59:59'),
                billStatusList: JSON.stringify(billStatus)
              }
            }).then(res => {
              const { errorCode, errorMsg } = res.data;
              if (errorCode === '0') {
                Message.success('导出成功，账单会通过邮件发送到您的邮箱');
                setSelectZDDate([])
                setBillStatus([]);
                setTimeout(() => {
                  setVisibleZD(false);
                }, 500);
              }
            })
          }}
          onClose={() => {
            setVisibleZD(false)
          }}
          onCancel={() => {
            setVisibleZD(false)
          }}
          visible={visibleZD}
        >
          <Form labelAlign="left" {...formItemLayout}>
            <FormItem label="账单创建时间">
              <RangePicker
                style={{ width: 280 }}
                defaultValue={selectZDDate}
                onChange={(values) => {
                  setSelectZDDate(values);
                  console.log(values)
                }}
                disabledDate={(value) => {
                  console.log(value)
                  console.log(value.valueOf())
                  return value.valueOf() >= (new Date()).getTime();
                }}
              />
            </FormItem>
            <FormItem label="分佣状态">
              <Select
                mode="tag"
                defaultValue={billStatus}
                onChange={(value) => {
                  setBillStatus(value)
                }}
                dataSource={billStatusList}
                style={{ width: 280 }}
                allowClear
              />
            </FormItem>
          </Form>
        </Dialog>
      </div>
    </div>
  )
}
