.success-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-tip {
  font-weight: bold;
  margin: 10px 0 50px;
  font-size: 16px;
}

.colonel-btn {
  text-align: right;
  margin: 10px 0 10px;
}

.colonel-tips {
  i {
    font-style: normal;
    color: red;
  }

  span {
    margin-left: 50px;
  }
}

.boreas-table {
  margin-top: 10px;
}

.colonel-dialog {
  width: 600px;
}

.task-toolbar {
  margin-top: 10px;
  margin-bottom: 8px;
}

.task-state-success {
  color: #5CB87A;
}

.task-state-failed {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #ff3000;
}

.hasStatueIcon {
  display: flex;
  flex-direction: row;
  align-items: center;

  .icon {
    width: 6px;
    height: 6px;
    flex-shrink: 0;
    margin-right: 4px;
    border-radius: 3px;
  }
}
