import { request } from '@/packages/request'
import React, { useMemo, useState, useEffect } from 'react'
import { withRouter } from 'react-router-dom'
import { Table, Form, Input, Button, Message, Dialog } from '@alifd/next'
import { ItemInfoBuilder } from '@ali/boreas2/lib/marketing/Goods/index'
import './Detail.less'
import { getFormJson } from "../api";
import { buildEnv } from '@/packages/env'

function Upload({ id, onSuccess }) {
  const [formJson, updateFormJson] = useState({ fields: [] });

  useEffect(() => {
    getFormJson({
      "uuid": "7fFYyMUo55HahFUmyonWAv",
      "workspaceUuid": "2VYRqu7kbaj9C2rZsx1tWf"
    }).then(({ data: { data } }) => updateFormJson(data)).catch(err => {
      Message.error(err);
    })
  }, [])
  if (formJson.fields.length === 0) {
    return null;
  }
  const builder = new ItemInfoBuilder(formJson);
  const ItemImport = builder.buildImporter() // 导表组件
  return <div className="goodsImport">
    <ItemImport
      env={{
        'prod': 'prod',
        'ppe': 'pre',
        'pre': 'pre',
        'daily': 'daily',
        'local': 'daily'
      }[buildEnv()]}
      onChange={(v) => {
        request(
          {
            url: `mtop://mtop.ele.newretail.play.platform.task.submit`,
            method: 'POST'
          },
          {
            body: {
              taskProjectType: 8001,
              uploadKey: v[0].response.name,
              fileName: v[0].name,
              instanceId: id
            }
          }
        ).then(res => {
          onSuccess(res.data.data)
          Message.success('添加成功')
        });
      }}
    />
  </div>
}

let list = [];

const Detail = ({ id, history }) => {
  const [formJson, updateFormJson] = useState({ fields: [] })
  const [visible, setVisible] = useState(false)
  useEffect(() => {
    getFormJson({
      "uuid": "7fFYyMUo55HahFUmyonWAv",
      "workspaceUuid": "2VYRqu7kbaj9C2rZsx1tWf"
    }).then(({ data: { data } }) => {
      updateFormJson(data)
    }).catch(err => {
      Message.error(err);
    })
  }, [])

  const { ItemInfo } = useMemo(() => {
    if (formJson.fields.length === 0) {
      return { ItemInfo: null }
    } else {
      const builder = new ItemInfoBuilder(formJson)
      const ItemInfo = builder.build()
      return { ItemInfo }
    }
  }, [formJson])

  if (formJson.fields.length === 0) {
    return null
  }

  return (<div className="assemble_detail">
      <ItemInfo
        instanceId={id} // 玩法 id
        onImport={() => {
          setVisible(true)
        }}
        onViewAsyncTasks={() => {
          history.push('/assemble/tasks')
        }}
        participantService={{
          query: (instanceId, query) => {
            console.log(instanceId, query);
            if (query.statusList && query.statusList.length > 0) {
              query.statusList = JSON.stringify(query.statusList)
            } else {
              delete query.statusList
            }
            return request(`mtop://mtop.ele.newretail.play.platform.query.detail`, {
              query: {
                instanceId,
                ...query
              }
            }).then(res => {
              if (res.data.errorCode === '0') {
                const { data } = res.data;
                console.log({
                  total: data.total,
                  participants: data.participants
                });
                list = data.participants;
                return {
                  total: data.total,
                  participants: data.participants
                }
              } else {
                list = [];
                return {
                  total: 0,
                  participants: []
                }
              }
            })
          },
          modify: (instanceId, data) => {
            console.log(data);
            console.log(data);
            console.log(list);
            console.log(list);
            let self = list.find(item => {
              return item.participantId === data[0].participantId
            });
            console.log(self);
            console.log(Object.assign(self.partInfo, data[0]));
            return request(`mtop://mtop.ele.newretail.play.platform.update.detail`, {
              query: {
                cmdJson: JSON.stringify({
                  instanceId,
                  participantModifyList: data
                })
              }
            }).then(res => {
              if (res.data.errorCode === '0') {
                Message.success('修改成功')
              }
            })
          }
        }}
      />
      {
        visible && <Dialog
          footer={<div style={{ display: 'none' }}></div>}
          title="添加商品"
          visible={visible}
          onCancel={() => {
            setVisible(false)
          }}
          onClose={() => {
            setVisible(false)
          }}>
          <div style={{ width: '500px', height: '177px' }}>
            <Upload id={id} onSuccess={(res) => {
              setTimeout(() => {
                setVisible(false)
              }, 100)
            }} />
          </div>
        </Dialog>
      }
    </div>
  )
}

export default withRouter(Detail)
