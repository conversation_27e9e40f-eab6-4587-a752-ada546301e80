import React from 'react'
import { But<PERSON>, Message, Icon } from '@alifd/next'

const SuccessComp = props => {
  console.log('SuccessCompo', props)
  const { history, formData } = props

  let { data } = formData.data;
  return (
    <div className="success-wrap">
      <Icon type="success-filling" size="xxxl" style={{ color: '#FF7C4D', marginTop: '50px' }} />
      <span className="success-tip">创建成功</span>
      <p>
        <Button onClick={() => {
          history.push('/assemble')
        }} style={{ marginRight: 10 }}>活动管理</Button>
        {
          (data.object && data.object.metas) && <Button
            onClick={() => {
              history.push(`/assemble/detail?id=${formData.data.data.instanceId}&key=info&playId=${data.object.metas.playId}`)
            }}
            type="primary"
          >活动详情</Button>
        }
      </p>
    </div>
  )
}


export default SuccessComp
