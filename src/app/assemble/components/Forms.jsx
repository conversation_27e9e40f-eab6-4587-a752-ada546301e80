import React, { useState, useEffect, useCallback } from 'react'
import { Select, Button, Message } from '@alifd/next'
import ActivityForm from '@ali/boreas2/lib/marketing/ActivityForm'
import { request } from '@/packages/request'
import moment from 'moment'
import './Forms.less'
import { getUserInfo, getForm<PERSON>son, postFormData, getInstance<PERSON>son } from '../api';


const defaultUserInfo = {
  workid: '',
  lastName: '',
  login: ''
}

const Forms = ({ onCancel, onCheck, view = "edit", history, id }) => {
  const list = history && new URLSearchParams(history.location.search)
  if (list) {
    if (list.get('id')) {
      id = list.get('id')
    }
  }
  const [userInfo, serUserInfo] = useState(defaultUserInfo);
  const [formJson, updateFormJson] = useState({ fields: [] });
  const [saveLoading, setSaveLoading] = useState(false);
  const [instance<PERSON><PERSON>, updateInst<PERSON><PERSON><PERSON>] = useState({
    fields: {}
  })

  const { workid, lastName, login } = userInfo;

  useEffect(() => {
    if (view === 'edit') {
      request({
        url: `mtop://mtop.ele.newretail.play.platform.auth`,
        method: 'POST'
      }).then(res => {
        if (res.data.data) {
          const { userDTO } = res.data.data;
          serUserInfo({
            workid: userDTO.id,
            lastName: userDTO.name,
            login: userDTO.prefix
          })
        }
      })
    }
    getFormJson({
      "uuid": "7fFYyMUo55HahFUmyonWAv",
      "workspaceUuid": "2VYRqu7kbaj9C2rZsx1tWf"
    }).then(({ data: { data } }) => updateFormJson(data)).catch(err => {
      Message.error(err);
    })
    if (view === 'detail') {
      getInstanceJson(id).then(({ data }) => {
        updateInstanceJson({ fields: data.data.instance.fields })
        if (data.data.instance.fields.creator) {
          let user = JSON.parse(data.data.instance.fields.creator)
          console.log(user);
          serUserInfo({
            workid: user.id,
            lastName: user.name,
            login: user.prefix
          })
        }
      })
    } else if (view === 'edit' && id !== undefined) {
      // 复制
      getInstanceJson(id).then(({ data }) => {
        updateInstanceJson({ fields: data.data.instance.fields })
      })
    }
  }, [])

  const onNext = useCallback(async (formData, formJson, onCheck) => {
    setSaveLoading(true)
    const { data, type } = formData;
    try {
      postFormData({
        data,
        componentModelUuid: formJson.uuid,
        type,
        workspaceUuid: formJson.workspaceUuid,
        instanceId: undefined
      }).then(res => {
        if (res !== null) {
          Message.success('创建成功！');
          onCheck(res)
        }
        setSaveLoading(false)
      })
    } catch (error) {
      Message.error(error);
      onCheck()
      setSaveLoading(false)
    }
  }, [])

  if (!formJson.fields.length) {
    return null;
  }
  if (view === 'detail' && JSON.stringify(instanceJson.fields) === '{}') {
    return null;
  }
  if (view === 'edit' && id !== undefined && JSON.stringify(instanceJson.fields) === '{}') {
    return null;
  }
  if (!workid) {
    return null;
  }
  return <ActivityForm
    className={'activityCreateForm'}
    user={{ id: workid, name: lastName, prefix: login }}
    model={formJson}
    view={view}
    instance={instanceJson}
    appendRow={(ctx) => {
      return view === 'detail' ? null : (
        <div>
          <Button
            onClick={onCancel}
            style={{ marginRight: '20px' }}
          >
            取消
          </Button>
          <Button
            type='primary'
            loading={saveLoading}
            disabled={saveLoading}
            onClick={async () => {
              try {
                const formData = await ctx.validate();
                onNext(formData, formJson, onCheck)
              } catch (error) {
                if (Array.isArray(error)) {
                  Message.error(error[0].message);
                }
              }
            }}
          >
            下一步
          </Button>
        </div>
      )
    }}
  />
}

export default Forms
