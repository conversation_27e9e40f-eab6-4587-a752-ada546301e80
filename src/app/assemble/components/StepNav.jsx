import React from 'react'
import { Step } from '@alifd/next'
import './StepNav.less'

const StepNav = ({ data, state, stepAction = {}, history }) => {
  const { current, formData } = state
  const Component = data[current].component
  return (<div className="wrapper StepNav">
    <Step className="step" current={current} shape="circle">
      {data && data.map(({ title }, i) => <Step.Item key={title} title={title} disabled={current < i} />)}
    </Step>
    <div className="crm-wrap">
      {Component ? <Component {...stepAction} formData={formData} history={history} /> : null}
    </div>
  </div>)
}

export default StepNav
