import React, { useState, useEffect } from 'react'
import { request } from '@/packages/request'
import { Button, Message, Icon, Upload } from '@alifd/next'
import './GoodsImporter.less'
import { ItemInfoBuilder } from '@ali/boreas2/lib/marketing/Goods/index'
import { getFormJson } from "../api";
import { buildEnv } from '@/packages/env'

const GoodsImporter = ({ onCancel, goBack, onCheck, formData }) => {
  const [value, setValue] = useState([])
  const [formJson, updateFormJson] = useState({ fields: [] });

  useEffect(() => {
    getFormJson({
      "uuid": "7fFYyMUo55HahFUmyonWAv",
      "workspaceUuid": "2VYRqu7kbaj9C2rZsx1tWf"
    }).then(({ data: { data } }) => updateFormJson(data)).catch(err => {
      Message.error(err);
    })
  }, [])
  if (formJson.fields.length === 0) {
    return null;
  }
  const builder = new ItemInfoBuilder(formJson);
  const ItemImport = builder.buildImporter() // 导表组件
  return <div className="goodsImport">
    <ItemImport
      env={{
        'prod': 'prod',
        'ppe': 'pre',
        'pre': 'pre',
        'daily': 'daily',
        'local': 'daily'
      }[buildEnv()]}
      value={value}
      onChange={value => {
        console.log('onChange', value)
        setValue(value)
      }}
    />
    <p style={{ marginTop: 15 }}>
      <Button onClick={onCancel} style={{ marginRight: 10 }}>取消</Button>
      <Button onClick={goBack} style={{ marginRight: 10 }}>上一步</Button>
      <Button onClick={async () => {
        const files = value.map(v => {
          return {
            taskProjectType: 8001,
            uploadKey: v.response.name,
            fileName: v.name,
            instanceId: formData.data.data.instanceId
          }
        })
        // TODO: 过滤异常状态的数据，再上传
        await Promise.all(files.map(async file => {
          try {
            await request({
              url: `mtop://mtop.ele.newretail.play.platform.task.submit`,
              method: 'POST'
            }, {
              body: file
            })
          } catch (e) {
            console.error(e)
          }
        }))
        onCheck({
          ...formData,
          files
        });
      }} type="primary">提交</Button>
    </p>
  </div>
}

export default GoodsImporter
