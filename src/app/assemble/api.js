import { request } from '@/packages/request'
import { config } from '../../api/config'
import { goToLogin, goToApply } from '@/api/auth';

// 获取用户信息
export const getUserInfo = () => {
  return fetch(config.KL_ROOT + '/user/info', {
    credentials: 'include'
  })
    .then((response) => {
      return response.json();
    })
    .then((myJson) => {
      return myJson.data;
    })
    .catch(() => {
      setTimeout(() => {
        return goToLogin(encodeURI(window.location.href));
      }, 1000);
    });
}

// 营销组件api
export const getFormJson = (params) => request(
  {
    url: `mtop://mtop.ele.newretail.play.platform.create.template`,
    method: 'POST'
  },
  {
    body: { ...params }
  }
);

// 提交表单信息
// export const postFormData = (params) => request(
//   {
//     url: `mtop://mtop.ele.newretail.play.platform.create.act`,
//     method: 'POST'
//   },
//   {
//     body: { ...params }
//   }
// )
export const postFormData = ({ data, ...reset }) => request('mtop://mtop.ele.newretail.play.platform.create.act', {
  query: {
    data: JSON.stringify(data),
    ...reset
  }
})

// 获取表单信息
export const getInstanceJson = id => request(`mtop://mtop.ele.newretail.play.platform.activity.info.query`, {
  query: {
    id
  }
})
