import React, { Component, Suspense, lazy } from 'react'
import Routers from '@/packages/routers'

import activityList from './activityList'
import activityCreate from './activityCreate'
import activityDetail from './activityDetail'
import colonelList from './colonelList'
import taskList from './taskList'

function toPath(file) {
  const prts = file.split('.')
  prts.pop()
  return '/' + prts.filter(x => x).join('/')

}

const routerList = [
  {
    subRouterPath: '/assemble',
    routerItems: [
      {
        path: '/',
        component: activityList,
        breadcrumbName: '活动管理列表'
      },
      {
        path: '/create',
        component: activityCreate,
        breadcrumbName: '活动创建'
      },
      {
        path: '/edit',
        component: activityCreate,
        breadcrumbName: '活动配置编辑'
      },
      {
        path: '/detail',
        component: activityDetail,
        breadcrumbName: '活动配置详情'
      },
      {
        path: '/colonel',
        component: colonelList,
        breadcrumbName: '团长列表'
      },
      {
        path: '/tasks',
        component: taskList,
        breadcrumbName: '异步任务列表'
      }
    ]
  }
]

const breadcrumbMap = {
  '/assemble': '活动管理',
  '/assemble/create': '活动创建',
  '/assemble/edit': '活动配置编辑',
  '/assemble/detail': '活动详情',
  '/assemble/tasks': '查看异步任务'
}

function App() {
  return <Routers routerList={routerList} redirectPath="/assemble" breadcrumbMap={breadcrumbMap} />
}
export default App
