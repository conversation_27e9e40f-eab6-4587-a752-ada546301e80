import _ from 'lodash'
import moment from 'moment'
import React, { useEffect, useReducer } from 'react'
import { Link } from 'react-router-dom'
import { request } from '@/packages/request'
import { Button, Table, Pagination, Balloon, Icon } from '@alifd/next'
import { buildEnv } from '@/packages/env'

function getTasks(current, pageSize) {
  return request('mtop://mtop.ele.newretail.play.platform.task.query', {
    query: {
      currentPage: current,
      pageSize
    }
  })
}

const Column = Table.Column

const columns = [
  <Column key="playId" title="玩法ID" dataIndex="playId" />,
  <Column key="name" title="玩法名称" dataIndex="name" />,
  <Column key="taskId" title="任务ID" dataIndex="taskId" />,
  <Column key="taskTypeName" title="任务类型" dataIndex="taskTypeName" />,
  <Column key="fileName" title="文件名称" dataIndex="fileName" />,
  <Column key="createTime" title="创建时间" dataIndex="createTime" cell={value => {
    return (<div>{moment(value).format('YYYY-MM-DD HH:mm:ss')}</div>)
  }} />,
  <Column key="status" title="任务状态" dataIndex="process" cell={(value, index, record) => {
    const text = ['', '进行中', '进行中', '成功', '失败']
    const states = ['', 'processing', 'processing', 'success', 'failed']
    return <div className={`task-state-${states[+value]}`}>
      <div>{text[+value]}</div>
      {
        (value === 4 && record.reason) &&
        <Balloon
          trigger={<Icon type="help" size={14} style={{ marginLeft: 5 }} />}
          align="b"
          closable={false}
        >
          <div>{record.reason}</div>
        </Balloon>
      }
    </div>
  }} />,
  <Column key="detail" title="查看详情" dataIndex="detail" cell={(value, index, record) => {
    return (record.process === 4 && record.downloadKey) && <a
      href={({
        prod: 'https://play-platform.alibaba-inc.com',
        ppe: 'http://pre-play-platform.alibaba-inc.com',
        pre: 'http://pre-play-platform.alibaba-inc.com',
        local: 'http://nrmkt-play-platform.eleme.test',
        daily: 'http://nrmkt-play-platform.eleme.test'
      }[buildEnv()]) + '/getFailFile?taskId=' + record.taskId}>下载</a>
  }} />
]

const initialState = {
  current: 1,
  pageSize: 10,
  tasks: [],
  total: 0
}

function reducer(state, action) {
  const { type, payload } = action
  switch (type) {
    case 'init':
      return {
        ...state,
        ...payload
      }
    case 'changeTasks':
      return {
        ...state,
        ...payload
      }
    default:
      return state
  }
}

function handleChangePagination(pagination, dispatch) {
  getTasks(pagination.current, pagination.pageSize).then(res => {
    dispatch({
      type: 'changeTasks',
      payload: {
        ...pagination,
        total: _.get(res.data, 'data.total'),
        tasks: _.get(res.data, 'data.taskProcessVOList')
      }
    })
  })
}

const TaskList = () => {
  const [state, dispatch] = useReducer(reducer, initialState)

  useEffect(() => {
    getTasks(1, 10).then(res => {
      dispatch({
        type: 'init',
        payload: {
          current: 1,
          pageSize: 10,
          total: _.get(res.data, 'data.total'),
          tasks: _.get(res.data, 'data.taskProcessVOList')
        }
      })
    })
  }, [])


  return (
    <div className="right-content">
      <div className="task-toolbar">
        <Button
          type="primary"
          onClick={() => handleChangePagination(state, dispatch)}>
          刷新列表
        </Button>
      </div>
      <Table
        className="boreas-table"
        total={state.total}
        dataSource={state.tasks}
        hasBorder={false}>
        {columns}
      </Table>
      {state.total && state.tasks.length ? (
        <Pagination
          className="boreas-pagination"
          total={state.total}
          totalRender={() => `共${state.total}条记录`}
          defaultCurrent={1}
          current={state.current}
          pageSizeSelector={'dropdown'}
          pageSizePosition="end"
          pageSizeList={[10, 20, 50, 100]}
          shape={'arrow-only'}
          onPageSizeChange={pageSize => {
            const current = Math.ceil(state.current * state.pageSize / pageSize)
            handleChangePagination({ current, pageSize }, dispatch)
          }}
          pageSize={+state.pageSize}
          onChange={current => {
            handleChangePagination({ current, pageSize: state.pageSize }, dispatch)
          }}
        />
      ) : null}
    </div>
  )
}

export default TaskList
