import React, { useEffect, useState, useReducer } from "react";
import { Dialog } from '@alifd/next'
import { StepNav, Forms, GoodsImporter, SuccessComp } from './components'
import './index.scss'

const STEP_NAME = [
  { title: '活动信息', component: Forms },
  { title: '添加商品', component: GoodsImporter },
  { title: '创建成功', component: SuccessComp }
]
const initialState = { current: 0, formData: {} };

function reducer(state, action) {
  switch (action.type) {
    case 'prev':
      return { ...state, current: state.current - 1 };
    case 'next':
      return { ...state, current: state.current + 1 };
    case 'formData':
      return { ...state, formData: action.value };
    default:
      throw new Error();
  }
}

const breads = [
  {
    path: '/list',
    breadcrumbName: '玩法配置'
  }
]

const ActivityCreate = (props) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const onCancel = () => {
    Dialog.confirm({
      title: '取消玩法',
      content: '信息未保存，是否确认取消？',
      onOk: () => props.history.push('/assemble'),
      onCancel: () => console.log('cancel')
    });
  }

  const onCheck = (data) => {
    dispatch({ type: 'formData', value: { ...data } })
    dispatch({ type: 'next' })
  }

  const goBack = () => dispatch({ type: 'prev' })

  return <div style={{ backgroundColor: 'white', padding: '10px 0 20px 0', margin: '0 20px' }}>
    <StepNav data={STEP_NAME} state={state} stepAction={{ onCancel, onCheck, goBack }} history={props.history} />
  </div>
}

export default ActivityCreate
