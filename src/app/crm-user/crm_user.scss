:global {
  .next-dialog .next-btn {
    width: auto !important;
  }
}

.set-title{
  font-size: 20px;
  color: #333333;
  letter-spacing: 0;
  text-align: left;
  line-height: 20px;
}


.crm-status {
  font-size: 14px;
  color: #666666;
  position: relative;
  padding-left: 15px;
  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    display: inline-block;
    width: 0;
    height: 0;
    border: 3px solid #00ccaa;
    border-radius: 50%;
    margin-top: -3px;
  }
  &-3, &-4, &-5 {
    &:before {
      display: inline-block;
      width: 0;
      height: 0;
      border: 3px solid #cccccc;
      border-radius: 50%;
    }
  }
  &-0, &-1 {
    &:before {
      display: inline-block;
      width: 0;
      height: 0;
      border: 3px solid #ff0000;
      border-radius: 50%;
    }
  }
}

.card-line {
  display: flex;
  padding:16px;
  flex-wrap:wrap;
  width:100%;

}
.card-wrap{
  width:250px;
  height:80px;
  margin:16px;
  position: relative;
  border:1px solid #f1f2f4;
  text-align: center;
  display:flex;

  flex-direction: row;

  .card-lf-title{
    width:180px;
    text-align: center;
    vertical-align: 80px;
    padding-top:20px;
    padding-bottom:32px;
  }

  .card-rt-ops{
    border-left:1px solid #f1f2f4;
    width:70px;
    line-height: 20px;
    vertical-align: 80px;
    text-align: center;
    padding-top:24px;
    height:80px;
  }
}

.treasure-detail-block{
  margin-bottom: 8px;
}



.treasure-detail {
  .rights-wrap{
    border-top:1px solid #f1f2f4;
  }
  .newplan-title{
    padding-left:16px;
    padding-top:16px;
    margin-top:16px;
  }

.info-card{
  border: 1px solid #ebebeb;
  padding:0px;
  }
  .boreas-form-title {
    width: 900px;
    margin: 0 auto;
    margin:16px 0 0 16px;
    .next-form-item-label {
      text-align: left;
    }
  }
  &-header {
      margin-left:16px;
      padding:16px;
    span {
      color: #999999;
    }
  }
  &-block {
    width: 900px;
    margin: 0 auto;
    font-size: 14px;
    padding: 16px 44px 16px 44px;

    .next-table {
      width: 513px;
      border: 1px solid #ececec;
      // padding-top: 10px;
      display: inline-block;
    }
    span {
      color: #999999;
      padding-right: 11px;
      vertical-align: top;
    }
  }
}
.rights-card{
  border:none;
  border-top:1px solid #ebebeb;
  padding:32px 40px 32px 40px;


  .detail{
    // margin:32px 40px 32px 40px;
    .detail-row{
      border: 1px solid #ebebeb;
      border-top:none;
      &:nth-child(1){
        border-top:1px solid #ebebeb!important;
      }
     }
     .detail-row:nth-child(1){
      border-top:1px solid #ebebeb!important;
    }

     .lf-label{
       background: #fafafa;
       border-right: 1px solid #ebebeb;
       word-break: break-all;
       word-wrap: break-word;
       max-width: 140px;
       min-height: 60px;
       line-height: 40px;
       padding: 10px;
       box-sizing: border-box;
       white-space: pre-wrap;
     }
     .lr-content{
       word-break: break-all;
       word-wrap: break-word;
       min-height: 60px;
       line-height: 40px;
       padding: 10px;
       box-sizing: border-box;
       background: #fff;
       white-space: pre-wrap;
       .detail-img{
        width:100px;
        height:100px;
        margin:8px;
        display: inline;
      }
     }
  }

  .rights-title{
    color: #999;
    margin:8px;

    font-size:14px;

  }


}

.treasure-detail-content {
  position: relative;
  &-editor {
    position: absolute;
    right: 88px;
    top: 30px;
    font-size: 16px;
  }
}

.treasure-log-fitertable {
  width: 900px;
  margin: 0 auto;
  font-size: 16px;
  .next-table-header {
    height: 80px;
  }
  .next-table-row {
    height: 80px;
  }
}

.treasure-log {
  .boreas-table {
    width: 900px;
    margin: 0 auto;
  }
}

.setting-detail-content{
  position: relative;
  padding-top:16px;
  &-editor {
    position: absolute;
    right: 88px;
    top: 30px;
    font-size: 16px;
  }
  .next-collapse-panel-icon{
    margin-right:16px;

  }
  .setting-detail-col{
    margin-top:16px;
    background:#ffffff;
    border:none;

  }

  .next-collapse{
    border:none;
  }
  .next-collapse-panel-title{
    background:#ffffff;
    border-bottom:1px solid #f1f2f4;
  }
  .next-collapse-panel{
    border:none!important;
  }
  .next-collapse{
    border:none!important;
  }
}

.crm-wrap {
  width: 60%;
  margin: 40px auto 0;
  .crm-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .crm-select {
    width: 100%;
  }
  .crm-row, .crm-detail {
    padding: 20px;
    background: #fafafa;
    margin: 5px 0 20px;
  }
  .crm-detail {
    border: 1px solid rgb(228, 228, 228);
    margin-top:10px;
    padding: 10px;
    h2 {
      font-size: 14px;
    }
    a {
      margin-right: 15px;
    }
    p {
      word-break: break-word;
    }
  }
}

.crm-content {
  width: 80%;
  max-width: 80%;
}

.crm-name {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right:20px;
  &>span {
    font-size: 18px;
    color: #000;
    font-weight: 500;
  }
  margin-bottom: 20px;
}

.wrapper {
  background: white;
  padding:40px;
}

.crm-btns {
  text-align: right;
  margin-top: 40px;
  .detail {
    color: #FF7C4D;
    margin-right: 10px;
    cursor: pointer;
  }
}

.next-overlay-wrapper.opened .next-overlay-backdrop {
  opacity: 1;
}

.crm-add {
  color: #FF7C4D;
  margin-top: 10px;
  cursor: pointer;
}

.crm-rights {
  margin-top: 20px;
  h2 {
    background: #f1f1f1;
    padding: 5px 10px;
    font-size: 14px;
    font-weight: normal;
    span {
      color: #FF7C4D;
    }
  }
  p {
    margin: 20px;
    word-break: break-word;
    a {
      margin-right: 15px;
    }
  }
}

.crm-link {
  display: block;
  border: 1px solid #FF845B;
  border-radius: 4px;
  padding: 8px;
}

/**
* 通用配置详情
*/

.commonDetail-noresult{
  padding:20px;
  text-align: center;
  height: 100vh;
}
.commonDetail-result{
  display: block;
  position: relative;
  .edit-common{
    text-align: right;
    position: absolute;
    top:8px;
    right:16px;
  }
}
.page-title{
  font-size: 20px;
  color: #333333;
  letter-spacing: 0;
  text-align: left;

}

.notice-text{
  text-align:center;
  margin-top:100px;
  font-size: 14px;
  color: #999999;
  letter-spacing: 0;
}
