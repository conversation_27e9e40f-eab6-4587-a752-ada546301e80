import React, { Component, Suspense, lazy } from 'react'
import Routers from '@/packages/routers'

import SettingList from './setting-list'
import ActTypeList from './actType-list'
import SettingDetailWrap from './setting-detail'
import BeltDetailWrap from './beltNewDetail'
import Activity from './AddForm/activity'
import BeltNewActivity from './AddForm/beltNewActivity'

import WhiteList from './white-list'

import CommonSetForm from './AddForm/commonSetForm'

import CommonSetDetail from './commonSetDetail'
import NewPage from './NewPage'
// [[GBAI STAR]]
import Hello from './Hello'
// [[GBAI END]]

function toPath(file) {
  const prts = file.split('.')
  prts.pop()
  return '/' + prts.filter(x => x).join('/')

}

const req = require.context('./routes')

const files = req.keys()

const routerList = [
  {
    // 玩法列表
    subRouterPath: '/crm_user',
    routerItems: [
      {
        path: '/',
        component: ActTypeList,
        breadcrumbName: '玩法配置'
      },
      {
        path: '/commonCreate',
        component: CommonSetForm,
        breadcrumbName: '通用配置创建'
      },
      {
        path: '/commonEdit/:type(\\d+)?',
        component: CommonSetForm,
        breadcrumbName: '通用配置编辑'
      },
      {
        path: '/beltCreate',
        component: BeltNewActivity,
        breadcrumbName: '老带新添加'
      },
      {
        path: '/commonDetail',
        component: CommonSetDetail,
        breadcrumbName: '通用配置详情'
      },
      {
        path: '/entry',
        component: ActTypeList,
        breadcrumbName: '活动列表'
      },
      {
        path: '/create',
        component: Activity,
        breadcrumbName: '创建活动'
      },
      {
        path: '/list',
        component: SettingList,
        breadcrumbName: '活动列表'
      },
      {
        path: '/detail',
        component: SettingDetailWrap,
        breadcrumbName: '活动详情'
      },
      {
        path: '/beltDetail',
        component: BeltDetailWrap,
        breadcrumbName: '活动详情'
      },
      {
        path: '/whiteList',
        component: WhiteList,
        breadcrumbMap: '配置白名单'
      },
      {
        path: '/new',
        component: NewPage,
        breadcrumbName: '新页面'
      },
      // [[GBAI STAR]]
      {
        path: '/hello',
        component: Hello,
        breadcrumbName: 'Page Hello'
      }
      // [[GBAI END]]
    ]
  }
]

const breadcrumbMap = {
  '/': '玩法配置',
  '/crm_user/entry': '玩法配置',
  '/crm_user/list': '活动列表',
  '/crm_user/detail': '活动详情',
  '/crm_user/log': '活动日志',
  '/crm_user/whiteList': '配置白名单',
  '/crm_user/new': '新页面',
  // [[GBAI STAR]]
  '/crm_user/hello': 'Page Hello'
  // [[GBAI END]]
}

function App() {
  return <Routers routerList={routerList} redirectPath="/crm_user" breadcrumbMap={breadcrumbMap} />
}
export default App