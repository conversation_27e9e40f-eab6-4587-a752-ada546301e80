import React, { useEffect, useState, useReducer } from 'react'
import { <PERSON><PERSON>, Message, Dialog, Step } from '@alifd/next'
import moment from 'moment';
import CreateFormHelper from '@/packages/ali-form/CreateFormHelper'
import Editable from '@/packages/Editable'
import withForm from '@/packages/ali-form/withForm'
import { StepNav, UploadImg, Forms, Tables } from '../components'

moment.locale('zh-cn');

const formatDate = (date) => moment(date).format('YYYY-MM-DD HH:mm:ss')
const STEP_NAME = [{ title: '基础信息', component: Forms }, { title: '权益设置', component: Tables }, { title: '提交' }]
const initialState = { current: 0, formData: {} };
function disabledDate(date, view) {
  const currentDate = moment();
  switch (view) {
    case 'date':
      return date.valueOf() <= currentDate.valueOf();
    case 'year':
      return date.year() < currentDate.year();
    case 'month':
      return date.year() * 100 + date.month() < currentDate.year() * 100 + currentDate.month();
    default: return false;
  }
}

function reducer(state, action) {
  switch (action.type) {
    case 'prev':
      return { ...state, current: state.current - 1 };
    case 'next':
      return { ...state, current: state.current + 1 };
    case 'formData':
      return { ...state, formData: action.value };
    default:
      throw new Error();
  }
}

const cfh = new CreateFormHelper()

const basic = cfh.createGroup('基本信息')
// basic.addField('id', 'Select', {
//   label: '玩法大类',
//   placeholder: '请选择玩法大类',
//   dataSource: [],
//   disabled: true,
//   style: { width: '100%' },
//   rules: [{
//     required: true,
//     message: '玩法大类不能为空'
//   }]
// })

basic.addField('type', 'Select', {
  label: '玩法类型',
  placeholder: '请选择玩法类型',
  dataSource: [],
  disabled: false,
  style: { width: '100%' },
  rules: [{
    required: true,
    message: '玩法类型不能为空'
  }]
})

basic.addField('name', 'Input', {
  label: '玩法标题',
  placeholder: '请填写玩法标题，不超过20个字',
  maxLength: 20,
  cutString: true,
  hasLimitHint: true,
  rules: [{
    required: true,
    message: '玩法标题不能为空'
  }]
})

basic.addField('subtitle', 'Input', {
  label: '玩法副标题',
  cutString: true,
  placeholder: '请填写玩法副标题，不超过20个字',
  maxLength: 20,
  hasLimitHint: true,
  rules: [{
    required: true,
    message: '玩法副标题不能为空'
  }]
})

basic.addField('description', 'TextArea', {
  label: '玩法说明',
  placeholder: '请填写玩法说明，不超过80个字',
  maxLength: 80,
  cutString: true,
  hasLimitHint: true,
  rules: [{
    required: true,
    message: '玩法说明不能为空'
  }]
})

basic.addField('images', 'UploadImg', {
  label: '说明图片',
  hints: ['图片上传数量：最多三张', '上传尺寸：440*440px', '上传格式：png'],
  maxImages: 3,
  actionUrl: 'crm://activity/image/upload',
  actionHeader: { 'X-Requested-With': null },
  successResult: (result) => result.data.data.url || result.data.url 
})

basic.addField('time', 'RangePicker', {
  label: '玩法生效时间',
  style: { width: '100%' },
  showTime: true,
  disabledDate,
  rules: [{
    required: true,
    message: '请选择玩法生效时间'
  }]
})

basic.addField('signTime', 'RangePicker', {
  label: '玩法报名时间',
  style: { width: '100%' },
  showTime: true,
  rules: [{
    required: true,
    message: '请选择玩法报名时间'
  }]
})

// [[GBAI STAR]]
basic.addField('limitCount', 'NumberPicker', {
  label: '限制人数',
  placeholder: '请输入限制人数',
  min: 1,
  max: 999999,
  precision: 0,
  style: { width: '100%' },
  rules: [{
    required: true,
    message: '限制人数不能为空'
  }, {
    type: 'number',
    min: 1,
    message: '限制人数必须大于0'
  }]
})
// [[GBAI END]]

export default withForm({
  uiSource: cfh.toJSON(),
  onContextInit: context => {
    context.componentMap.addComponent('UploadImg', UploadImg)
  },
  requestAction: {
    url: `crm://activity/category/listSubCategoryByFirstCategory`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, ...o } = json
      return {
        id: 1,
        ...o,
        pageNum
      }
    },
    isActive: ({ props }) => true,
    mapResponse: (res = []) => {
      res = res.data // 本次新加了一层又

      console.log('res create',res)

      basic.modifyField('type', 'Select', {
        label: '玩法类型',
        placeholder: '请选择玩法类型',
        disabled: true,
        dataSource: (res || []).map(item => ({ label: item.name, value: item.type })),
        defaultValue: 300, // 默认是会场招商，并且不可修改
        style: { width: '100%' },
        rules: [{
          required: true,
          message: '玩法类型不能为空'
        }]
      })

      return {
        categoryList: res
      }
    }
  },
})((props) => {
  const { context, submit, formContext, history } = props
  const [state, dispatch] = useReducer(reducer, initialState);
  console.log(props, '====')


  const onCancel = () => {
    Dialog.confirm({
      title: '取消玩法',
      content: '信息未保存，是否确认取消？',
      onOk: () => props.history.push('/crm_user/list'),
      onCancel: () => console.log('cancel')
    });
  }

  const onCheck = () => {
    console.log(formContext.field)
    formContext.field.validate((errors, data) => {
      if (errors) {
        const arr = Object.keys(errors).map(x => errors[x])
        Message.error(arr[0].errors[0])
      } else {
        const [timeStart, timeEnd] = formContext.field.getValue('time')
        const [signTimeStart, signTimeEnd] = formContext.field.getValue('signTime')
        if (signTimeStart.valueOf() > timeStart.valueOf() || signTimeEnd.valueOf() > timeEnd.valueOf()) {
          return Message.error('玩法生效时间和玩法报名时间不符')
        }
        const { time, signTime, categoryList, ...reset } = data
        dispatch({ type: 'formData', value: { ...reset, signStartTime: formatDate(signTime[0]), signEndTime: formatDate(signTime[1]), startTime: formatDate(time[0]), endTime: formatDate(time[1]) } })
        dispatch({ type: 'next' })
      }
    })
  }

  const goBack = () => dispatch({ type: 'prev' })
  return <StepNav data={STEP_NAME} state={state} stepAction={[{ onCancel, onCheck }, { goBack }]} history={history} />
})

