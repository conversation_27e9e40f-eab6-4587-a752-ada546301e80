
/**
 * 通用配置表单数据
 */

import React, { Component, useEffect, useState, useReducer } from 'react'
import { Button, Message, Dialog, Step, Checkbox, CascaderSelect } from '@alifd/next'
import moment from 'moment';
import CreateFormHelper from '@/packages/ali-form/CreateFormHelper'
import Editable from '@/packages/Editable'
import TouchUp from '../components/TouchUp'
import withForm from '@/packages/ali-form/withForm'
import requestDataSource from '@/source/requestDataSource'
// import { StepNav, UploadImg, Forms, Tables } from '../components'
import Render from '@/packages/ali-form'
import CommonSetFormAdapter from './commonSetFormAdapter'

const categoryTypeList = {
    100: 'N元购',
    200: '老带新',
    300: '门店拉新'

}

let cityList = [] // 由于需要



moment.locale('zh-cn');

const checkNum = (min, max, name) => {
  return (rule, value, callback) => {
    if (!min || !max) callback()
    if (value !== null || value !== undefined) {
      if (!/\d+/.test(value)) return callback(`${name}必须填入数字`)
      if (min <= value && max >= value) return callback()
      return callback(`${name}不能小于${min}且大于${max}`)
    } else {
      callback()
    }
  }
}

function timesHelper(label, min, max, defaultValue, innerAfter = '', isRequired = true, precision = 1) {
  return {
    label,
    innerAfter,
    placeholder: min || Number(min) === 0 ? `支持输⼊数字${min}～${max}` : '只支持输⼊数字',
    step: min || 1,
    type: 'number',
    min,
    max,
    precision,
    defaultValue,
    style: { width: '100%' },
    rules: [{
      required: isRequired,
      message: `请填写${label}`
    }, { validator: checkNum(min, max, label) }]
  }
}

const cfh = new CreateFormHelper();

const basic = cfh.createGroup('基本信息')
basic.addField('invitedTimeOut', 'Input', timesHelper('被邀请人下单时效', 1, 30, null, "自然日内 "));
basic.addField('orderFinishedTimeOut', 'Input', timesHelper('订单完成结算时效',1, 30, null, '自然日内'));
basic.addField('offcial', 'TextArea', {
  label: '活动规则(用户可见)',
  placeholder: '活动规则c端用户可见，请详细描述活动范围和规则，如：商户类目范围、结算时效、奖励内容等，修改后即刻生效，该内容对用户可见，最多可输入2000字。',
  maxLength: 2000,
  cutString: true,
  hasLimitHint: true,
  rules: [{
    required: true,
    message: '活动规则不能为空'
  }]
});

basic.addField('cityRelation', 'CascaderSelect', {
  label: '城市范围',
  placeholder: '请选择城市范围',
  hasClear: true,
  multiple: true,
  listStyle: { width: '150px', height: '160px' },
  dataSource: [],
  style: { width: '100%' },
  rules: [{
    required: true,
    message: '城市范围不能为空'
  }]
})

const setting = cfh.createGroup('触达配置')
setting.addField('touchDtos1', 'TouchUp',{
  label: '触达配置',
  style: {
    width: '100%'
  },
  title: '邀请人配置',
  disabled: true,
  disabledText: '暂不支持敬请期待'
})
setting.addField('touchDtos2', 'TouchUp',{
  label: ' ',
  style: {
    width: '100%'
  },
  onChange: (data) => {
    console.log('touchUp',data)
    // console.log(field)
    // this.props.field.setValue('isEdit', 1)
  },
  title: '被邀请人配置',
  help: true, // 显示帮助的问号
  helpText: '被邀请并已成功注册/登陆，但是指定时间内仍未在新零售下单(或订单被取消)的新零售新客', // 同时可设置帮助问号点开提示的文案
  disabled: false,
})


@requestDataSource({
  action: {
    
    url: `crm://activity/category/getModel`,
    method: 'POST',
    mapRequest: (body, { props }) => {
      return {
        code: "Province"
      }
    }
  },
  mapStateToProps: (res = {}) => {
    console.log('cityList',res.data)
    const data = res.data && res.data.data
    basic.modifyField('cityRelation', 'CascaderSelect', {
      label: '城市范围',
      placeholder: '请选择城市范围',
      hasClear: true,
      multiple: true,
      canOnlyCheckLeaf: true,
      showSearch: true,
      listStyle: { width: '150px', height: '160px' },
      dataSource: data,
      style: { width: '100%' },
      rules: [{
        required: true,
        message: '城市范围不能为空'
      }]
    })
    cityList = data
    return {
      cityList: data
    }
   }
})
class commonSetForm extends Component {
  render() {

    const { context, submit, formContext } = this.props
    const type = this.props.match.params.type;
    // console.log('render中的cityList')
    // console.log(this.props.cityList)
    const onSubmit = () => {
      // console.log(this.props)
      // console.log(formContext.field.setValue({ 'abc': '12' }))
     // console.log('formContext.field.getValues()',formContext.field.getValues())
      formContext.field.validate((errors, data) => {
        // console.log('errors?',errors)
        if (errors) {
          const arr = Object.keys(errors).map(x => errors[x])
          Message.error(arr[0].errors[0])
        } else {
          const Adapter = new CommonSetFormAdapter()
          const isError = Adapter.validate(formContext.field.getValues())
          if (isError) return Message.error(isError.message)
          Dialog.confirm({
            title: '保存配置',
            content: '确定要保存通用配置吗？',
            onOk: () => {
              const result = this.props.submit()
              result.then(res => {
              // console.log('res,是不是好几层', res)
                if (String(res.errorCode) === "0" || res.errorDesc === "success") {
                  Message.show({
                    type: 'success',
                    content: '保存成功',
                    afterClose: () => this.props.history.push(`/crm_user/commonDetail?type=${type}`)
                  });
                } else {
                  Message.error(res.errorDesc || '保存失败')
                }
              }).catch(msg => {
                // console.log('报错了嘛？？')
                // console.log(msg)
                Message.error(msg.error || '提交失败')
              })
            },
            onCancel: () => console.log('cancel')
          });
        }
      })
    }


  const onCancel = () => {
    const type = this.props.match.params.type;
    Dialog.confirm({
      title: '取消修改',
      content: '确定要取消当前修改吗？',
      onOk: () => this.props.history.push(`/crm_user/commonDetail?type=${type}`),
      onCancel: () => console.log('cancel')
    });
  }

  return <div className="treasure-wrap">
    <Render />
    <p style={{ textAlign: 'right' }}>
      <Button onClick={onCancel} style={{ marginRight: 10 }}>取消</Button>
      <Button onClick={onSubmit} type="primary">保存</Button>
    </p>
  </div>
  }
}
const typeMap = {
  '老带新(券)': 200,
  '会场招商': 300
}

export default withForm({
  uiSource: cfh && cfh.toJSON(),
  onContextInit: context => {
    context.componentMap.addComponent('TouchUp', TouchUp)
    context.componentMap.addComponent('CascaderSelect', CascaderSelect)
  },
  requestAction: {
    url: `crm://activity/category/categoryInfo`,
    method: 'POST',
    mapRequest: (query,obj) => {  
      return {
        type: obj.props.match.params.type || 100,
      }
    },

    isActive: ({ props }) => {
      // console.log('-------props',props)
      // console.log(props.match.params)
      let isEdit = typeof (props.match.params.type) !== 'undefined'
      return isEdit
     
    },
    mapResponse: (res = {}) => {
      // console.log('detailData',res.data)
      const Adapter = new CommonSetFormAdapter()
      const formData = Adapter.interfaceToForm(res.data)

      // console.log('formData', formData)
      return {
        ...formData
      }
    }
  },
  submitAction: {
      url: `crm://activity/category/editCategoryInfo`,
      mapRequest: (query, obj) => {
        // console.log('通用配置的提交方法 submitAction', query)
        // console.log(obj)
        const Adapter = new CommonSetFormAdapter()
        query.cityList = cityList

        const params = Adapter.formToInterface(query)
        console.log(params)
        return {
          ...params
        }
      },
      mapResponse: (res) => {
        return {
          ...res
        }
      }
  },
})(commonSetForm)
