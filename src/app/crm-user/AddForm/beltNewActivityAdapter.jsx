import moment from 'moment'

const formatDate = (date) => moment(date).format('YYYY-MM-DD HH:mm:ss')
export default class BeltNewActivityAdapter {
  // 默认的表单数据
  defaultFormInfo() {
    return {
        "name": "老带新last",
        "startTime": "2020-02-19 10:00:00",
        "endTime": "2020-03-31 10:00:00",
        "creatorName": "junzhao.zj", // 这个先不传了，因为前端获取不到
        "categoryId": 4,
        "budget": 0, // 这个是什么东西？
        "type": 200,
        "creatorId": 237565,
        "cityRelations": [
            {
                "cityType": 100,
                "cityName": "直营城市"
            },
            {
                
                "cityType": 200,
                "cityName": "代理商城市"
            }
        ],
        "rights": [
            {
                "rightType": 100,
                "rightName": "被邀请人权益",
                "crowdType": 500200,
                "willChoose": 0,
                "businesses": [
                    {
                        "businessId": 1578123,
                        "businessName": "限时招商55",
                        "creator": "张雅琳"
                    },
                    {
                        "businessId": 1195123,
                        "businessName": "啥活动12555",
                        "creator": "张雅琳"
                    }
                ],
                "rightsChannelListReqDTOS": [{ "channel": 100,"channelName": "饿了么","subType": 1,"sort": 0 }]
            },
            {
                "rightType": 200,
                "rightName": "邀请人权益",
                "crowdType": 500100,
                "willChoose": 0,
                "businesses": [
                    {
                        "businessId": 1578123,
                        "businessName": "限时招商55",
                        "creator": "张雅琳"
                    },
                    {
                        "businessId": 1195123,
                        "businessName": "啥活动12555",
                        "creator": "张雅琳"
                    }
                ]
            }
        ]
    }
  }

  // 将表单数据转成接口格式
  formToInterface(form) {
      console.log(form)
      const { categoryId, invite, invited, name, time, investedAmount, investorAmount } = form
      const startTime = formatDate(time[0])
      const endTime = formatDate(time[1])
      
      const cityRelations = []
      const rights = []
      const inviteBusinesses = invite.map((item, index) => {
        return {
            businessId: item.planId,
            businessName: item.name,
            creator: item.creator
        }
      })

      const invitedBusinesses = invited.map((item, index) => {
        return {
            businessId: item.planId,
            businessName: item.name,
            creator: item.creator
        }
      })
      const inviteRight = {
        rightType: 200,
        rightName: "邀请人权益",
        crowdType: 500100,
        willChoose: 0,
        businesses: inviteBusinesses || []
      } // 邀请人的权益
     
      const invitedRight = {
        rightType: 100,
        rightName: "被邀请人权益",
        crowdType: 500200,
        willChoose: 0,
        businesses: invitedBusinesses || []
      } // 被邀请人的权益

      // console.log('邀请人的invite',invite)
      // console.log('被邀请人的invited', invited)
      rights.push(inviteRight)
      rights.push(invitedRight)

    const req = {
        name,
        startTime,
        investorAmount,
        investedAmount,
        endTime,
        categoryId,
        type: 200, // 老带新传固定值
        cityRelations, // 页面没有这个选项，传空吧？ 或是不传？
        rights,
        images: [
          {
          url: form.images[0],
          official: '',
          type: 300
        }
        ],
    }

    return req
  }

  // 将接口数据转成表单数据
  interfaceToForm(data) { // 暂时不需要编辑
    // console.log('转换前的data---======',data)
    return {
        ...data
    };
  }

  verifyFormData() {
    
  }

  // 校验
  validate(data) {
    console.log(data)
    let error = null

    return error
  }
}
