import QueryString from "qs"
import moment from 'moment'

const formatTime = (time) => moment(time).format('HH:mm:ss')

export default class commonSetFormAdapter {
  // 默认的表单数据
  defaultFormInfo() {
    return {
      // "id": 4,
      "name": "",
      "parentId": 1,
      "type": 200,
      "parentName": "平台玩法",
      "subtitile": "老带新的一种玩法",
      "generalProperties": {
          "newLedOldDto": {
              "instructions": "老带新玩法的简介",
              "invitedTimeOut": 7,
              "orderFinishedTimeOut": 2,
              "offcial": "老带新规则文案",
              "touchDtos": [
                  {
                      "crowdType": 500200,
                      "crowdName": "被邀请人",
                      "touchType": 200,
                      "smsDto": {
                          "message": "短信消息触达",
                          "cron": "",
                          "day": 4,
                          "maxTouchTime": 8
                      }
                  }
              ]
          }
      },
      "cityRelation": [
          {
              "categoryType": 200,
              "cityType": 100,
              "cityTypeName": "直营城市"
          },
          {
              "id": 13,
              "categoryType": 200,
              "cityType": 100,
              "cityTypeName": "直营城市"
          }
      ],
      "channelListReqDTOS": [
          {
              "channel": 100,
              "channelName": "饿了么",
              "subType": 1,
              "sort": 0
          }
      ],
      "deleteChannel": []
  }
  }

  unique(arr) {
    let hash = [];
    for (let i = 0; i < arr.length; i++) {
       if (hash.indexOf(arr[i]) == -1) {
        hash.push(arr[i]);
       }
    }
    return hash;
  }

  // 将表单数据转成接口格式
  formToInterface(form) {
      
      console.log('展示一下form的字段',form)
      const newLedOldDto = { // 基础信息部分
          instructions: '老带新玩法的简介',
          invitedTimeOut: form.invitedTimeOut,
          orderFinishedTimeOut: form.orderFinishedTimeOut,
          offcial: form.offcial,

      }
      const touchDtos = [] // 邀请人以及被邀请人部分
      const crowedInfo = {
        "crowdType": 500200,
        "crowdName": "被邀请人",
       
      }
      
      const smsDto = { // 消息触达部分
        "message": form.touchDtos2.message,
        "cron": formatTime(form.touchDtos2.cron) || '',
        "day": form.touchDtos2.day,
        "maxTouchTime": form.touchDtos2.maxTouchTime
      }
      const pushDto = { // 推送触达部分
        "message": form.touchDtos2.message,
        "cron": formatTime(form.touchDtos2.cron) || '',
        "day": form.touchDtos2.day,
        "maxTouchTime": form.touchDtos2.maxTouchTime,
        "linkUrl": form.touchDtos2.linkUrl
      }
      if (form.touchDtos2.type === 'message') { // 消息触达
        crowedInfo.smsDto = smsDto
        crowedInfo.touchType = 200
      } else { // 推送触达
        crowedInfo.pushDto = pushDto
        crowedInfo.touchType = 100
      }
     
      touchDtos.push(crowedInfo)
      newLedOldDto.touchDtos = touchDtos
      const cityRelation = [];
    
      let array = []
      form.cityList.forEach((city, i) => {
        let strict = {
          value: String(city.value),
          label: city.label
        }

        array = array.concat(strict) // 把城市都展开

        // console.log(city.children)
        if (city.children) {
          array = array.concat(city.children)
        }    
      })
      // console.log('array======', array)
      let formCityRelation = this.unique(form.cityRelation)
        formCityRelation.forEach((cityId, index) => {
         console.log(cityId)
        array.forEach((city, ix) => {
          let temp = {}
          // console.log(city)
          if (String(cityId) === String(city.value)) {
            temp.categoryType = 200
            // temp.id = form.id
            temp.cityId = String(city.value);
            temp.cityName = city.label;

            cityRelation.push(temp)
          }
        })
      })

      const channelListReqDTOS = []
      const channelInfo = {
        "channel": 100,
        "channelName": "饿了么",
        "subType": 1,
        "sort": 0
      }
      channelListReqDTOS.push(channelInfo)
      const deleteChannel = []

      const req = {
          name: '老带新(券)',
          parentId: 1,
          type: 200,
          parentName: '平台玩法',
          subtitile: '老带新的一种玩法',
          generalProperties: {
              newLedOldDto
          },
          cityRelation,
          channelListReqDTOS,
          deleteChannel,
      }

      // console.log('req======21111',req)
      if (form.id) {
        req.id = form.id
      }
     

    return req
  }

  // 将接口数据转成表单数据
  interfaceToForm(data) {

    if (!data) {
      return {}
    }

    console.log('转换前的data',data)
    let { name, parentId, type, subtitile, id,
      generalProperties, 
      cityRelations, channelListReqDTOS, deleteChannel } = data

      generalProperties = JSON.parse(generalProperties)
     
      const newLedOldDto = generalProperties.newLedOldDto

      const { instructions, invitedTimeOut, orderFinishedTimeOut, offcial } = newLedOldDto
     
      const cityRelationKey = []
      const cityTypeName = []

    
      cityRelations.forEach((item, index) => {
        if (!(cityRelationKey.indexOf(item.cityId) > -1)) {
          cityRelationKey.push(item.cityId)
          cityTypeName.push(item.cityTypeName || item.cityName || '')
        }
      })

      const { touchDtos } = newLedOldDto
    
      let touchDtos1 = {} // 邀请人的
      let touchDtos2 = {} // 被邀请人的，暂时只允许设置被邀请人的

      const touchDto = touchDtos[0] // 从触达消息中取出第一个
    
      touchDtos2 = touchDto.pushDto || touchDto.smsDto || {}
      touchDtos2.type = touchDto.pushDto ? 'push' : 'message'
      if (touchDtos2.type === 'push') {
        touchDtos2.cron = moment(((touchDto.pushDto && touchDto.pushDto.cron) || '12:00:00'),'HH:mm:ss',true)
      } else {
        touchDtos2.cron = moment(((touchDto.smsDto && touchDto.smsDto.cron) || '12:00:00'), 'HH:mm:ss', true)
      }


      const form = {
        id,
        instructions,
        invitedTimeOut,
        orderFinishedTimeOut,
        offcial,
        cityRelation: cityRelationKey,
        cityTypeName: (cityTypeName.join('、') || ''),
        touchDtos1,
        touchDtos2,
      }

      console.log('查看一下form', form)
     
    return form;
  }

  verifyFormData() {
    
  }

  // 校验
  validate(data) {
    console.log('校验没有通过嘛？')
    console.log(data)
    let error = null

    let touchDtos1 = data.touchDtos1 || {}
    let touchDtos2 = data.touchDtos2 || {}

    
    if (JSON.stringify(touchDtos2) !== "{}") {
      const { cron, day, maxTouchTime, message, type } = touchDtos2
      let linkUrl = touchDtos2.linkUrl
      
        !cron && (error = { message: `请先设置触达时间` })
        !day && (error = { message: `请先设置天数` })
        !maxTouchTime && (error = { message: `请先设置最大触达次数` })
        !message && (error = { message: `请先设置消息` })
        type === 'push' && !linkUrl && (error = { message: `请先设置链接` })

    } else {
      error = null
    }

    return error
  }
}
