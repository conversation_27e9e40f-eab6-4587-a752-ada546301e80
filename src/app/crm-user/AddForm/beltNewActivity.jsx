/**
 * 老带新的表单
 * 推荐一种玩法一个表单，不要在组件内部加过多的判断交叉逻辑
 */

import React, { useEffect, useState, useReducer } from 'react'
import { Button, Message, Dialog, Step } from '@alifd/next'
import moment from 'moment';
import CreateFormHelper from '@/packages/ali-form/CreateFormHelper'
// import Editable from '@/packages/Editable'
import RightsTable from '../components/RightsTable'
import withForm from '@/packages/ali-form/withForm'
import { config } from '@/api/config'
// import { StepNav, UploadImg, Forms, Tables } from '../components'
import Render from '@/packages/ali-form'
import BeltNewActivityAdapter from './beltNewActivityAdapter'
import "../crm_user.scss"



const categoryTypeList = {
    100: 'N元购',
    200: '老带新',
    300: '门店拉新'
}

const typeMap = {
  '老带新(券)': 200,
  '会场招商': 300
}


const categoryList = [
  {
    label: 'N元购',
    value: 100
  },
  {
    label: '老带新',
    value: 200
  },{
    label: '门店拉新',
    value: 300
  }
]

function disabledDate(date, view) {
  const currentDate = moment();
  switch (view) {
    case 'date':
      return date.valueOf() <= currentDate.valueOf();
    case 'year':
      return date.year() < currentDate.year();
    case 'month':
      return date.year() * 100 + date.month() < currentDate.year() * 100 + currentDate.month();
    default: return false;
  }
}

moment.locale('zh-cn');

const checkNum = (min, max, name) => {
  return (rule, value, callback) => {
    if (!min || !max) callback()
    if (value !== null || value !== undefined) {
      if (!/\d+/.test(value)) return callback(`${name}必须填入数字`)
      if (min <= value && max >= value) return callback()
      return callback(`${name}不能小于${min}且大于${max}`)
    } else {
      callback()
    }
  }
}

function timesHelper(label, min, max, defaultValue, innerAfter = '', isRequired = true, precision = 1) {
  return {
    label,
    innerAfter,
    placeholder: min || Number(min) === 0 ? `支持输⼊数字${min}～${max}` : '只支持输⼊数字',
    step: min || 1,
    type: 'number',
    min,
    max,
    precision,
    defaultValue,
    style: { width: '100%' },
    rules: [{
      required: isRequired,
      message: `请填写${label}`
    }, { validator: checkNum(min, max, label) }]
  }
}

const isValidNum = (formContext) => {

  console.log('values' + JSON.stringify(formContext.field.getValues()))
  let isSend = false;
 
  return isSend
}


const cfh = new CreateFormHelper();

const basic = cfh.createGroup('基础信息')
basic.addField('categoryId', 'Select', {
  label: '玩法类型',
  placeholder: '请选择玩法类型',
  dataSource: categoryList,
  defaultValue: 200,
  disabled: true,
  style: { width: '100%' },
  rules: [{
    required: true,
    message: '玩法类型不能为空'
  }]
})
basic.addField('name', 'Input', {
  label: '玩法名称',
  placeholder: '请填写玩法名称，不超过20个字',
  maxLength: 20,
  cutString: true,
  hasLimitHint: true,
  rules: [{
    required: true,
    message: '玩法名称不能为空'
  }]
})
basic.addField('time', 'RangePicker', {
  label: '玩法生效时间',
  style: { width: '100%' },
  showTime: true,
  disabledDate,
  rules: [{
    required: true,
    message: '请选择玩法生效时间'
  }]
})

const setting = cfh.createGroup('玩法规则')

setting.addField('invite', 'RightsTable',{
  label: '邀请人权益',
  dataSource: [
    { label: '北京', value: 1 }
  ],
  // columns: [ ],
  style: { width: '100%' },
  rules: [{
    required: true,
    message: '邀请人权益不能为空'
  }]
})

setting.addField('invited', 'RightsTable',{
  label: '被邀请人权益',
  dataSource: [
    { 
      id: '1',
      name: '111'
     }
  ],
  // columns: [ ],
  style: { width: '100%' },
  rules: [{
    required: true,
    message: '被邀请人权益不能为空'
  }]
})

setting.addField('investorAmount', 'Input', {
  label: '邀请人红包金额',
  placeholder: '请填写邀请人红包金额，不能超过99元',
  maxLength: 2,
  cutString: true,
  hasLimitHint: true,
  rules: [{
    required: true,
    pattern: /([1-9]*[0-9])/,
    message: '玩法名称不能为空'
  }]
})

setting.addField('investedAmount', 'Input', {
  label: '被邀请人红包金额（最高）',
  placeholder: '请填写邀请人红包金额，不能超过99元',
  maxLength: 2,
  cutString: true,
  hasLimitHint: true,
  rules: [{
    required: true,
    pattern: /([1-9]*[0-9])/,
    message: '玩法名称不能为空'
  }]
})
setting.addField('images', 'UploadImger', {
  label: '分享图片',
  hints: ['上传尺寸：750px*600px', '上传格式：png,jpeg,jpg,apng', '图片大小：不超过500kb'],
  pattern: {
    sizePx: '750*600',
    maxSize: 5000
  },
  rules: [{
    required: true
  }],
  maxImages: 1,
  actionUrl: 'crm://activity/image/upload',
  actionHeader: { 'X-Requested-With': null },
  successResult: (result) => result.data.data.url || result.data.url 
})

const BeltNewActivity = (props) => {

  
  const { context, submit, formContext } = props
  const onSubmit = () => {
    const detailQuery = window.parseUrl(window.location.href).query
    console.log('detailQuery', detailQuery)

    formContext.field.validate((errors, data) => {
      if (errors) {
        const arr = Object.keys(errors).map(x => errors[x])
        Message.error(arr[0].errors[0])
      } else {
        const Adapter = new BeltNewActivityAdapter()
        const isError = Adapter.validate(formContext.field.getValues())
        console.log('formContext.field.getValues()', formContext.field.getValues())
        if (isError) return Message.error(isError)
        Dialog.confirm({
          title: '创建玩法',
          content: '确定要创建该活动玩法吗？',
          onOk: () => {
            const result = props.submit()
            result.then(res => {
              if (String(res.errorCode) === "0" || res.errorDesc === "success") {
                Message.show({
                  type: 'success',
                  content: '创建成功',
                  afterClose: () => {
                    props.history.push(`/crm_user/list?type=${detailQuery.type}&categoryId=${detailQuery.categoryId}`)
                  }
                });
              } else {
                Message.error(res.errorDesc || '创建失败')
              }
            }).catch(msg => {
              Message.error(msg.error || '提交失败')
            })
          },
          onCancel: () => console.log('cancel')
        });
      }
    })
  }


  const onCancel = () => {
    Dialog.confirm({
      title: '取消玩法',
      content: '确定要取消该活动玩法吗？',
      onOk: () => props.history.push('/'),
      onCancel: () => console.log('cancel')
    });
  }

  return <div className="treasure-wrap">
  <Render />
  <p style={{ textAlign: 'center' }}>
    <Button onClick={onCancel} style={{ marginRight: 10 }}>取消</Button>
    <Button onClick={onSubmit} type="primary">创建</Button>
  </p>
</div>
}

export default withForm({
  uiSource: cfh && cfh.toJSON(),
  onContextInit: context => {
   context.componentMap.addComponent('RightsTable', RightsTable)
  },
  submitAction: {
      url: `crm://activity/create`,
      mapRequest: (query, obj) => {
        const Adapter = new BeltNewActivityAdapter()
        const data = Adapter.formToInterface(query)
        return {
          ...data
        }
      }
  },
})(BeltNewActivity)
