/**
 * 添加白名单
 */

import React from 'react'
import formDataSource from '@/source/formDataSource'
import fusionInputFix from '@/utils/fusionInputFix'

import { Message, Dialog, Input, Form, Select } from '@alifd/next'

const FormItem = Form.Item
const XInput = fusionInputFix()(Input)

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 }
}
const categoryList = [
  {
    label: '老带新',
    value: 200
  },
  {
    label: '线下拉新',
    value: 400
  }
]

const CreateWhite = formDataSource({
  submitAction: {
    url: `crm://activity/edit/user/add`,
    mapRequest: (query, obj) => {
      console.log('query', query)
      console.log('obj', obj)

      return {
        categoryType: query.type,
        categoryName: categoryList.find(item => {
          return item.value === query.type
        }).label,
        creatorAccount: parseInt(query.creatorAccount, 10),
        creatorName: query.creatorName
      }
    }
  },

})(({ visible, setVisible, reload, field, submit }) => {
  return (
    <Dialog
      title="新增白名单"
      footerActions={['cancel', 'ok']}
      onOk={() => {
        console.log('ok')
        const r = field.validate((err, values) => {
          if (err) {
            console.log(err)
            Message.error(err)
            return
          }
          submit()
            .then((res) => {
              console.log('-----res', res)
              if (res.errorCode !== '0') {
                Message.error(res.errorDesc)
                setVisible(false)
                return
              }
              console.log('res', res)

              Message.success('添加白名单成功')
              setVisible(false)
              reload()
            })
            .catch((err) => {
              Message.error(err)

            })
        })
      }}
      style={{
        width: '676px'
      }}
      onCancel={() => {
        console.log('cancel')
        setVisible(false)
      }}
      onClose={() => {
        console.log('onClose')
        setVisible(false)
      }}
      visible={visible}
    >
      <div style={{ marginTop: '20px' }} className="bg-fff">
        <Form field={field}>
          <FormItem label="玩法类型:" {...formItemLayout}>
            <Select
              dataSource={categoryList}
              placeholder="请输入要添加的类型"
              {...field.init('type', {
                initValue: 200,
                rules: {
                  required: true,
                  message: '请选择要添加的类型'
                }
              })}
            />
          </FormItem>
          <FormItem label="工号:" {...formItemLayout}>
            <XInput
              type="Text"
              placeholder="请输入要添加的阿里工号"
              {...field.init('creatorAccount', {
                rules: {
                  required: true,
                  message: '请输入要添加的阿里工号'
                }
              })}
            />
          </FormItem>
          <FormItem label="姓名:" {...formItemLayout}>
            <XInput
              type="Text"
              placeholder="请输入要添加姓名"
              {...field.init('creatorName', {
                rules: {
                  required: true,
                  message: '请输入要添加姓名'
                }
              })}
            />
          </FormItem>
        </Form>
      </div>

    </Dialog>
  )
})

export default CreateWhite
