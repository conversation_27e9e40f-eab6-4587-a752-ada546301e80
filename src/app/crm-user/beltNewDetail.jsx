/**
 * 老带新的活动详情
 */

import React, { Component, useState, useEffect } from 'react'
import { observer, inject } from 'mobx-react'
import { Link } from 'react-router-dom'
import PropTypes from 'prop-types'

import { Breadcrumb, Radio, Message, Form, Button, Dialog, Grid, Collapse } from '@alifd/next'
import OpPlanHeader from '@/components/header'
import { Filter, Table } from '@/packages/table/FilterTable'
import Title from '@/components/title'
import path from '@/api/modules/benefit'
import { request } from '@/packages/request'
import formatDate from '@/utils/formatDate'
import requestDataSource from '@/source/requestDataSource'
import BeltNewActivityAdapter from './AddForm/beltNewActivityAdapter'
import { config } from '@/api/config'

import './crm_user.scss'

const { Row, Col } = Grid

const Panel = Collapse.Panel;
const breads = [
  {
    path: '/crm_user/list',
    name: '玩法配置'
  },
  {
    name: '玩法详情'
  }
]

@requestDataSource({
  action: {
    url: `crm://activity/info?id={query.id}`,
    method: 'POST'
  },
  mapStateToProps: ({ data }) => {
    console.log('detailData玩法的',data)
    return {
      detailData: data.data
    }
  }
})
class SettingDetailWrap extends Component {
  render() {
    const { detailData, history } = this.props
    const Adatper = new BeltNewActivityAdapter()

    // const data = Adatper.interfaceToForm(detailData)
    // console.log('转换之后 的data', data)

    return (
      <SettingDetail detailData={detailData} _history={history} />
    )
  }
}

function RightsCard({ data }) {
  function generateIds(businesses) {
    const nodes = businesses.map((item, index) => {
      return (
        <span>
          &nbsp;
          &nbsp;
          <a target={"_blank"} rel="noopener noreferrer" href={`${config.ZHAO_SHANG}/main/activity/manage/lookUp/${item.businessId}/${item.businessName}`}>
            {item.businessId}
          </a>
          &nbsp;
          &nbsp;
        </span>

      )
    })
    return nodes;
  }


  return (data || []).map((item, index) => {
    return (
      <div>
        <div className="rights-title">
          {item.rightName}
        </div>
        <div className="detail">
          <Row className="detail-row">
            <Col span={4} className="lf-label">
              权益名称
            </Col>
            <Col span={12} className="lr-content">
              {item.rightName}
            </Col>
          </Row>
          <Row className="detail-row">
            <Col span={4} className="lf-label">
              已绑定的招商活动ID
            </Col>
            <Col span={12} className="lr-content">
              {generateIds(item.businesses)}
            </Col>
          </Row>
        </div>
      </div>
    )
  })
}

function SettingDetail({ detailData, _history }) {
  console.log(detailData)
  const headerSource = {
    title: '玩法详情',
    renderCell: () => {
      return (
        <span
          className={`boreas-status boreas-status-${detailData.status}`}
          style={{ marginLeft: '20px' }}
        >
          {+detailData.status === 2 ? '已下线' : '生效中'}
        </span>
      )
    }
  }

  function init(images) {
    let node = images.map((item, index) => {
      return (
        <img className="detail-img" src={item} key={index} />
      )
    })
    return node;

  }

  function generateBussinessNodes(businesses) {

    const columns = [
      {
        title: '投放计划ID',
        dataIndex: 'businessId'
      },
      {
        title: '投放计划名称',
        dataIndex: 'businessName'
      },
      {
        title: '创建人',
        dataIndex: 'creator'
      }
    
    ]

    return (
      <Table
        dataSource={businesses}
        columns={columns}
        hasBorder={false}
        />
  
    )
  }

  function generateInviteNodes(rights) {
    console.log(rights)
    const nodes = (rights || []).map((item, index) => {
      return (
        <Row className="detail-row">
          <Col className="lf-label">
            <span>{item.rightName}：</span>
          </Col>
          <Col className="lr-content">
            {generateBussinessNodes(item.businesses)}
          </Col>
        </Row>
      )
    })
    return nodes
  }

  return (
    <div className="right-content treasure-detail">
      <Breadcrumb style={{ margin: '20px 0 40px 0' }}>
        {breads.map(x => <Breadcrumb.Item>
          {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
        </Breadcrumb.Item>)}
      </Breadcrumb>
      <div>
        <h3>{detailData.name}</h3>
      </div>
      <div
        style={{ marginTop: '10px', padding: '0 20px 30px' }}
        className="bg-fff setting-detail-content"
      >
        <div
          style={{ marginTop: '10px', padding: '0 20px 30px' }}
          className="bg-fff plan-detail-content"
        >
          {/* 基本信息 */}
          <Title title="基本信息">
            <div className="rights-card">
              <div className="detail">
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法类型：</span>
                  </Col>
                  <Col className="lr-content">
                    {detailData.typeStr}
                  </Col>
                </Row>
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法ID：</span>
                  </Col>
                  <Col className="lr-content">
                    {detailData.id}
                  </Col>
                </Row>
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法名称：</span>
                  </Col>
                  <Col className="lr-content">
                    {detailData.name}
                  </Col>
                </Row>
             
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法生效时间：</span>
                  </Col>
                  <Col className="lr-content">
                    {formatDate(detailData.startTime)} ~ {formatDate(detailData.endTime)}
                  </Col>
                </Row>
               
              </div>
            </div>
          </Title>

          <Title title="玩法规则">
            <div className="rights-card">
            <div className="detail">
              {generateInviteNodes(detailData.rights)}
            </div>
            <div className="detail">
              <Row className="detail-row">
                <Col className="lf-label">
                  <span>被邀请人红包金额(最高)</span>
                </Col>
                <Col className="lr-content">
                  { detailData.investedAmount >= 0 ? `${detailData.investedAmount} 元` : ''} 
                </Col>
              </Row>
              <Row className="detail-row">
                <Col className="lf-label">
                  <span>邀请人红包金额</span>
                </Col>
                <Col className="lr-content">
                { detailData.investorAmount >= 0 ? `${detailData.investorAmount} 元` : ''}
                </Col>
              </Row>
              <Row className="detail-row">
                <Col className="lf-label">
                  <span>活动图片</span>
                </Col>
                <Col className="lr-content">
                {<img src={detailData.images[0] && detailData.images[0].url} alt="" />}
                </Col>
              </Row>
            </div>
              
              {/* <RightsCard data={detailData.rights} /> */}
            </div>
          </Title>
        </div>
      </div>
    </div>
  )
}

export default SettingDetailWrap
