/**
 * 配置白名单列表页面
 */
/* eslint-disable radix */

import React, { Component, useState } from 'react'
import { Link } from 'react-router-dom'
import { Dialog, Input, Form, Message } from '@alifd/next'
import _ from 'lodash'

import filterDataSource from '@/source/filterDataSource'
import requestDataSource from '@/source/requestDataSource'

import { Filter, Table } from '@/packages/table/FilterTable'
import Header from '@/components/header'
import fusionInputFix from '@/utils/fusionInputFix'
import formDataSource from '@/source/formDataSource'
import parseUrl from '@/packages/request/parseUrl'
// import MyBreadcrumb from '@/components/breadcrumb'

import formatDate from '@/utils/formatDate'
import BoreasDialog from '@/components/dialog'
import { request } from '@/packages/request'

import CreateWhite from './create-white'

const FormItem = Form.Item
const XInput = fusionInputFix()(Input)

const statusList = {
  0: "待提交",
  1: "待生效",
  2: "生效中",
  3: "暂停中",
  4: "已关闭",
  5: "已结束"
}
const categoryList = [
  {
    label: '线下拉新',
    value: 400
  }
]

let playTypeList = [
  {
    value: '',
    label: '全部类型'
  }
]

const breads = [
  {
    path: '/list',
    breadcrumbName: '玩法配置'
  }
]


@requestDataSource({
  action: {
    url: `crm://activity/category/listSubCategoryByFirstCategory`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, ...o } = json
      return {
        id: 1,
        ...o,
        pageNum
      }
    },
    mapResponse: (json, { props, query }) => {
      return {
        data: json.data || []
      }
    }
  },
  mapStateToProps: props => {
    console.log('props.data ', props.data)
    let data = (props.data && props.data.data) || []
    let levelTwo = null;

    levelTwo = data.map((cItem, cIndex) => {
      let _data = { value: cItem.type, label: cItem.name }
      playTypeList.push(_data)

      return _data
    })


    let newArr = [];
    newArr.push(levelTwo)

    return {
      category: levelTwo
    }
  }
})
@filterDataSource({
  action: {
    url: `crm://activity/edit/user/list`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, ...o } = json
      return {
        ...o,
        pageNum
      }
    },
    mapResponse: (json, { props, query }) => {
      return {
        total: json.data.total,
        data: json.data.resultData || []
      }
    }
  }
})

class List extends Component {
  constructor(props) {
    super(props)
    this.state = {
      visible: false
    }
  }

  setVisible = (val) => {
    this.setState({
      visible: val
    })
  }

  reloadList = () => {
    this.props.tableProps.reload()

  }


  changeStatus = (record, type, reload) => {
    request(
      {
        url: `crm://activity/edit/user/del`,
        method: 'POST'
      },
      {
        body: {
          id: record.id
        }
      }
    ).then(() => {
      Message.success('操作成功')
      reload()
    })
  }

  handleChange = (record, type, reload) => {
    // type 1上线 2 下线

    BoreasDialog({
      title: '删除',
      content: '确认删除白名单后，该用户将无法操作，是否确认删除？',
      onOk: () => this.changeStatus(record, type, reload)
    })
  }

  render() {

    const searchParmas = parseUrl(window.location.href).query || {}
    const handleEvent = (e, values, field) => {
      if (e === 'reset') {
        field.setValues({
          categoryType: '',
          creatorAccount: '',

        })
        this.props.filterProps.search({
          categoryType: undefined,
          creatorAccount: undefined,
        })
      }
    }

    const filterUISource = {
      'x-component': 'Filter',
      labelAlign: 'top',
      children: [
        {
          defaultValue: searchParmas.creatorAccount,
          label: '账户:',
          name: 'creatorAccount',
          style: {
            width: 300
          },
          placeholder: '请输入账户ID',
          'x-component': 'Input',
        },
        {
          defaultValue: searchParmas.categoryType,
          label: '玩法类型:',
          name: 'categoryType',
          style: {
            width: 300
          },
          placeholder: '请选择玩法类型',
          'x-component': 'Select',
          dataSource: categoryList
        },
      ]
    }

    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        cell: value => {
          return (
            <span>
              {value}
            </span>
          )
        }
      },
      {
        title: '类型',
        dataIndex: 'categoryType',
        cell(value) {
          const type = categoryList.find(item => {
            return item.value === value
          })
          if (type) {
            return <div>{type.label}</div>
          }
        }
      },
      {
        title: '账户',
        dataIndex: 'creatorAccount'
      },
      {
        title: '姓名',
        dataIndex: 'creatorName',
        width: '120px',
      },
      {
        title: '操作',
        dataIndex: 'ops',
        width: '120px',
        cell: (value, index, record, { reload }) => {
          return (
            <div className="boreas-cell">
              {/* 除了已关闭和已结束，都可以下线 */}
              {(record.status !== 4 && record.status !== 5) && (
                <span onClick={() => this.handleChange(record, 4, reload)} className="pl">
                  删除
                </span>
              )}
            </div>
          )
        }
      }
    ]


    return (
      <div className="right-content">
        {/* <MyBreadcrumb routes={breads} /> */}
        <div className="bg-fff crm-list-wrap" style={{ padding: '20px' }}>
          <div className="newplan-title">
            <Header title="配置白名单" buttonText="新增" handleClickButton={() => {
              this.setVisible(true);
            }} />
          </div>
          <Filter resetType="reset" uiSource={filterUISource} onEvent={handleEvent} {...this.props.filterProps} />
          <Table
            className="boreas-table"
            {...this.props.tableProps}
            columns={columns}
            hasBorder={false}
            emptyContent="请添加白名单~"
          />
        </div>
        <CreateWhite
          visible={this.state.visible}
          setVisible={this.setVisible}
          reload={this.reloadList}
        />
      </div>
    )
  }
}

export default List
