/**
 * 玩法配置-列表
 */
/* eslint-disable radix */

import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { Dialog, Input, Form, Message, Button } from '@alifd/next'
import Moment from 'moment'
import requestDataSource from '@/source/requestDataSource'

import fusionInputFix from '@/utils/fusionInputFix'
import './crm_user.scss'

import MenuCard from './components/MenuCard'

const FormItem = Form.Item
const XInput = fusionInputFix()(Input)
const breads = [
  {
    path: '/',
    breadcrumbName: '玩法配置'
  }
]


function List(props) {
  const { data } = props
  return (
    <div style={{ padding: '20px' }}>
       <div className="set-title">玩法配置</div>
        <div className="bg-fff" style={{ padding: '20px', marginTop: '20px' }}>
          {/* <TypeCards dataSource={data} /> */}
          <MenuCard dataSource={data} />
        </div>
    </div>
  )
}



export default requestDataSource({
  action: {
    url: `crm://activity/category/listSubCategoryByFirstCategory`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, ...o } = json
      return {
        id: 1,
        ...o,
        pageNum
      }
    },
    mapResponse: (json, { props, query }) => {
        return {
          data: json.data || []
        }
      }
    },
    mapStateToProps: props => {

      return {
        data: props.data.data
      }
    }
})(List)
