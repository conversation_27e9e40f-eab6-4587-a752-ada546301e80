/**
 * 玩法配置-列表
 */
/* eslint-disable radix */

import React, { Component } from 'react'
import { Link } from 'react-router-dom'
import { Dialog, Input, Form, Message } from '@alifd/next'
import _ from 'lodash'

import filterDataSource from '@/source/filterDataSource'
import requestDataSource from '@/source/requestDataSource'

import { Filter, Table } from '@/packages/table/FilterTable'
import Header from '@/components/header'
import fusionInputFix from '@/utils/fusionInputFix'
import formDataSource from '@/source/formDataSource'
import parseUrl from '@/packages/request/parseUrl'
// import MyBreadcrumb from '@/components/breadcrumb'

import formatDate from '@/utils/formatDate'
import BoreasDialog from '@/components/dialog'
import { request } from '@/packages/request'

// const FormItem = Form.Item
// const XInput = fusionInputFix()(Input)
const detailPath = {
  '会场招商': '/crm_user/detail',
  '老带新(券)': '/crm_user/beltDetail'
}
const statusList = {
  0: "待提交",
  1: "待生效",
  2: "生效中",
  3: "暂停中",
  4: "已关闭",
  5: "已结束"
}

let playTypeList = [
  {
    value: '',
    label: '全部类型'
  }
]
const pathMap = {
  200: '/crm_user/beltCreate',
  300: '/crm_user/create'
}

const breads = [
  {
    path: '/list',
    breadcrumbName: '玩法配置'
  }
]

@requestDataSource({
  action: {
    url: `crm://activity/category/listSubCategoryByFirstCategory`,
    method: 'POST',
    mapRequest: json => {
      const { pageNum, ...o } = json
      return {
        id: 1,
        ...o,
        pageNum
      }
    },
    mapResponse: (json, { props, query }) => {
      return {
        data: json.data || []
      }
    }
  },
  mapStateToProps: props => {
    console.log('props.data ',props.data)
    let data = (props.data && props.data.data) || []
    let levelTwo = null;
   
      levelTwo = data.map((cItem, cIndex) => {
        let _data = { value: cItem.type, label: cItem.name }
        playTypeList.push(_data)

        return _data
      })


    let newArr = [];
   newArr.push(levelTwo)

    return {
      category: levelTwo
    }
  }
})
@filterDataSource({
  action: {
    url: `crm://activity/list`,
    method: 'POST',
    mapRequest: json => {
      delete json.categoryId
      console.log(json)
      
      const { pageNum, ...o } = json
      
      return {
        ...o,
        pageNum
      }
    },
    mapResponse: (json, { props, query }) => {
      console.log('json', json)
      return {
        total: json.data.total,
        data: json.data.resultData || []
      }
    }
  }
})

class List extends Component {
  changeStatus = (record, type, reload) => {
    request(
      {
        url: `crm://activity/status`,
        method: 'POST'
      },
      {
        body: {
          status: type,
          id: record.id
        }
      }
    ).then(() => {
      Message.success('操作成功')
      reload()
    })
  }

  handleChange = (record, type, reload) => {
    // type 1上线 2 下线

    BoreasDialog({
      title: '下线',
      content: '确认关闭活动后，活动将即刻下线，无法重启，是否确认关闭？',
      onOk: () => this.changeStatus(record, type, reload)
    })
  }

  handleCreate =() => {
    console.log('如果当前路径中有type,就按照type',this.props)
    const searchParams = parseUrl(window.location.href).query || {}
    console.log(searchParams)
    let type = searchParams.type || ''
    let categoryId = searchParams.categoryId || ''

    if (type) {
      this.props.history.replace({
        pathname: `${pathMap[+type]}`,
        search: `?type=${type}&categoryId=${categoryId}`
      })
    } else { // 弹出选择创建什么类型的
      this.props.history.push({
        pathname: `/`,
        search: ''
      })
    }
  }

  render() {

    const searchParmas = parseUrl(window.location.href).query || {}
    const handleEvent = (e, values, field) => {
      if (e === 'reset') {
        field.setValues({
          name: '',
          id: '',
          creatorName: '',
          type: '',
          status: '',
        })
        this.props.filterProps.search({
          name: undefined,
          id: undefined,
          creatorName: undefined,
          type: undefined,
          status: undefined,
        })
      }
    }

    const filterUISource = {
      'x-component': 'Filter',
      labelAlign: 'top',
      children: [
        {
          defaultValue: searchParmas.name,
          label: '玩法名称:',
          name: 'name',
          placeholder: '请输入活动名称',
          'x-component': 'Input'
        },
        {
          defaultValue: searchParmas.id,
          label: '玩法活动ID:',
          name: 'id',
          placeholder: '请输入玩法活动ID',
          'x-component': 'Input',
          'data-type': 'number'
        },
        {
          defaultValue: searchParmas.creatorName,
          label: '创建人:',
          name: 'creatorName',
          placeholder: '请输入创建人名称',
          'x-component': 'Input'
        },
        {
          defaultValue: searchParmas.type,
          label: '玩法类型:',
          name: 'type',
          placeholder: '请选择玩法类型',
          'x-component': 'Select',
          dataSource: (this.props && this.props.category) || []
        },
        {
          defaultValue: searchParmas.status,
          label: '活动状态:',
          name: 'status',
          placeholder: '请选择玩法活动状态',
          'x-component': 'Select',
          dataSource: [
            {
              value: '',
              label: '全部状态'
            },
            {
              value: '0',
              label: '待提交'
            },
            {
              value: '1',
              label: '待生效'
            },
            {
              value: '2',
              label: '生效中'
            },
            {
              value: '3',
              label: '暂停中'
            },
            {
              value: '4',
              label: '已关闭'
            },
            {
              value: '5',
              label: '已结束'
            }
          ]
        }
      ]
    }

    const columns = [
      {
        title: '活动ID',
        dataIndex: 'id',
        cell: value => {
          return (
            <span>
              {value}
            </span>
          )
        }
      },
      {
        title: '活动名称',
        dataIndex: 'name'
      },
      {
        title: '玩法类型',
        dataIndex: 'typeStr',
        cell: (value, index, record, { reload }) => {
          return (
            <span>
              {record.typeStr}
            </span>
          )

        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: '120px',
        cell: value => {
          return (
            <span className={`crm-status crm-status-${value}`}>
              {statusList[value]}
            </span>
          )
        }
      },
      {
        title: '活动报名时间',
        dataIndex: 'signStartTime',
        cell: (value, index, record, { reload }) => {
          return (
            <span>
              {formatDate(record.signStartTime)}~{formatDate(record.signEndTime)}
            </span>
          )

        }
      },
      {
        title: '活动时间',
        dataIndex: 'startTime',
        cell: (value, index, record, { reload }) => {
          return (
            <span>
              {formatDate(record.startTime)}~{formatDate(record.endTime)}
            </span>
          )

        }
      },
      {
        title: '创建人',
        dataIndex: 'creatorName',
        width: '120px',
      },
      {
        title: '最新更新时间',
        dataIndex: 'gmtModified',
        cell: (value, index, record, { reload }) => {
          return (
            <span>
              {formatDate(record.gmtModified)}
            </span>
          )

        }
      },
      {
        title: '操作',
        dataIndex: 'ops',
        width: '120px',
        cell: (value, index, record, { reload }) => {
          return (
            <div className="boreas-cell">
              <Link
                to={{
                  pathname: detailPath[record.typeStr],
                  search: `?id=${record.id}`
                }}
              >
                <span className="pr">查看</span>
              </Link>
              {/* 除了已关闭和已结束，都可以下线 */}
              {(record.status !== 4 && record.status !== 5 && record.canEdit) && (
                <span onClick={() => this.handleChange(record, 4, reload)} className="pl">
                  关闭
                </span>
              )}
            </div>
          )
        }
      }
    ]

 
    return (
      <div className="right-content">
        {/* <MyBreadcrumb routes={breads} /> */}
        <div className="bg-fff crm-list-wrap" style={{ padding: '20px' }}>
          <div className="newplan-title">
            <Header title="玩法列表" buttonText="新建玩法" handleClickButton={this.handleCreate} />
          </div>
          <Filter resetType="reset" uiSource={filterUISource} onEvent={handleEvent} {...this.props.filterProps} />
          <Table
            className="boreas-table"
            {...this.props.tableProps}
            columns={columns}
            hasBorder={false}
            emptyContent="请添加活动~"
          />
        </div>
      </div>
    )
  }
}

export default List
