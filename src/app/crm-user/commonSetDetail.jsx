/*
老带新通用配置详情
*/
import React, { Component } from 'react'

import { Breadcrumb, Radio, Message, Form, Table, Button, Dialog, Grid, Collapse } from '@alifd/next'
import { Link } from 'react-router-dom'
import requestDataSource from '@/source/requestDataSource'
import Title from '@/components/title'
import formatDate from '@/utils/formatDate'
import moment from 'moment'
import CommonSetFormAdapter from './AddForm/commonSetFormAdapter'

import './crm_user.scss'

const formatTime = (time) => moment(time).format('HH:mm:ss')
const typeMap = {
  'message': '短信',
  'push': '饿了么站内PUSH'
}
const { Row, Col } = Grid

const breads = [
  {
    path: '/crm_user/list',
    index: 1,
    name: '玩法配置'
  },
  {
    index: 2,
    name: '通用配置详情'
  }
]

@requestDataSource({
  action: {
    url: `crm://activity/category/categoryInfo?type={query.type}`,
    method: 'POST'
  },
  mapRequest: (query, obj) => {
    console.log(query, obj)
    return {
      type: query.type
    }
  },
  mapStateToProps: ({ data }) => {
    console.log('detailData玩法的',data)
    return {
      detailData: data.data
    }
  }
})
class CommonSetDetailWrap extends Component {
  render() {
    const { detailData, history } = this.props
    
    // console.log(this.props.match.params)

     return (
       <CommonSetDetail detailData={detailData} />
     )
  }
}

const CommonSetDetail = (props) => {

  console.log('detailData' + JSON.stringify(props.detailData))
  console.log('type', props)
  const { detailData } = props;
  const canEdit = detailData.canEdit || false
  const type = detailData.type
  const Adapter = new CommonSetFormAdapter()
  const data = Adapter.interfaceToForm(detailData)
  console.log('interfaceTo form之后 的data', data)
  const dataNull = Object.keys(data).length === 0

  function generateTouch(data) {
    let { touchDtos1, touchDtos2 } = data

    console.log('touchDtos2',touchDtos2)

    // touchDto1暂时用不到
    const touchDtos1Nodes = touchDtos1 && (<div>
          <p> 邀请人配置： 已邀请未下单新客</p>
          </div>
        )

    const touchDtos2Nodes = touchDtos2 && 
      (<div>
        <p> 被邀请人配置： 已被邀请未下单新客</p>
        <p> 触达配置：{typeMap[touchDtos2.type]} </p>
        <p> 触达内容：{touchDtos2.message} </p>
        {touchDtos2.type === 'push' && 
        <p> 触达链接：{touchDtos2.linkUrl} </p>}
        <p> 触达时间：{formatTime(touchDtos2.cron)} </p>
        <p> 触达频率：被邀请后{touchDtos2.day}日触达一次，最多{touchDtos2.maxTouchTime}次 </p>
        </div>)

    return (
      <div>
        {/* {touchDtos1Nodes} */}
        {touchDtos2Nodes}
      </div>
    )
  }

  return (
    <div className="right-content treasure-detail">
    <Breadcrumb style={{ margin: '20px 0 40px 0' }}>
      {breads.map(x => <Breadcrumb.Item key={x.index}>
        {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
      </Breadcrumb.Item>)}
    </Breadcrumb>
   {dataNull && <div className="commonDetail-noresult bg-fff">
      <div className="page-title">老带新通用配置详情</div>
      <p className="notice-text">暂未设置通用配置</p>
      <Link
        to={{
          pathname: `/crm_user/commonCreate`
        }}
      >
      <Button type="normal" warning>立即配置</Button>
      </Link>
    </div>
    }
   {(!dataNull) && <div className="commonDetail-result"> 
      { canEdit && <div className="edit-common">
        <Link
          to={{
            pathname: `/crm_user/commonEdit/${type}`,
          
          }}
        >
          <Button type="normal" warning>修改配置</Button>
        </Link>
      </div> }
      <div
        style={{ marginTop: '10px', padding: '0 20px 30px' }}
        className="bg-fff plan-detail-content"
          >
        {/* 基本信息 */}
        <Title title="基础信息">
          <div className="rights-card">
            <div className="detail">
              <Row className="detail-row ">
                <Col className="lf-label">
                  <span>被邀请人下单时效</span>
                </Col>
                <Col className="lr-content">
                  {data.invitedTimeOut}
                </Col>
              </Row>
              <Row className="detail-row ">
                <Col className="lf-label">
                  <span>订单完成结算时效</span>
                </Col>
                <Col className="lr-content">
                  {data.orderFinishedTimeOut}
                </Col>
              </Row>
              <Row className="detail-row ">
                <Col className="lf-label">
                  <span>活动规则</span>
                </Col>
                <Col className="lr-content">
                  {data.offcial}
                </Col>
              </Row>
              <Row className="detail-row ">
                <Col className="lf-label">
                  <span>城市范围</span>
                </Col>
                <Col className="lr-content">
                  {data.cityTypeName}
                </Col>
              </Row>    
            </div>
          </div>
        </Title>
        {/* 触达配置 */}
        <Title title="触达配置">
          <div className="rights-card">
            <div className="detail">
              <Row className="detail-row ">
                <Col className="lf-label">
                  <span>触达配置选择</span>
                </Col>
                <Col className="lr-content">
                  {generateTouch(data)}
                </Col>
              </Row>
            </div>
          </div>
        </Title>
        </div>
      </div>
      }
    </div>
  )
}

export default CommonSetDetailWrap
