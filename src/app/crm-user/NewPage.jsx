// [[GBAI STAR]]
import React from 'react'
import { Button } from '@alifd/next'
import { useHistory } from 'react-router-dom'
import './crm_user.scss'

const NewPage = () => {
  const history = useHistory()

  const handleJump = () => {
    history.push('/crm_user/')
  }

  return (
    <div style={{ padding: '20px' }}>
      <div className="set-title">新页面</div>
      <div className="wrapper" style={{ marginTop: '20px' }}>
        <div className="crm-name">
          <span>新页面</span>
        </div>
        <div className="crm-content">
          <p>新页面</p>
          <div style={{ marginTop: '20px' }}>
            <Button type="primary" onClick={handleJump}>
              跳转
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NewPage
// [[GBAI END]]
