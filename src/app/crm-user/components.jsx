import React, { useState, useEffect, useContext, Component, useReducer } from 'react'
import { Step, Button, Grid, Select, Dialog, Message } from '@alifd/next'
import ImageUploader from '@/components/coupon/ImageUploader'
import ComponentMap from '@/packages/ali-form/componentMap'
import filterDataSource from '@/source/filterDataSource'
import Render from '@/packages/ali-form'
import { Filter, Table } from '@/packages/table/FilterTable'
import { request } from '@/packages/request'
import { config } from '@/api/config'
import parseUrl from '@/packages/request/parseUrl'

import "./crm_user.scss"

const { Row, Col } = Grid;
const Option = Select.Option;
const MAX_NUM = 2
const CRM_STATUS = [
  {
    value: 0,
    label: '待提交'
  },
  {
    value: 1,
    label: '待生效'
  },
  {
    value: 2,
    label: '生效中'
  },
  {
    value: 3,
    label: '暂停中'
  },
  {
    value: 4,
    label: '已关闭'
  },
  {
    value: 5,
    label: '已结束'
  }
]

let rightType = []

const columns = [
  {
    title: '活动ID',
    dataIndex: 'businessId'
  },
  {
    title: '活动名称',
    dataIndex: 'businessName'
  },
  {
    title: '业务模版',
    dataIndex: 'rightType',
    cell: value => {
      const result = rightType.find(x => x.rightType === value)
      return result ? result.rightName : ''
    }
  },
  {
    title: '活动时间',
    dataIndex: 'time',
    cell: (value, index, record) => `${record.startTime} - ${record.endTime}`
  },
  {
    title: '活动类型',
    dataIndex: 'status',
    cell: value => CRM_STATUS.find(x => +x.value === value).label
  },
  {
    title: '商品数量',
    dataIndex: 'goodsNum'
  },
  {
    title: '状态',
    dataIndex: 'statusStr'
  },
  {
    title: '创建人',
    dataIndex: 'creatorName'
  },
  {
    title: '操作',
    dataIndex: 'isCheck',
    cell: (value, index, record, { actionFunc }) => {
      const onChoose = () => {
        actionFunc(list => {
          const newID = record.businessId
          record.isCheck = !record.isCheck
          const isExist = list.find(x => x.businessId === newID)
          return isExist ? list.filter(x => x.businessId !== newID) : [...list, { businessId: newID, businessName: record.businessName }]
        })
      }
      // main/activity/manage/lookUp/{activityId}/{activityName}
      return <p><span><a href={`${config.ZHAO_SHANG}/main/activity/manage/lookUp/${record.businessId}/${record.businessName}`} target="_blank" rel="noopener noreferrer">查看</a></span> | <span onClick={onChoose} style={{ color: '#FF7C4D' }}>{value ? '已绑定' : '绑定'}</span></p>
    }
  }
]

const filterUISource = {
  'x-component': 'Filter',
  buttonLastPos: true,
  children: [
    {
      label: '活动ID',
      placeholder: '请输入招商活动ID',
      name: 'businessId',
      'x-component': 'Input'
    },
    {
      label: '活动名称',
      placeholder: '请输入招商活动名称',
      name: 'businessName',
      'x-component': 'Input',
    },
    {
      label: '创建人',
      placeholder: '请输入创建人',
      name: 'creatorName',
      'x-component': 'Input',
    },
    {
      label: '活动状态',
      placeholder: '请选择活动状态',
      name: 'status',
      'x-component': 'Select',
      dataSource: [{
        value: 1,
        label: '未开始'
      }, {
        value: 2,
        label: '活动中'
      }, {
        value: 3,
        label: '活动暂停'
      }, {
        value: 4,
        label: '活动取消'
      }, {
        value: 5,
        label: '活动结束'
      }]
    },
    {
      label: '招商类型',
      placeholder: '请选择招商类型',
      name: 'investmentType',
      'x-component': 'Select',
      dataSource: [
        {
          value: 1,
          label: '长期招商'
        },
        {
          value: 2,
          label: '限时招商'
        }
      ]
    }
  ]
}

const initialList = { value: [false], list: [[]] };
function listReducer(state, action) {
  switch (action.type) {
    case 'value':
      return { ...state, value: action.payload };
    case 'list':
      return { ...state, list: action.payload };
    case 'reset':
      return { ...initialList };
    case 'add':
      return { value: [...state.value, false], list: [...state.list, []] };
    default:
      throw new Error();
  }
}

const onComfirm = (formData, state, history, hasRight = true) => {
  // 为了兼容新类型的活动
  const searchParmas = parseUrl(window.location.href).query || {}

  let images = [];
  
  (formData.images || []).forEach((item,index) => {
    let temp = {}
    temp.url = item
    temp.official = ''
    images.push(temp)
  })


  request('crm://activity/create', {
    method: 'POST',
    body: {
      ...formData,
      images,
      // channelListReqDTOS: [],
      // deleteChannel: [],
      categoryId: searchParmas.categoryId || 3,
      ...hasRight && !state.value.some(x => !x) && {
        rights: state.value.map((val, i) => {
          return {
            rightType: val,
            rightName: rightType.find(x => x.rightType === val).rightName,
            willChoose: 0,
            businesses: state.list[i]
          }
        })
      }
    }
  }).then(res => {
    // console.log('创建成功了么')
    // console.log(res)
    if (String(res.errorCode) === "0" || res.errorDesc === 'success') {
      Message.show({
        type: 'success',
        content: '创建成功',
        afterClose: () => history.push('/crm_user/list')
      });
    } else {
      Message.error(res.msg || '创建失败')
    }
  }).catch(err => {
    console.log(err.message || '创建失败！')
  })
}

const ListTemp = ({ options, num, value, list, handleRemove, onChangeOption, handleBind }) => {
  return <div>
    <div className="crm-title">
      <span>权益{num + 1}</span>
      {value[num] && <Col fixedSpan="4"><Button type="normal" warning text onClick={handleRemove}>删除</Button></Col>}
    </div>
    <div className="crm-row">
      <Row align="center">
        <Col fixedSpan="4">权益类型</Col>
        <Col fixedSpan="20">
          <Select value={value[num]} className="crm-select" placeholder="请选择权益类型" onChange={onChangeOption} disabled={!options.length}>
            {options && options.map((x, n) => <Select.Option key={n} value={x.rightType}>{x.rightName}</Select.Option>)}
          </Select>
        </Col>
        {value[num] && <Col fixedSpan="4"><Button type="secondary" text onClick={handleBind}>去绑定</Button></Col>}
      </Row>
      {!!list[num].length && <Row align="center">
        <Col fixedSpan="4"></Col>
        <Col className="crm-detail" fixedSpan="20">
          <h2>已绑定 {list[num].length} 个招商活动ID</h2>
          {/* /main/activity/manage/lookUp/{activityId}/{activityName} */}
          <p>{list[num].length ? list[num].map(x => <a href={`${config.ZHAO_SHANG}/main/activity/manage/lookUp/${x.businessId}/${x.businessName}`} target="_blank" rel="noopener noreferrer">
            {x.businessId}
          </a>) : <span>目前还没有招商活动ID，请先去操作里面绑定～</span>}</p>
        </Col>
        <Col fixedSpan="4"></Col>
      </Row>}
    </div>
  </div>
}


const RightList = filterDataSource({
  requestImmediately: query => true,
  action: {
    url: `crm://activity/right/businessList`,
    method: 'POST',
    mapResponse: (json, { props: { list } }) => {
      list.forEach(x => {
        const res = json.data.resultData.find(y => y.businessId === x.businessId)
        if (res) {
          res.isCheck = true
        }
      })
      return {
        total: json.data.total,
        data: json.data.resultData
      }
    },
    mapRequest: (query, { props }) => {
      return {
        ...props.data,
        ...query
      }
    }
  }
})((props) => {
  console.log(props, '=====')

  const onOpenCheck = () => {
    Dialog.confirm({
      title: '去创建招商活动',
      content: `创建的内容需要在招商平台里操作，确认要去创建吗？`,
      onOk: () => window.open(`${config.ZHAO_SHANG}/main/activity/create`)
    })
  }
  return (
    <div>
      <p className="crm-name">
        <span>绑定招商活动</span>
        <Button type="normal" warning onClick={onOpenCheck}>创建招商活动</Button>
      </p>
      <Filter
        uiSource={filterUISource}
        {...props.filterProps}
        resetType="reset"
      />
      <Table
        className="boreas-table"
        {...props.tableProps}
        columns={columns}
        actionFunc={props.setList}
        emptyContent="暂无数据"
      />
      <section className="crm-rights">
        <h2>已绑定 <span>{props.list.length}</span> 个招商活动ID</h2>
        <p>{props.list.length ? props.list.map(x => <a href={`${config.ZHAO_SHANG}/main/activity/manage/lookUp/${x.businessId}/${x.businessName}`} target="_blank" rel="noopener noreferrer">
          {x.businessId}
        </a>) : <span>目前还没有招商活动ID，请先去操作里面绑定～</span>}</p>
      </section>
    </div>
  )
})

const RightChoose = ({ options, formData, state: { value, list }, dispatch }) => {
  const [visible, setVisible] = useState(false)
  const [num, setNum] = useState(0)
  const onVisible = () => setVisible(c => !c)
  const [singleList, setSingList] = useState(list[num])
  const data = {
    startTime: formData.startTime,
    endTime: formData.endTime,
    rightType: value[num]
  }

  const onChangeOption = (k) => {
    return (e) => {
      const val = [...value]
      const orignList = [...list]
      val[k] = e
      orignList[k] = []
      dispatch({ type: 'value', payload: val })
      dispatch({ type: 'list', payload: orignList })
    }
  }

  const handleBind = (k) => () => {
    onVisible()
    setNum(k)
    setSingList(list[k])
  }

  const handleRemove = (k) => () => {
    if (value.length === 1) {
      dispatch({ type: 'reset' })
    } else {
      const removeEle = x => x.filter((v, i) => i !== k)
      dispatch({ type: 'value', payload: removeEle(value) })
      dispatch({ type: 'list', payload: removeEle(list) })
    }
  }


  const onCheckChoose = () => {
    if (singleList.length > 20) return Message.error('最多支持绑定20个招商活动')
    const payload = [...list]
    payload[num] = singleList
    dispatch({ type: 'list', payload })
    onVisible()
  }

  const onCancelChoose = () => {
    setSingList(list[num])
    onVisible()
  }

  const onAddChoose = () => {
    dispatch({ type: 'add' })
    setSingList([])
  }

  return <section>
    {value.map((x, i) => {
      return <ListTemp num={i} key={i} value={value} list={list} options={options} handleRemove={handleRemove(i)} onChangeOption={onChangeOption(i)} handleBind={handleBind(i)} />
    })}
    {!!list[0].length && MAX_NUM > list.length && <p className="crm-add" onClick={onAddChoose}> ⊕ 新增一个权益</p>}
    <Dialog title=" "
      className="crm-content"
      visible={visible}
      isFullScreen
      closeable='close,esc,mask'
      onOk={onCheckChoose}
      onCancel={onCancelChoose}
      onClose={onVisible}>
      <RightList data={data} setList={setSingList} list={singleList} />
    </Dialog>
  </section>
}

export const StepNav = ({ data, state, stepAction = [], history }) => {
  const { current, formData } = state
  const Component = data[current].component
  return (<div className="wrapper">
    <Step current={current} shape="circle">
      {data && data.map(({ title }, i) => <Step.Item key={title} title={title} disabled={current < i} />)}
    </Step>
    <div className="crm-wrap">
      {Component ? <Component {...stepAction[current]} formData={formData} history={history} /> : null}
    </div>
  </div>)
}

export const UploadImg = (props) => {
  const [images, setImage] = useState(props.value)
  const handleRemove = props.onChange && props.onChange(null)
  const handleChange = (x) => {
    props.onChange && props.onChange(x)
    setImage(x)
  }

  return <ImageUploader
    {...props}
    onRemove={handleRemove}
    value={images}
    onChange={handleChange} />
}

export const Forms = ({ onCancel, onCheck }) => <React.Fragment><Render />
  <p style={{ textAlign: 'right' }}>
    <Button onClick={onCancel} style={{ marginRight: 10 }}>取消</Button>
    <Button onClick={onCheck} type="primary">下一步</Button>
  </p>
</React.Fragment>

export const Tables = ({ goBack, formData, history }) => {
  const [state, dispatch] = useReducer(listReducer, initialList)
  const [options, setOption] = useState([])
  useEffect(() => {
    request('crm://activity/right/list', {
      method: 'POST',
    }).then(res => {
      
      console.log('options',res.data)
      setOption(res.data)
      rightType = res.data
    }).catch(err => {
      console.log(err)
    })
  }, [])

  const handleComfirm = (hasRight) => {
    Dialog.confirm({
      title: '创建玩法',
      content: '确定要创建该活动玩法吗？',
      onOk: () => onComfirm(formData, state, history, hasRight),
      onCancel: () => console.log('cancel')
    })
  }

  return <React.Fragment>
    <RightChoose formData={formData} options={options} dispatch={dispatch} state={state} />
    <p className="crm-btns">
      <span className="detail" onClick={() => handleComfirm(false)}>跳过此步，直接提交保存 &gt;&gt;</span>
      <Button onClick={goBack} style={{ marginRight: 10 }}>上一步</Button>
      <Button onClick={handleComfirm} type="primary">保存</Button>
    </p>
  </React.Fragment>
}
