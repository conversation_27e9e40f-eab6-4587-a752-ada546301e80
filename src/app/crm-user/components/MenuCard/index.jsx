import React from 'react'
import './index.scss'
import { Link } from 'react-router-dom'
import iconHui from '@/assets/icon_hui.png'
import iconQuan from '@/assets/icon_quan.png'

const iconMap = {
  '老带新(券)': iconQuan,
  '会场招商': iconHui,
}

const pathMap = {
  '老带新(券)': '/crm_user/beltCreate',
  '会场招商': '/crm_user/create'
}

export default (props) => {
  return (
    <div>
      <div className="cardTitle">
        <span className="subTitle">潜客拉新</span>
        <span className="subDesc">针对新零售新客，包括纯新和半新用户</span>
      </div>
      <div className="cards">
        {Card(props.dataSource || [])}
      </div>
    </div>
  )
}

function Card(children) {
  return children.map((item, index) => {
    return (
      <div className="card-container" key={item.id}>
        <div className="card-icon">
          <img src={iconMap[item.name]} />
        </div>
        <div className="card-content">
          <p className="card-title">
            {item.name}
            <span className="card-memo">{item.parentName}</span>
          </p>
          <p className="sub-title">
            {item.subtitile}
          </p>
          <p className="ops">
            <Link
              style={{ display: item.name.indexOf('老带新') > -1 ? '' : 'none' }}
              className="card-link"
              to={{
                pathname: '/crm_user/commonDetail',
                search: `?type=${item.type}`
              }}>通用配置 &nbsp;&nbsp;|</Link>
            <Link
              className="card-link"
              to={{
                pathname: '/crm_user/list',
                search: `type=${item.type}&categoryId=${item.id}`
              }}>玩法列表 &nbsp;&nbsp;|</Link>
            <Link
              className="card-link"
              to={{
                pathname: pathMap[item.name],
                search: `type=${item.type}&categoryId=${item.id}`
              }}>创建活动</Link>
          </p>
        </div>
      </div>
    )
  })
}
