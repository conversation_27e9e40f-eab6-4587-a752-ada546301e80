.cardTitle {
  .subTitle {
    font-size: 22px;
  }

  .subDesc {
    color: #adadad;
    margin-left: 10px;
  }
}

.cards {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.card-container {
  display: flex;
  position: relative;
  background: #FFFFFF;
  border: 1px solid #EBEBEB;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.05);
  margin: 20px 0;
  width: 370px;
  flex-direction: row;
  padding: 20px;

  &:first-child {
    margin-right: 20px;
  }

  .card-icon {
    // flex:1 24px;
    margin-right: 8px;

    width: 42px;
    height: 42px;
    // border:1px solid #333;
    border-radius: 4px;
  }

  .card-content {
    flex: 9;
    display: flex;
    flex-direction: column;

    .card-title {
      font-size: 20px;
      color: #333333;
      letter-spacing: 0;
      line-height: 20px;
    }

    .sub-title {
      font-size: 12px;
      color: #999999;
      letter-spacing: 0;
      flex: 1;
      line-height: 22px;
    }

    .card-memo {
      background: #EBECF0;
      border-radius: 3px;
      border-radius: 3px;
      float: right;
      width: 64px;
      height: 20px;
      font-size: 12px;
      color: #666666;

      line-height: 12px;
      padding: 4px;
      text-align: center;
    }
  }

  .card-link {
    margin-left: 10px;
    font-size: 14px;
    color: #666666;
    letter-spacing: 0;
    text-align: center;
    line-height: 14px;

  }

  .ops {
    display: inline;
    float: right;
    line-height: 14px;
    margin: 0;
    align-self: flex-end;
  }
}

