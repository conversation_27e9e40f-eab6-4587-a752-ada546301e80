import React, { Component } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Grid } from '@alifd/next'
import '../crm_user.scss'

const { Row, Col } = Grid


class Card extends Component {
  render() {
    let data = this.props.dataSource
    return (
      <div className="card-wrap" key={data.id}>
        <div className="card-lf-title">
          {data.name}
          <br />
          {data.subtitle}
        </div>
        <div className="card-rt-ops">
          <Link
            to={{
              pathname: '/crm_user/list',
              search: `categoryId=${data.id}`
            }}
          >配置</Link>
        </div>
      </div>
    )
  }
}

class TypeWrap extends Component {

  generateCards = () => {
    let { dataSource } = this.props
    let nodes = dataSource.map((item, index) => {
      return (
        <Card key={index} dataSource={item} />
      )
    })

    return nodes
  }

  render() {
    return (
      <div className={'card-line'}>
        {this.generateCards()}
      </div>

    )
  }
}

class TypeCards extends Component {

  constructor(props) {
    super(props)
    console.log(props)
  }

  generateCards = () => {
    let { dataSource } = this.props;

    let nodes = (dataSource || []).map((category, index) => {
      return (<div key={index}>
        <h3>{category.name}</h3>
        <TypeWrap dataSource={category.children} />
      </div>)
    })

    return nodes;
  }

  render() {
    return (
      <div>
        {this.generateCards()}
      </div>
    )
  }
}

export default TypeCards
