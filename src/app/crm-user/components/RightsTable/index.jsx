/**
 * 绑定权益的表格
 */
import React, { useState, useEffect, useContext, Component, useReducer } from 'react'
import { Table, Form, Input, TimePicker, Grid, Dialog } from '@alifd/next';
import { Link } from 'react-router-dom'
import PlanModal from './PlanModal'
import { config } from '@/api/config'

import './index.scss'

 const RightsTable = (props) => {


const defaultColumns = [
  {
    title: '序号',
    dataIndex: 'id'
  },
  {
    title: '投放计划ID',
    dataIndex: 'planId'
  },
  {
    title: '投放计划名称',
    dataIndex: 'name'
  },
  {
    title: '创建人',
    dataIndex: 'creator'
  },
  {
    title: '操作',
    dataIndex: 'ops',
    cell: (value, index, record) => {
      return (
        <span onClick={() => handleRemoveFromPool(record)} className="boreas-cell">
          删除
        </span>
      )
    }
  }
]

  const columns = props.columns || defaultColumns
//   const data = props.data || []
  const [visible, setVisible] = useState(false)
  const [data, setData] = useState([])

  const modalProps = {
    visible,
    formData: {},
    state: {},
    onChange,
    onConfirm,
    onClose,
    onCancel
  }

  function handleRemoveFromPool(record) {
    console.log('record', record)
    console.log('props', props)
    setData(() => {
      return []
    })
    props.onChange([])
  }

  function onCancel() {
    setVisible(() => {
      return false
    })
  }

  function onClose() {
    setVisible(() => {
      return false
    })
  }

  function onChange(ids, records) {
    console.log('ids', ids)
    console.log('records', records)
    // setData(() => {
    //   return records
    // })
  }

  function onConfirm(list) {
      console.log(list)
      list = (list || []).map((item,index) => {
        return {
          ...item,
          id: index + 1
        }
      })
      setData(() => {
          return list
      })
      setVisible(() => {
        return false
      })

      props.onChange(list)
  }

  function onShowModal() {
    setVisible(() => {
      return true;
    })
  }
  const onOpenCheck = () => {
    Dialog.confirm({
      title: '去创建投放计划',
      content: `创建的内容需要在权益平台里操作，确认要去创建吗？`,
      onOk: () => window.open(`${config.PLAN_URL}`)
    })
  }

  return (<React.Fragment>
    <Table dataSource={data}>
      {columns.map((col,index) => {
        return <Table.Column key={index} {...col} />
      })}
    </Table>
    <div className="ops"> 
        <div className="left-add">
        <a className="add" onClick={onShowModal}>
        <i className="users-add" />添加邀请人权益</a>|
        <span>最多可添加{1}个</span>
        </div>
        <div className="right-ops">
        <span>无可用权益？</span>
        <a onClick={onOpenCheck} className="right-create">点击创建</a>
        </div>
        <PlanModal value={data} {...modalProps} />
    </div>
  </React.Fragment>
    
  )
 }

 export default RightsTable
