/**
 * 绑定邀请人权益
 */
import React, { useState, useEffect, useContext, Component, useReducer } from 'react'
import { Step, Button, Grid, Select, Dialog, Message } from '@alifd/next'
import ImageUploader from '@/components/coupon/ImageUploader'
import ComponentMap from '@/packages/ali-form/componentMap'
import filterDataSource from '@/source/filterDataSource'
import Render from '@/packages/ali-form'
import { Filter, Table } from '@/packages/table/FilterTable'
import { request } from '@/packages/request'
import { config } from '@/api/config'

let rightType = []

const columns = [
  {
    title: '投放计划ID',
    dataIndex: 'planId'
  },
  {
    title: '投放计划名称',
    dataIndex: 'name'
  },
  {
    title: '创建人',
    dataIndex: 'creator'
  }
]

const filterUISource = {
  'x-component': 'Filter',
  buttonLastPos: true,
  children: [
    {
      label: '投放计划ID',
      placeholder: '请输入投放计划ID',
      name: 'planId',
      'x-component': 'Input'
    }
  ]
}


const PlanModal = filterDataSource({
  requestImmediately: query => true,
  action: {
    url: `crm://activity/right/queryPlanManage`,
    method: 'POST',
    mapResponse: (json) => {
      console.log('投放计划列表', json)
      return {
        total: (json.data && json.data.total) || 0,
        data: (json.data && json.data.list) || []
      }

    },
    mapRequest: (query, { props }) => {
      return {
        ...props.data,
        currentPage: query.pageNum,
        pageSize: query.pageSize,
        planId: query.planId || ''
      }
    }
  }
})((props) => {
  const visible = props.visible
  const setVisible = props.setVisible

  const [planIds, setPlanIds] = useState([])
  const [chooseList, setChooseList] = useState([])
  const [rowSelection, setRowSelection] = useState({
    mode: 'single',
    onChange(ids, records) {
      rowSelection.selectedRowKeys = ids;
      setRowSelection(() => {
        return rowSelection
      });
      setPlanIds(ids)
      setChooseList(records)
    },
    selectedRowKeys: [],
  })


  useEffect(() => {
    // if (inited) {
    console.log('planModal ', '更新组件，重新渲染组件的状态')
    console.log(props.value)

  }, [props.value])

  const onOk = () => {
    // console.log(props)
    if (chooseList && chooseList.length === 0) {
      Message.error('请选择一个投放计划进行绑定')
      return
    }

    props.onConfirm(chooseList)
  }

  const onClose = () => {
    props.onClose()
  }
  const onOpenCheck = () => {
    Dialog.confirm({
      title: '去创建投放计划',
      content: `创建的内容需要在权益平台里操作，确认要去创建吗？`,
      onOk: () => window.open(`${config.PLAN_URL}`)
    })
  }


  return (

    <section>
      <Dialog
        title="绑定投放计划"
        className=""
        visible={visible}
        footerActions={['cancel', 'ok']}
        onOk={onOk}
        onCancel={onClose}
        onClose={onClose}
        style={{ maxWidth: 'auto' }}
      >
        <div>
          <Button
            type="normal" style={{ position: 'absolute', top: 14, right: 55 }} warning
            onClick={onOpenCheck}>去创建投放计划</Button>
          <Filter
            uiSource={filterUISource}
            {...props.filterProps}
            resetType="reset"
          />
          <Table
            className="boreas-table"
            {...props.tableProps}
            primaryKey='planId'
            rowSelection={rowSelection}
            columns={columns}
            actionFunc={props.setList}
            emptyContent="暂无数据"
          />
        </div>

      </Dialog>
    </section>
  )
})

export default PlanModal
