/**
 * 触达配置
 */
import React, { useState, useEffect, useContext } from 'react'
import { Checkbox, Radio, Balloon, Icon, Form, Input, TimePicker, Grid } from '@alifd/next';
import { useHistory } from 'react-router';
import FormContext from '@/packages/ali-form/FormContext'
import moment from 'moment'
import fusionInputFix from '@/utils/fusionInputFix'

import './index.scss'

const Tooltip = Balloon.Tooltip;
const { Group: CheckboxGroup } = Checkbox;
const RadioGroup = Radio.Group;
const defaultVal = moment('12:00:00', 'HH:mm:ss', true);

const { Row, Col } = Grid;
const FormItem = Form.Item;

const TextArea = fusionInputFix('textarea')(Input.TextArea)
const XInput = fusionInputFix()(Input)
const formItemLayout = {
    labelCol: {
        span: 6
    },
    wrapperCol: {
        span: 14
    }
};


const list = [
    {
        value: '1',
        label: '已被邀请未下单新客'
      }
];

const setList = [
  {
    value: '1',
    label: '短信'
  },
  {
    value: '2',
    label: '饿了么站内PUSH'
  }
]

 const TouchUp = (props) => {

  // console.log('touchup props',props)

  const title = props.title || '被邀请人的配置'
  const disabled = props.disabled || false
  const disabledText = props.disabledText || '暂不支持，敬请期待'
  const help = props.help || false
  const helpText = props.helpText || '触达配置是关于。。。在C端显示的触达方式等'
  console.log('---------------',props.value)
  const [check, setCheck] = useState([]) // 被选中的被邀请人配置值列表
  const [singleCheck, setSingleCheck] = useState('') // 已被邀请未下单新客触达配置
  const { formContext } = useContext(FormContext.ctx)
  // const [data, setData] = useState([]) // 组件的data
  const [message, setMessage] = useState('')
  const [linkUrl, setLinkUrl] = useState('')
  const [maxTouchTime, setMaxTouchTime] = useState('')
  const [day, setDay] = useState('')
  const [cron, setCron] = useState(null)



  function onChange(val) {
    // console.log('onChange', val)
    // console.log(check)
    setCheck(() => {
      return val;
    })
    
  }

  function onChangeCheckList(val) {
    // console.log('val',val)
    setSingleCheck(() => {
      return val
    })

    let data = {
      message,
      maxTouchTime,
      day,
      cron,
    }
    
    if (val === '1') {
      console.log(val)
      data.type = 'message'

    } else {
      console.log(val)
      data.linkUrl = linkUrl
      data.type = 'push'

    }
    
    props.onChange(data)
    
  }

  function onDeleteCheck() {
    setCheck(() => {
      return [];
    })
    setSingleCheck(() => {
      return '1';
    })

    setDay(() => {
      return ''
    })

    setCron(() => {
      return ''
    })

    setLinkUrl(() => {
      return ''  
    })

    setMaxTouchTime(() => {
      return ''
    })

    setMessage(() => {
      return ''
    })
  }

  useEffect(() => {
    // if (inited) {
     console.log('update','更新组件，重新渲染组件的状态')
     console.log(props.value)

    // }
    // console.log('update touchup 更新组件，重新渲染组件的状态')
    // console.log(props.value)
    if (props.value && props.value.type === 'message') {
      const { cron, message, maxTouchTime, day } = props.value
      setSingleCheck(() => {
        return '1';
      })
      setCheck(() => {
        return ['1'];
      })

      setCron(() => {
        return cron
      })
      setMessage(() => {
        return message
      })

      setMaxTouchTime(() => {
        return maxTouchTime
      })

      setDay(() => {
        return day
      })
    } else if (props.value && props.value.type === 'push') {
      const { cron, message, linkUrl, maxTouchTime, day } = props.value

      setSingleCheck(() => {
        return '2';
      })
      setCheck(() => {
        return ['1'];
      })

      setCron(() => {
        return cron
      })
      setMessage(() => {
        return message
      })

      setMaxTouchTime(() => {
        return maxTouchTime
      })

      setDay(() => {
        return day
      })

      setLinkUrl(() => {
        return linkUrl
      })
    }

    // props.onChange && props.onChange(props.value)
    
  }, [props.value])


  // useEffect(() => {
  //   // if (inited) {
  //     props.onChange && props.onChange(data)
  //   // }
  // }, [data])

  useEffect(() => {
    if (check) {
      console.log('update', check)
    }
  }, [check])

  function onChangeContent(val) {
    setMessage(() => {
        return val
    })
    let data = {
      message: val,
      linkUrl,
      maxTouchTime,
      day,
      cron,
    }
   // props.onChange(data)
  }

  function onChangeUrl(val) {
    setLinkUrl(() => {
          return val
      })

      let data = {
        message,
        linkUrl: val,
        maxTouchTime,
        day,
        cron,
      }
      props.onChange(data)
  }

  function onChangeTimes(val) {
    setMaxTouchTime(() => {
      return val
    })

    let data = {
      message,
      linkUrl,
      maxTouchTime: val,
      day,
      cron
    }
    props.onChange(data)
  }

  function onChangeDay(val) {
    setDay(() => {
      return val
    })
    let data = {
      message,
      linkUrl,
      maxTouchTime,
      day: val,
      cron
    }
    props.onChange(data)
  }

  function onChangeTouchTime(val) {
    setCron(() => {
      return val
    })
    let data = {
      message,
      linkUrl,
      maxTouchTime,
      day,
      cron: val,
    }
    props.onChange(data)
  }

  const toolBtn = <Icon className="help" type="help" size="small" />
  return (
    <div className={'touch-container'}>
      {title}：{disabled ? <div className={'disabled-text'}>{disabledText}</div> : 
      <CheckboxGroup value={check} dataSource={list} onChange={onChange} />}
      {help && <Tooltip trigger={toolBtn} align="tr">{helpText}</Tooltip>}
      {check && check.length > 0 && 
        <div className="set-container">
          <p>已被邀请未下单新客触达配置 &nbsp;&nbsp;<a type="primary" onClick={onDeleteCheck}>删除</a></p>
          <div>
            触达方式：<RadioGroup value={singleCheck} dataSource={setList} onChange={onChangeCheckList} />
          </div>
           
          <div className="message-wrap">
              <p>{title}</p>
              <Row className="message-row">
                <Col className="label-lf" span={6}>
                  触达内容：
                </Col>
                <Col span={18}>
                <TextArea maxLength={55} value={message} onChange={onChangeContent} placeholder="不超过55字" />
                </Col>
              </Row>
              {singleCheck === '2' && <Row className="message-row">
                <Col className="label-lf" span={6}>
                  跳转链接：
                </Col>
                <Col span={18}>
                  <XInput value={linkUrl} onChange={onChangeUrl} placeholder={'请输入push跳转链接'} name="url" />
                </Col>
              </Row>}
              <Row className="message-row">
                <Col className="label-lf" span={6}>
                  触达时间：
                </Col>
                <Col span={18}>
                  <TimePicker value={cron} onChange={onChangeTouchTime} />
                </Col>
              </Row>
              <Row className="message-row">
                <Col className="label-lf" span={6}>
                  触达频率：
                </Col>
                <Col span={18}>
                    被邀请后
                    <XInput type="number" value={day} onChange={onChangeDay} style={{ width: 60 }} />
                    日触达一次,
                    最多
                    <XInput type="number" value={maxTouchTime} onChange={onChangeTimes} style={{ width: 50 }} />
                    次 
                </Col>
              </Row>
            </div>
        </div>
      }
    </div>
  )
 }

 export default TouchUp
