import React, { Component, useState, useEffect } from 'react'
import { observer, inject } from 'mobx-react'
import { Link } from 'react-router-dom'
import PropTypes from 'prop-types'

import { Breadcrumb, Radio, Message, Form, Table, Button, Dialog, Grid, Collapse } from '@alifd/next'
import OpPlanHeader from '@/components/header'
import Title from '@/components/title'
import path from '@/api/modules/benefit'
import { request } from '@/packages/request'
import formatDate from '@/utils/formatDate'
import requestDataSource from '@/source/requestDataSource'
import { config } from '@/api/config'
// import BoxRuleShow from './BoxRuleShow'
import './crm_user.scss'

const { Row, Col } = Grid

const Panel = Collapse.Panel;
const breads = [
  {
    path: '/crm_user/list',
    name: '玩法配置'
  },
  {
    name: '玩法详情'
  }
]

@requestDataSource({
  action: {
    url: `crm://activity/info?id={query.id}`,
    method: 'POST'
  },
  mapStateToProps: ({ data }) => {
    console.log('detailData玩法的',data)
    return {
      detailData: data.data
    }
  }
})
class SettingDetailWrap extends Component {
  render() {
    const { detailData, history } = this.props

    return (
      <SettingDetail detailData={detailData} _history={history} />
    )
  }
}

function RightsCard({ data }) {
  function generateIds(businesses) {
    const nodes = businesses.map((item, index) => {
      return (
        <span>
          &nbsp;
          &nbsp;
          <a target={"_blank"} rel="noopener noreferrer" href={`${config.ZHAO_SHANG}/main/activity/manage/lookUp/${item.businessId}/${item.businessName}`}>
            {item.businessId}
          </a>
          &nbsp;
          &nbsp;
        </span>

      )
    })
    return nodes;
  }


  return (data || []).map((item, index) => {
    return (
      <div>
        <div className="rights-title">
          {item.rightName}
        </div>
        <div className="detail">
          <Row className="detail-row">
            <Col span={4} className="lf-label">
              权益名称
            </Col>
            <Col span={12} className="lr-content">
              {item.rightName}
            </Col>
          </Row>
          <Row className="detail-row">
            <Col span={4} className="lf-label">
              已绑定的招商活动ID
            </Col>
            <Col span={12} className="lr-content">
              {generateIds(item.businesses)}
            </Col>
          </Row>
        </div>
      </div>
    )
  })
}

function SettingDetail({ detailData, _history }) {
  const headerSource = {
    title: '玩法详情',
    renderCell: () => {
      return (
        <span
          className={`boreas-status boreas-status-${detailData.status}`}
          style={{ marginLeft: '20px' }}
        >
          {+detailData.status === 2 ? '已下线' : '生效中'}
        </span>
      )
    }
  }

  function init(images) {
    let node = images.map((item, index) => {
      return (
        <img className="detail-img" src={item} key={index} />
      )
    })
    return node;

  }

  // 根据玩法类型返回不同的RuleComponent

  function RuleComponet(str) {

    if (str === '老带新') {
      return <div>老带新</div>
    } else { // 其它的类型的
     return <Title title="权益设置">
      <div className="rights-card">
        <RightsCard data={detailData.rights} />
      </div>
    </Title>
    }
  }


  return (
    <div className="right-content treasure-detail">
      <Breadcrumb style={{ margin: '20px 0 40px 0' }}>
        {breads.map(x => <Breadcrumb.Item>
          {x.path ? <Link to={x.path}>{x.name}</Link> : x.name}
        </Breadcrumb.Item>)}
      </Breadcrumb>
      <div>
        <h3>{detailData.name}</h3>
      </div>
      <div
        style={{ marginTop: '10px', padding: '0 20px 30px' }}
        className="bg-fff setting-detail-content"
      >
        <div
          style={{ marginTop: '10px', padding: '0 20px 30px' }}
          className="bg-fff plan-detail-content"
        >
          {/* 基本信息 */}
          <Title title="基本信息">
            <div className="rights-card">
              <div className="detail">
                <Row className="detail-row ">
                  <Col className="lf-label">
                    <span>玩法大类：</span>
                  </Col>
                  <Col className="lr-content">
                    {detailData.parentCategoryStr}
                  </Col>
                </Row>

                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法类型：</span>
                  </Col>
                  <Col className="lr-content">
                    {detailData.categoryStr}
                  </Col>
                </Row>
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法标题：</span>
                  </Col>
                  <Col className="lr-content">
                    {detailData.name}
                  </Col>
                </Row>
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法副标题：</span>
                  </Col>
                  <Col className="lr-content">
                    {detailData.subtitle}
                  </Col>
                </Row>
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法说明：</span>
                  </Col>
                  <Col className="lr-content">
                    {detailData.description}
                  </Col>
                </Row>
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法图片：</span>
                  </Col>
                  <Col className="lr-content">
                    {detailData.images && detailData.images.length > 0 ? (<div>{init(detailData.images)}</div>) : null}
                  </Col>
                </Row>
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法生效时间：</span>
                  </Col>
                  <Col className="lr-content">
                    {formatDate(detailData.startTime)} ~ {formatDate(detailData.endTime)}
                  </Col>
                </Row>
                <Row className="detail-row">
                  <Col className="lf-label">
                    <span>玩法报名时间：</span>
                  </Col>
                  <Col className="lr-content">
                    {formatDate(detailData.signStartTime)} ~ {formatDate(detailData.signEndTime)}
                  </Col>
                </Row>
              </div>


            </div>
          </Title>

          {/* {RuleComponet(detailData.categoryStr)} */}
          <Title title="权益设置">
            <div className="rights-card">
              <RightsCard data={detailData.rights} />
            </div>
          </Title>
        </div>
      </div>
    </div>
  )
}

export default SettingDetailWrap
