export function buildEnv() {
    const host = window.location.host;
    try {
        const storeEnv = localStorage['force-env']
            // 线上强制走prod
        if (
            storeEnv && ['prod', 'ppe', 'pre', 'daily'].includes(storeEnv) &&
            !(['market.m.taobao.com', 'boreas.kunlun.alibaba-inc.com', 'boreas.ele.me'].includes(host))
        ) {
            return storeEnv
        }
    } catch (ex) {
        console.error('buildEnv error', ex)
    }

    if (['market.m.taobao.com', 'boreas.kunlun.alibaba-inc.com'].includes(host)) {
        return 'prod'
    } else if (['boreas.ele.me'].includes(host)) {
        return 'prod'
    } else if (['market.wapa.taobao.com'].includes(host)) {
        return 'daily'
    } else if (/.test/.test(window.location.host)) {
        return 'pre'
    } else if (['pre-boreas.ele.me'].includes(host)) {
        return 'pre'
    } else if (/localhost/.test(window.location.host)) {
        return 'localhost'
    } else if (['local.kunlun.alibaba.net:3333', 'local.kunlun.alibaba.net'].includes(window.location.host)) {
        return 'local'
    } else if (['boreas.kunlun.alibaba.net:3333'].includes(host)) {
        return 'local'
    } else if (host === 'local.kunlun.alibaba.net:3333') {
        return 'local'
    }
    return 'daily'
}