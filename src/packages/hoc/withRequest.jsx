import React, { useState, useEffect } from 'react'
import { Loading } from '@alifd/next'
import { request } from '@/packages/request'

export default function withRequest({ action, mapStateToProps = x => x }) {
  return Target => {
    function Proxy(props) {
      const [inited, setInited] = useState(false)
      const [data, setData] = useState(null)

      useEffect(() => {
        request(action, {}, { props })
        .then(res => {
          setInited(true)
          setData(res)
        }).catch(err => {
          console.log(err)
          setInited(true)
        })
      }, [])

      if (!inited) {
        return null
      }
      return <Target {...props} {...mapStateToProps({ data })} />
    }
    return Proxy
  }
}
