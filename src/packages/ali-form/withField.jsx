import React, { Component, useState, useEffect, useContext, useMemo } from 'react'
import { Form } from '@alifd/next'
import _ from 'lodash'
import FormContext from './FormContext'

const { withFormConsumer } = FormContext
const FormItem = Form.Item



function withField(Wrapper = React.Fragment) {

  return Target => {

    const FieldProxy = (_props) => {
      const { formContext } = useContext(FormContext.ctx)
      const { props, name } = _props
      const { rules, defaultValue } = props || {}
      const defaults = formContext.defaults

      const initialProps = useMemo(() => {
        const field = formContext.field.init(name, {
          initValue: defaultValue,
          rules
        })
        return {
          ...props,
          ...(defaults[name] || {}).props,
          value: field.value,
          error: ""
        }
      }, [])

      // 支持运行时设置参数
      /*
      * demo

      actGroup.addField('newUserNum', 'Input', Object.assign({}, timesHelper('开箱需要新用户数', 0, 99, 1), {
        placeholder: '支持输入数字0～99，不可超过宝箱成团人数',
        running(formContext) {
          return {
            disabled: formContext.field.getValue('smallBoxNum') !== 1
          }
        }
      }))
      * */
      if (props.running) {
        Object.assign(initialProps, props.running(formContext));
      }

      const [savedProps, setSavedProps] = useState(initialProps)
      const [dirty, setDirty] = useState(false)

      function handleChange(value) {
        setSavedProps(x => {
          x.value = value
          return { ...x }
        })
      }

      function handleMessage({ type, change }) {
        switch (type) {
          case 'change': {
            setSavedProps(currentProps => {
              return Object.assign({}, currentProps, change.props)
            })

            break
          }
          default:
            break
        }
      }

      useEffect(() => {
        formContext.listen(name, x => {
          handleMessage(x)
        })
      }, [])

      useEffect(() => {
        if (formContext.trigger) {
          formContext.trigger(name, savedProps.value)
          savedProps.triggle && formContext.emitChanges(savedProps.triggle, { props: formContext.uiSource.find(x => x.name === savedProps.triggle).props })
        }

        if (!dirty) {
          setDirty(true)
          return
        }
        const I = setTimeout(() => {
          setSavedProps(x => {
            const errors = formContext.field.getError(name)
            const verror = errors && errors.length > 0 ?
              errors[0] : ''



            if (x.error == verror) {
              return x
            }
            x.error = verror
            return { ...x }
          })
        }, 300)
        return () => {
          clearTimeout(I)
        }
      }, [savedProps.value])


      return formContext.componentMap.render(Target, savedProps, {
        value: savedProps.value,
        handleChange
      })

    }
    return FieldProxy
  }
}

export default withField
