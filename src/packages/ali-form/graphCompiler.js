import _ from 'lodash'
import 'graph'
import { fromJS } from 'immutable'
import Parser from './compiler/parser'


const Graph = window.Graph

function* traverseXComponent(obj) {
  if (obj && typeof obj === 'object') {
    if (obj.xComponent) {
      yield obj
    }
    for (let key in obj) {
      yield* traverseXComponent(obj[key])
    }
  }
}


function* findRules(obj, parent, key, path = []) {
  if (typeof obj === 'string') {
    if (obj.match(/\$.*\$/)) {
      delete parent[key]
      yield { source: obj, path }
    }
  }
  else if (typeof obj === 'object') {
    for (let _k in obj) {
      yield* findRules(obj[_k], obj, _k, path.concat(_k))
    }
  }
}


function buildGraph(config) {
  const g = new Graph()
  const parser = new Parser()

  const items = [...Array.from(traverseXComponent(config))]

  for (let item of items) {
    const rawRules = [...Array.from(findRules(item))]
    for (let rule of rawRules) {
      const { source, path } = rule
      const ast = parser.parse(source)
      const ids = Object.keys(ast.lexicalScope)
      const updator = (env) => {
        const value = ast.evaluate(env)
        if (value === undefined) {
          return ''
        }
        return value
      }

      ids.forEach(id => {
        const idPath = toPath(id)
        const [sourceName] = idPath

        if (!g.get(sourceName, item.name)) {
          g.dir(sourceName, item.name, [])
        }
        const edge = g.get(sourceName, item.name)
        edge.push({
          path,
          updator
        })
      })
    }

  }

  window._x = g
  return g
}

function toPath(str) {
  return str.split(/[[].]/).filter(x => x)
}

class GraphCompiler {

  constructor() {
    this.listeners = []
  }

  init(config, env) {
    this.graph = buildGraph(config)
    this.config = config
    this.env = fromJS(env)
    // console.log('init env', this.env.toJS())
  }

  listen = (handler) => {
    this.listeners.push(handler)

  }

  getDefaults() {

    // console.log('begin update', name, value)
    // 广度优先搜索
    const traversed = {}

    const queue = new Queue()

    for (let key in this.graph._graph) {

      queue.enqueue(key)
    }


    const changes = {}

    while (queue.size() > 0) {
      const cur = queue.dequeue()
      traversed[cur] = 1

      const outNodes = this.graph.adj(cur)
      if (!outNodes) {
        continue
      }


      for (let outNode of Object.keys(outNodes)) {
        if (traversed[outNode]) {
          continue
        }
        const updators = this.graph.get(cur, outNode)
        for (let { updator, path } of updators) {
          const v = updator(this.env)
          this.env = this.env.setIn(path, v)
          if (!changes[outNode]) {
            changes[outNode] = {}
          }
          _.set(changes[outNode], path, v)
        }
        queue.enqueue(outNode)
      }
    }
    // const finalChanges = Object.keys(changes).map(x => { return { name: x, change: changes[x] } })
    return changes
  }

  update(name, value) {
    if (!name) {
      throw `[form] name is undefined`
    }

    // console.log('begin update', name, value)
    // 广度优先搜索
    const traversed = {}
    const [nodeName] = toPath(name)
    // console.log('nodeName', nodeName)

    this.env = this.env.setIn(toPath(name), value)


    const queue = new Queue()
    queue.enqueue(nodeName)


    const changes = {}

    while (queue.size() > 0) {
      const cur = queue.dequeue()
      traversed[cur] = 1

      const outNodes = this.graph.adj(cur)
      if (!outNodes) {
        continue
      }


      for (let outNode of Object.keys(outNodes)) {
        if (traversed[outNode]) {
          continue
        }
        const updators = this.graph.get(cur, outNode)
        for (let { updator, path } of updators) {
          const v = updator(this.env)
          if (this.env.getIn(path) !== v) {
            this.env = this.env.setIn(path, v)
            if (!changes[outNode]) {
              changes[outNode] = {}
            }
            _.set(changes[outNode], path, v)

          }
        }
        queue.enqueue(outNode)
      }
    }
    const finalChanges = Object.keys(changes).map(x => { return { name: x, change: changes[x] } })
    this.listeners.forEach(handler => {
      handler(finalChanges)
    })

  }


}


class Queue {

  constructor() {
    this.data = []
    this.l = 0
    this.r = 0
    this.n = 0
    this.MAX = 10
  }

  enqueue(item) {
    if (this.n >= this.MAX) {
      throw 'queue overflow'
    }
    this.data[this.r++] = item
    this.n++
  }

  dequeue() {
    if (this.l === this.r) {
      throw 'queue inflow'
    }
    this.n--
    return this.data[this.l++]
  }

  size() {
    return this.n
  }

}

export default GraphCompiler
