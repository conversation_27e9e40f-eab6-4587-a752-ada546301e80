
import React, { Component } from 'react'
import { fromJS } from 'immutable'
import { Field } from '@alifd/next'
import GraphCompiler from './graphCompiler'

const ctx = React.createContext(null)



export default class FormContext {
  constructor(_this) {
    // 初始化field
    this.graphCompiler = new GraphCompiler()
    this.changeHandlers = []
    this.field = new Field(_this || {}, {
      parseName: true
    })
    this.handlers = {}

    // graphCompiler 监听变化
    this.graphCompiler.listen(this.handleUpdatorChange)
  }

  listenChange = (handler) => {
    this.changeHandlers.push(handler)
    return () => {
      this.changeHandlers = this.changeHandlers.filter(x => x !== handler)
    }

  }

  setComponentMap(map) {
    this.componentMap = map
  }

  init(uiSource, data) {
    const a = fromJS(uiSource)
    const b = a.toJS()
    this.uiSource = uiSource
    this.graphCompiler.init(b, data)
    this.defaults = this.graphCompiler.getDefaults()
  }


  getFormMeta() {
    return {
      uiSource: this.graphCompiler.config,
      data: this.graphCompiler.env.toJS()
    }

  }

  setFormMeta(type, data) { // 更新FormMeta
    return {
      uiSource: type === 'uiSource' ? data : this.graphCompiler.config,
      data: type === 'data' ? data : this.graphCompiler.env.toJS()
    }
  }

  handleUpdatorChange = changes => {
    for (let { name, change } of changes) {
      if (change.props && change.props.value) {
        this.field.setValue(name, change.props.value)
      }
      this.emitChanges(name, change)
    }
    this.changeHandlers.forEach(handler => {
      handler()
    })
  }

  trigger = (name, value) => {
    this.field.setValue(name, value)
    this.graphCompiler.update(name, value)
  }

  listen = (name, handler) => {
    this.handlers[name] = handler
  }

  emitChanges = (name, change) => {
    this.handlers[name] && this.handlers[name]({
      type: 'change',
      change
    })
  }
}

FormContext.Provider = ctx.Provider
FormContext.Consumer = ctx.Consumer
FormContext.ctx = ctx


FormContext.withFormConsumer = () => (Target) => {
  class ConsumerProxy extends Component {
    render() {
      const Consumer = ctx.Consumer
      return (
        <Consumer>{
          value => {
            return <Target {...this.props} formContext={value.formContext} />
          }
        }
        </Consumer>
      )
    }
  }
  return ConsumerProxy
}
