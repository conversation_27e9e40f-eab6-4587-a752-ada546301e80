import React, { Component } from 'react'
import { Form, Grid } from '@alifd/next'
import './default-render-style.scss'
import FormContext from '../FormContext'

const { Row, Col } = Grid

const FormItem = Form.Item

const defaultFormItemLayout = {
  labelCol: {
    span: 10
  },
  wrapperCol: {
    span: 14
  }
}



@FormContext.withFormConsumer()
export default class AlForm extends Component {


  renderItem = (item, index) => {


    if (Array.isArray(item)) {
      return item.map((x, i) => {
        return this.renderItem(x, i)
      })
    }

    else if (item.children) {
      return <div key={index} style={{ marginBottom: '30px' }}>
        <Row>
          <Col fixedSpan={12} style={{ textAlign: 'right', paddingRight: '10px' }}>
            <h2 style={{ fontSize: '16px', marginBottom: '10px' }}>{item.title}</h2>
          </Col>
        </Row>
        {item.children && item.children.map((subItem) => {
          return this.renderItem(subItem)
        })}
      </div>
    }
    else if (item.xComponent) {
      const C = this.props.formContext.componentMap.get(item.xComponent)
      if (!C) {
        throw `[form] component with ${item.xComponent} not registed.`
      }

      return <C {...item} key={item.name} />

    }
    return null
  }

  render() {
    const { formContext } = this.props
    // const { uiSource } = formContext.getFormMeta()
  
    let uiSource = formContext.uiSource
    formContext.setFormMeta('uiSource', uiSource)

    return <Form key={this.props.key} style={{
      // width: '80%',
      backgroundColor: 'white',
      padding: '10px',
      margin: '10px 0 10px 0',
      paddingRight: '120px'
    }}
      {...defaultFormItemLayout}>
      {this.renderItem(uiSource)}
    </Form>
  }
}

