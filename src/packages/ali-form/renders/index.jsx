import React, { Component } from 'react'
import { Form } from '@alifd/next'
import COMPOMENT_MAP from '../componentMap'
import Title from '@/components/title'
// import formUISource from './source/uisource'
import './style.css'
import withForm from '../withForm'
import withField from '../withField'

const FormItem = Form.Item

@withForm.withFormConsumer()
export default class AlForm extends Component {



  renderItem = (item) => {

    if (item.children) {
      return <div>
        {item.children && item.children.map((subItem) => {
          return this.renderItem(subItem)
        })}
      </div>
    }

    if (item.xComponent) {

      const C = COMPOMENT_MAP[item.xComponent]
      const ProxyC = withField()(C)

      return <FormItem key={item.name}>
        <ProxyC {...item} />
      </FormItem>
    }
    return null
  }

  render() {
    const { formContext } = this.props
    // const { uiSource } = formContext.getFormMeta()
    let uiSource = formContext.uiSource
    formContext.setFormMeta('uiSource', uiSource)
    
    return <div>
      {this.renderItem(uiSource)}
    </div>
  }
}

