import React from 'react'
import {
  Form,
  Input,
  NumberPicker,
  DatePicker,
  Checkbox,
  Radio,
  Select
} from '@alifd/next'
import UploadImger from '@/components/UploadImg'
import withField from './withField'
import fusionInputFix from '@/utils/fusionInputFix'

const RadioGroup = Radio.Group

const { RangePicker } = DatePicker


const defaultItemLayout = {
  labelCol: { fixedSpan: 12 }
}

function defaultFieldRender(C, props, {
  value,
  handleChange
}) {
  const isRequired = props.rules && (Array.isArray(props.rules) ? props.rules.some(x => x.required) : props.rules.required)
  const listenChange = (e) => {
    if (e === null || e === undefined) return
    props.onChange && props.onChange(e)
    handleChange && handleChange(e)
  }
  return <Form.Item
    validateState={props.error ? 'error' : 'success'}
    {...defaultItemLayout} label={props.label} error={props.error} required={isRequired}>
    <C
      {...props}
      onChange={listenChange}
      value={value}
      label=""
    // {...props.hash && { key: props.name || props.label }}
    />
  </Form.Item>
}

const defaultOptions = {
  wrap: true,
  fieldRender: defaultFieldRender
}

export default class ComponentMap {

  static inst = null

  static getInst() {
    if (!ComponentMap.inst) {
      ComponentMap.inst = new ComponentMap()
    }
    return ComponentMap.inst
  }

  constructor(options) {
    options = { ...defaultOptions, ...options }
    this.wrap = options.wrap
    this.options = options

    this.init()
  }

  get(type) {
    return this.components[type]
  }

  render(Target, props, options) {
    return this.options.fieldRender(Target, props, options)
  }


  init() {
    this.components = {}
    this.addComponent("Input", fusionInputFix()(Input))
    this.addComponent("NumberPicker", NumberPicker)
    this.addComponent("Select", Select)
    this.addComponent("RangePicker", RangePicker)
    this.addComponent("RadioGroup", RadioGroup)
    this.addComponent("CheckboxGroup", Checkbox.Group)
    this.addComponent("NumberPicker", NumberPicker)
    this.addComponent("TextArea", fusionInputFix('textarea')(Input.TextArea))
    this.addComponent('UploadImger', UploadImger)
  }

  addComponent(type, Compo) {

    if (this.components[type]) {
      return
    }
    if (this.wrap) {
      // console.log('type', type)
      this.components[type] = withField()(Compo)
    } else {
      this.components[type] = Compo
    }
  }
}

