### 表单支持
1. rules 表单校验  同fusion
2. valueRule 关联规则
  ```
  {
    xComponent: 'Input',
    name: 'avg',
    valueRule: '$(amount+10)/num$',
  }
  ```
3. cell 自定义dom渲染
   ```
   {
     xComponent: 'Cell',
     name: 'desc',
     props: {
       label: 'cell 测试:'
     },
     cell: record => {
       console.log(record)
       return <div>123</div>
     }
   }
   ```
4. props/onKeyDown onFocus onBlur  onPressEnter
   ```
   {
     props: {
        label: '投放计划名称:',
        onKeyDown: (k, v, record) => {
          console.log('hhh', k, v, record)
        }
     }
   }
   ```
5. label/placeholder 支持变量和函数自定义render
  ```
   {
      xComponent: 'Input',
      name: 'num',
      // initValue: 1,
      props: {
        label: '投放数量$name$:'
      }
    },
    ```
    ```
    {
      xComponent: 'Input',
      name: 'total',
      // initValue: '$amount*num$',
      valueRule: '$amount*num$',
      props: {
        label: env => {
          return `总金额${env.total || ""}:`
        }
      }
    }
    ```
6. datasource支持自定义条件渲染
   ```
    {
      xComponent: 'Select',
      name: 'status',
      props: {
        label: '投放计划状态:',
        valueRender: item => {
          return <div>{item.label}item</div>
        },
        dataSource: env => {
          console.log(env)
          return [
            {
              value: '0',
              label: '全部'
            },
            {
              value: '1',
              label: '生效中'
            },
            {
              value: '2',
              label: '已下线'
            }
          ]
        }
      }
    }
    ```
7. 交互支持
   visible 控制显隐
   ```
   {
      xComponent: 'Input',
      name: 'num',
      visible: '$status!=2$',
      // initValue: 1,
      props: {
        label: '投放数量$name$:'
      }
    }
   ```
8. compilerExp
  ```
  const  graphUpdator = new GraphUpdator()
   const r = graphUpdator.compilerExp('$map.base.name$', {
      map: {
        name: 1,
        status: false,
        base: {
          name: 2
        }
      }
    })

    console.log(r)
  ```
8. dataSource 异步更新
  ```
    {
      xComponent: 'Select',
      name: 'testShow',
      // initValue: '',
      props: {
        label: '异步获取数据展示:',
        // valueRender: item => {
        //   return <div>{item.label}item</div>
        // },
        dataSource: (env, context) => {
          let r = []
          setTimeout(() => {
            r = [
              {
                value: 'kk',
                label: 'test'
              }
            ]
            context.dispatch('forceUpdate', 'children[7].props.dataSource', r)
          }, 10000)
          return r
        }
      }
    }
  ```

9.  jj
   



需要支持： '$status!==2$' ${status === 1} 

1. $name$ 判断是否有某个值
2. $name ? 1: 2 $


#### 需变更： 
1. context.addRule('name', 'status', () => {

}).pipe(() => {})
需要支持： '$$' ${status === 1} $?name$ 判断是否有某个值


context.addRule('name', 'status.datasource', ())





