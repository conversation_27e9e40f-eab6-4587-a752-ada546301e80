import React, { Component, useState, useEffect } from 'react'
import _ from 'lodash'
import { request } from '@/packages/request'
import FormContext from './FormContext'
import ComponentMap from './componentMap'


export const useFormContext = (options) => {

  const [savedContext, setSavedContext] = useState(null)
  const [errors, setErrors] = useState([{}])
  const {
    uiSource,
    requestAction,
    submitAction,
    fieldRender,
    props,
    onContextInit
  } = options

  useEffect(() => {
    if (requestAction && requestAction.isActive({ props })) {
      request(requestAction, {}, { props }).then(data => {
        initContext(data)
      }).catch(err => {
        console.warn(err)
      })
    }
    else {
      initContext({})
    }

  }, [])

  useEffect(() => {
    let remove = null
    if (savedContext) {
      remove = savedContext.listenChange(_.debounce(asyncValidate))
    }
    return () => {
      remove && remove()
    }
  }, [savedContext])

  function initContext(data) {

    const context = new FormContext()
    const componentOptions = { wrap: true }
    if (fieldRender) {
      componentOptions.fieldRender = fieldRender
    }
    context.setComponentMap(new ComponentMap(componentOptions))
    options.onContextInit && options.onContextInit(context)
    context.field.setValues(data)
    context.init(uiSource, data)
    setSavedContext(context)
  }


  function asyncValidate() {
    savedContext.field.validate((errors, values) => {
      if (!errors) {
        errors = {}
      }
      setErrors(Object.keys(errors).map(x => errors[x]))
    })
  }


  function submit() {
    return new Promise((resolve, reject) => {

      savedContext.field.validate((errors, data) => {
        // const values = this.formContext.field.getValues()

        if (errors) {
          const arr = Object.keys(errors).map(x => errors[x])
          reject({
            error: arr[0].errors[0]
          })
          return
        }
        request(
          submitAction,
          { method: 'POST', body: data },
          { props }
        ).then(json => {
          resolve(json)
        })
          .catch(ex => {
            reject(ex)
          })
      })
    })
  }

  return [savedContext, submit, errors]

}

function withForm(options) {
  return Target => {

    const FormProxy = (props) => {
      const [formContext, submit, errors] = useFormContext({
        ...options,
        props
      })
      const Provider = FormContext.Provider
      const Consumer = FormContext.Consumer
      if (!formContext) {
        return null
      }
      return (
        <Provider
          value={{
            formContext
          }}
        >
          <Target
            formContext={formContext}
            submit={submit}
            errors={errors}
            {...props}
          />
        </Provider>
      )
    }

    return FormProxy
  }
}


export default withForm
