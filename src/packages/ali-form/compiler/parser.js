const lexer = require('./lexer')
const exprParser = require('./exprParser')
const { Id, _String, Numeral, Expr } = require('./ast')


class Parser {

  parse(sourceCode) {
    this.tokens = lexer(sourceCode)
    this.lookahead = this.tokens[0]
    this.index = 0
    const stmt = this.parseStmt()
    stmt.buildLexicalScope()
    return stmt
  }

  match(x) {

    if (this.lookahead.value === x) {
      this.lookahead = this.tokens[++this.index]
    } else {
      throw `[form compiler] syntax error : expect ${x} but ${this.lookahead.value} found`
    }


  }

  // Stmt -> $ (BranchStmt | Expr) $
  // BranchStmt = [ BranchExprs ]
  parseStmt() {
    this.match('$')
    const expr = this.parseExpr()
    this.match('$')
    return expr
  }


  parseExpr() {
    return exprParser(this)
  }

  parseFactor() {
    const val = this.lookahead.value
    if (this.lookahead.type === 'id') {
      this.match(this.lookahead.value)
      return new Id(val)
    } else if (this.lookahead.type === 'string') {
      this.match(this.lookahead.value)
      return new _String(val)
    }
    else if (this.lookahead.type === 'number') {
      this.match(val)
      return new Numeral(val)
    }
    else {
      throw `[form compiler] expect a factor but ${this.lookahead.value} found`
    }
  }
}

module.exports = Parser


// const parser = new Parser()

// {
//   block: [

//   ]
//   groups: [
//     {
//       name: 'fulladdress',
//       xCompoment: 'select',
//       children: [{
//         xCompoment: 
//       }]
//       visible: true,
//       props: {
//         value: '$person.name+":"+person.address$'
//       }
//     }
//   ]
// }

// const x = [{
//   name: 'fulladdress',
//   xCompoment: 'select',
//   visible: true,
//   props: {
//     value: '$person.name+":"+person.address$'
//   }
// }]


// const ast1 = parser.parse('$1+2==3*1$')
// 1 2 3 
// + ==
// ast1.print()
// const ast2 = parser.parse(`$status == (1*100/100) ? (status == 1, '已审核'; status == 2, 'OK')+person.address:'未审核'$`)


// ast2.print()
// console.log(ast1.evaluate({
//   person: {
//     name: 'ramroll',
//     address: 'beijing'
//   },
//   status: 1,
//   b: 2,
//   c: 3
// }))


// console.log(ast2.evaluate({
//   person: {
//     name: 'ramroll',
//     address: 'beijing'
//   },
//   status: 1,
//   b: 2,
//   c: 3
// }))

// console.log(ast2.lexicalScope)
