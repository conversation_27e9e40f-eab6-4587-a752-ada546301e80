
const KEYWORDS = {
  bind: 1
}

function makeToken(type, value) {
  return { type, value }
}

function lexer(str) {

  const tokens = []

  for (let i = 0; i < str.length;) {
    const char = str[i]

    if (char === '"' || char === '\'') {
      const token = string(str, i)
      const len = token.value.length
      token.value = token.value.replace(/^['"]/, "").replace(/['"]$/, '')
      tokens.push(token)
      i += len

    }
    else if (char.match(/[-+\*/&|=!;()?:,]/)) {
      // console.log(char)
      const token = op(str, i)
      tokens.push(token)
      i += token.value.length
    }
    else if (char.match(/[0-9]/g)) {
      let v = ''
      for (let j = i; j < str.length; j++) {
        const strJ = str[j]
        if (strJ.match(/[0-9\.]/)) {
          v += strJ
        } else {
          i = j
          tokens.push(makeToken('number', Number(v)))
          // console.log('here', str[i])
          break
        }
      }
    }
    else if (char.match(/[A-Za-z_]/)) {
      const token = literal(str, i)
      tokens.push(token)
      i += token.value.length
    }
    else if (char === '$') {
      i++
      tokens.push(makeToken('begin/end', '$'))

    }
    else if (char.match(/[\s]/)) {
      i++
      continue
    }
    else if (char === '[' || char === ']') {
      i++
      tokens.push(makeToken('op', char))
    }
    else if (char === '{' || char === '}') {
      i++
      tokens.push(makeToken('op', char))
    }
    else if (char === 'eof') {
      break
    }
    else {
      throw `[form compiler] unexpected char ${char}`
    }

  } // end for
  return tokens
}



function string(
  sourceCode,
  index
) {
  let state = 0
  let str = ''

  function getNextChar() {
    return sourceCode[index++]
  }

  while (true) {
    switch (state) {
      case 0: {
        const c = getNextChar()
        if (c === '\'') {
          str += c
          state = 1
        }
        else if (c === '"') {
          str += c
          state = 2
        } else {
          throw `[form compiler] unexpected char ${c}`
        }
        break
      }
      case 1: {
        const c = getNextChar()
        str += c
        if (c === '\'') {
          return makeToken('string', str)
        }
        else if (c === '\\') {
          state = 3
        }
        if (c === 'eof') {
          throw `[form compiler] lexical error`
        }
        break
      }
      case 2: {
        const c = getNextChar()
        str += c
        if (c === '\"') {
          return makeToken('string', str)
        }
        else if (c === '\\') {
          state = 4
        }
        if (c === 'eof') {
          throw `[form compiler] lexical error`
        }
        break
      }
      case 3: {
        const c = getNextChar()
        str += c
        if (c === 'eof') {
          throw `[form compiler] lexical error`
        }
        state = 1
        break
      }
      case 4: {
        const c = getNextChar()
        str += c
        if (c === 'eof') {
          throw `[form compiler] lexical error`
        }
        state = 2
        break
      }
      default:
    }
  }
}

function literal(
  sourceCode,
  index
) {
  let state = 0
  let str = ''

  function getNextChar() {
    return sourceCode[index++]
  }

  while (true) {
    switch (state) {
      case 0: {
        const c = getNextChar()
        if (c.match(/[A-Za-z_]/)) {
          str += c
          state = 1
        } else {
          throw 'not a illegal operator'
        }
        break
      }
      case 1: {
        const c = getNextChar()
        if (c === '.') {
          str += c
          state = 2
        } else if (c === '[') {
          str += c
          state = 3
        }
        else if (c.match(/[A-Za-z0-9_]/)) {
          str += c
        } else if (KEYWORDS[str]) {
          return makeToken('keyword', str)
        } else {
          return makeToken('id', str)
        }
        break
      }
      case 2: {
        const c = getNextChar()
        if (c.match(/[A-Za-z0-9_]/)) {
          str += c
          state = 1
        }
        break
      }
      case 3: {
        const c = getNextChar()
        if (c.match(/[0-9]/)) {
          str += c
          state = 4
        } else {
          throw '[form compiler] lexical error'
        }
        break
      }
      case 4: {
        const c = getNextChar()
        if (c === ']') {
          str += c
          state = 1
        }
        else if (c.match([0 - 9])) {
          str += c
        }
        break
      }
      default:
    }
  }

}


function op(sourceCode, index) {
  let state = 0
  let operator = ''

  while (true) {

    const c = sourceCode[index++]
    operator += c

    switch (state) {
      case 0: {
        switch (c) {
          case '+':
            state = 1
            break
          case '-':
            state = 2
            break
          case '*':
          case '/':
            return makeToken('op', operator)

          case '=':
            state = 5
            break
          case '&':
            state = 6
            break
          case '|':
            state = 7
            break
          case '>':
            state = 8
            break
          case '<':
            state = 9
            break
          case '!':
            state = 10
            break
          case '(':
          case ')':
          case ':':
          case ';':
          case '?':
          case ',':
            return makeToken('op', operator)
          default:
            throw `[form compiler] ${c} not an op`
        }
        break

      }
      case 1: {
        if (c === '+') {
          return makeToken('op', '++')
        }
        return makeToken('op', '+')
      }
      case 2: {
        if (c === '-') {
          return makeToken('op', '--')
        }
        return makeToken('op', '-')
      }

      case 5: {
        if (c === '=') {
          return makeToken('op', '==')
        }
        return makeToken('op', '=')
      }
      case 6: {
        if (c === '&') {
          return makeToken('op', '&&')
        }
        return makeToken('op', '&')

      }
      case 7: {
        if (c === '|') {
          return makeToken('op', '||')
        }
        return makeToken('op', '|')
      }
      case 8: {
        if (c === '=') {
          return makeToken('op', '>=')
        }
        return makeToken('op', '>')
      }
      case 9: {
        if (c === '=') {
          return makeToken('op', '<=')
        }
        return makeToken('op', '<')
      }
      case 10: {
        if (c === '=') {
          return makeToken('op', '!=')
        }
        return makeToken('op', '!')
      }
      default:
        throw `[form compiler] ${c} not an op`
    }

  }

}

// function print(tokens) {
//   console.log(tokens.map(x => x.value).join(' '))
// }

// const tokens1 = lexer(`$name+1$`)
// const tokens2 = lexer(`$name+100/money$`)
// const tokens3 = lexer(`$bind(x)$`)
// const tokens4 = lexer(`$
//   [
//     x==1 '100';
//     x==2 '200';
//     '300'
//   ]
// $`)

// print(tokens1)
// print(tokens2)
// print(tokens3)
// print(tokens4)

module.exports = lexer
