
const _ = require('lodash')

class Factor {
  constructor(value) {
    this.value = value
  }

  print(level) {
    console.log(''.padStart(level * 2) + this.value)
  }

  evaluate() {
    return this.value
  }

  buildLexicalScope(scope) {

    if (this instanceof Id) {
      scope[this.value] = 1
    }
  }

}

class Id extends Factor {
  evaluate(env) {
    const path = this.value.split(/[\[\]\.]/).filter(x => x)
    const value = env.getIn(path)
    if (!value) {
      return ""
    }
    return value
    // return _.get(env, path)
    // return env[this.value]
  }
}


class Numeral extends Factor { }

class _String extends Factor { }


class Expr {
  constructor(op, left, right) {
    this.op = op
    this.left = left
    this.right = right
  }

  buildLexicalScope(scope) {
    if (!scope) {
      this.lexicalScope = {}
    } else {
      this.lexicalScope = scope
    }

    this.left && this.left.buildLexicalScope && this.left.buildLexicalScope(this.lexicalScope)
    this.right && this.right.buildLexicalScope && this.right.buildLexicalScope(this.lexicalScope)

  }

  print(level = 0) {
    const pad = ''.padStart(level * 2)
    console.log(pad + this.op)
    this.left && this.left.print(level + 1)
    this.right && this.right.print(level + 1)
  }

  evaluate(env) {
    if (this.op === '<branch>') {
      const satisfy = this.left.evaluate(env)
      if (satisfy) {
        return this.right.evaluate(env)
      }
      else {
        return false
      }
    }



    if (this.op === '?') {

      const leftValue = this.left.evaluate(env)
      if (leftValue) {
        return this.right.left.evaluate(env)
      } else {
        return this.right.right.evaluate(env)
      }
    } else if (this.op === ';') {
      const leftValue = this.left.evaluate(env)
      if (!leftValue) {
        return this.right.evaluate(env)
      } else {
        return leftValue
      }
    }

    switch (this.op) {
      case '+':
        return this.left.evaluate(env) + this.right.evaluate(env)
      case '-':
        return this.left.evaluate(env) - this.right.evaluate(env)
      case '*':
        return this.left.evaluate(env) * this.right.evaluate(env)
      case '/':
        return this.left.evaluate(env) / this.right.evaluate(env)
      case '>':
        return this.left.evaluate(env) > this.right.evaluate(env)
      case '>=':
        return this.left.evaluate(env) >= this.right.evaluate(env)
      case '<':
        return this.left.evaluate(env) < this.right.evaluate(env)
      case '<=':
        return this.left.evaluate(env) <= this.right.evaluate(env)
      case '==':
        return this.left.evaluate(env) == this.right.evaluate(env)
      case '&&':
        return this.left.evaluate(env) && this.right.evaluate(env)
      case '||':
        return this.left.evaluate(env) || this.right.evaluate(env)
      case '!=':
        return this.left.evaluate(env) != this.right.evaluate(env)
      case ',':
        return this.left.evaluate(env) ? this.right.evaluate(env) : ''
      default:
        throw `[form compiler] unexpected op ${this.op}`
    }
  }
}



module.exports = {
  Expr,
  Id,
  _String,
  Numeral

}
