import _ from 'lodash'
import 'graph'
import parser from './parser'

class Compiler {
  constructor({ config, env }, action) {
    this.config = config
    this.env = env
    // 建立关联关系表
    this.graph = new window.Graph()
    // 绘制关联关系表
    this.drawGraph()
  }

  drawGraph = () => {
    if (Object.prototype.toString.call(this.config) === '[object Object]') {
      this.drawGraphPoint(this.config, 'config')
    } else if (Object.prototype.toString.call(this.config) === '[object Array]') {
      this.config.forEach((v, i) => {
        this.drawGraphPoint(v, 'config[i]')
      })
    }
    console.log('rrrrrr', this.graph._graph)
  }

  drawGraphPoint = (_config, _path) => {
    const name = _config.name
    if (_.has(_config, 'xComponent')) {
      // 配置必须含有xCompoment，才读取关联配置关系
      // 读取xComponent同一层
      this.draw(name, _config, _path)
      // 读取xComponent下一层
      this.draw(name, _config.props, `${_path}.props`)
    }
    if (_.has(_config, 'children')) {
      _config.children.forEach((item, index) => {
        this.drawGraphPoint(
          {
            ...item,
            name: name || item.name
          },
          `${_path}.children[${index}]`
        )
      })
    }
  }

  draw = (_name, __config, __path) => {
    _.keys(__config).forEach(item => {
      const expression = __config[item]
      if (expression && typeof expression === 'string') {
        const expressionArr = expression.toString().match(/(.*)(\$.*\$)(.*)$/)
        if (!expressionArr) {
          return expression
        }
        const [x, before, _expression, after] = expressionArr
        const ids = parser.parseIds(_expression)
        ids.forEach(v => {
          if (_name !== v) {
            const from = {
              from: v,
              to: _name,
              path: `${__path}.${item}`,
              rule: expression,
              updator: () => {
                return this.compileExp(_expression, this.env)
              }
            }
            // 绘制graph
            this.graph.set(v, _name, from)
          }
        })
      }
    })
  }

  compileExp = (expression, _env = {}) => {
    if (!expression) {
      return expression
    } else if (expression && typeof expression === 'function') {
      return expression(_env)
    }
    const expressionArr = expression.toString().match(/(.*)(\$.*\$)(.*)$/)
    if (!expressionArr) {
      return expression
    }

    const [x, before, ruleExpression, after] = expressionArr

    const ast = parser.parse(ruleExpression)
    let compiledValue = ast.evaluate(_env)
    if (isNaN(compiledValue)) {
      compiledValue = ''
    }

    if (before || after) {
      return `${before}${compiledValue}${after}`
    } else {
      return compiledValue
    }
  }

  // 更新关联表 及相应值
  trigerUpdate = (name, env = {}) => {
    if (!this.graphMap[name]) {
      return null
    }
    let updateValues = {}
    const queue = this.graphMap[name]
    while (queue.length > 0) {
      const { rule, to, updator } = queue.shift()
      updateValues[to] = updator(rule, env)
    }

    return updateValues
  }
}

export default Compiler
