import { Validator } from 'jsonschema'
import formUISource from './uisource'

const formSchema = {
  id: 'form',
  type: 'object',
  properties: {
    children: {
      type: 'array',
      items: {
        $ref: '/ComponentItem'
      }
    }
  },
  required: ['children']
}

const componentItemSchema = {
  id: '/ComponentItem',
  type: 'object',
  required: ['xComponent', 'name'],
  properties: {
    name: {
      type: 'string'
    },
    xComponent: {
      type: 'string'
    },
    props: {
      type: 'object'
    }
  }
}

function compiler(jsonConfig) {
  /* JSON格式校验 */

  const validator = new Validator()

  const v = validator.validate(jsonConfig, formSchema)
  console.log(v)
  // if (errors) {
  //   throw errors
  //     .map((error, i) => {
  //       return `(${i + 1})配置JSON校验失败：${errors[0].property} ${
  //         errors[0].message
  //       }`
  //     })
  //     .join('\n')
  // }
}

compiler(formUISource)
