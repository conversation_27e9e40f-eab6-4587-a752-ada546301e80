
export default class CreateFormHelper {

  constructor() {
    this.groups = []
    this.fieldAdd = false
  }

  createGroup(title) {
    if (this.fieldAdd) {
      throw `[form] when CreateFormHelper.addField have been used you cannot create group`
    }
    const g = new Group(title)
    this.groups.push(g)
    return g
  }

  addField(name, type, props, options) {
    if (this.groups.length > 1) {
      throw `[form] group size > 1, please use group.add`
    }
    if (this.groups.length === 0) {
      this.fieldAdd = true
      this.groups.push(new Group())
    }

    this.groups[0].add(name, type, props, options)
  }


  toJSON() {

    if (this.groups.length === 0) {
      throw '[form] a form configuration must have at least on group'
    }

    else if (this.groups.length === 1) {
      return this.groups[0].children
    }
    else {
      return this.groups.map(x => {
        return {
          title: x.title,
          children: x.children
        }
      })
    }

  }
}

class Group {
  constructor(title) {
    this.title = title
    this.children = []
  }

  addField(name, type, props, options = {}) {
    this.children.push({
      name,
      xComponent: type,
      props,
      ...options
    })
    return this
  }

  modifyField(name, type, props, options = {}) {
    const result = this.children.find(x => x.name === name)
    if (!result) {
      throw `[form] when CreateFormHelper.modifyField have been used you cannot add field`
    }
    result.props = props
    Object.assign(result, { ...options, hash: Math.random().toString(36).slice(-4) })
    // return this
  }
}
