import _ from 'lodash'
import qs from 'qs'
import resolve from './resolve'
import Mock from '@/api/mock'
import parseUrl from '@/packages/request/parseUrl'
import DomainConfigure from './DomainConfigure'

class RequestAction {
  constructor(option, options, templateParams) {
    let resolved = null
    this.options = options
    this.templateParams = templateParams

    if (typeof option === 'string') {
      resolved = resolve(option, templateParams)
    } else {
      resolved = resolve(option.url, templateParams)
      if (option.method) {
        options.method = option.method
      }
      this.onRequest = option.onRequest
      this.onResponse = option.onResponse
      this.mapResponse = option.mapResponse
      this.mapRequest = option.mapRequest
    }
    this.resolved = resolved
  }

  isMock() {
    return this.resolved.type === 'mock'
  }

  mock() {
    if (this.options.method === 'GET') {
      return Mock[this.resolved.domain][this.resolved.path]()
    } else {
      return Mock[this.resolved.domain][
        this.resolved.path + '.' + this.options.method.toLowerCase()
      ]()
    }
  }

  pipeRequest(payload, templateParams) {
    this.onRequest && this.onRequest()
    const currentQuery = parseUrl(window.location.href).query

    if (this.mapRequest) {
      return this.mapRequest(payload, templateParams)
    }
    return payload
  }

  pipeResponse = (json, templateParams) => {
    emit('response', json)

    this.onResponse && this.onResponse(json)
    if (this.mapResponse) {
      return this.mapResponse(json || { data: {} }, templateParams)
    } else {
      return json
    }
  }
}

let outerHeaders = {}
if (window.location.pathname.includes('cardcoupon')) {
  const ssot = window.localStorage.getItem('SKYWORK_KUNLUN_SSOT')
  outerHeaders = {
    "x-ele-platform": "new_kunlun",
    "x-ele-newkunlun-token": ssot,
  }
}

export async function request(url, options, templateParams = {}) {
  options = _.merge(
    {
      method: 'GET',
      query: {},
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    },
    options
  )

  const action = new RequestAction(url, options, templateParams)

  if (action.isMock()) {
    const json = action.mock()
    const result = action.pipeResponse(json)
    return result
  }

  try {
    let resp = null
    if (action.resolved.type === 'mtop') {
      let payload = {
        ...action.resolved.query,
        ...options.query,
        ...options.body
      }
      payload = action.pipeRequest(payload, templateParams)
      const json = await mTop(action, options, payload)
      const result = action.pipeResponse(json)
      return result
    } else {
      let turl = typeof action.resolved === 'string' ? action.resolved : `${action.resolved.protocol}://${action.resolved.baseurl}${
        action.resolved.pathname
        }`

      let payload = null
      if (options.method === 'GET') {
        payload = {
          ...action.resolved.query,
          ...action.options.query
        }
        payload = action.pipeRequest(payload, {
          ...templateParams,
          query: parseUrl(window.location.href).query
        })
        const tqstr = qs.stringify(payload)
        turl = turl ? turl + '?' + tqstr : turl
        resp = await fetch(turl, options)
      } else {
        if (typeof options.body !== 'string') {
          if (options.body instanceof Array) {
            payload = action.pipeRequest(
              options.body,
              { ...templateParams, query: parseUrl(window.location.href).query }
            )
          } else {
            payload = action.pipeRequest(
              {
                ...action.resolved.query,
                ...options.query,
                ...options.body
              },
              { ...templateParams, query: parseUrl(window.location.href).query }
            )
          }
          if (options.body instanceof FormData) {
            delete options.headers['Content-Type']
          } else {
            options.body = JSON.stringify(payload)
          }
        }
        resp = await fetch(turl, options)
      }

      let json = await resp.json()

      if (request.globalResponseMapper) {
        json = request.globalResponseMapper({
          ...json,
          status: resp.status,
          options,
          url
        })
      }

      json = action.pipeResponse(json, templateParams)
      return json
    }
  } catch (ex) {
    throw ex
  }
}

let errorHandler = null

async function mTop(action, options, payload) {
  let mtopEnv = DomainConfigure.mtop[action.resolved.env]
  const tag = mtopEnv.tag
  window.lib.mtop.config.prefix = mtopEnv.prefix // mtop的前缀 默认‘acs’
  window.lib.mtop.config.subDomain = mtopEnv.subDomain // mtop的子域 默认‘’
  window.lib.mtop.config.mainDomain = mtopEnv.mainDomain // mtop的主域 默认‘taobao.com’

  let moptions = {
    api: action.resolved.pathname.replace(/^\//g, ''), // 必须
    v: '1.0', // 必须
    data: payload,
    type: 'GET', // options.method,
    ecode: 0, // 必须（客户端请求必备参数，可简单理解为需要登录的接口 1，反之0）
    dataType: 'json',
    tb_eagleeyex_scm_project: window.localStorage.getItem(
      'tb_eagleeyex_scm_project',
    ),
    headers: outerHeaders,
  }

  try {
    if (window.sessionStorage.getItem('token')) {
      moptions.data.ssoToken = window.sessionStorage.getItem('token')
      window.sessionStorage.removeItem('token')
    }
    console.log('moptions', moptions)
    // 如果外部传入了POST,则使用POST发送，王浩然
    if (options.method === 'POST') {
      moptions.type = 'POST'
    }
    const data = await window.lib.mtop.request(moptions)
    return Promise.resolve(data)
  } catch (res) {
    console.error(res)
    if (errorHandler) {
      if (errorHandler(res)) {
        return null
      }
    }

    throw {
      ...res
    }
    // return Promise.reject(new Error('接口错误！').message)
  }
}

request.setCommonErrorHandler = handler => {
  errorHandler = handler
}

let handlers = []
request.registerGlobalEventEmitter = handler => {
  handlers.push(handler)
}

function emit(type, data) {
  for (let i = 0; i < handlers.length; i++) {
    handlers[i](type, data)
  }
}
