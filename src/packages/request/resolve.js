import _ from 'lodash'
import qs from 'qs'
import DomainConfigure from './DomainConfigure'
import parseUrl from '@/packages/request/parseUrl'
import { buildEnv } from '@/packages/env'

/**
 * 适配规则
 *
 */
function resolve(url, data) {
  /*
   * 将 xxx{abc.def} 转成  `xxx${data['abc']['def']}`
   */
  // console.log("查看一下请求的地址")
  // console.log(url)

  if (!url) {
    throw new Error(`url requried`)
  }
  if (data) {
    url = resolveTemplate(url, data)
  }

  const regex = /^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/
  let [, protocol, , , pre, path, query] = url.match(regex)
  if (!protocol) {
    throw new Error(`url 规则不符合规范 ` + url)
  }
  query = query ? query.replace(/^[?]*/g, '') : ''
  if (protocol.match(/^(http|https):/)) {
    return url
  }

  const pathname = '/' + pre + path
  let [domain, env] = protocol.split(/[+:]/)
  if (env === 'mock') {
    return {
      type: 'mock',
      domain,
      path: query ? `${pathname}?${query}` : pathname
    }
  }
  if (!env) {
    env = buildEnv()
  }
  if (!domain) {
    domain = 'boreas'
  }

  const domainConf = DomainConfigure[domain]
  if (!domainConf) {
    throw new Error(`domain ${domain} not exists`)
  }

  const baseurl = domainConf[env]
  if (!baseurl) {
    throw new Error(`domain:${env}'s baseurl not exists`)
  }

  // console.log('查看')
  // console.log(domain)

  return {
    type: domain === 'mtop' ? 'mtop' : 'https',
    // eslint-disable-next-line no-nested-ternary
    protocol: domain === 'mtop' ? 'mtop' : 'https',
    domain,
    env,
    baseurl,
    pathname,
    query: qs.parse(query)
  }
  // if (!baseurl) {
  //   return `${pathname}?${query}`
  // }
  // if (query) {
  //   return `https://${baseurl}${pathname}?${query}`
  // }
  // return `https://${baseurl}${pathname}`
}

function resolveTemplate(url, data) {
  data.query = parseUrl(window.location.href).query
  return url.replace(/\{([^{}]+)\}/g, m => {
    const key = m.replace(/[{}]/g, '')
    const value = _.get(data, key)
    return value || ''
  })
}

module.exports = resolve
