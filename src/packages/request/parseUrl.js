import qs from 'qs'

export default function parseUrl(url) {
  const regex = /^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/
  let [, protocol, , , host, path, query, , fragment, , ,] = url.match(regex)
  query = query ? query.replace(/^[?]*/g, '').replace('/', '') : ''
  if (fragment && fragment.indexOf('?') !== -1) {
    const [a, b] = fragment.split('?')
    query = { ...query, ...qs.parse(b) }
  }
  return {
    hasQuery: query.length > 0,
    urlpath: `${protocol}//${host}${path}`,
    protocol,
    host,
    path,
    query: qs.parse(query) || {}
  }
}

window.parseUrl = parseUrl
