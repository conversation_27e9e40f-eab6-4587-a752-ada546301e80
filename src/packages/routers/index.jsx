import React, { Component, Suspense, lazy, useEffect } from 'react'
import PropTypes from 'prop-types'
import { withRouter, Route, Switch, Redirect } from 'react-router-dom'
import { StaticRouter } from 'react-router'
import MyBreadcrumb from '@/components/breadcrumb'
// import Permission from '@/packages/permission'

@withRouter
class Routers extends Component {
  static propTypes = {
    routerList: PropTypes.arrayOf(PropTypes.object),
    redirectPath: PropTypes.string,
    // eslint-disable-next-line react/forbid-prop-types
    breadcrumbMap: PropTypes.object
  }

  static defaultProps = {
    routerList: [],
    redirectPath: '/',
    breadcrumbMap: {}
  }

  render() {
    return (
      <div className="router-wrap">
        <Route
          render={routerParams => {
            routerParams.breadcrumbMap = this.props.breadcrumbMap
            return <MyBreadcrumb {...routerParams} />
          }}
        />
        <Switch>
          {this.props.routerList.map(router => {
            const { subRouterPath, subComponent, routerItems } = router
            if (!routerItems || routerItems.length === 0) {
              return (
                <Route key={subRouterPath} exact path={subRouterPath} component={subComponent} />
              )
            }
            return routerItems.map(({ path, component }) => (
              <Route key={path} exact path={`${subRouterPath}${path}`} component={component} />
            ))
          })}
          <Redirect from="/*" to={this.props.redirectPath} />
        </Switch>
      </div>
    )
  }
}

export default Routers
