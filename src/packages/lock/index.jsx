import React from 'react'
import ReactDOM from 'react-dom'
import { Loading } from '@alifd/next'

import './lock.scss'


let lockDOM = null
const tipsStyle = {
  position: 'absolute',
  width: '313px',
  left: '-140px',
  top: '-25px',
  background: '#fff',
  color: '#666',
  fontSize: '14px',
  height: '122px',
  paddingTop: '84px',
  zIndex: -1,
  borderRadius: '4px'
}
export function lockScreen(tip) {
  if (!lockDOM) {
    lockDOM = document.createElement('div')
    lockDOM.classList.add('lock-container')
    ReactDOM.render(
      <Loading
        size="medium"
        tip={<div style={tipsStyle}>{`${tip || ""}`}</div>}
        style={{
          width: "100%",
          height: "100%"
        }}
      ></Loading>,
      lockDOM
    );
    document.body.append(lockDOM)
  }

  lockDOM.style.display = 'block'

}

export function releaseScreen() {
  lockDOM
    ? lockDOM.style.display = 'none'
    : null
}
