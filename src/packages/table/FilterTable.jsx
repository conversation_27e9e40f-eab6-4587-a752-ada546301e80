import React, { Component, useState, useEffect } from 'react'
import { Table as FusionTable, Pagination } from '@alifd/next'
import _ from 'lodash'
import MFilter from '@/packages/filter'

import './style.scss'

export function Table(props) {
  const {
    columns,
    total,
    pageSize,
    changePageSize,
    search,
    reload,
    actionFunc,
    defaultCurrent,
    updateRow,
    ...others
  } = props

  function injectCell() {
    return columns.map(x => {
      const y = { ...x }
      if (y.cell) {
        const ocell = x.cell
        y.cell = (value, index, record) => {
          return ocell(value, index, record, {
            search,
            changePageSize,
            updateRow,
            reload,
            actionFunc
          })
        }
      }
      return y
    })
  }
  return (
    <React.Fragment>
      <FusionTable {...others}>
        {injectCell(columns).map((column, i) => {
          return <FusionTable.Column key={i} {...column} />
        })}
      </FusionTable>
      {total && others.dataSource.length ? (
        <Pagination
          className="boreas-pagination"
          total={total}
          totalRender={() => `共${total}条记录`}
          defaultCurrent={defaultCurrent}
          current={defaultCurrent}
          pageSizeSelector={'dropdown'}
          pageSizePosition="end"
          pageSizeList={[10, 20, 50, 100]}
          shape={'arrow-only'}
          onPageSizeChange={changePageSize}
          pageSize={+pageSize}
          onChange={no => {
            search({ pageNum: no })
          }}
        />
      ) : null}
    </React.Fragment>
  )
}

export function Filter(props) {
  const { search, onEvent, ...others } = props
  function handleFilterEvent(...args) {
    onEvent && onEvent(...args)
    const [e, data] = args
    if (e === 'reset') {
      _.assign(data, { pageNum: 1, pageSize: props.defaultPageSize || 10 })
    }
    if (e === 'query' || e === 'reset') {
      return new Promise((resolve, reject) => {
        search(data)
          .then(() => resolve())
          .catch(() => reject())
      })
    }
  }

  return (
    <div className="boreas-table">
      <MFilter {...others} onEvent={handleFilterEvent} />
    </div>
  )
}
