export default {
  id: 'rule-a20eef4e-c2f2-42b4-19e7-f2690ae2cd71',
  // 'title': '',
  description: '',
  type: 'object',
  'x-component': 'Table',
  // 'pagination': null,
  pagination: {
    pageSizeSelector: 'dropdown',
    pageSizePosition: 'end',
    pageSizeList: [10, 20, 50],
    shape: 'arrow-only',
    onPageSizeChange: 'handlePageSizeChange',
  },
  emptyContent: '暂无数据',
  children: [
    {
      title: 'ID',
      dataIndex: 'coupon_name',
      width: 140,
    },
    {
      title: '名称',
      dataIndex: 'rule',
    },
    {
      title: '创建人',
      dataIndex: 'date',
    },
    {
      title: '创建时间',
      dataIndex: 'total',
    },
    {
      title: '操作',
      dataIndex: 'opreate',
      cell: true,
    },
  ],
}
