import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { toJS } from 'mobx'
import { Table, Pagination } from '@alifd/next'
import Title from '../../components/title'
import tableUISource from './uisource'
import './style.scss'

class MyTable extends Component {
  static propTypes = {
    dataSource: PropTypes.arrayOf(PropTypes.object),
    // eslint-disable-next-line react/forbid-prop-types
    uiSource: PropTypes.object,
    onEvent: PropTypes.func,
    total: PropTypes.number,
    setCellProps: PropTypes.func,
    setRowProps: PropTypes.func
  }

  static defaultProps = {
    uiSource: tableUISource,
    dataSource: [],
    onEvent: () => {},
    setCellProps: () => {},
    setRowProps: () => {},
    total: 0
  }

  constructor(props) {
    super(props)
    this.state = {
      loading: false
    }
  }

  renderCell = (value, index, record) => {
    const handleCellClick = _item => {
      this.props.onEvent('cell', _item, record)
    }
    return (
      <div className="boreas-cell">
        {value && value.length
          ? value.map(item => (
            <span onClick={handleCellClick.bind(this, item)}>
              {React.createElement(React.Fragment, null, item)}
            </span>
          ))
          : null}
      </div>
    )
  }

  renderParent = domData => {
    // console.log('dataSource', toJS(this.props.dataSource))
    const xComponent = domData['x-component']
    return (
      <React.Fragment key={domData.label}>
        {/* title */}
        {domData.title && Title(domData)}
        {/* table */}
        {xComponent === 'Table' && (
          <React.Fragment>
            <Table
              dataSource={this.props.dataSource}
              loading={this.state.loading}
              emptyContent={this.props.uiSource.emptyContent}
              key={domData.id}
              cellProps={this.props.setCellProps}
              rowProps={this.props.setRowProps}
            >
              {domData.children &&
                domData.children.map(child => {
                  if (child.cell) {
                    return (
                      <Table.Column
                        key={child.title}
                        className="only-bottom-border"
                        {...child}
                        cell={this.renderCell}
                      />
                    )
                  }
                  return (
                    <Table.Column key={child.title} className="only-bottom-border" {...child} />
                  )
                })}
            </Table>
            {domData.pagination && this.props.dataSource.length ? (
              <Pagination
                {...domData.pagination}
                total={this.props.total}
                onChange={this.hanldePageChange}
                onPageSizeChange={this.handlePageSizeChange}
                className="boreas-pagination"
                totalRender={() => (this.props.total ? `共${this.props.total}条记录` : '')}
              />
            ) : null}
          </React.Fragment>
        )}
      </React.Fragment>
    )
  }

  handlePageSizeChange = size => {
    this.props.onEvent('pageSizeChange', 'size', size)
  }

  hanldePageChange = page => {
    this.props.onEvent('pageChange', 'page', page)
  }

  render() {
    const { dataSource, uiSource } = this.props
    if (!uiSource || !dataSource) {
      return null
    }
    return <div className="boreas-table">{this.renderParent(uiSource)}</div>
  }
}

export default MyTable
