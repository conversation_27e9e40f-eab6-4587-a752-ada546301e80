import React, { Component } from 'react'
import PropTypes from 'prop-types'

import { Form, Grid, Field, Icon } from '@alifd/next'

// import inlineUISource from './uisource'
import './index.scss'

const FormItem = Form.Item
const { Row, Col } = Grid

const formItemLayout = {
  // labelCol: { fixedSpan: 4 },
  // inline: true,
  labelCol: { span: 9 },
  wrapperCol: { span: 15 }
}

// const arrayCut = (arr = [], num = 3) => {
//   let j = 0
//   let o = j
//   const newArray = []
//   while (j < arr.length) {
//     j += num
//     newArray.push(arr.slice(o, j))
//     o = j
//   }
//   return newArray
// }

class InlineItem extends Component {
  static defaultProps = {
    onEvent: () => {},
    data: {
      discount: 2,
      platform_subsidy: 3
    },
    operation: null
  }

  // constructor(props) {
  //   super(props)
  //   this.state = {
  //     operation: true,
  //   }
  // }

  componentWillReceiveProps(prev, next) {
    console.log(prev)
    console.log(next)
  }

  handleMinusLine = e => {
    const { index } = this.props
    this.props.onMinus(index)
    this.props.onEvent(e)
  }

  render() {
    // const children = React.Children.map(this.props.children, (child) => <li>{child}</li>)
    console.log(this.props.data)
    return (
      <Row>
        {this.props.children}
        <Col span={2}>
          <Form.Item>
            <Icon
              type="minus"
              size="xs"
              onClick={this.handleMinusLine}
              style={{ display: this.props.operation && this.props.index !== 0 ? '' : 'none' }}
            />
          </Form.Item>
        </Col>
      </Row>
    )
  }
}

InlineItem.propTypes = {
  onEvent: PropTypes.func,
  operation: PropTypes.bool,
  data: PropTypes.objectOf(PropTypes.string)
}

class Inline extends Component {
  field = new Field(this, {
    onChange: (name, value) => {
      this.props.onEvent('change', name, value)
    }
  })

  static defaultProps = {
    onEvent: () => {},
    dataSource: [
      {
        type: '1',
        discount: '2',
        platform_subsidy: '3'
      },
      {
        type: '1',
        discount: '2',
        platform_subsidy: '3'
      }
    ],
    operation: null
  }

  // constructor(props) {
  //   super(props)
  //   //   this.state = {
  //   //     dataList: arrayCut(inlineUISource.children),
  //   //   }
  // }

  handleAdd = () => {
    console.log('正在操作添加')
    const newData = this.props.dataSource
    const data = this.props.initInline
    console.log(data)
    data.index = this.props.dataSource.length
    newData.push(data)
    // 不对，array
    this.props.onChange(newData)
    console.log(newData)
  }

  handleMinus = index => {
    console.log(index)
    const { dataSource } = this.props
    dataSource.splice(index, 1)
    this.props.onChange(dataSource)
  }

  handleEvent = e => {
    console.log('inlineItem有什么操作么')
    console.log(e)
  }

  render() {
    const { operation, max, add, minus } = this.field
    console.log(this.props.operation)
    const len = this.props.dataSource.length
    return (
      <div>
        <Row>
          <Col span={2}>
            <div
              onClick={this.handleAdd}
              style={{ display: this.props.operation === false ? 'none' : '' }}
            >
              <Icon type="add" size="xs" />
              添加规则
            </div>
          </Col>
        </Row>
        {this.props.dataSource &&
          this.props.dataSource.map((item, index) => (
            <div key={index}>
              <InlineItem
                onMinus={this.handleMinus}
                data={item}
                index={index}
                operation={this.props.operation}
                onEvent={this.handleEvent}
              >
                {this.props.children}
              </InlineItem>
            </div>
          ))}
      </div>
    )
  }
}

Inline.propTypes = {
  onEvent: PropTypes.func,
  operation: PropTypes.bool,
  dataSource: PropTypes.arrayOf(PropTypes.object)
}
export default Inline
