import _ from 'lodash'



function formatSource(responseData, dataSource) {
  const r = {}
  if (!dataSource) { // 如果没有定义要转换的格式，就原样返回数据
    return responseData
  }


  // 定义了接口格式
  function format(data, parent) {
    if (typeof data !== 'object') {
      return
    }


    Object.keys(data).forEach((item) => {
      if (typeof data[item] === 'object') {
        const v = parent ? `${parent}.` : ''
        format(data[item], `${v}${item}`)
      } else {
        const fv = data[item].replace && data[item].replace('$.', '')
        if (parent) {

          if (parent.indexOf('.') > -1) {
            if (!r[parent]) { // 父级第一次没有应该初始化
              r[parent] = {}
            }
            console.log('兼容一下parent有.的情况')
            console.log(parent)
            r[`${parent}`][item] = _.get(responseData, fv)
          } else {
            if (!r[parent]) { // 父级第一次没有应该初始化
              r[parent] = {}
            }
            r[parent][item] = _.get(responseData, fv)
          }

        } else {
          r[item] = _.get(responseData, fv)
        }
      }
    })
  }

  format(dataSource)
  // 返回格式化后的数据r
  return r
}

export default formatSource


