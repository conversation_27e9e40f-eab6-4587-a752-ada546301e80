
/**
 * 例子
 * 投放详情接口格式对应
 */

const planDetail = {
  result: {
    errNum: '$.errorCode',
    errMsg: '$.errorMessage',
    createAt: '$.value.gmtCreate',
    updateAt: '$.value.gmtCreate',
    planId: '$.value.planId',
    status: '$.value.status',
    targetList: '$.value.targetObjects',
    timeRange: '$.value.timeRange',
    data: { // 试一下嵌套的可以兼容不。暂时不允许超过2层
      type: '$.value.type',
      name: '$.value.name'
    }
  },
}

export default planDetail
