import _ from 'lodash'
//  读取json配置 -》 生成唯一id
// dataSource 乐高 type(function/json/ajax)
const couponDataSource = [
  {
    url: '/mktnewretail/main/getactivity',
    params: {
      activity_id: '$.activity_info.activity_id',
    },
    result: {
      baseData: {
        activityId: '$.activity_id',
        activityName: '$.name',
        platform: '$.platform',
        group: '$.activity_info.group',
      },
      ruleData: {
        creator: '$.rule_info.creator',
        // [{"price":10,"discount":1,"platform_subsidy":1,"shop_rate":0,"agent_rate":0}]
        benifits: '$.rule_info.benifits',
      },
      test: '$.test',
    },
  },
]

export function formatSource(responseData, dataSource) {
  const r = {}
  function format(data, parent) {
    if (typeof data !== 'object') {
      return
    }
    Object.keys(data).forEach((item) => {
      if (typeof data[item] === 'object') {
        const v = parent ? `${parent}.` : ''
        format(data[item], `${v}${item}`)
      } else {
        const fv = data[item].replace('$.', '')
        if (parent) {
          r[`${parent}.${item}`] = _.get(responseData, fv)
        } else {
          r[item] = _.get(responseData, fv)
        }
      }
    })
  }
  format(dataSource[0].result)
  Object.keys(r).forEach((item) => {
    _.set(dataSource[0].result, item, r[item])
  })
  return dataSource
}


export default couponDataSource
