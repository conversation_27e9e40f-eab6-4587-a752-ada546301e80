.newtade-190 {
  // width: 190px !important;
}

.newtade-260 {
  width: 260px !important;
}

// .boreas-filter {
//   .next-form-item-label {
//     text-align: left;
//   }
// }

.width100percent {
  width: 100% !important;
}

.boreas-filter {
  background: #ffffff;
  display: flex;
  &-list {
    flex: 1;
  }
  &-button {
    padding-left: 40px;
    button {
      width: 88px;
    }
  }
  &-child {
    margin-right: 0 !important;
  }
  &-child-1 {
    width: 80%;
  }
  &-child-2 {
    width: 50%;
  }
  &-child-3 {
    // flex: 1;
    width: 33.33%;
  }
}

// .boreas-cascader-select {
//   width: 33.33% !important;
//   .next-cascader-select-dropdown {
//     border: 0 none;
//   }
//   .next-cascader-inner {
//     width: 58.33% !important;
//     display: flex;
//   }
//   .next-cascader-menu-wrapper {
//     // width: 100% !important;
//     flex: 1;
//     border: 1px solid #d7d9db;
//   }
// }

.phoneInput input {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  &[type="number"] {
    -moz-appearance: textfield;
  }
}
