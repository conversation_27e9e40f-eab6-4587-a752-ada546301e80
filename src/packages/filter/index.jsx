import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { Form, Input, Field, Grid, Button, Select, DatePicker } from '@alifd/next'
import _ from 'lodash'
import filterUISource from './uisource'
import fusionInputFix from '@/utils/fusionInputFix'
import './style.scss'

const { RangePicker } = DatePicker
const { AutoComplete } = Select

const XInput = fusionInputFix()(Input)

const FormItem = Form.Item
const { Row, Col } = Grid

class Filter extends Component {
  constructor(props) {
    super(props)
    this.state = {
      queryLoading: false,
      resetLoading: false,
    }
  }

  field = new Field(this, {
    onChange: (name, value) => {
      this.props.onEvent('change', name, value)
    },
  })

  minLength = Math.min(this.props.uiSource.children && this.props.uiSource.children.length, 3)

  static propTypes = {
    onEvent: PropTypes.func,
    // eslint-disable-next-line react/forbid-prop-types
    uiSource: PropTypes.object,
    canReset: PropTypes.bool,
    queryDisabled: PropTypes.bool,
    // eslint-disable-next-line react/forbid-prop-types
    formItemLayout: PropTypes.object,
  }

  static defaultProps = {
    onEvent: () => {
    },
    uiSource: filterUISource,
    canReset: true,
    queryDisabled: false,
    formItemLayout: {
      // labelCol: { fixedSpan: 4 },
      // inline: true,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
  }

  handleQuery = (type = 'query') => {
    if (typeof type !== 'string') {
      type = 'query'
    }
    this.setState({
      [`${type}Loading`]: true,
    })
    this.props
      .onEvent(type, this.field.getValues(), this.field)
      .then(() => {
        this.setState({
          [`${type}Loading`]: false,
        })
      })
      .catch(() => {
        this.setState({
          [`${type}Loading`]: false,
        })
      })
  }

  clickReset = () => {
    if (this.props.resetType === 'reset') {
      this.field.reset()
    } else {
      this.field.resetToDefault()
    }
    this.handleQuery('reset')
  }

  addonBefore = item => {
    const { init } = this.field
    switch (item && item['x-component']) {
      case 'Select':
        return (
          <Select
            name={item.name}
            defaultValue={item.defaultValue}
            {...init(`${item.name}`, {
              initValue: this.props[item.name] || item.defaultValue,
            })}
            hasClear={item.hasClear}
            dataSource={item.dataSource || []}
          />
        )
      default:
        return false
    }
  }

  render() {
    const { init } = this.field
    const formItemLayoutStyle = this.props.formItemLayout
    const { labelAlign, labelTextAlign } = this.props.uiSource
    const filterList = _.chunk(this.props.uiSource.children, this.props.chunkLength || 3)
    return (
      <Form
        inline
        field={this.field}
        role="grid"
        style={this.props.style || {}}
        className="boreas-filter"
      >
        <div className="boreas-filter-list">
          {filterList.map((child, i) => (
            <div key={`row-${i}`}>
              {child.map(item => {
                const layout = item.itemLayout || formItemLayoutStyle
                const { dataSource, ...others } = item
                let R = (
                  <XInput
                    key={`input-${i}`}
                    className={(item.addonBefore ? 'newtade-260' : 'width100percent') + ' ' + (item.noArrow ? 'phoneInput' : '')}
                    name={item.name}
                    trim
                    type={item['data-type'] || 'text'}
                    addonBefore={this.addonBefore(item.addonBefore)}
                    placeholder={item.placeholder}
                    defaultValue={item.defaultValue}
                    {...init(`${item.name}`, {
                      initValue: this.props[item.name] || item.defaultValue || '',
                    })}
                  />
                )
                if (item['x-component'] === 'Select') {
                  R = (
                    <Select
                      key={`select-${i}`}
                      className="width100percent"
                      name={item.name}
                      {...init(`${item.name}`, {
                        initValue: this.props[item.name] || item.defaultValue,
                      })}
                      filterLocal={item.filterLocal}
                      hasClear={item.hasClear}
                      showSearch={item.showSearch}
                      dataSource={item.dataSource || []}
                      onSearch={item.onSearch}
                    />
                  )
                } else if (item['x-component'] === 'AutoComplete') {
                  R = (
                    <AutoComplete
                      key={`select-${i}`}
                      className="width100percent"
                      defaultValue={item.defaultValue}
                      {...init(`${item.name}`, {
                        initValue: this.props[item.name] || item.defaultValue,
                      })}
                      filterLocal={item.filterLocal}
                      name={item.name}
                      hasClear={item.hasClear}
                      dataSource={item.dataSource}
                      onChange={item.onChange}
                    />
                  )
                } else if (item['x-component'] === 'RangePicker') {
                  R = (
                    <RangePicker
                      key={`input-${i}`}
                      className={item.addonBefore ? 'newtade-260' : 'width100percent'}
                      name={item.name}
                      trim
                      placeholder={item.placeholder}
                      defaultValue={item.defaultValue}
                      {...init(`${item.name}`, {
                        initValue: this.props[item.name] || item.defaultValue || '',
                      })}
                    />
                  )
                }
                return (
                  <FormItem
                    labelAlign="left"
                    labelTextAlign={labelTextAlign || 'right'}
                    {...others}
                    className={`boreas-filter-child boreas-filter-child-${item.minLength || this.minLength || 4}`}
                    {...layout}
                  >
                    {R}
                  </FormItem>
                )
              })}
            </div>
          ))}
        </div>
        <div className="boreas-filter-button">
          <Button
            type="primary"
            loading={this.state.queryLoading}
            disabled={this.props.queryDisabled}
            style={{ marginRight: '12px' }}
            onClick={this.handleQuery}
          >
            查询
          </Button>
          {this.props.canReset && (
            <Button onClick={this.clickReset} loading={this.state.resetLoading}>
              重置
            </Button>
          )}
        </div>
      </Form>
    )
  }
}

export default Filter
