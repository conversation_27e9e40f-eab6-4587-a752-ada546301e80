import React, { Component } from 'react'
import PropTypes from 'prop-types'
import _ from 'lodash'
import { Form, Input, DatePicker, Checkbox, Radio, Field, Balloon, Button } from '@alifd/next'
import BoreasHeader from '@/components/header'
// import loadable from '@loadable/component'
import Title from '../../components/title'
import formUISource from './uisource'
import './style.scss'

const moment = require('moment')
require('moment/locale/zh-cn')

moment.locale('zh-cn')
// const AsyncComponent = loadable(props => import(`${props.component}`))

const FormItem = Form.Item
const RadioGroup = Radio.Group

const { RangePicker } = DatePicker
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 10 }
}

const componentList = {
  next: { Form, Input, RangePicker, RadioGroup, 'Checkbox.Group': Checkbox.Group },
  boreas: {
    BoreasHeader
  }
}
class FormField extends Component {
  field = new Field(this, {
    onChange: (name, value) => {
      this.props.onEvent && this.props.onEvent('change', name, value, this.field)
    }
  })

  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    uiSource: PropTypes.object,
    // eslint-disable-next-line react/forbid-prop-types
    dataSource: PropTypes.object
  }

  static defaultProps = {
    uiSource: formUISource,
    dataSource: {}
  }

  // renderFormItem
  renderFormItem = (__uiSource, __dataSource = {}) => {
    const componentName = __uiSource['x-component']
    const { dataSource, initValue, innerLabel, ...otherData } = __uiSource
    if (componentName === 'Cell') {
      return (
        <FormItem {...formItemLayout} {...otherData}>
          {React.createElement(
            'div',
            null,
            <div className="next-form-text-align">{__uiSource.cell}</div>
          )}
        </FormItem>
      )
    }

    let otherParams = {}
    if (Object.prototype.toString.call(__dataSource) === '[object Object]') {
      otherParams = __dataSource
    } else {
      otherParams = {
        value: __dataSource
      }
    }

    const { value, ...othersAttar } = otherParams

    const params = {
      ...otherData,
      ...othersAttar,
      ...this.field.init(`${__uiSource.name}`, {
        initValue: value || initValue || '',
        rules: __uiSource.rules
      })
    }

    params.label = innerLabel
    const type = __uiSource['component-type'] || 'next'
    let childEle = null
    if (__uiSource.children) {
      childEle = (
        <React.Fragment>
          {__uiSource.children &&
            __uiSource.children.map(child => this.renderFormItem(child, __dataSource[child.name]))}
        </React.Fragment>
      )
    }

    return (
      <FormItem {...formItemLayout} {...otherData}>
        {React.createElement(componentList[type][componentName], params, childEle)}
      </FormItem>
    )
  }

  // renderParent
  renderParent = (_uiSource, _dataSource = {}) => {
    const componentName = _uiSource['x-component']
    return (
      <React.Fragment key={_uiSource.label}>
        {/* 表单项 */}
        {componentName === 'Form' && (
          <React.Fragment>
            {_uiSource.children &&
              _uiSource.children.map(child => this.renderFormItem(child, _dataSource[child.name]))}
          </React.Fragment>
        )}
      </React.Fragment>
    )
  }

  render() {
    const { uiSource, dataSource } = this.props
    if (!uiSource) {
      return null
    }

    return (
      <div className="boreas-form">
        {uiSource['x-component'] === 'Form' && (
          <Form {...formItemLayout} field={this.field}>
            {uiSource.title && <Title title={uiSource.title} />}
            {this.renderParent(uiSource, dataSource)}
          </Form>
        )}
      </div>
    )
  }
}

export default FormField
