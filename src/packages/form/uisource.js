export default {
  id: 'base-2a616e9d-45b8-4d7f-b1e7-6073e2c5fbb3',
  title: '基本信息',
  // description: '',
  'x-component': 'Form',
  children: [
    {
      default: '',
      label: '名称:',
      innerLabel: '',
      name: 'launch_name',
      'data-type': 'string',
      placeholder: '请输入',
      // 'hasLimitHint': true,
      'x-component': 'Input',
      rules: {
        required: true,
        // maxLength: 10,
        // minmaxLengthMessage: '最多不超过10个字',
        // requiredTrigger: 'onBlur',
        autoValidate: true,
        requiredMessage: '投放计划名称不能为空'
        // 自定义验证规则
        // validator: (rule, value, fn) => {
        //   fn(<span>error lalal</span>)
        // }
        // pattern: /[\w]{6,16}/,
        // patternMessage: '活动名称格式有误'
        // help: '活动名称有误'
      }
    },
    {
      label: '有效期限:',
      name: 'range_date',
      // showTime: true,
      'data-type': 'string',
      innerLabel: '',
      'x-component': 'RangePicker',
      rules: {
        required: true
      },
      initValue: []
    },
    {
      label: '生效平台:',
      name: 'platform',
      'data-type': 'array',
      'feedback-class': 'boreas-gray',
      'x-component': 'Checkbox.Group',
      rules: {
        required: true,
        autoValidate: true,
        requiredMessage: '生效平台有误'
      },
      dataSource: [
        {
          value: '14',
          label: '全部平台',
          description: '(包括饿了么APP、淘宝APP、星选APP、支付宝APP等)'
        },
        {
          value: '10',
          label: '饿了么'
        }
      ],
      initValue: []
    },
    {
      label: '生效人群:',
      name: 'group',
      'data-type': 'array',
      'x-component': 'Checkbox.Group',
      'feedback-class': 'boreas-gray',
      rules: {
        required: true,
        autoValidate: true,
        requiredMessage: '生效人群有误'
      },
      dataSource: [
        {
          value: '全部人群',
          label: '全部人群',
          description: '(所有用户都可以享受此活动，不区分新老客)'
        }
      ],
      initValue: []
    },
    {
      label: '活动地区:',
      name: 'area',
      'data-type': 'string',
      'x-component': 'Cell',
      rules: {
        required: true
      },
      cell: '不限地区'
    },
    {
      label: '单选测试:',
      name: 'test',
      'data-type': 'array',
      'x-component': 'RadioGroup',
      rules: {
        required: true,
        autoValidate: true,
        requiredMessage: '测试人群有误'
      },
      dataSource: [
        {
          value: '1',
          label: '选项1'
        },
        {
          value: '2',
          label: '选项2'
        }
      ]
    }
  ]
}
