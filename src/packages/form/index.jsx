import React, { Component } from 'react'
import PropTypes from 'prop-types'
import _ from 'lodash'
import Moment from 'moment'
import {
  Form,
  Input,
  DatePicker,
  Checkbox,
  Radio,
  Field,
  Balloon,
  Button,
  Select
} from '@alifd/next'
// import loadable from '@loadable/component'
import Title from '../../components/title'
import formUISource from './uisource'
import './style.scss'
import fusionInputFix from '@/utils/fusionInputFix'

const XInput = fusionInputFix()(Input)

const moment = require('moment')
require('moment/locale/zh-cn')

moment.locale('zh-cn')
// const AsyncComponent = loadable(props => import(`${props.component}`))

const FormItem = Form.Item
const RadioGroup = Radio.Group

const { RangePicker } = DatePicker
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 10 }
}

const componentList = {
  next: { Form, Input: XInput, Select, RangePicker, RadioGroup, 'Checkbox.Group': Checkbox.Group }
}
class FormField extends Component {
  field = new Field(this, {
    onChange: (name, value) => {
      this.props.onEvent && this.props.onEvent('change', name, value, this.field)
    }
  })

  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    uiSource: PropTypes.object,
    // eslint-disable-next-line react/forbid-prop-types
    dataSource: PropTypes.object
  }

  static defaultProps = {
    uiSource: formUISource,
    dataSource: {}
  }

  renderFormItem = data => {
    const componentName = data['x-component']
    const {
      dataSource,
      initValue,
      hasLimitHint,
      showTime,
      disabledDate,
      innerLabel,
      ...otherData
    } = data
    if (componentName === 'Cell') {
      return (
        <FormItem {...formItemLayout} {...otherData}>
          <div className="next-form-text-align">{React.createElement('div', null, data.cell)}</div>
        </FormItem>
      )
    }

    const params = {
      ...otherData,
      ...this.field.init(`${data.name}`, {
        initValue: this.props.dataSource[data.name] || initValue || '',
        rules: data.rules
      })
    }

    dataSource && (params.dataSource = dataSource)
    hasLimitHint && (params.hasLimitHint = hasLimitHint)
    showTime && (params.showTime = showTime)
    disabledDate && (params.disabledDate = disabledDate)

    params.label = innerLabel

    this.props.onEvent && this.props.onEvent('init', null, null, this.field)

    // disabledDate
    if (componentName === 'RangePicker') {
      params.disabledDate = v => {
        return (
          Date.parse(moment().format('YYYY-MM-DD 00:00:00')) > v.valueOf() ||
          v.valueOf() > Date.parse('2029-12-31 23:59:59')
        )
      }
    }
    return (
      <FormItem {...formItemLayout} {...otherData}>
        {React.createElement(componentList.next[componentName], params)}
      </FormItem>
    )
  }

  renderParent = uiSource => {
    const componentName = uiSource['x-component']
    return (
      <React.Fragment key={uiSource.label}>
        {/* 表单项 */}
        {componentName === 'Form' && (
          <React.Fragment>
            {uiSource.children && uiSource.children.map(child => this.renderFormItem(child))}
          </React.Fragment>
        )}
      </React.Fragment>
    )
  }

  render() {
    const { uiSource } = this.props
    if (!uiSource) {
      return null
    }

    return (
      <div className="boreas-form">
        {uiSource['x-component'] === 'Form' && (
          <Form {...formItemLayout} field={this.field}>
            {uiSource.title && <Title title={uiSource.title} />}
            {this.renderParent(uiSource)}
          </Form>
        )}
      </div>
    )
  }
}

export default FormField
