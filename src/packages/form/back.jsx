import React, { Component } from 'react'
import PropTypes from 'prop-types'
import _ from 'lodash'
import { Form, Input, DatePicker, Checkbox, Radio, Field, Balloon, Button } from '@alifd/next'
// import loadable from '@loadable/component'
import Title from '../../components/title'
import formUISource from './uisource'
import './style.scss'

const moment = require('moment')
require('moment/locale/zh-cn')

moment.locale('zh-cn')
// const AsyncComponent = loadable(props => import(`${props.component}`))

const FormItem = Form.Item
const RadioGroup = Radio.Group

const { RangePicker } = DatePicker
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 10 }
}

// const test = {
//   next: [
//     {
//       name: 'Input',
//       Component: Input
//     },
//     {
//       name: 'RangePicker',
//       Component: RangePicker
//     },
//     {
//       name: 'Radio.Group',
//       Component: RadioGroup
//     },
//     {
//       name: 'Checkbox.Group',
//       Component: Checkbox.Group
//     }
//   ]
// }
class FormField extends Component {
  static defaultProps = {
    uiSource: formUISource
  }

  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    uiSource: PropTypes.object
  }

  field = new Field(this, {
    onChange: (name, value) => {
      this.props.onEvent && this.props.onEvent('change', name, value, this.field)
    }
  })

  renderParent = domData => {
    const Xcomponent = domData['x-component']
    return (
      <React.Fragment key={domData.label}>
        {/* 表单项 */}
        {Xcomponent === 'Form' && (
          <React.Fragment>
            {domData.children && domData.children.map(child => this.renderParent(child))}
          </React.Fragment>
        )}
        {Xcomponent === 'Input' && (
          <FormItem {...formItemLayout} {...domData}>
            <Input
              {...domData}
              label=""
              defaultValue={domData.defaultValue}
              {...this.field.init(`${domData.name}`, {
                initValue: this.props[domData.name] || '',
                rules: domData.rules
              })}
            />
          </FormItem>
        )}
        {Xcomponent === 'Cell' && (
          <FormItem {...formItemLayout} {...domData}>
            <div className="next-form-text-align">{domData.cell}</div>
          </FormItem>
        )}
        {Xcomponent === 'rangeDate' && (
          <FormItem {...formItemLayout} {...domData}>
            <RangePicker
              name={domData.name}
              showTime={domData.showTime}
              {...this.field.init(`${domData.name}`, {
                initValue: this.props[domData.name] || [],
                rules: domData.rules
              })}
            />
          </FormItem>
        )}
        {Xcomponent === 'Radio.Group' && (
          <FormItem {...formItemLayout} {...domData}>
            <RadioGroup
              dataSource={domData.datasource}
              name={domData.name}
              defaultValue={domData.defaultValue}
              {...this.field.init(`${domData.name}`, {
                initValue: this.props[domData.name] || '',
                rules: domData.rules
              })}
            />
          </FormItem>
        )}
        {Xcomponent === 'Checkbox.Group' && (
          <FormItem {...formItemLayout} {...domData}>
            <Checkbox.Group
              name={domData.name}
              {...this.field.init(`${domData.name}`, {
                initValue: this.props[domData.name] || [],
                rules: domData.rules
              })}
            >
              {domData.datasource &&
                domData.datasource.map(child => (
                  <Checkbox {...child} key={child.label}>
                    <span className={`${domData['feedback-class'] || 'boreas-gray'}`}>
                      {child.description}
                    </span>
                  </Checkbox>
                ))}
            </Checkbox.Group>
          </FormItem>
        )}
      </React.Fragment>
    )
  }

  render() {
    const { uiSource } = this.props
    if (!uiSource) {
      return null
    }

    return (
      <div className="boreas-form">
        {uiSource['x-component'] === 'Form' && (
          <Form {...formItemLayout} field={this.field}>
            {uiSource.title && <Title title={uiSource.title} />}
            {this.renderParent(uiSource)}
          </Form>
        )}
      </div>
    )
  }
}

export default FormField
