{"extends": "airbnb", "parser": "babel-es<PERSON>", "env": {"browser": true}, "plugins": ["react", "jsx-a11y", "import", "autofix"], "rules": {"react/jsx-filename-extension": [1, {"extensions": [".jsx"]}], "global-require": 0, "react/prefer-stateless-function": 0, "padded-blocks": 0, "react/jsx-wrap-multilines": 0, "react/jsx-closing-tag-location": 0, "react/jsx-curly-brace-presence": 0, "react/jsx-one-expression-per-line": 0, "implicit-arrow-linebreak": 0, "no-script-url": 0, "max-len": 0, "no-useless-escape": 0, "no-cond-assign": 0, "no-continue": 0, "import/extensions": 0, "eqeqeq": 0, "jsx-a11y/click-events-have-key-events": 0, "jsx-a11y/no-noninteractive-element-interactions": 0, "jsx-a11y/no-static-element-interactions": 0, "react/no-array-index-key": 0, "react/destructuring-assignment": 0, "react/no-unused-state": 0, "no-use-before-define": 0, "react/no-access-state-in-setstate": 0, "import/no-dynamic-require": 0, "import/no-mutable-exports": 0, "react/self-closing-comp": 0, "comma-spacing": 0, "object-property-newline": 0, "quote-props": 0, "no-console": 0, "import/no-unresolved": 0, "no-trailing-spaces": 0, "consistent-return": 0, "no-throw-literal": 0, "no-param-reassign": 0, "prefer-const": 0, "no-multiple-empty-lines": 0, "operator-linebreak": 0, "react/sort-comp": 0, "object-curly-newline": 0, "no-constant-condition": 0, "eact/jsx-filename-extension": 0, "no-unused-expressions": 0, "react/jsx-max-props-per-line": 0, "react/jsx-closing-bracket-location": 0, "import/no-named-as-default-member": 0, "no-shadow": 0, "prefer-promise-reject-errors": 0, "react/jsx-first-prop-new-line": 0, "prefer-arrow-callback": 0, "jsx-a11y/anchor-is-valid": 0, "react/prop-types": 0, "prefer-template": 0, "comma-dangle": 0, "jsx-a11y/alt-text": 0, "jsx-quotes": 0, "no-debugger": 0, "no-plusplus": 0, "no-else-return": 0, "no-restricted-syntax": 0, "brace-style": 0, "prefer-destructuring": 0, "guard-for-in": 0, "class-methods-use-this": 0, "no-sequences": 0, "import/prefer-default-export": 0, "no-inner-declarations": 0, "semi": 0, "quotes": 0, "indent": 0, "no-underscore-dangle": 0, "no-unused-vars": 0, "arrow-body-style": 0, "arrow-parens": 0, "react/jsx-indent": 0, "react/jsx-indent-props": 0}, "settings": {"import/core-modules": ["react", "react-dom", "react-router", "react-router-dom", "@common/js/mockCore"]}}