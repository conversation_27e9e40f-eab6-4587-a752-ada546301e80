# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ali/baxia@^1.0.11":
  version "1.1.7"
  resolved "https://registry.npm.alibaba-inc.com/@ali/baxia/download/@ali/baxia-1.1.7.tgz#457245c11aa67c342f198f60554601c4bea94cc3"
  integrity sha1-RXJFwRqmfDQvGY9gVUYBxL6pTMM=

"@ali/boreas2@0.7.5-beta.24995495":
  version "0.7.5-beta.24995495"
  resolved "https://registry.npm.alibaba-inc.com/@ali/boreas2/download/@ali/boreas2-0.7.5-beta.24995495.tgz#906d3cf696a5aa2e9a4cb2ed5d620d595b9b410c"
  integrity sha1-kG089palqi6aTLLtXWINWVubQQw=
  dependencies:
    "@babel/runtime" "^7.7.7"
    "@babel/standalone" "~7.8.4"
    antd-mobile "^2.3.1"
    async-validator "^3.2.4"
    axios "^0.19.2"
    bignumber.js "^9.0.0"
    classnames "^2.2.6"
    immutable "^4.0.0-rc.12"
    jsonschema "^1.2.4"
    lodash "^4.17.15"
    mockdate "^2.0.5"
    moment "2.24.0"
    react-error-boundary "^2.3.0"
    react-loading "^2.0.3"
    react-router "^5.1.2"
    react-router-dom "^5.1.2"
    react-tiny-virtual-list "^2.2.0"
    shortid "^2.2.15"
    validate "^5.1.0"
    validator "^12.2.0"

"@ali/builder-rum@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/@ali/builder-rum/download/@ali/builder-rum-2.0.0.tgz#aec0bfe389199804574a36bf9bf1829c7eb76b53"
  integrity sha1-rsC/44kZmARXSja/m/GCnH63a1M=
  dependencies:
    "@ali/eslint-config-tbfed" "^0.6.11"
    "@ali/eslint-plugin-tbfed" "^1.1.7"
    "@babel/core" "^7.0.0-beta.34"
    art-template "^4.13.0"
    art-template-loader "^1.4.3"
    autoprefixer "^7.2.1"
    babel-core "^6.26.0"
    babel-eslint "^7.2.3"
    babel-loader "^7.1.2"
    babel-plugin-add-module-exports "^0.2.1"
    babel-plugin-import "^1.4.0"
    babel-plugin-transform-decorators-legacy "^1.3.5"
    babel-plugin-transform-react-jsx "^6.24.1"
    babel-plugin-transform-runtime "^6.23.0"
    babel-preset-env "^1.6.1"
    babel-preset-es2015 "^6.24.1"
    babel-preset-es2016 "^6.24.1"
    babel-preset-react "^6.24.1"
    babel-preset-stage-0 "^6.24.1"
    babel-runtime "^6.26.0"
    chalk "^2.3.0"
    clean-webpack-plugin "^0.1.17"
    css-hot-loader "^1.4.2"
    css-loader "^1.0.0"
    cssnano "^3.10.0"
    eslint "^5.4.0"
    eslint-config-airbnb-base "^13.1.0"
    eslint-config-standard "^11.0.0"
    eslint-config-standard-jsx "^6.0.2"
    eslint-loader "^2.1.0"
    eslint-plugin-babel "^5.1.0"
    eslint-plugin-import "^2.14.0"
    eslint-plugin-jest "^21.22.0"
    eslint-plugin-node "^7.0.1"
    eslint-plugin-promise "^4.0.0"
    eslint-plugin-react "^7.11.1"
    eslint-plugin-standard "^3.1.0"
    eslint-plugin-vue "^4.7.1"
    extract-text-webpack-plugin "^4.0.0-beta.0"
    file-loader "^1.1.11"
    glob "^7.1.2"
    html-inline-source-webpack-plugin "^1.1.0"
    html-webpack-inline-source-plugin "^0.0.10"
    html-webpack-plugin "^3.2.0"
    less "^3.8.0"
    less-loader "^4.1.0"
    lodash "^4.17.4"
    mini-css-extract-plugin "^0.4.1"
    node-sass "^4.7.2"
    parse-git-config "^1.1.1"
    portfinder "^1.0.17"
    post-compile-webpack-plugin "^0.1.2"
    postcss-aspect-ratio-mini "0.0.2"
    postcss-cssnext "^3.0.2"
    postcss-import "^11.0.0"
    postcss-loader "^2.0.9"
    postcss-px-to-viewport "0.0.3"
    postcss-pxtorem "^4.0.1"
    postcss-simple-vars "^4.1.0"
    postcss-url-mapper "^1.2.0"
    postcss-viewport-units "^0.1.4"
    progress-bar-webpack-plugin "^1.10.0"
    react "^16.2.0"
    react-dom "^16.2.0"
    sass-loader "^6.0.6"
    style-loader "^0.13.2"
    url-join "^2.0.2"
    url-loader "^0.6.2"
    vue-loader "^15.4.1"
    vue-style-loader "^4.1.2"
    vue-template-compiler "^2.5.17"
    webpack "^4.16.0"
    webpack-cli "^3.0.8"
    webpack-dev-middleware "^3.1.3"
    webpack-dev-server "^3.1.4"
    webpack-hot-middleware "^2.22.2"
    yargs-parser "^8.0.0"

"@ali/eslint-config-tbfed@^0.6.11":
  version "0.6.11"
  resolved "https://registry.npm.alibaba-inc.com/@ali/eslint-config-tbfed/download/@ali/eslint-config-tbfed-0.6.11.tgz#91caba13d118d8ddd554eb1beb36661084e8eccd"
  integrity sha1-kcq6E9EY2N3VVOsb6zZmEITo7M0=

"@ali/eslint-plugin-tbfed@^1.1.7":
  version "1.1.7"
  resolved "https://registry.npm.alibaba-inc.com/@ali/eslint-plugin-tbfed/download/@ali/eslint-plugin-tbfed-1.1.7.tgz#220d7d8d014f3ce02794b1ff554ce443864acd7f"
  integrity sha1-Ig19jQFPPOAnlLH/VUzkQ4ZKzX8=
  dependencies:
    jsx-ast-utils "^1.3.4"

"@ali/imagespace-upload-sdk@^0.1.9":
  version "0.1.9"
  resolved "https://registry.npm.alibaba-inc.com/@ali/imagespace-upload-sdk/download/@ali/imagespace-upload-sdk-0.1.9.tgz#51024d7a4a9ebc0e98f43a2a6e069ff4a36f78f5"
  integrity sha1-UQJNekqevA6Y9Doqbgaf9KNvePU=
  dependencies:
    "@ali/baxia" "^1.0.11"
    axios "^0.19.0"
    qs "^6.8.0"

"@ali/shell-sdk@^0.0.9":
  version "0.0.9"
  resolved "https://registry.npm.alibaba-inc.com/@ali/shell-sdk/download/@ali/shell-sdk-0.0.9.tgz#1c75eda4d00ca6ea6aaf8b58f9863333f8ec8b80"
  integrity sha1-HHXtpNAMpupqr4tY+YYzM/jsi4A=

"@alifd/field@~1.3.3":
  version "1.3.5"
  resolved "https://registry.npm.alibaba-inc.com/@alifd/field/download/@alifd/field-1.3.5.tgz#b8f370b8d9dbc2aba2ac85f6a5989ecd51f36673"
  integrity sha1-uPNwuNnbwquirIX2pZiezVHzZnM=
  dependencies:
    "@alifd/validate" "^1.1.3"
    prop-types "^15.5.8"

"@alifd/field@~1.5.5":
  version "1.5.8"
  resolved "https://registry.npm.alibaba-inc.com/@alifd/field/download/@alifd/field-1.5.8.tgz#18a0bd92dec109a4efd40def6f52aecf6550296f"
  integrity sha1-GKC9kt7BCaTv1A3vb1Kuz2VQKW8=
  dependencies:
    "@alifd/validate" "^1.2.0"
    prop-types "^15.5.8"

"@alifd/next@^1.19.13":
  version "1.26.8"
  resolved "https://registry.npm.alibaba-inc.com/@alifd/next/download/@alifd/next-1.26.8.tgz#a4bc933d7649ceedc742a5a3e9f2080fccb2d3c7"
  integrity sha512-VU91pzt+mV2qGSCsUHbAf4LbL3LkyHciEDHF6VdbTPJux11daxKSfWoclEA4Q4xp+vMCy734A2Nl2XO3ek6dLw==
  dependencies:
    "@alifd/field" "~1.5.5"
    "@alifd/overlay" "^0.2.9"
    "@alifd/validate" "~1.2.0"
    babel-runtime "^6.26.0"
    big.js "^6.2.0"
    classnames "^2.2.3"
    dayjs "^1.9.6"
    hoist-non-react-statics "^3.0.0"
    lodash.clonedeep "^4.5.0"
    prop-types "^15.6.0"
    react-lifecycles-compat "^3.0.4"
    react-transition-group "^2.2.1"
    resize-observer-polyfill "^1.5.1"
    shallow-element-equals "^1.0.1"

"@alifd/overlay@^0.2.9":
  version "0.2.12"
  resolved "https://registry.npm.alibaba-inc.com/@alifd/overlay/download/@alifd/overlay-0.2.12.tgz#2696864d33def52d81dabb1bfe18f1250993bd4c"
  integrity sha512-4IJGuuD7pJiF9YsUVIpDYXWg2YDNPIDXahld42pWwePcFMptO0thIG11XxfqvAULAIuR5Mqf4NdiK+jeSiVcGw==
  dependencies:
    resize-observer-polyfill "^1.5.1"

"@alifd/validate@^1.1.3", "@alifd/validate@^1.2.0":
  version "1.4.0"
  resolved "https://registry.npm.alibaba-inc.com/@alifd/validate/download/@alifd/validate-1.4.0.tgz#30dc391743bf1e09b30fac3eff6448b2f5bc23d2"
  integrity sha512-RNayg1HVrJBhP5wOmjRq9x0xCC/2H1isDy038V69ggPyAP0k+3JAzIZKNkDoCLJlF4dWPCcsSwXaJafr0A60Wg==

"@alifd/validate@~1.1.4":
  version "1.1.5"
  resolved "https://registry.npm.alibaba-inc.com/@alifd/validate/download/@alifd/validate-1.1.5.tgz#e44896785051182cb94db7ec2b33a99b4c86943a"
  integrity sha1-5EiWeFBRGCy5TbfsKzOpm0yGlDo=

"@alifd/validate@~1.2.0":
  version "1.2.3"
  resolved "https://registry.npm.alibaba-inc.com/@alifd/validate/download/@alifd/validate-1.2.3.tgz#3b7f880a1a955470b303fddce5afa42a9f2d9ff2"
  integrity sha1-O3+IChqVVHCzA/3c5a+kKp8tn/I=

"@alife/kunlun-base@^1.2.3":
  version "1.4.8"
  resolved "https://registry.npm.alibaba-inc.com/@alife/kunlun-base/download/@alife/kunlun-base-1.4.8.tgz#1a61e24f7e3d9b7b9dc2d50ec6eaa22f5f929369"
  integrity sha1-GmHiT349m3udwtUOxuqiL1+Sk2k=
  dependencies:
    "@alife/theme-nr-op" "1.1.0"
    "@alife/waterMark" "^1.2.4"
    "@babel/runtime" "^7.4.2"
    axios "^0.19.0"

"@alife/next@~1.15.12":
  version "1.15.13"
  resolved "https://registry.npm.alibaba-inc.com/@alife/next/download/@alife/next-1.15.13.tgz#a5cb0222b8be951dfe6a3e08f3d19d4a95925060"
  integrity sha1-pcsCIri+lR3+aj4I89GdSpWSUGA=
  dependencies:
    babel-runtime "^6.26.0"
    classnames "^2.2.3"
    hoist-non-react-statics "^2.1.0"
    prop-types "^15.6.0"
    react-lifecycles-compat "^3.0.4"
    react-transition-group "^2.2.1"
    shallow-element-equals "^1.0.1"

"@alife/next@~1.18.16":
  version "1.18.17"
  resolved "https://registry.npm.alibaba-inc.com/@alife/next/download/@alife/next-1.18.17.tgz#bb950e11e764abec55a4c7bdcc0240ab76b5944f"
  integrity sha1-u5UOEedkq+xVpMe9zAJAq3a1lE8=
  dependencies:
    "@alifd/field" "~1.3.3"
    "@alifd/validate" "~1.1.4"
    babel-runtime "^6.26.0"
    classnames "^2.2.3"
    hoist-non-react-statics "^2.1.0"
    prop-types "^15.6.0"
    react-lifecycles-compat "^3.0.4"
    react-transition-group "^2.2.1"
    shallow-element-equals "^1.0.1"

"@alife/theme-nr-op@1.1.0":
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/@alife/theme-nr-op/download/@alife/theme-nr-op-1.1.0.tgz#e85be4ff1d3f80c7e70b0ab3c95d9e0c08ced3c0"
  integrity sha1-6Fvk/x0/gMfnCwqzyV2eDAjO08A=
  dependencies:
    "@alife/next" "~1.18.16"

"@alife/theme-nr-op@^0.14.0":
  version "0.14.0"
  resolved "https://registry.npm.alibaba-inc.com/@alife/theme-nr-op/download/@alife/theme-nr-op-0.14.0.tgz#2872860aed33a0967f1c53e5fc3be192ec4c26d7"
  integrity sha1-KHKGCu0zoJZ/HFPl/DvhkuxMJtc=
  dependencies:
    "@alife/next" "~1.15.12"

"@alife/waterMark@^1.2.4":
  version "1.3.2"
  resolved "https://registry.npm.alibaba-inc.com/@alife/waterMark/download/@alife/waterMark-1.3.2.tgz#7ee1b019bcec6660fa61a379075bc66afc68af63"
  integrity sha512-1Q48VLlekcuDuxV4mJ58BZB+sCq/9LZsfeUCBoddJD+ie3lCSFGOhF7n9XvpOZo3DaYsVWuQSc062gzVPl9KWA==

"@ampproject/remapping@^2.1.0":
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/@ampproject/remapping/download/@ampproject/remapping-2.2.0.tgz#56c133824780de3174aed5ab6834f3026790154d"
  integrity sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==
  dependencies:
    "@jridgewell/gen-mapping" "^0.1.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@babel/code-frame@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/@babel/code-frame/download/@babel/code-frame-7.0.0-beta.44.tgz#2a02643368de80916162be70865c97774f3adbd9"
  integrity sha1-KgJkM2jegJFhYr5whlyXd08629k=
  dependencies:
    "@babel/highlight" "7.0.0-beta.44"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npm.alibaba-inc.com/@babel/code-frame/download/@babel/code-frame-7.18.6.tgz#3b25d38c89600baa2dcc219edfa88a74eb2c427a"
  integrity sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/compat-data@^7.20.0":
  version "7.20.1"
  resolved "https://registry.npm.alibaba-inc.com/@babel/compat-data/download/@babel/compat-data-7.20.1.tgz#f2e6ef7790d8c8dbf03d379502dcc246dcce0b30"
  integrity sha512-EWZ4mE2diW3QALKvDMiXnbZpRvlj+nayZ112nK93SnhqOtpdsbVD4W+2tEoT3YNBAG9RBR0ISY758ZkOgsn6pQ==

"@babel/core@^7.0.0-beta.34":
  version "7.20.2"
  resolved "https://registry.npm.alibaba-inc.com/@babel/core/download/@babel/core-7.20.2.tgz#8dc9b1620a673f92d3624bd926dc49a52cf25b92"
  integrity sha512-w7DbG8DtMrJcFOi4VrLm+8QM4az8Mo+PuLBKLp2zrYRCow8W/f9xiXm5sN53C8HksCyDQwCKha9JiDoIyPjT2g==
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.20.2"
    "@babel/helper-compilation-targets" "^7.20.0"
    "@babel/helper-module-transforms" "^7.20.2"
    "@babel/helpers" "^7.20.1"
    "@babel/parser" "^7.20.2"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.1"
    "@babel/types" "^7.20.2"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.1"
    semver "^6.3.0"

"@babel/generator@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/@babel/generator/download/@babel/generator-7.0.0-beta.44.tgz#c7e67b9b5284afcf69b309b50d7d37f3e5033d42"
  integrity sha1-x+Z7m1KEr89pswm1DX038+UDPUI=
  dependencies:
    "@babel/types" "7.0.0-beta.44"
    jsesc "^2.5.1"
    lodash "^4.2.0"
    source-map "^0.5.0"
    trim-right "^1.0.1"

"@babel/generator@^7.20.1", "@babel/generator@^7.20.2":
  version "7.20.4"
  resolved "https://registry.npm.alibaba-inc.com/@babel/generator/download/@babel/generator-7.20.4.tgz#4d9f8f0c30be75fd90a0562099a26e5839602ab8"
  integrity sha512-luCf7yk/cm7yab6CAW1aiFnmEfBJplb/JojV56MYEK7ziWfGmFlTfmL9Ehwfy4gFhbjBfWO1wj7/TuSbVNEEtA==
  dependencies:
    "@babel/types" "^7.20.2"
    "@jridgewell/gen-mapping" "^0.3.2"
    jsesc "^2.5.1"

"@babel/helper-compilation-targets@^7.20.0":
  version "7.20.0"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.20.0.tgz#6bf5374d424e1b3922822f1d9bdaa43b1a139d0a"
  integrity sha512-0jp//vDGp9e8hZzBc6N/KwA5ZK3Wsm/pfm4CrY7vzegkVxc65SgSn6wYOnwHe9Js9HRQ1YTCKLGPzDtaS3RoLQ==
  dependencies:
    "@babel/compat-data" "^7.20.0"
    "@babel/helper-validator-option" "^7.18.6"
    browserslist "^4.21.3"
    semver "^6.3.0"

"@babel/helper-environment-visitor@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.18.9.tgz#0c0cee9b35d2ca190478756865bb3528422f51be"
  integrity sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==

"@babel/helper-function-name@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-function-name/download/@babel/helper-function-name-7.0.0-beta.44.tgz#e18552aaae2231100a6e485e03854bc3532d44dd"
  integrity sha1-4YVSqq4iMRAKbkheA4VLw1MtRN0=
  dependencies:
    "@babel/helper-get-function-arity" "7.0.0-beta.44"
    "@babel/template" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-function-name@^7.19.0":
  version "7.19.0"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-function-name/download/@babel/helper-function-name-7.19.0.tgz#941574ed5390682e872e52d3f38ce9d1bef4648c"
  integrity sha512-WAwHBINyrpqywkUH0nTnNgI5ina5TFn85HKS0pbPDfxFfhyR/aNQEn4hGi1P1JyT//I0t4OgXUlofzWILRvS5w==
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/types" "^7.19.0"

"@babel/helper-get-function-arity@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.0.0-beta.44.tgz#d03ca6dd2b9f7b0b1e6b32c56c72836140db3a15"
  integrity sha1-0Dym3SufewseazLFbHKDYUDbOhU=
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-hoist-variables@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.18.6.tgz#d4d2c8fb4baeaa5c68b99cc8245c56554f926678"
  integrity sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.18.6.tgz#1e3ebdbbd08aad1437b428c50204db13c5a3ca6e"
  integrity sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-transforms@^7.20.2":
  version "7.20.2"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.20.2.tgz#ac53da669501edd37e658602a21ba14c08748712"
  integrity sha512-zvBKyJXRbmK07XhMuujYoJ48B5yvvmM6+wcpv6Ivj4Yg6qO7NOZOSnvZN9CRl1zz1Z4cKf8YejmCMh8clOoOeA==
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.20.2"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.19.1"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.1"
    "@babel/types" "^7.20.2"

"@babel/helper-simple-access@^7.20.2":
  version "7.20.2"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.20.2.tgz#0ab452687fe0c2cfb1e2b9e0015de07fc2d62dd9"
  integrity sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA==
  dependencies:
    "@babel/types" "^7.20.2"

"@babel/helper-split-export-declaration@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.0.0-beta.44.tgz#c0b351735e0fbcb3822c8ad8db4e583b05ebd9dc"
  integrity sha1-wLNRc14PvLOCLIrY205YOwXr2dw=
  dependencies:
    "@babel/types" "7.0.0-beta.44"

"@babel/helper-split-export-declaration@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.18.6.tgz#7367949bc75b20c6d5a5d4a97bba2824ae8ef075"
  integrity sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.19.4":
  version "7.19.4"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.19.4.tgz#38d3acb654b4701a9b77fb0615a96f775c3a9e63"
  integrity sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
  version "7.19.1"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.19.1.tgz#7eea834cf32901ffdc1a7ee555e2f9c27e249ca2"
  integrity sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==

"@babel/helper-validator-option@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.18.6.tgz#bf0d2b5a509b1f336099e4ff36e1a63aa5db4db8"
  integrity sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==

"@babel/helpers@^7.20.1":
  version "7.20.1"
  resolved "https://registry.npm.alibaba-inc.com/@babel/helpers/download/@babel/helpers-7.20.1.tgz#2ab7a0fcb0a03b5bf76629196ed63c2d7311f4c9"
  integrity sha512-J77mUVaDTUJFZ5BpP6mMn6OIl3rEWymk2ZxDBQJUG3P+PbmyMcF3bYWvz0ma69Af1oobDqT/iAsvzhB58xhQUg==
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.1"
    "@babel/types" "^7.20.0"

"@babel/highlight@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/@babel/highlight/download/@babel/highlight-7.0.0-beta.44.tgz#18c94ce543916a80553edcdcf681890b200747d5"
  integrity sha1-GMlM5UORaoBVPtzc9oGJCyAHR9U=
  dependencies:
    chalk "^2.0.0"
    esutils "^2.0.2"
    js-tokens "^3.0.0"

"@babel/highlight@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npm.alibaba-inc.com/@babel/highlight/download/@babel/highlight-7.18.6.tgz#81158601e93e2563795adcbfbdf5d64be3f2ecdf"
  integrity sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.18.10", "@babel/parser@^7.20.1", "@babel/parser@^7.20.2":
  version "7.20.3"
  resolved "https://registry.npm.alibaba-inc.com/@babel/parser/download/@babel/parser-7.20.3.tgz#5358cf62e380cf69efcb87a7bb922ff88bfac6e2"
  integrity sha512-OP/s5a94frIPXwjzEcv5S/tpQfc6XhxYUnmWpgdqMWGgYCuErA3SzozaRAMQgSZWKeTJxht9aWAkUY+0UzvOFg==

"@babel/runtime-corejs3@^7.10.2":
  version "7.20.1"
  resolved "https://registry.npm.alibaba-inc.com/@babel/runtime-corejs3/download/@babel/runtime-corejs3-7.20.1.tgz#d0775a49bb5fba77e42cbb7276c9955c7b05af8d"
  integrity sha512-CGulbEDcg/ND1Im7fUNRZdGXmX2MTWVVZacQi/6DiKE5HNwZ3aVTm5PV4lO8HHz0B2h8WQyvKKjbX5XgTtydsg==
  dependencies:
    core-js-pure "^3.25.1"
    regenerator-runtime "^0.13.10"

"@babel/runtime@^7.1.2", "@babel/runtime@^7.10.2", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.13", "@babel/runtime@^7.18.9", "@babel/runtime@^7.4.2", "@babel/runtime@^7.7.2", "@babel/runtime@^7.7.7":
  version "7.20.1"
  resolved "https://registry.npm.alibaba-inc.com/@babel/runtime/download/@babel/runtime-7.20.1.tgz#1148bb33ab252b165a06698fde7576092a78b4a9"
  integrity sha512-mrzLkl6U9YLF8qpqI7TB82PESyEGjm/0Ly91jG575eVxMMlb8fYfOXFZIJ8XfLrJZQbm7dlKry2bJmXBUEkdFg==
  dependencies:
    regenerator-runtime "^0.13.10"

"@babel/standalone@~7.8.4":
  version "7.8.8"
  resolved "https://registry.npm.alibaba-inc.com/@babel/standalone/download/@babel/standalone-7.8.8.tgz#1f69a58a1736b336206d86b4d8cf3f7f77317075"
  integrity sha1-H2mlihc2szYgbYa02M8/f3cxcHU=

"@babel/template@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/@babel/template/download/@babel/template-7.0.0-beta.44.tgz#f8832f4fdcee5d59bf515e595fc5106c529b394f"
  integrity sha1-+IMvT9zuXVm/UV5ZX8UQbFKbOU8=
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    babylon "7.0.0-beta.44"
    lodash "^4.2.0"

"@babel/template@^7.18.10":
  version "7.18.10"
  resolved "https://registry.npm.alibaba-inc.com/@babel/template/download/@babel/template-7.18.10.tgz#6f9134835970d1dbf0835c0d100c9f38de0c5e71"
  integrity sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.18.10"
    "@babel/types" "^7.18.10"

"@babel/traverse@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/@babel/traverse/download/@babel/traverse-7.0.0-beta.44.tgz#a970a2c45477ad18017e2e465a0606feee0d2966"
  integrity sha1-qXCixFR3rRgBfi5GWgYG/u4NKWY=
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/generator" "7.0.0-beta.44"
    "@babel/helper-function-name" "7.0.0-beta.44"
    "@babel/helper-split-export-declaration" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    babylon "7.0.0-beta.44"
    debug "^3.1.0"
    globals "^11.1.0"
    invariant "^2.2.0"
    lodash "^4.2.0"

"@babel/traverse@^7.20.1":
  version "7.20.1"
  resolved "https://registry.npm.alibaba-inc.com/@babel/traverse/download/@babel/traverse-7.20.1.tgz#9b15ccbf882f6d107eeeecf263fbcdd208777ec8"
  integrity sha512-d3tN8fkVJwFLkHkBN479SOsw4DMZnz8cdbL/gvuDuzy3TS6Nfw80HuQqhw1pITbIruHyh7d1fMA47kWzmcUEGA==
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.20.1"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.20.1"
    "@babel/types" "^7.20.0"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@7.0.0-beta.44":
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/@babel/types/download/@babel/types-7.0.0-beta.44.tgz#6b1b164591f77dec0a0342aca995f2d046b3a757"
  integrity sha1-axsWRZH3fewKA0KsqZXy0Eazp1c=
  dependencies:
    esutils "^2.0.2"
    lodash "^4.2.0"
    to-fast-properties "^2.0.0"

"@babel/types@^7.18.10", "@babel/types@^7.18.6", "@babel/types@^7.19.0", "@babel/types@^7.20.0", "@babel/types@^7.20.2":
  version "7.20.2"
  resolved "https://registry.npm.alibaba-inc.com/@babel/types/download/@babel/types-7.20.2.tgz#67ac09266606190f496322dbaff360fdaa5e7842"
  integrity sha512-FnnvsNWgZCr232sqtXggapvlkk/tuwR/qhGzcmxI0GXLCjmPYQPzio2FbdlWuY6y1sHFfQKk+rRbUZ9VStQMog==
  dependencies:
    "@babel/helper-string-parser" "^7.19.4"
    "@babel/helper-validator-identifier" "^7.19.1"
    to-fast-properties "^2.0.0"

"@eivifj/dot@^1.0.1":
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/@eivifj/dot/download/@eivifj/dot-1.0.3.tgz#b3a6d9662cd84ff4105e0f05620d1045e9d0d9fc"
  integrity sha1-s6bZZizYT/QQXg8FYg0QRenQ2fw=

"@jridgewell/gen-mapping@^0.1.0":
  version "0.1.1"
  resolved "https://registry.npm.alibaba-inc.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.1.1.tgz#e5d2e450306a9491e3bd77e323e38d7aff315996"
  integrity sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/gen-mapping@^0.3.2":
  version "0.3.2"
  resolved "https://registry.npm.alibaba-inc.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.2.tgz#c1aedc61e853f2bb9f5dfe6d4442d3b565b253b9"
  integrity sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.0.tgz#2203b118c157721addfe69d47b70465463066d78"
  integrity sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==

"@jridgewell/set-array@^1.0.0", "@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/@jridgewell/set-array/download/@jridgewell/set-array-1.1.2.tgz#7c6cf998d6d20b914c0a55a91ae928ff25965e72"
  integrity sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==

"@jridgewell/sourcemap-codec@1.4.14", "@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.14"
  resolved "https://registry.npm.alibaba-inc.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.14.tgz#add4c98d341472a289190b424efbdb096991bb24"
  integrity sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==

"@jridgewell/trace-mapping@^0.3.9":
  version "0.3.17"
  resolved "https://registry.npm.alibaba-inc.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.17.tgz#793041277af9073b0951a7fe0f0d8c4c98c36985"
  integrity sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@loadable/component@^5.10.2":
  version "5.15.2"
  resolved "https://registry.npm.alibaba-inc.com/@loadable/component/download/@loadable/component-5.15.2.tgz#b6c418d592e0a64f16b1d614ca9d3b1443d3b498"
  integrity sha512-ryFAZOX5P2vFkUdzaAtTG88IGnr9qxSdvLRvJySXcUA4B4xVWurUNADu3AnKPksxOZajljqTrDEDcYjeL4lvLw==
  dependencies:
    "@babel/runtime" "^7.7.7"
    hoist-non-react-statics "^3.3.1"
    react-is "^16.12.0"

"@types/glob@^7.1.1":
  version "7.2.0"
  resolved "https://registry.npm.alibaba-inc.com/@types/glob/download/@types/glob-7.2.0.tgz#bc1b5bf3aa92f25bd5dd39f35c57361bdce5b2eb"
  integrity sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npm.alibaba-inc.com/@types/json5/download/@types/json5-0.0.29.tgz#ee28707ae94e11d2b827bcbe5270bcea7f3e71ee"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/minimatch@*":
  version "5.1.2"
  resolved "https://registry.npm.alibaba-inc.com/@types/minimatch/download/@types/minimatch-5.1.2.tgz#07508b45797cb81ec3f273011b054cd0755eddca"
  integrity sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA==

"@types/node@*":
  version "18.11.9"
  resolved "https://registry.npm.alibaba-inc.com/@types/node/download/@types/node-18.11.9.tgz#02d013de7058cea16d36168ef2fc653464cfbad4"
  integrity sha512-CRpX21/kGdzjOpFsZSkcrXMGIBWMGNIHXXBVFSH+ggkftxg+XYP20TESbh+zFvFj3EQOl5byk0HTRn1IL6hbqg==

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/@types/parse-json/download/@types/parse-json-4.0.0.tgz#2f8bb441434d163b35fb8ffdccd7138927ffb8c0"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

"@vue/component-compiler-utils@^3.1.0":
  version "3.3.0"
  resolved "https://registry.npm.alibaba-inc.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.3.0.tgz#f9f5fb53464b0c37b2c8d2f3fbfe44df60f61dc9"
  integrity sha1-+fX7U0ZLDDeyyNLz+/5E32D2Hck=
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.36"
    postcss-selector-parser "^6.0.2"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"
  optionalDependencies:
    prettier "^1.18.2 || ^2.0.0"

"@webassemblyjs/ast@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz#bd850604b4042459a5a41cd7d338cbed695ed964"
  integrity sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz#3c3d3b271bddfc84deb00f71344438311d52ffb4"
  integrity sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=

"@webassemblyjs/helper-api-error@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz#203f676e333b96c9da2eeab3ccef33c45928b6a2"
  integrity sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=

"@webassemblyjs/helper-buffer@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz#a1442d269c5feb23fcbc9ef759dac3547f29de00"
  integrity sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=

"@webassemblyjs/helper-code-frame@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz#647f8892cd2043a82ac0c8c5e75c36f1d9159f27"
  integrity sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz#c05256b71244214671f4b08ec108ad63b70eddb8"
  integrity sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=

"@webassemblyjs/helper-module-context@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz#25d8884b76839871a08a6c6f806c3979ef712f07"
  integrity sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz#4fed8beac9b8c14f8c58b70d124d549dd1fe5790"
  integrity sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=

"@webassemblyjs/helper-wasm-section@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz#5a4138d5a6292ba18b04c5ae49717e4167965346"
  integrity sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz#15c7a0fbaae83fb26143bbacf6d6df1702ad39e4"
  integrity sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz#f19ca0b76a6dc55623a09cffa769e838fa1e1c95"
  integrity sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz#04d33b636f78e6a6813227e82402f7637b6229ab"
  integrity sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=

"@webassemblyjs/wasm-edit@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz#3fe6d79d3f0f922183aa86002c42dd256cfee9cf"
  integrity sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz#50bc70ec68ded8e2763b01a1418bf43491a7a49c"
  integrity sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz#2211181e5b31326443cc8112eb9f0b9028721a61"
  integrity sha1-IhEYHlsxMmRDzIES658LkChyGmE=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz#9d48e44826df4a6598294aa6c87469d642fff65e"
  integrity sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz#3031115d79ac5bd261556cecc3fa90a3ef451914"
  integrity sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz#4935d54c85fef637b00ce9f52377451d00d47899"
  integrity sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.npm.alibaba-inc.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

abbrev@1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/abbrev/download/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
  version "1.3.8"
  resolved "https://registry.npm.alibaba-inc.com/accepts/download/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-jsx@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/acorn-jsx/download/acorn-jsx-3.0.1.tgz#afdf9488fb1ecefc8348f6fb22f464e32a58b36b"
  integrity sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s=
  dependencies:
    acorn "^3.0.4"

acorn-jsx@^5.0.0:
  version "5.3.2"
  resolved "https://registry.npm.alibaba-inc.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn@^3.0.4:
  version "3.3.0"
  resolved "https://registry.npm.alibaba-inc.com/acorn/download/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"
  integrity sha1-ReN/s56No/JbruP/U2niu18iAXo=

acorn@^5.0.3, acorn@^5.5.0:
  version "5.7.4"
  resolved "https://registry.npm.alibaba-inc.com/acorn/download/acorn-5.7.4.tgz#3e8d8a9947d0599a1796d10225d7432f4a4acf5e"
  integrity sha1-Po2KmUfQWZoXltECJddDL0pKz14=

acorn@^6.0.7, acorn@^6.4.1:
  version "6.4.2"
  resolved "https://registry.npm.alibaba-inc.com/acorn/download/acorn-6.4.2.tgz#35866fd710528e92de10cf06016498e47e39e1e6"
  integrity sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=

add-dom-event-listener@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/add-dom-event-listener/download/add-dom-event-listener-1.1.0.tgz#6a92db3a0dd0abc254e095c0f1dc14acbbaae310"
  integrity sha1-apLbOg3Qq8JU4JXA8dwUrLuq4xA=
  dependencies:
    object-assign "4.x"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/ajv-errors/download/ajv-errors-1.0.1.tgz#f35986aceb91afadec4102fbd85014950cefa64d"
  integrity sha1-81mGrOuRr63sQQL72FAUlQzvpk0=

ajv-keywords@^3.1.0, ajv-keywords@^3.4.1:
  version "3.5.2"
  resolved "https://registry.npm.alibaba-inc.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@^5.0.0:
  version "5.5.2"
  resolved "https://registry.npm.alibaba-inc.com/ajv/download/ajv-5.5.2.tgz#73b5eeca3fab653e3d3f9422b341ad42205dc965"
  integrity sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ajv@^6.1.0, ajv@^6.10.2, ajv@^6.12.3, ajv@^6.9.1:
  version "6.12.6"
  resolved "https://registry.npm.alibaba-inc.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

alphanum-sort@^1.0.1, alphanum-sort@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz#97a1119649b211ad33691d9f9f486a8ec9fbe0a3"
  integrity sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/amdefine/download/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"
  integrity sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=

ansi-colors@^3.0.0:
  version "3.2.4"
  resolved "https://registry.npm.alibaba-inc.com/ansi-colors/download/ansi-colors-3.2.4.tgz#e3a3da4bfbae6c86a9c285625de124a234026fbf"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-escapes@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npm.alibaba-inc.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-html-community@0.0.8:
  version "0.0.8"
  resolved "https://registry.npm.alibaba-inc.com/ansi-html-community/download/ansi-html-community-0.0.8.tgz#69fbc4d6ccbe383f9736934ae34c3f8290f1bf41"
  integrity sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/ansi-regex/download/ansi-regex-3.0.1.tgz#123d6479e92ad45ad897d4054e3c7ca7db4944e1"
  integrity sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==

ansi-regex@^4.1.0:
  version "4.1.1"
  resolved "https://registry.npm.alibaba-inc.com/ansi-regex/download/ansi-regex-4.1.1.tgz#164daac87ab2d6f6db3a29875e2d1766582dabed"
  integrity sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.alibaba-inc.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.alibaba-inc.com/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npm.alibaba-inc.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npm.alibaba-inc.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

antd-mobile@^2.3.1:
  version "2.3.4"
  resolved "https://registry.npm.alibaba-inc.com/antd-mobile/download/antd-mobile-2.3.4.tgz#8f584707b30343d102f3ca10fdaf677e2a5d1cc8"
  integrity sha1-j1hHB7MDQ9EC88oQ/a9nfipdHMg=
  dependencies:
    array-tree-filter "~2.1.0"
    babel-runtime "6.x"
    classnames "^2.2.1"
    normalize.css "^7.0.0"
    rc-checkbox "~2.0.0"
    rc-collapse "~1.9.1"
    rc-slider "~8.2.0"
    rc-swipeout "~2.0.0"
    rmc-calendar "^1.0.0"
    rmc-cascader "~5.0.0"
    rmc-date-picker "^6.0.8"
    rmc-dialog "^1.0.1"
    rmc-drawer "^0.4.11"
    rmc-feedback "^2.0.0"
    rmc-input-number "^1.0.0"
    rmc-list-view "^0.11.0"
    rmc-notification "~1.0.0"
    rmc-nuka-carousel "~3.0.0"
    rmc-picker "~5.0.0"
    rmc-pull-to-refresh "~1.0.1"
    rmc-steps "~1.0.0"
    rmc-tabs "~1.2.0"
    rmc-tooltip "~1.0.0"

anymatch@^1.3.0:
  version "1.3.2"
  resolved "https://registry.npm.alibaba-inc.com/anymatch/download/anymatch-1.3.2.tgz#553dcb8f91e3c889845dfdba34c77721b90b9d7a"
  integrity sha1-VT3Lj5HjyImEXf26NMd3IbkLnXo=
  dependencies:
    micromatch "^2.1.5"
    normalize-path "^2.0.0"

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/anymatch/download/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npm.alibaba-inc.com/anymatch/download/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

aproba@^1.0.3, aproba@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/aproba/download/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

are-we-there-yet@~1.1.2:
  version "1.1.7"
  resolved "https://registry.npm.alibaba-inc.com/are-we-there-yet/download/are-we-there-yet-1.1.7.tgz#b15474a932adab4ff8a50d9adfa7e4e926f21146"
  integrity sha1-sVR0qTKtq0/4pQ2a36fk6SbyEUY=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npm.alibaba-inc.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

aria-query@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npm.alibaba-inc.com/aria-query/download/aria-query-4.2.2.tgz#0d2ca6c9aceb56b8977e9fed6aed7e15bbd2f83b"
  integrity sha1-DSymyazrVriXfp/tau1+FbvS+Ds=
  dependencies:
    "@babel/runtime" "^7.10.2"
    "@babel/runtime-corejs3" "^7.10.2"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/arr-diff/download/arr-diff-2.0.0.tgz#8f3b827f955a8bd669697e4a4256ac3ceae356cf"
  integrity sha1-jzuCf5Vai9ZpaX5KQlasPOrjVs8=
  dependencies:
    arr-flatten "^1.0.1"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.0.1, arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/array-find-index/download/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"
  integrity sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npm.alibaba-inc.com/array-flatten/download/array-flatten-2.1.2.tgz#24ef80a28c1a893617e2149b0c6d0d788293b099"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-includes@^3.1.4, array-includes@^3.1.5, array-includes@^3.1.6:
  version "3.1.6"
  resolved "https://registry.npm.alibaba-inc.com/array-includes/download/array-includes-3.1.6.tgz#9e9e720e194f198266ba9e18c29e6a9b0e4b225f"
  integrity sha512-sgTbLvL6cNnw24FnbaDyjmvddQ2ML8arZsgaJhoABMoplz/4QRhtrYS+alr1BUM1Bwp6dhx8vVCBSLG+StwOFw==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    get-intrinsic "^1.1.3"
    is-string "^1.0.7"

array-tree-filter@2.1.x, array-tree-filter@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/array-tree-filter/download/array-tree-filter-2.1.0.tgz#873ac00fec83749f255ac8dd083814b4f6329190"
  integrity sha1-hzrAD+yDdJ8lWsjdCDgUtPYykZA=

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.alibaba-inc.com/array-unique/download/array-unique-0.2.1.tgz#a1d97ccafcbc2625cc70fadceb36a50c58b01a53"
  integrity sha1-odl8yvy8JiXMcPrc6zalDFiwGlM=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.npm.alibaba-inc.com/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.flat@^1.2.5:
  version "1.3.1"
  resolved "https://registry.npm.alibaba-inc.com/array.prototype.flat/download/array.prototype.flat-1.3.1.tgz#ffc6576a7ca3efc2f46a143b9d1dda9b4b3cf5e2"
  integrity sha512-roTU0KWIOmJ4DRLmwKd19Otg0/mT3qPNt0Qb3GWW8iObuZXxrjB/pzn0R3hqpRSWg4HCwqx+0vwOnWnvlOyeIA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npm.alibaba-inc.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.1.tgz#1aae7903c2100433cb8261cd4ed310aab5c4a183"
  integrity sha512-8UGn9O1FDVvMNB0UlLv4voxRMze7+FpHyF5mSMRjWHUMlpoDViniy05870VlxhfgTnLbpuwTzvD76MTtWxB/mQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"

array.prototype.reduce@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/array.prototype.reduce/download/array.prototype.reduce-1.0.5.tgz#6b20b0daa9d9734dd6bc7ea66b5bbce395471eac"
  integrity sha512-kDdugMl7id9COE8R7MHF5jWk7Dqt/fs4Pv+JXoICnYwqpjjjbUurz6w5fT5IG6brLdJhv6/VoHB0H7oyIBXd+Q==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-array-method-boxes-properly "^1.0.0"
    is-string "^1.0.7"

array.prototype.tosorted@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/array.prototype.tosorted/download/array.prototype.tosorted-1.1.1.tgz#ccf44738aa2b5ac56578ffda97c03fd3e23dd532"
  integrity sha512-pZYPXPRl2PqWcsUs6LOMn+1f1532nEoPTYowBtqLwAW+W8vSVhkIGnmOX1t/UQjD6YGI0vcD2B1U7ZFGQH9jnQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"
    get-intrinsic "^1.1.3"

art-template-loader@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npm.alibaba-inc.com/art-template-loader/download/art-template-loader-1.4.3.tgz#0c22565fe2f6cee37ff7dedfffbf8b53e0fba02e"
  integrity sha1-DCJWX+L2zuN/997f/7+LU+D7oC4=
  dependencies:
    loader-utils "^1.1.0"

art-template@^4.13.0:
  version "4.13.2"
  resolved "https://registry.npm.alibaba-inc.com/art-template/download/art-template-4.13.2.tgz#4c4cbd44de08aad031660240871f45c7d737cfc1"
  integrity sha1-TEy9RN4IqtAxZgJAhx9Fx9c3z8E=
  dependencies:
    acorn "^5.0.3"
    escodegen "^1.8.1"
    estraverse "^4.2.0"
    html-minifier "^3.4.3"
    is-keyword-js "^1.0.3"
    js-tokens "^3.0.1"
    merge-source-map "^1.0.3"
    source-map "^0.5.6"

asap@~2.0.3:
  version "2.0.6"
  resolved "https://registry.npm.alibaba-inc.com/asap/download/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1.js@^5.2.0:
  version "5.4.1"
  resolved "https://registry.npm.alibaba-inc.com/asn1.js/download/asn1.js-5.4.1.tgz#11a980b84ebb91781ce35b0fdc2ee294e3783f07"
  integrity sha1-EamAuE67kXgc41sP3C7ilON4Pwc=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    safer-buffer "^2.1.0"

asn1@~0.2.3:
  version "0.2.6"
  resolved "https://registry.npm.alibaba-inc.com/asn1/download/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1:
  version "1.5.0"
  resolved "https://registry.npm.alibaba-inc.com/assert/download/assert-1.5.0.tgz#55c109aaf6e0aefdb3dc4b71240c70bf574b18eb"
  integrity sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

ast-types-flow@^0.0.7:
  version "0.0.7"
  resolved "https://registry.npm.alibaba-inc.com/ast-types-flow/download/ast-types-flow-0.0.7.tgz#f70b735c6bca1a5c9c22d982c3e39e7feba3bdad"
  integrity sha1-9wtzXGvKGlycItmCw+Oef+ujva0=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/astral-regex/download/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

async-each@^1.0.0, async-each@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/async-each/download/async-each-1.0.3.tgz#b727dbf87d7651602f06f4d4ac387f47d91b0cbf"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-foreach@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npm.alibaba-inc.com/async-foreach/download/async-foreach-0.1.3.tgz#36121f845c0578172de419a97dbeb1d16ec34542"
  integrity sha1-NhIfhFwFeBct5Bmpfb6x0W7DRUI=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/async-limiter/download/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async-validator@^3.2.4:
  version "3.5.2"
  resolved "https://registry.npm.alibaba-inc.com/async-validator/download/async-validator-3.5.2.tgz#68e866a96824e8b2694ff7a831c1a25c44d5e500"
  integrity sha1-aOhmqWgk6LJpT/eoMcGiXETV5QA=

async@^2.4.1, async@^2.6.4:
  version "2.6.4"
  resolved "https://registry.npm.alibaba-inc.com/async/download/async-2.6.4.tgz#706b7ff6084664cd7eae713f6f965433b5504221"
  integrity sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==
  dependencies:
    lodash "^4.17.14"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.alibaba-inc.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.alibaba-inc.com/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

autoprefixer@^6.3.1:
  version "6.7.7"
  resolved "https://registry.npm.alibaba-inc.com/autoprefixer/download/autoprefixer-6.7.7.tgz#1dbd1c835658e35ce3f9984099db00585c782014"
  integrity sha1-Hb0cg1ZY41zj+ZhAmdsAWFx4IBQ=
  dependencies:
    browserslist "^1.7.6"
    caniuse-db "^1.0.30000634"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^5.2.16"
    postcss-value-parser "^3.2.3"

autoprefixer@^7.1.1, autoprefixer@^7.2.1:
  version "7.2.6"
  resolved "https://registry.npm.alibaba-inc.com/autoprefixer/download/autoprefixer-7.2.6.tgz#256672f86f7c735da849c4f07d008abb056067dc"
  integrity sha1-JWZy+G98c12oScTwfQCKuwVgZ9w=
  dependencies:
    browserslist "^2.11.3"
    caniuse-lite "^1.0.30000805"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^6.0.17"
    postcss-value-parser "^3.2.3"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.alibaba-inc.com/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.11.0"
  resolved "https://registry.npm.alibaba-inc.com/aws4/download/aws4-1.11.0.tgz#d61f46d83b2519250e2784daf5b09479a8b41c59"
  integrity sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=

axe-core@^4.4.3:
  version "4.5.2"
  resolved "https://registry.npm.alibaba-inc.com/axe-core/download/axe-core-4.5.2.tgz#823fdf491ff717ac3c58a52631d4206930c1d9f7"
  integrity sha512-u2MVsXfew5HBvjsczCv+xlwdNnB1oQR9HlAcsejZttNjKKSkeDNVwB1vMThIUIFI9GoT57Vtk8iQLwqOfAkboA==

axios@^0.19.0, axios@^0.19.2:
  version "0.19.2"
  resolved "https://registry.npm.alibaba-inc.com/axios/download/axios-0.19.2.tgz#3ea36c5d8818d0d5f8a8a97a6d36b86cdc00cb27"
  integrity sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=
  dependencies:
    follow-redirects "1.5.10"

axobject-query@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/axobject-query/download/axobject-query-2.2.0.tgz#943d47e10c0b704aa42275e20edf3722648989be"
  integrity sha1-lD1H4QwLcEqkInXiDt83ImSJib4=

babel-cli@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-cli/download/babel-cli-6.26.0.tgz#502ab54874d7db88ad00b887a06383ce03d002f1"
  integrity sha1-UCq1SHTX24itALiHoGODzgPQAvE=
  dependencies:
    babel-core "^6.26.0"
    babel-polyfill "^6.26.0"
    babel-register "^6.26.0"
    babel-runtime "^6.26.0"
    commander "^2.11.0"
    convert-source-map "^1.5.0"
    fs-readdir-recursive "^1.0.0"
    glob "^7.1.2"
    lodash "^4.17.4"
    output-file-sync "^1.1.2"
    path-is-absolute "^1.0.1"
    slash "^1.0.0"
    source-map "^0.5.6"
    v8flags "^2.1.1"
  optionalDependencies:
    chokidar "^1.6.1"

babel-code-frame@^6.22.0, babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-code-frame/download/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-core@^6.26.0:
  version "6.26.3"
  resolved "https://registry.npm.alibaba-inc.com/babel-core/download/babel-core-6.26.3.tgz#b2e2f09e342d0f0c88e2f02e067794125e75c207"
  integrity sha1-suLwnjQtDwyI4vAuBneUEl51wgc=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-generator "^6.26.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.26.0"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    convert-source-map "^1.5.1"
    debug "^2.6.9"
    json5 "^0.5.1"
    lodash "^4.17.4"
    minimatch "^3.0.4"
    path-is-absolute "^1.0.1"
    private "^0.1.8"
    slash "^1.0.0"
    source-map "^0.5.7"

babel-eslint@^7.2.3:
  version "7.2.3"
  resolved "https://registry.npm.alibaba-inc.com/babel-eslint/download/babel-eslint-7.2.3.tgz#b2fe2d80126470f5c19442dc757253a897710827"
  integrity sha1-sv4tgBJkcPXBlELcdXJTqJdxCCc=
  dependencies:
    babel-code-frame "^6.22.0"
    babel-traverse "^6.23.1"
    babel-types "^6.23.0"
    babylon "^6.17.0"

babel-eslint@^8.2.2:
  version "8.2.6"
  resolved "https://registry.npm.alibaba-inc.com/babel-eslint/download/babel-eslint-8.2.6.tgz#6270d0c73205628067c0f7ae1693a9e797acefd9"
  integrity sha1-YnDQxzIFYoBnwPeuFpOp55es79k=
  dependencies:
    "@babel/code-frame" "7.0.0-beta.44"
    "@babel/traverse" "7.0.0-beta.44"
    "@babel/types" "7.0.0-beta.44"
    babylon "7.0.0-beta.44"
    eslint-scope "3.7.1"
    eslint-visitor-keys "^1.0.0"

babel-generator@^6.26.0:
  version "6.26.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-generator/download/babel-generator-6.26.1.tgz#1844408d3b8f0d35a404ea7ac180f087a601bd90"
  integrity sha1-GERAjTuPDTWkBOp6wYDwh6YBvZA=
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.17.4"
    source-map "^0.5.7"
    trim-right "^1.0.1"

babel-helper-bindify-decorators@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-bindify-decorators/download/babel-helper-bindify-decorators-6.24.1.tgz#14c19e5f142d7b47f19a52431e52b1ccbc40a330"
  integrity sha1-FMGeXxQte0fxmlJDHlKxzLxAozA=
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-builder-binary-assignment-operator-visitor@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-builder-binary-assignment-operator-visitor/download/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz#cce4517ada356f4220bcae8a02c2b346f9a56664"
  integrity sha1-zORReto1b0IgvK6KAsKzRvmlZmQ=
  dependencies:
    babel-helper-explode-assignable-expression "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-builder-react-jsx@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-builder-react-jsx/download/babel-helper-builder-react-jsx-6.26.0.tgz#39ff8313b75c8b65dceff1f31d383e0ff2a408a0"
  integrity sha1-Of+DE7dci2Xc7/HzHTg+D/KkCKA=
  dependencies:
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    esutils "^2.0.2"

babel-helper-call-delegate@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-call-delegate/download/babel-helper-call-delegate-6.24.1.tgz#ece6aacddc76e41c3461f88bfc575bd0daa2df8d"
  integrity sha1-7Oaqzdx25Bw0YfiL/Fdb0Nqi340=
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-define-map@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-define-map/download/babel-helper-define-map-6.26.0.tgz#a5f56dab41a25f97ecb498c7ebaca9819f95be5f"
  integrity sha1-pfVtq0GiX5fstJjH66ypgZ+Vvl8=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-explode-assignable-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-explode-assignable-expression/download/babel-helper-explode-assignable-expression-6.24.1.tgz#f25b82cf7dc10433c55f70592d5746400ac22caa"
  integrity sha1-8luCz33BBDPFX3BZLVdGQArCLKo=
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-explode-class@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-explode-class/download/babel-helper-explode-class-6.24.1.tgz#7dc2a3910dee007056e1e31d640ced3d54eaa9eb"
  integrity sha1-fcKjkQ3uAHBW4eMdZAztPVTqqes=
  dependencies:
    babel-helper-bindify-decorators "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz#d3475b8c03ed98242a25b48351ab18399d3580a9"
  integrity sha1-00dbjAPtmCQqJbSDUasYOZ01gKk=
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz#8f7782aa93407c41d3aa50908f89b031b1b6853d"
  integrity sha1-j3eCqpNAfEHTqlCQj4mwMbG2hT0=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-hoist-variables@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-hoist-variables/download/babel-helper-hoist-variables-6.24.1.tgz#1ecb27689c9d25513eadbc9914a73f5408be7a76"
  integrity sha1-HssnaJydJVE+rbyZFKc/VAi+enY=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-optimise-call-expression/download/babel-helper-optimise-call-expression-6.24.1.tgz#f7a13427ba9f73f8f4fa993c54a97882d1244257"
  integrity sha1-96E0J7qfc/j0+pk8VKl4gtEkQlc=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-regex@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-regex/download/babel-helper-regex-6.26.0.tgz#325c59f902f82f24b74faceed0363954f6495e72"
  integrity sha1-MlxZ+QL4LyS3T6zu0DY5VPZJXnI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-remap-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-remap-async-to-generator/download/babel-helper-remap-async-to-generator-6.24.1.tgz#5ec581827ad723fecdd381f1c928390676e4551b"
  integrity sha1-XsWBgnrXI/7N04HxySg5BnbkVRs=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helper-replace-supers/download/babel-helper-replace-supers-6.24.1.tgz#bf6dbfe43938d17369a213ca8a8bf74b6a90ab1a"
  integrity sha1-v22/5Dk40XNpohPKiov3S2qQqxo=
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-helpers/download/babel-helpers-6.24.1.tgz#3471de9caec388e5c850e597e58a26ddf37602b2"
  integrity sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-loader@^7.1.2:
  version "7.1.5"
  resolved "https://registry.npm.alibaba-inc.com/babel-loader/download/babel-loader-7.1.5.tgz#e3ee0cd7394aa557e013b02d3e492bfd07aa6d68"
  integrity sha1-4+4M1zlKpVfgE7AtPkkr/QeqbWg=
  dependencies:
    find-cache-dir "^1.0.0"
    loader-utils "^1.0.2"
    mkdirp "^0.5.1"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-messages/download/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  integrity sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-add-module-exports@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-add-module-exports/download/babel-plugin-add-module-exports-0.2.1.tgz#9ae9a1f4a8dc67f0cdec4f4aeda1e43a5ff65e25"
  integrity sha1-mumh9KjcZ/DN7E9K7aHkOl/2XiU=

babel-plugin-check-es2015-constants@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-check-es2015-constants/download/babel-plugin-check-es2015-constants-6.22.0.tgz#35157b101426fd2ffd3da3f75c7d1e91835bbf8a"
  integrity sha1-NRV7EBQm/S/9PaP3XH0ekYNbv4o=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-import@^1.12.0, babel-plugin-import@^1.4.0:
  version "1.13.5"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-import/download/babel-plugin-import-1.13.5.tgz#42eed1c5afd9a35ee1b1f8fe922b07c44077d753"
  integrity sha512-IkqnoV+ov1hdJVofly9pXRJmeDm9EtROfrc5i6eII0Hix2xMs5FEm8FG3ExMvazbnZBbgHIt6qdO8And6lCloQ==
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"

babel-plugin-macros@^2.6.1:
  version "2.8.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-macros/download/babel-plugin-macros-2.8.0.tgz#0f958a7cc6556b1e65344465d99111a1e5e10138"
  integrity sha1-D5WKfMZVax5lNERl2ZERoeXhATg=
  dependencies:
    "@babel/runtime" "^7.7.2"
    cosmiconfig "^6.0.0"
    resolve "^1.12.0"

babel-plugin-syntax-async-functions@^6.13.0, babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-async-functions/download/babel-plugin-syntax-async-functions-6.13.0.tgz#cad9cad1191b5ad634bf30ae0872391e0647be95"
  integrity sha1-ytnK0RkbWtY0vzCuCHI5HgZHvpU=

babel-plugin-syntax-async-generators@^6.5.0:
  version "6.13.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-async-generators/download/babel-plugin-syntax-async-generators-6.13.0.tgz#6bc963ebb16eccbae6b92b596eb7f35c342a8b9a"
  integrity sha1-a8lj67FuzLrmuStZbrfzXDQqi5o=

babel-plugin-syntax-class-constructor-call@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-class-constructor-call/download/babel-plugin-syntax-class-constructor-call-6.18.0.tgz#9cb9d39fe43c8600bec8146456ddcbd4e1a76416"
  integrity sha1-nLnTn+Q8hgC+yBRkVt3L1OGnZBY=

babel-plugin-syntax-class-properties@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-class-properties/download/babel-plugin-syntax-class-properties-6.13.0.tgz#d7eb23b79a317f8543962c505b827c7d6cac27de"
  integrity sha1-1+sjt5oxf4VDlixQW4J8fWysJ94=

babel-plugin-syntax-decorators@^6.1.18, babel-plugin-syntax-decorators@^6.13.0:
  version "6.13.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-decorators/download/babel-plugin-syntax-decorators-6.13.0.tgz#312563b4dbde3cc806cee3e416cceeaddd11ac0b"
  integrity sha1-MSVjtNvePMgGzuPkFszurd0RrAs=

babel-plugin-syntax-do-expressions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-do-expressions/download/babel-plugin-syntax-do-expressions-6.13.0.tgz#5747756139aa26d390d09410b03744ba07e4796d"
  integrity sha1-V0d1YTmqJtOQ0JQQsDdEugfkeW0=

babel-plugin-syntax-dynamic-import@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-dynamic-import/download/babel-plugin-syntax-dynamic-import-6.18.0.tgz#8d6a26229c83745a9982a441051572caa179b1da"
  integrity sha1-jWomIpyDdFqZgqRBBRVyyqF5sdo=

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-exponentiation-operator/download/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz#9ee7e8337290da95288201a6a57f4170317830de"
  integrity sha1-nufoM3KQ2pUoggGmpX9BcDF4MN4=

babel-plugin-syntax-export-extensions@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-export-extensions/download/babel-plugin-syntax-export-extensions-6.13.0.tgz#70a1484f0f9089a4e84ad44bac353c95b9b12721"
  integrity sha1-cKFITw+QiaToStRLrDU8lbmxJyE=

babel-plugin-syntax-flow@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-flow/download/babel-plugin-syntax-flow-6.18.0.tgz#4c3ab20a2af26aa20cd25995c398c4eb70310c8d"
  integrity sha1-TDqyCiryaqIM0lmVw5jE63AxDI0=

babel-plugin-syntax-function-bind@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-function-bind/download/babel-plugin-syntax-function-bind-6.13.0.tgz#48c495f177bdf31a981e732f55adc0bdd2601f46"
  integrity sha1-SMSV8Xe98xqYHnMvVa3AvdJgH0Y=

babel-plugin-syntax-jsx@^6.3.13, babel-plugin-syntax-jsx@^6.8.0:
  version "6.18.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-jsx/download/babel-plugin-syntax-jsx-6.18.0.tgz#0af32a9a6e13ca7a3fd5069e62d7b0f58d0d8946"
  integrity sha1-CvMqmm4Tyno/1QaeYtew9Y0NiUY=

babel-plugin-syntax-object-rest-spread@^6.8.0:
  version "6.13.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz#fd6536f2bce13836ffa3a5458c4903a597bb3bf5"
  integrity sha1-/WU28rzhODb/o6VFjEkDpZe7O/U=

babel-plugin-syntax-trailing-function-commas@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz#ba0360937f8d06e40180a43fe0d5616fff532cf3"
  integrity sha1-ugNgk3+NBuQBgKQ/4NVhb/9TLPM=

babel-plugin-transform-async-generator-functions@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-async-generator-functions/download/babel-plugin-transform-async-generator-functions-6.24.1.tgz#f058900145fd3e9907a6ddf28da59f215258a5db"
  integrity sha1-8FiQAUX9PpkHpt3yjaWfIVJYpds=
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-generators "^6.5.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-async-to-generator@^6.22.0, babel-plugin-transform-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-async-to-generator/download/babel-plugin-transform-async-to-generator-6.24.1.tgz#6536e378aff6cb1d5517ac0e40eb3e9fc8d08761"
  integrity sha1-ZTbjeK/2yx1VF6wOQOs+n8jQh2E=
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-class-constructor-call@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-class-constructor-call/download/babel-plugin-transform-class-constructor-call-6.24.1.tgz#80dc285505ac067dcb8d6c65e2f6f11ab7765ef9"
  integrity sha1-gNwoVQWsBn3LjWxl4vbxGrd2Xvk=
  dependencies:
    babel-plugin-syntax-class-constructor-call "^6.18.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-class-properties@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-class-properties/download/babel-plugin-transform-class-properties-6.24.1.tgz#6a79763ea61d33d36f37b611aa9def81a81b46ac"
  integrity sha1-anl2PqYdM9NvN7YRqp3vgagbRqw=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-plugin-syntax-class-properties "^6.8.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-decorators-legacy@^1.3.5:
  version "1.3.5"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-decorators-legacy/download/babel-plugin-transform-decorators-legacy-1.3.5.tgz#0e492dffa0edd70529072887f8aa86d4dd8b40a1"
  integrity sha1-Dkkt/6Dt1wUpByiH+KqG1N2LQKE=
  dependencies:
    babel-plugin-syntax-decorators "^6.1.18"
    babel-runtime "^6.2.0"
    babel-template "^6.3.0"

babel-plugin-transform-decorators@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-decorators/download/babel-plugin-transform-decorators-6.24.1.tgz#788013d8f8c6b5222bdf7b344390dfd77569e24d"
  integrity sha1-eIAT2PjGtSIr33s0Q5Df13Vp4k0=
  dependencies:
    babel-helper-explode-class "^6.24.1"
    babel-plugin-syntax-decorators "^6.13.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-do-expressions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-do-expressions/download/babel-plugin-transform-do-expressions-6.22.0.tgz#28ccaf92812d949c2cd1281f690c8fdc468ae9bb"
  integrity sha1-KMyvkoEtlJws0SgfaQyP3EaK6bs=
  dependencies:
    babel-plugin-syntax-do-expressions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-arrow-functions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-arrow-functions/download/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz#452692cb711d5f79dc7f85e440ce41b9f244d221"
  integrity sha1-RSaSy3EdX3ncf4XkQM5BufJE0iE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoped-functions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-block-scoped-functions/download/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz#bbc51b49f964d70cb8d8e0b94e820246ce3a6141"
  integrity sha1-u8UbSflk1wy42OC5ToICRs46YUE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoping@^6.23.0, babel-plugin-transform-es2015-block-scoping@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-block-scoping/download/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz#d70f5299c1308d05c12f463813b0a09e73b1895f"
  integrity sha1-1w9SmcEwjQXBL0Y4E7CgnnOxiV8=
  dependencies:
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-plugin-transform-es2015-classes@^6.23.0, babel-plugin-transform-es2015-classes@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-classes/download/babel-plugin-transform-es2015-classes-6.24.1.tgz#5a4c58a50c9c9461e564b4b2a3bfabc97a2584db"
  integrity sha1-WkxYpQyclGHlZLSyo7+ryXolhNs=
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-computed-properties@^6.22.0, babel-plugin-transform-es2015-computed-properties@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-computed-properties/download/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz#6fe2a8d16895d5634f4cd999b6d3480a308159b3"
  integrity sha1-b+Ko0WiV1WNPTNmZttNICjCBWbM=
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-destructuring@^6.22.0, babel-plugin-transform-es2015-destructuring@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-destructuring/download/babel-plugin-transform-es2015-destructuring-6.23.0.tgz#997bb1f1ab967f682d2b0876fe358d60e765c56d"
  integrity sha1-mXux8auWf2gtKwh2/jWNYOdlxW0=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-duplicate-keys@^6.22.0, babel-plugin-transform-es2015-duplicate-keys@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-duplicate-keys/download/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz#73eb3d310ca969e3ef9ec91c53741a6f1576423e"
  integrity sha1-c+s9MQypaePvnskcU3QabxV2Qj4=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-for-of@^6.22.0, babel-plugin-transform-es2015-for-of@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-for-of/download/babel-plugin-transform-es2015-for-of-6.23.0.tgz#f47c95b2b613df1d3ecc2fdb7573623c75248691"
  integrity sha1-9HyVsrYT3x0+zC/bdXNiPHUkhpE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@^6.22.0, babel-plugin-transform-es2015-function-name@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-function-name/download/babel-plugin-transform-es2015-function-name-6.24.1.tgz#834c89853bc36b1af0f3a4c5dbaa94fd8eacaa8b"
  integrity sha1-g0yJhTvDaxrw86TF26qU/Y6sqos=
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-literals@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-literals/download/babel-plugin-transform-es2015-literals-6.22.0.tgz#4f54a02d6cd66cf915280019a31d31925377ca2e"
  integrity sha1-T1SgLWzWbPkVKAAZox0xklN3yi4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-modules-amd@^6.22.0, babel-plugin-transform-es2015-modules-amd@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-modules-amd/download/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz#3b3e54017239842d6d19c3011c4bd2f00a00d154"
  integrity sha1-Oz5UAXI5hC1tGcMBHEvS8AoA0VQ=
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-commonjs@^6.23.0, babel-plugin-transform-es2015-modules-commonjs@^6.24.1:
  version "6.26.2"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz#58a793863a9e7ca870bdc5a881117ffac27db6f3"
  integrity sha1-WKeThjqefKhwvcWogRF/+sJ9tvM=
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-types "^6.26.0"

babel-plugin-transform-es2015-modules-systemjs@^6.23.0, babel-plugin-transform-es2015-modules-systemjs@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-modules-systemjs/download/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz#ff89a142b9119a906195f5f106ecf305d9407d23"
  integrity sha1-/4mhQrkRmpBhlfXxBuzzBdlAfSM=
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-umd@^6.23.0, babel-plugin-transform-es2015-modules-umd@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-modules-umd/download/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz#ac997e6285cd18ed6176adb607d602344ad38468"
  integrity sha1-rJl+YoXNGO1hdq22B9YCNErThGg=
  dependencies:
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-object-super@^6.22.0, babel-plugin-transform-es2015-object-super@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-object-super/download/babel-plugin-transform-es2015-object-super-6.24.1.tgz#24cef69ae21cb83a7f8603dad021f572eb278f8d"
  integrity sha1-JM72muIcuDp/hgPa0CH1cusnj40=
  dependencies:
    babel-helper-replace-supers "^6.24.1"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-parameters@^6.23.0, babel-plugin-transform-es2015-parameters@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-parameters/download/babel-plugin-transform-es2015-parameters-6.24.1.tgz#57ac351ab49caf14a97cd13b09f66fdf0a625f2b"
  integrity sha1-V6w1GrScrxSpfNE7CfZv3wpiXys=
  dependencies:
    babel-helper-call-delegate "^6.24.1"
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-shorthand-properties@^6.22.0, babel-plugin-transform-es2015-shorthand-properties@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-shorthand-properties/download/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz#24f875d6721c87661bbd99a4622e51f14de38aa0"
  integrity sha1-JPh11nIch2YbvZmkYi5R8U3jiqA=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-spread@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-spread/download/babel-plugin-transform-es2015-spread-6.22.0.tgz#d6d68a99f89aedc4536c81a542e8dd9f1746f8d1"
  integrity sha1-1taKmfia7cRTbIGlQujdnxdG+NE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@^6.22.0, babel-plugin-transform-es2015-sticky-regex@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-sticky-regex/download/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz#00c1cdb1aca71112cdf0cf6126c2ed6b457ccdbc"
  integrity sha1-AMHNsaynERLN8M9hJsLta0V8zbw=
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-template-literals@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-template-literals/download/babel-plugin-transform-es2015-template-literals-6.22.0.tgz#a84b3450f7e9f8f1f6839d6d687da84bb1236d8d"
  integrity sha1-qEs0UPfp+PH2g51taH2oS7EjbY0=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-typeof-symbol@^6.22.0, babel-plugin-transform-es2015-typeof-symbol@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-typeof-symbol/download/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz#dec09f1cddff94b52ac73d505c84df59dcceb372"
  integrity sha1-3sCfHN3/lLUqxz1QXITfWdzOs3I=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@^6.22.0, babel-plugin-transform-es2015-unicode-regex@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-es2015-unicode-regex/download/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz#d38b12f42ea7323f729387f18a7c5ae1faeb35e9"
  integrity sha1-04sS9C6nMj9yk4fxinxa4frrNek=
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-exponentiation-operator@^6.22.0, babel-plugin-transform-exponentiation-operator@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-exponentiation-operator/download/babel-plugin-transform-exponentiation-operator-6.24.1.tgz#2ab0c9c7f3098fa48907772bb813fe41e8de3a0e"
  integrity sha1-KrDJx/MJj6SJB3cruBP+QejeOg4=
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.24.1"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-export-extensions@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-export-extensions/download/babel-plugin-transform-export-extensions-6.22.0.tgz#53738b47e75e8218589eea946cbbd39109bbe653"
  integrity sha1-U3OLR+deghhYnuqUbLvTkQm75lM=
  dependencies:
    babel-plugin-syntax-export-extensions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-flow-strip-types@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-flow-strip-types/download/babel-plugin-transform-flow-strip-types-6.22.0.tgz#84cb672935d43714fdc32bce84568d87441cf7cf"
  integrity sha1-hMtnKTXUNxT9wyvOhFaNh0Qc988=
  dependencies:
    babel-plugin-syntax-flow "^6.18.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-function-bind@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-function-bind/download/babel-plugin-transform-function-bind-6.22.0.tgz#c6fb8e96ac296a310b8cf8ea401462407ddf6a97"
  integrity sha1-xvuOlqwpajELjPjqQBRiQH3fapc=
  dependencies:
    babel-plugin-syntax-function-bind "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-object-rest-spread@^6.22.0, babel-plugin-transform-object-rest-spread@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-object-rest-spread/download/babel-plugin-transform-object-rest-spread-6.26.0.tgz#0f36692d50fef6b7e2d4b3ac1478137a963b7b06"
  integrity sha1-DzZpLVD+9rfi1LOsFHgTepY7ewY=
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.8.0"
    babel-runtime "^6.26.0"

babel-plugin-transform-react-display-name@^6.23.0:
  version "6.25.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-react-display-name/download/babel-plugin-transform-react-display-name-6.25.0.tgz#67e2bf1f1e9c93ab08db96792e05392bf2cc28d1"
  integrity sha1-Z+K/Hx6ck6sI25Z5LgU5K/LMKNE=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-react-jsx-self@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-react-jsx-self/download/babel-plugin-transform-react-jsx-self-6.22.0.tgz#df6d80a9da2612a121e6ddd7558bcbecf06e636e"
  integrity sha1-322AqdomEqEh5t3XVYvL7PBuY24=
  dependencies:
    babel-plugin-syntax-jsx "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-react-jsx-source@^6.22.0:
  version "6.22.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-react-jsx-source/download/babel-plugin-transform-react-jsx-source-6.22.0.tgz#66ac12153f5cd2d17b3c19268f4bf0197f44ecd6"
  integrity sha1-ZqwSFT9c0tF7PBkmj0vwGX9E7NY=
  dependencies:
    babel-plugin-syntax-jsx "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-react-jsx@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-react-jsx/download/babel-plugin-transform-react-jsx-6.24.1.tgz#840a028e7df460dfc3a2d29f0c0d91f6376e66a3"
  integrity sha1-hAoCjn30YN/DotKfDA2R9jduZqM=
  dependencies:
    babel-helper-builder-react-jsx "^6.24.1"
    babel-plugin-syntax-jsx "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-regenerator@^6.22.0, babel-plugin-transform-regenerator@^6.24.1:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-regenerator/download/babel-plugin-transform-regenerator-6.26.0.tgz#e0703696fbde27f0a3efcacf8b4dca2f7b3a8f2f"
  integrity sha1-4HA2lvveJ/Cj78rPi03KL3s6jy8=
  dependencies:
    regenerator-transform "^0.10.0"

babel-plugin-transform-runtime@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-runtime/download/babel-plugin-transform-runtime-6.23.0.tgz#88490d446502ea9b8e7efb0fe09ec4d99479b1ee"
  integrity sha1-iEkNRGUC6puOfvsP4J7E2ZR5se4=
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-strict-mode@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz#d5faf7aa578a65bbe591cf5edae04a0c67020758"
  integrity sha1-1fr3qleKZbvlkc9e2uBKDGcCB1g=
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-polyfill@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-polyfill/download/babel-polyfill-6.26.0.tgz#379937abc67d7895970adc621f284cd966cf2153"
  integrity sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM=
  dependencies:
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    regenerator-runtime "^0.10.5"

babel-preset-env@^1.6.1, babel-preset-env@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-preset-env/download/babel-preset-env-1.7.0.tgz#dea79fa4ebeb883cd35dab07e260c1c9c04df77a"
  integrity sha1-3qefpOvriDzTXasH4mDBycBN93o=
  dependencies:
    babel-plugin-check-es2015-constants "^6.22.0"
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-to-generator "^6.22.0"
    babel-plugin-transform-es2015-arrow-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoping "^6.23.0"
    babel-plugin-transform-es2015-classes "^6.23.0"
    babel-plugin-transform-es2015-computed-properties "^6.22.0"
    babel-plugin-transform-es2015-destructuring "^6.23.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.22.0"
    babel-plugin-transform-es2015-for-of "^6.23.0"
    babel-plugin-transform-es2015-function-name "^6.22.0"
    babel-plugin-transform-es2015-literals "^6.22.0"
    babel-plugin-transform-es2015-modules-amd "^6.22.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.23.0"
    babel-plugin-transform-es2015-modules-systemjs "^6.23.0"
    babel-plugin-transform-es2015-modules-umd "^6.23.0"
    babel-plugin-transform-es2015-object-super "^6.22.0"
    babel-plugin-transform-es2015-parameters "^6.23.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.22.0"
    babel-plugin-transform-es2015-spread "^6.22.0"
    babel-plugin-transform-es2015-sticky-regex "^6.22.0"
    babel-plugin-transform-es2015-template-literals "^6.22.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.23.0"
    babel-plugin-transform-es2015-unicode-regex "^6.22.0"
    babel-plugin-transform-exponentiation-operator "^6.22.0"
    babel-plugin-transform-regenerator "^6.22.0"
    browserslist "^3.2.6"
    invariant "^2.2.2"
    semver "^5.3.0"

babel-preset-es2015@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-preset-es2015/download/babel-preset-es2015-6.24.1.tgz#d44050d6bc2c9feea702aaf38d727a0210538939"
  integrity sha1-1EBQ1rwsn+6nAqrzjXJ6AhBTiTk=
  dependencies:
    babel-plugin-check-es2015-constants "^6.22.0"
    babel-plugin-transform-es2015-arrow-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoping "^6.24.1"
    babel-plugin-transform-es2015-classes "^6.24.1"
    babel-plugin-transform-es2015-computed-properties "^6.24.1"
    babel-plugin-transform-es2015-destructuring "^6.22.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.24.1"
    babel-plugin-transform-es2015-for-of "^6.22.0"
    babel-plugin-transform-es2015-function-name "^6.24.1"
    babel-plugin-transform-es2015-literals "^6.22.0"
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-plugin-transform-es2015-modules-systemjs "^6.24.1"
    babel-plugin-transform-es2015-modules-umd "^6.24.1"
    babel-plugin-transform-es2015-object-super "^6.24.1"
    babel-plugin-transform-es2015-parameters "^6.24.1"
    babel-plugin-transform-es2015-shorthand-properties "^6.24.1"
    babel-plugin-transform-es2015-spread "^6.22.0"
    babel-plugin-transform-es2015-sticky-regex "^6.24.1"
    babel-plugin-transform-es2015-template-literals "^6.22.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.22.0"
    babel-plugin-transform-es2015-unicode-regex "^6.24.1"
    babel-plugin-transform-regenerator "^6.24.1"

babel-preset-es2016@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-preset-es2016/download/babel-preset-es2016-6.24.1.tgz#f900bf93e2ebc0d276df9b8ab59724ebfd959f8b"
  integrity sha1-+QC/k+LrwNJ235uKtZck6/2Vn4s=
  dependencies:
    babel-plugin-transform-exponentiation-operator "^6.24.1"

babel-preset-flow@^6.23.0:
  version "6.23.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-preset-flow/download/babel-preset-flow-6.23.0.tgz#e71218887085ae9a24b5be4169affb599816c49d"
  integrity sha1-5xIYiHCFrpoktb5Baa/7WZgWxJ0=
  dependencies:
    babel-plugin-transform-flow-strip-types "^6.22.0"

babel-preset-react@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-preset-react/download/babel-preset-react-6.24.1.tgz#ba69dfaea45fc3ec639b6a4ecea6e17702c91380"
  integrity sha1-umnfrqRfw+xjm2pOzqbhdwLJE4A=
  dependencies:
    babel-plugin-syntax-jsx "^6.3.13"
    babel-plugin-transform-react-display-name "^6.23.0"
    babel-plugin-transform-react-jsx "^6.24.1"
    babel-plugin-transform-react-jsx-self "^6.22.0"
    babel-plugin-transform-react-jsx-source "^6.22.0"
    babel-preset-flow "^6.23.0"

babel-preset-stage-0@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-preset-stage-0/download/babel-preset-stage-0-6.24.1.tgz#5642d15042f91384d7e5af8bc88b1db95b039e6a"
  integrity sha1-VkLRUEL5E4TX5a+LyIsduVsDnmo=
  dependencies:
    babel-plugin-transform-do-expressions "^6.22.0"
    babel-plugin-transform-function-bind "^6.22.0"
    babel-preset-stage-1 "^6.24.1"

babel-preset-stage-1@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-preset-stage-1/download/babel-preset-stage-1-6.24.1.tgz#7692cd7dcd6849907e6ae4a0a85589cfb9e2bfb0"
  integrity sha1-dpLNfc1oSZB+auSgqFWJz7niv7A=
  dependencies:
    babel-plugin-transform-class-constructor-call "^6.24.1"
    babel-plugin-transform-export-extensions "^6.22.0"
    babel-preset-stage-2 "^6.24.1"

babel-preset-stage-2@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-preset-stage-2/download/babel-preset-stage-2-6.24.1.tgz#d9e2960fb3d71187f0e64eec62bc07767219bdc1"
  integrity sha1-2eKWD7PXEYfw5k7sYrwHdnIZvcE=
  dependencies:
    babel-plugin-syntax-dynamic-import "^6.18.0"
    babel-plugin-transform-class-properties "^6.24.1"
    babel-plugin-transform-decorators "^6.24.1"
    babel-preset-stage-3 "^6.24.1"

babel-preset-stage-3@^6.24.1:
  version "6.24.1"
  resolved "https://registry.npm.alibaba-inc.com/babel-preset-stage-3/download/babel-preset-stage-3-6.24.1.tgz#836ada0a9e7a7fa37cb138fb9326f87934a48395"
  integrity sha1-g2raCp56f6N8sTj7kyb4eTSkg5U=
  dependencies:
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-generator-functions "^6.24.1"
    babel-plugin-transform-async-to-generator "^6.24.1"
    babel-plugin-transform-exponentiation-operator "^6.24.1"
    babel-plugin-transform-object-rest-spread "^6.22.0"

babel-register@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-register/download/babel-register-6.26.0.tgz#6ed021173e2fcb486d7acb45c6009a856f647071"
  integrity sha1-btAhFz4vy0htestFxgCahW9kcHE=
  dependencies:
    babel-core "^6.26.0"
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    home-or-tmp "^2.0.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    source-map-support "^0.4.15"

babel-runtime@6.x, babel-runtime@^6.18.0, babel-runtime@^6.2.0, babel-runtime@^6.22.0, babel-runtime@^6.23.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.24.1, babel-template@^6.26.0, babel-template@^6.3.0:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-template/download/babel-template-6.26.0.tgz#de03e2d16396b069f46dd9fff8521fb1a0e35e02"
  integrity sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.23.1, babel-traverse@^6.24.1, babel-traverse@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-traverse/download/babel-traverse-6.26.0.tgz#46a9cbd7edcc62c8e5c064e2d2d8d0f4035766ee"
  integrity sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.19.0, babel-types@^6.23.0, babel-types@^6.24.1, babel-types@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.alibaba-inc.com/babel-types/download/babel-types-6.26.0.tgz#a3b073f94ab49eb6fa55cd65227a334380632497"
  integrity sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@7.0.0-beta.44:
  version "7.0.0-beta.44"
  resolved "https://registry.npm.alibaba-inc.com/babylon/download/babylon-7.0.0-beta.44.tgz#89159e15e6e30c5096e22d738d8c0af8a0e8ca1d"
  integrity sha1-iRWeFebjDFCW4i1zjYwK+KDoyh0=

babylon@^6.17.0, babylon@^6.18.0:
  version "6.18.0"
  resolved "https://registry.npm.alibaba-inc.com/babylon/download/babylon-6.18.0.tgz#af2f3b88fa6f5c1e4c634d1a0f8eac4f55b395e3"
  integrity sha1-ry87iPpvXB5MY00aD46sT1WzleM=

balanced-match@0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.alibaba-inc.com/balanced-match/download/balanced-match-0.1.0.tgz#b504bd05869b39259dd0c5efc35d843176dccc4a"
  integrity sha1-tQS9BYabOSWd0MXvw12EMXbczEo=

balanced-match@^0.4.2:
  version "0.4.2"
  resolved "https://registry.npm.alibaba-inc.com/balanced-match/download/balanced-match-0.4.2.tgz#cb3f3e3c732dc0f01ee70b403f302e61d7709838"
  integrity sha1-yz8+PHMtwPAe5wtAPzAuYddwmDg=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.0.2:
  version "1.5.1"
  resolved "https://registry.npm.alibaba-inc.com/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.npm.alibaba-inc.com/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.npm.alibaba-inc.com/batch/download/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

big.js@^3.1.3:
  version "3.2.0"
  resolved "https://registry.npm.alibaba-inc.com/big.js/download/big.js-3.2.0.tgz#a5fc298b81b9e0dca2e458824784b65c52ba588e"
  integrity sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npm.alibaba-inc.com/big.js/download/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

big.js@^6.2.0:
  version "6.2.1"
  resolved "https://registry.npm.alibaba-inc.com/big.js/download/big.js-6.2.1.tgz#7205ce763efb17c2e41f26f121c420c6a7c2744f"
  integrity sha512-bCtHMwL9LeDIozFn+oNhhFoq+yQ3BNdnsLSASUxLciOb1vgvpHsIO1dsENiGMgbb4SkP5TrzWzRiLddn8ahVOQ==

bignumber.js@^9.0.0:
  version "9.1.0"
  resolved "https://registry.npm.alibaba-inc.com/bignumber.js/download/bignumber.js-9.1.0.tgz#8d340146107fe3a6cb8d40699643c302e8773b62"
  integrity sha512-4LwHK4nfDOraBCtst+wOWIHbu1vhvAPJK8g8nROd4iuc3PSEjWif/qwbkh8jwCJz6yDBvtU4KPynETgrfh7y3A==

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "https://registry.npm.alibaba-inc.com/binary-extensions/download/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/binary-extensions/download/binary-extensions-2.2.0.tgz#75f502eeaf9ffde42fc98829645be4ea76bd9e2d"
  integrity sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=

bindings@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npm.alibaba-inc.com/bindings/download/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

block-stream@*:
  version "0.0.9"
  resolved "https://registry.npm.alibaba-inc.com/block-stream/download/block-stream-0.0.9.tgz#13ebfe778a03205cfe03751481ebb4b3300c126a"
  integrity sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=
  dependencies:
    inherits "~2.0.0"

bluebird@^3.1.1, bluebird@^3.5.5:
  version "3.7.2"
  resolved "https://registry.npm.alibaba-inc.com/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.11.9:
  version "4.12.0"
  resolved "https://registry.npm.alibaba-inc.com/bn.js/download/bn.js-4.12.0.tgz#775b3f278efbb9718eec7361f483fb36fbbfea88"
  integrity sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=

bn.js@^5.0.0, bn.js@^5.1.1:
  version "5.2.1"
  resolved "https://registry.npm.alibaba-inc.com/bn.js/download/bn.js-5.2.1.tgz#0bc527a6a0d18d0aa8d5b0538ce4a77dccfa7b70"
  integrity sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ==

body-parser@1.20.1:
  version "1.20.1"
  resolved "https://registry.npm.alibaba-inc.com/body-parser/download/body-parser-1.20.1.tgz#b1812a8912c195cd371a3ee5e66faa2338a5c668"
  integrity sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://registry.npm.alibaba-inc.com/bonjour/download/bonjour-3.5.0.tgz#8e890a183d8ee9a2393b3844c691a42bcf7bc9f5"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npm.alibaba-inc.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "https://registry.npm.alibaba-inc.com/braces/download/braces-1.8.5.tgz#ba77962e12dff969d6b76711e914b737857bf6a7"
  integrity sha1-uneWLhLf+WnWt2cR6RS3N4V79qc=
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npm.alibaba-inc.com/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.alibaba-inc.com/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

brorand@^1.0.1, brorand@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/brorand/download/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/browserify-aes/download/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/browserify-des/download/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.0.1:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/browserify-rsa/download/browserify-rsa-4.1.0.tgz#b2fd06b5b75ae297f7ce2dc651f918f5be158c8d"
  integrity sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=
  dependencies:
    bn.js "^5.0.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.2.1"
  resolved "https://registry.npm.alibaba-inc.com/browserify-sign/download/browserify-sign-4.2.1.tgz#eaf4add46dd54be3bb3b36c0cf15abbeba7956c3"
  integrity sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM=
  dependencies:
    bn.js "^5.1.1"
    browserify-rsa "^4.0.1"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.3"
    inherits "^2.0.4"
    parse-asn1 "^5.1.5"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^1.3.6, browserslist@^1.5.2, browserslist@^1.7.6:
  version "1.7.7"
  resolved "https://registry.npm.alibaba-inc.com/browserslist/download/browserslist-1.7.7.tgz#0bd76704258be829b2398bb50e4b62d1a166b0b9"
  integrity sha1-C9dnBCWL6CmyOYu1Dkti0aFmsLk=
  dependencies:
    caniuse-db "^1.0.30000639"
    electron-to-chromium "^1.2.7"

browserslist@^2.0.0, browserslist@^2.11.3:
  version "2.11.3"
  resolved "https://registry.npm.alibaba-inc.com/browserslist/download/browserslist-2.11.3.tgz#fe36167aed1bbcde4827ebfe71347a2cc70b99b2"
  integrity sha1-/jYWeu0bvN5IJ+v+cTR6LMcLmbI=
  dependencies:
    caniuse-lite "^1.0.30000792"
    electron-to-chromium "^1.3.30"

browserslist@^3.2.6:
  version "3.2.8"
  resolved "https://registry.npm.alibaba-inc.com/browserslist/download/browserslist-3.2.8.tgz#b0005361d6471f0f5952797a76fc985f1f978fc6"
  integrity sha1-sABTYdZHHw9ZUnl6dvyYXx+Xj8Y=
  dependencies:
    caniuse-lite "^1.0.30000844"
    electron-to-chromium "^1.3.47"

browserslist@^4.21.3:
  version "4.21.4"
  resolved "https://registry.npm.alibaba-inc.com/browserslist/download/browserslist-4.21.4.tgz#e7496bbc67b9e39dd0f98565feccdcb0d4ff6987"
  integrity sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==
  dependencies:
    caniuse-lite "^1.0.30001400"
    electron-to-chromium "^1.4.251"
    node-releases "^2.0.6"
    update-browserslist-db "^1.0.9"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz#52fabcc6a606d1a00302802648ef68f639da268c"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/buffer-xor/download/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.2"
  resolved "https://registry.npm.alibaba-inc.com/buffer/download/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/bytes/download/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npm.alibaba-inc.com/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

cacache@^12.0.2:
  version "12.0.4"
  resolved "https://registry.npm.alibaba-inc.com/cacache/download/cacache-12.0.4.tgz#668bcbd105aeb5f1d92fe25570ec9525c8faa40c"
  integrity sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/call-bind/download/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@3.0.x:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/camel-case/download/camel-case-3.0.0.tgz#ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/camelcase-keys/download/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  integrity sha1-MIvur/3ygRkFHvodkyITyRuPkuc=
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/camelcase/download/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/camelcase/download/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

camelcase@^5.0.0:
  version "5.3.1"
  resolved "https://registry.npm.alibaba-inc.com/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caniuse-api@^1.5.2:
  version "1.6.1"
  resolved "https://registry.npm.alibaba-inc.com/caniuse-api/download/caniuse-api-1.6.1.tgz#b534e7c734c4f81ec5fbe8aca2ad24354b962c6c"
  integrity sha1-tTTnxzTE+B7F++isoq0kNUuWLGw=
  dependencies:
    browserslist "^1.3.6"
    caniuse-db "^1.0.30000529"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-api@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/caniuse-api/download/caniuse-api-2.0.0.tgz#b1ddb5a5966b16f48dc4998444d4bbc6c7d9d834"
  integrity sha1-sd21pZZrFvSNxJmERNS7xsfZ2DQ=
  dependencies:
    browserslist "^2.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-db@^1.0.30000529, caniuse-db@^1.0.30000634, caniuse-db@^1.0.30000639:
  version "1.0.30001434"
  resolved "https://registry.npm.alibaba-inc.com/caniuse-db/download/caniuse-db-1.0.30001434.tgz#03033e27262617a7dee9ae89b6a2a5c76b03c643"
  integrity sha512-S3C9KcO14uBVr1NDSwx3bGprJTiY0q90IyC2MyJWUmGxkPjsuWODfv/8Tl67Dok/n4q2l4yzS0/MqWDu/rZxDQ==

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30000792, caniuse-lite@^1.0.30000805, caniuse-lite@^1.0.30000844, caniuse-lite@^1.0.30001400:
  version "1.0.30001434"
  resolved "https://registry.npm.alibaba-inc.com/caniuse-lite/download/caniuse-lite-1.0.30001434.tgz#ec1ec1cfb0a93a34a0600d37903853030520a4e5"
  integrity sha512-aOBHrLmTQw//WFa2rcF1If9fa3ypkC1wzqqiKHgfdrXTWcU8C4gKVZT77eQAPWN1APys3+uQ0Df07rKauXGEYA==

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.npm.alibaba-inc.com/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.alibaba-inc.com/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npm.alibaba-inc.com/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.alibaba-inc.com/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

chokidar@^1.6.1:
  version "1.7.0"
  resolved "https://registry.npm.alibaba-inc.com/chokidar/download/chokidar-1.7.0.tgz#798e689778151c8076b4b360e5edd28cda2bb468"
  integrity sha1-eY5ol3gVHIB2tLNg5e3SjNortGg=
  dependencies:
    anymatch "^1.3.0"
    async-each "^1.0.0"
    glob-parent "^2.0.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^2.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
  optionalDependencies:
    fsevents "^1.0.0"

chokidar@^2.1.8:
  version "2.1.8"
  resolved "https://registry.npm.alibaba-inc.com/chokidar/download/chokidar-2.1.8.tgz#804b3a7b6a99358c3c5c61e71d8728f041cff917"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^3.4.1:
  version "3.5.3"
  resolved "https://registry.npm.alibaba-inc.com/chokidar/download/chokidar-3.5.3.tgz#1cf37c8707b932bd1af1ae22c0432e2acd1903bd"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^1.1.1:
  version "1.1.4"
  resolved "https://registry.npm.alibaba-inc.com/chownr/download/chownr-1.1.4.tgz#6fc9d7b42d32a583596337666e7d08084da2cc6b"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/chrome-trace-event/download/chrome-trace-event-1.0.3.tgz#1015eced4741e15d06664a957dbbf50d041e26ac"
  integrity sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/cipher-base/download/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

clap@^1.0.9:
  version "1.2.3"
  resolved "https://registry.npm.alibaba-inc.com/clap/download/clap-1.2.3.tgz#4f36745b32008492557f46412d66d50cb99bce51"
  integrity sha1-TzZ0WzIAhJJVf0ZBLWbVDLmbzlE=
  dependencies:
    chalk "^1.1.3"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.npm.alibaba-inc.com/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

classnames@2.x, classnames@^2.2.0, classnames@^2.2.1, classnames@^2.2.3, classnames@^2.2.4, classnames@^2.2.5, classnames@^2.2.6:
  version "2.3.2"
  resolved "https://registry.npm.alibaba-inc.com/classnames/download/classnames-2.3.2.tgz#351d813bf0137fcc6a76a16b88208d2560a0d924"
  integrity sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==

clean-css@4.2.x:
  version "4.2.4"
  resolved "https://registry.npm.alibaba-inc.com/clean-css/download/clean-css-4.2.4.tgz#733bf46eba4e607c6891ea57c24a989356831178"
  integrity sha1-czv0brpOYHxokepXwkqYk1aDEXg=
  dependencies:
    source-map "~0.6.0"

clean-webpack-plugin@^0.1.17:
  version "0.1.19"
  resolved "https://registry.npm.alibaba-inc.com/clean-webpack-plugin/download/clean-webpack-plugin-0.1.19.tgz#ceda8bb96b00fe168e9b080272960d20fdcadd6d"
  integrity sha1-ztqLuWsA/haOmwgCcpYNIP3K3W0=
  dependencies:
    rimraf "^2.6.1"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-width@^2.0.0:
  version "2.2.1"
  resolved "https://registry.npm.alibaba-inc.com/cli-width/download/cli-width-2.2.1.tgz#b0433d0b4e9c847ef18868a4ef16fd5fc8271c48"
  integrity sha1-sEM9C06chH7xiGik7xb9X8gnHEg=

cliui@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.alibaba-inc.com/cliui/download/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.alibaba-inc.com/cliui/download/cliui-6.0.0.tgz#511d702c0c4e41ca156d7d0e96021f23e13225b1"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone-deep@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/clone-deep/download/clone-deep-2.0.2.tgz#00db3a1e173656730d1188c3d6aced6d7ea97713"
  integrity sha1-ANs6Hhc2VnMNEYjD1qztbX6pdxM=
  dependencies:
    for-own "^1.0.0"
    is-plain-object "^2.0.4"
    kind-of "^6.0.0"
    shallow-clone "^1.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clone@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npm.alibaba-inc.com/clone/download/clone-2.1.2.tgz#1b7f4b9f591f1e8f83670401600345a02887435f"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npm.alibaba-inc.com/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

coa@~1.0.1:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/coa/download/coa-1.0.4.tgz#a9ef153660d6a86a8bdec0289a5c684d217432fd"
  integrity sha1-qe8VNmDWqGqL3sAomlxoTSF0Mv0=
  dependencies:
    q "^1.1.2"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.3.0, color-convert@^1.8.2, color-convert@^1.9.0, color-convert@^1.9.1:
  version "1.9.3"
  resolved "https://registry.npm.alibaba-inc.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.alibaba-inc.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.alibaba-inc.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.alibaba-inc.com/color-string/download/color-string-0.3.0.tgz#27d46fb67025c5c2fa25993bfbf579e47841b991"
  integrity sha1-J9RvtnAlxcL6JZk7+/V55HhBuZE=
  dependencies:
    color-name "^1.0.0"

color-string@^1.4.0, color-string@^1.5.2:
  version "1.9.1"
  resolved "https://registry.npm.alibaba-inc.com/color-string/download/color-string-1.9.1.tgz#4467f9146f036f855b764dfb5bf8582bf342c7a4"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^0.11.0:
  version "0.11.4"
  resolved "https://registry.npm.alibaba-inc.com/color/download/color-0.11.4.tgz#6d7b5c74fb65e841cd48792ad1ed5e07b904d764"
  integrity sha1-bXtcdPtl6EHNSHkq0e1eB7kE12Q=
  dependencies:
    clone "^1.0.2"
    color-convert "^1.3.0"
    color-string "^0.3.0"

color@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/color/download/color-1.0.3.tgz#e48e832d85f14ef694fb468811c2d5cfe729b55d"
  integrity sha1-5I6DLYXxTvaU+0aIEcLVz+cptV0=
  dependencies:
    color-convert "^1.8.2"
    color-string "^1.4.0"

color@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/color/download/color-2.0.1.tgz#e4ed78a3c4603d0891eba5430b04b86314f4c839"
  integrity sha1-5O14o8RgPQiR66VDCwS4YxT0yDk=
  dependencies:
    color-convert "^1.9.1"
    color-string "^1.5.2"

colormin@^1.0.5:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/colormin/download/colormin-1.1.2.tgz#ea2f7420a72b96881a38aae59ec124a6f7298133"
  integrity sha1-6i90IKcrlogaOKrlnsEkpvcpgTM=
  dependencies:
    color "^0.11.0"
    css-color-names "0.0.4"
    has "^1.0.1"

colors@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/colors/download/colors-1.1.2.tgz#168a4701756b6a7f51a12ce0c97bfa28c084ed63"
  integrity sha1-FopHAXVran9RoSzgyXv6KMCE7WM=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://registry.npm.alibaba-inc.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@*:
  version "9.4.1"
  resolved "https://registry.npm.alibaba-inc.com/commander/download/commander-9.4.1.tgz#d1dd8f2ce6faf93147295c0df13c7c21141cfbdd"
  integrity sha512-5EEkTNyHNGFPD2H+c/dXXfQZYa/scCKasxWcXJaWnNJ99pnQN9Vnmqow+p+PlFPE63Q6mThaZws1T+HxfpgtPw==

commander@2.17.x:
  version "2.17.1"
  resolved "https://registry.npm.alibaba-inc.com/commander/download/commander-2.17.1.tgz#bd77ab7de6de94205ceacc72f1716d29f20a77bf"
  integrity sha1-vXerfebelCBc6sxy8XFtKfIKd78=

commander@^2.11.0, commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry.npm.alibaba-inc.com/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@~2.15.0:
  version "2.15.1"
  resolved "https://registry.npm.alibaba-inc.com/commander/download/commander-2.15.1.tgz#df46e867d0fc2aec66a34662b406a9ccafff5b0f"
  integrity sha1-30boZ9D8Kuxmo0ZitAapzK//Ww8=

commander@~2.19.0:
  version "2.19.0"
  resolved "https://registry.npm.alibaba-inc.com/commander/download/commander-2.19.0.tgz#f6198aa84e5b83c46054b94ddedbfed5ee9ff12a"
  integrity sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-classes@^1.2.5:
  version "1.2.6"
  resolved "https://registry.npm.alibaba-inc.com/component-classes/download/component-classes-1.2.6.tgz#c642394c3618a4d8b0b8919efccbbd930e5cd691"
  integrity sha1-xkI5TDYYpNiwuJGe/Mu9kw5c1pE=
  dependencies:
    component-indexof "0.0.3"

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

component-indexof@0.0.3:
  version "0.0.3"
  resolved "https://registry.npm.alibaba-inc.com/component-indexof/download/component-indexof-0.0.3.tgz#11d091312239eb8f32c8f25ae9cb002ffe8d3c24"
  integrity sha1-EdCRMSI5648yyPJa6csAL/6NPCQ=

component-type@1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.alibaba-inc.com/component-type/download/component-type-1.2.1.tgz#8a47901700238e4fc32269771230226f24b415a9"
  integrity sha1-ikeQFwAjjk/DIml3EjAibyS0Fak=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.npm.alibaba-inc.com/compressible/download/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npm.alibaba-inc.com/compression/download/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.alibaba-inc.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "https://registry.npm.alibaba-inc.com/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

confusing-browser-globals@^1.0.5:
  version "1.0.11"
  resolved "https://registry.npm.alibaba-inc.com/confusing-browser-globals/download/confusing-browser-globals-1.0.11.tgz#ae40e9b57cdd3915408a2805ebd3a5585608dc81"
  integrity sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA==

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npm.alibaba-inc.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/console-browserify/download/console-browserify-1.2.0.tgz#67063cef57ceb6cf4993a2ab3a55840ae8c49336"
  integrity sha1-ZwY871fOts9Jk6KrOlWECujEkzY=

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/console-control-strings/download/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

consolidate@^0.15.1:
  version "0.15.1"
  resolved "https://registry.npm.alibaba-inc.com/consolidate/download/consolidate-0.15.1.tgz#21ab043235c71a07d45d9aad98593b0dba56bab7"
  integrity sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=
  dependencies:
    bluebird "^3.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/constants-browserify/download/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

contains-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.alibaba-inc.com/contains-path/download/contains-path-0.1.0.tgz#fe8cf184ff6670b6baef01a9d4861a5cbec4120a"
  integrity sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://registry.npm.alibaba-inc.com/content-disposition/download/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/content-type/download/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

convert-source-map@^1.5.0, convert-source-map@^1.5.1, convert-source-map@^1.7.0:
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/convert-source-map/download/convert-source-map-1.9.0.tgz#7faae62353fb4213366d0ca98358d22e8368b05f"
  integrity sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npm.alibaba-inc.com/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.5.0:
  version "0.5.0"
  resolved "https://registry.npm.alibaba-inc.com/cookie/download/cookie-0.5.0.tgz#d1f5d71adec6558c58f389987c366aa47e994f8b"
  integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==

copy-anything@^2.0.1:
  version "2.0.6"
  resolved "https://registry.npm.alibaba-inc.com/copy-anything/download/copy-anything-2.0.6.tgz#092454ea9584a7b7ad5573062b2a87f5900fc480"
  integrity sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==
  dependencies:
    is-what "^3.14.1"

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.alibaba-inc.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

core-js-pure@^3.25.1:
  version "3.26.1"
  resolved "https://registry.npm.alibaba-inc.com/core-js-pure/download/core-js-pure-3.26.1.tgz#653f4d7130c427820dcecd3168b594e8bb095a33"
  integrity sha512-VVXcDpp/xJ21KdULRq/lXdLzQAtX7+37LzpyfFM973il0tWSsDEoyzG38G14AjTpK9VTfiNM9jnFauq/CpaWGQ==

core-js@^1.0.0:
  version "1.2.7"
  resolved "https://registry.npm.alibaba-inc.com/core-js/download/core-js-1.2.7.tgz#652294c14651db28fa93bd2d5ff2983a4f08c636"
  integrity sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY=

core-js@^2.4.0, core-js@^2.5.0:
  version "2.6.12"
  resolved "https://registry.npm.alibaba-inc.com/core-js/download/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^5.0.0:
  version "5.2.1"
  resolved "https://registry.npm.alibaba-inc.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

cosmiconfig@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.alibaba-inc.com/cosmiconfig/download/cosmiconfig-6.0.0.tgz#da4fee853c52f6b1e6935f41c1a2fc50bd4a9982"
  integrity sha1-2k/uhTxS9rHmk19BwaL8UL1KmYI=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.7.2"

crc-32@^1.2.0:
  version "1.2.2"
  resolved "https://registry.npm.alibaba-inc.com/crc-32/download/crc-32-1.2.2.tgz#3cad35a934b8bf71f25ca524b6da51fb7eace2ff"
  integrity sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==

create-ecdh@^4.0.0:
  version "4.0.4"
  resolved "https://registry.npm.alibaba-inc.com/create-ecdh/download/create-ecdh-4.0.4.tgz#d6e7f4bffa66736085a0762fd3a632684dabcc4e"
  integrity sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/create-hash/download/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "https://registry.npm.alibaba-inc.com/create-hmac/download/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

create-react-class@^15.6.0:
  version "15.7.0"
  resolved "https://registry.npm.alibaba-inc.com/create-react-class/download/create-react-class-15.7.0.tgz#7499d7ca2e69bb51d13faf59bd04f0c65a1d6c1e"
  integrity sha1-dJnXyi5pu1HRP69ZvQTwxlodbB4=
  dependencies:
    loose-envify "^1.3.1"
    object-assign "^4.1.1"

cross-spawn@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/cross-spawn/download/cross-spawn-3.0.1.tgz#1256037ecb9f0c5f79e3d6ef135e30770184b982"
  integrity sha1-ElYDfsufDF9549bvE14wdwGEuYI=
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://registry.npm.alibaba-inc.com/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "https://registry.npm.alibaba-inc.com/crypto-browserify/download/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-animation@1.x, css-animation@^1.3.2:
  version "1.6.1"
  resolved "https://registry.npm.alibaba-inc.com/css-animation/download/css-animation-1.6.1.tgz#162064a3b0d51f958b7ff37b3d6d4de18e17039e"
  integrity sha1-FiBko7DVH5WLf/N7PW1N4Y4XA54=
  dependencies:
    babel-runtime "6.x"
    component-classes "^1.2.5"

css-color-function@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npm.alibaba-inc.com/css-color-function/download/css-color-function-1.3.3.tgz#8ed24c2c0205073339fafa004bc8c141fccb282e"
  integrity sha1-jtJMLAIFBzM5+voAS8jBQfzLKC4=
  dependencies:
    balanced-match "0.1.0"
    color "^0.11.0"
    debug "^3.1.0"
    rgb "~0.1.0"

css-color-names@0.0.4:
  version "0.0.4"
  resolved "https://registry.npm.alibaba-inc.com/css-color-names/download/css-color-names-0.0.4.tgz#808adc2e79cf84738069b646cb20ec27beb629e0"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-hot-loader@^1.4.2:
  version "1.4.4"
  resolved "https://registry.npm.alibaba-inc.com/css-hot-loader/download/css-hot-loader-1.4.4.tgz#ae784932cd8b7d092f7f15702af08b3ec9436052"
  integrity sha1-rnhJMs2LfQkvfxVwKvCLPslDYFI=
  dependencies:
    loader-utils "^1.1.0"
    lodash "^4.17.5"
    normalize-url "^1.9.1"

css-loader@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/css-loader/download/css-loader-1.0.1.tgz#6885bb5233b35ec47b006057da01cc640b6b79fe"
  integrity sha1-aIW7UjOzXsR7AGBX2gHMZAtref4=
  dependencies:
    babel-code-frame "^6.26.0"
    css-selector-tokenizer "^0.7.0"
    icss-utils "^2.1.0"
    loader-utils "^1.0.2"
    lodash "^4.17.11"
    postcss "^6.0.23"
    postcss-modules-extract-imports "^1.2.0"
    postcss-modules-local-by-default "^1.2.0"
    postcss-modules-scope "^1.1.0"
    postcss-modules-values "^1.3.0"
    postcss-value-parser "^3.3.0"
    source-list-map "^2.0.0"

css-select@^4.1.3:
  version "4.3.0"
  resolved "https://registry.npm.alibaba-inc.com/css-select/download/css-select-4.3.0.tgz#db7129b2846662fd8628cfc496abb2b59e41529b"
  integrity sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.0.1"
    domhandler "^4.3.1"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-selector-tokenizer@^0.7.0:
  version "0.7.3"
  resolved "https://registry.npm.alibaba-inc.com/css-selector-tokenizer/download/css-selector-tokenizer-0.7.3.tgz#735f26186e67c749aaf275783405cf0661fae8f1"
  integrity sha1-c18mGG5nx0mq8nV4NAXPBmH66PE=
  dependencies:
    cssesc "^3.0.0"
    fastparse "^1.1.2"

css-tree@1.0.0-alpha25:
  version "1.0.0-alpha25"
  resolved "https://registry.npm.alibaba-inc.com/css-tree/download/css-tree-1.0.0-alpha25.tgz#1bbfabfbf6eeef4f01d9108ff2edd0be2fe35597"
  integrity sha1-G7+r+/bu708B2RCP8u3Qvi/jVZc=
  dependencies:
    mdn-data "^1.0.0"
    source-map "^0.5.3"

css-unit-converter@^1.1.1:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/css-unit-converter/download/css-unit-converter-1.1.2.tgz#4c77f5a1954e6dbff60695ecb214e3270436ab21"
  integrity sha1-THf1oZVObb/2BpXsshTjJwQ2qyE=

css-what@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npm.alibaba-inc.com/css-what/download/css-what-6.1.0.tgz#fb5effcf76f1ddea2c81bdfaa4de44e79bac70f4"
  integrity sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano@^3.10.0:
  version "3.10.0"
  resolved "https://registry.npm.alibaba-inc.com/cssnano/download/cssnano-3.10.0.tgz#4f38f6cea2b9b17fa01490f23f1dc68ea65c1c38"
  integrity sha1-Tzj2zqK5sX+gFJDyPx3GjqZcHDg=
  dependencies:
    autoprefixer "^6.3.1"
    decamelize "^1.1.2"
    defined "^1.0.0"
    has "^1.0.1"
    object-assign "^4.0.1"
    postcss "^5.0.14"
    postcss-calc "^5.2.0"
    postcss-colormin "^2.1.8"
    postcss-convert-values "^2.3.4"
    postcss-discard-comments "^2.0.4"
    postcss-discard-duplicates "^2.0.1"
    postcss-discard-empty "^2.0.1"
    postcss-discard-overridden "^0.1.1"
    postcss-discard-unused "^2.2.1"
    postcss-filter-plugins "^2.0.0"
    postcss-merge-idents "^2.1.5"
    postcss-merge-longhand "^2.0.1"
    postcss-merge-rules "^2.0.3"
    postcss-minify-font-values "^1.0.2"
    postcss-minify-gradients "^1.0.1"
    postcss-minify-params "^1.0.4"
    postcss-minify-selectors "^2.0.4"
    postcss-normalize-charset "^1.1.0"
    postcss-normalize-url "^3.0.7"
    postcss-ordered-values "^2.1.0"
    postcss-reduce-idents "^2.2.2"
    postcss-reduce-initial "^1.0.0"
    postcss-reduce-transforms "^1.0.3"
    postcss-svgo "^2.1.1"
    postcss-unique-selectors "^2.0.2"
    postcss-value-parser "^3.2.3"
    postcss-zindex "^2.0.1"

csso@3.4.x:
  version "3.4.0"
  resolved "https://registry.npm.alibaba-inc.com/csso/download/csso-3.4.0.tgz#57b27ef553cccbf5aa964c641748641e9af113f3"
  integrity sha1-V7J+9VPMy/WqlkxkF0hkHprxE/M=
  dependencies:
    css-tree "1.0.0-alpha25"

csso@~2.3.1:
  version "2.3.2"
  resolved "https://registry.npm.alibaba-inc.com/csso/download/csso-2.3.2.tgz#ddd52c587033f49e94b71fc55569f252e8ff5f85"
  integrity sha1-3dUsWHAz9J6Utx/FVWnyUuj/X4U=
  dependencies:
    clap "^1.0.9"
    source-map "^0.5.3"

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.alibaba-inc.com/currently-unhandled/download/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  integrity sha1-mI3zP+qxke95mmE2nddsF635V+o=
  dependencies:
    array-find-index "^1.0.1"

cyclist@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/cyclist/download/cyclist-1.0.1.tgz#596e9698fd0c80e12038c2b82d6eb1b35b6224d9"
  integrity sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=

damerau-levenshtein@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npm.alibaba-inc.com/damerau-levenshtein/download/damerau-levenshtein-1.0.8.tgz#b43d286ccbd36bc5b2f7ed41caf2d0aba1f8a6e7"
  integrity sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.npm.alibaba-inc.com/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

dayjs@^1.9.6:
  version "1.11.6"
  resolved "https://registry.npm.alibaba-inc.com/dayjs/download/dayjs-1.11.6.tgz#2e79a226314ec3ec904e3ee1dd5a4f5e5b1c7afb"
  integrity sha512-zZbY5giJAinCG+7AGaw0wIhNZ6J8AhWuSXKvuc1KAyMiRsvGQWqh4L+MomvhdAYjN+lqvVCMq1I41e3YHvXkyQ==

de-indent@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/de-indent/download/de-indent-1.0.2.tgz#b2038e846dc33baa5796128d0804b455b8c1e21d"
  integrity sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=

debug@2.6.9, debug@^2.2.0, debug@^2.3.3, debug@^2.6.8, debug@^2.6.9:
  version "2.6.9"
  resolved "https://registry.npm.alibaba-inc.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@=3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@^3.1.0, debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npm.alibaba-inc.com/debug/download/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1:
  version "4.3.4"
  resolved "https://registry.npm.alibaba-inc.com/debug/download/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

decamelize@^1.1.2, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/deep-equal/download/deep-equal-1.1.1.tgz#b5c98c942ceffaf7cb051e24e1434a25a2e6076a"
  integrity sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-is@~0.1.3:
  version "0.1.4"
  resolved "https://registry.npm.alibaba-inc.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

default-gateway@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npm.alibaba-inc.com/default-gateway/download/default-gateway-4.2.0.tgz#167104c7500c2115f6dd69b0a536bb8ed720552b"
  integrity sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=
  dependencies:
    execa "^1.0.0"
    ip-regex "^2.1.0"

define-properties@^1.1.2, define-properties@^1.1.3, define-properties@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.alibaba-inc.com/define-properties/download/define-properties-1.1.4.tgz#0b14d7bd7fbeb2f3572c3a7eda80ea5d57fb05b1"
  integrity sha512-uckOqKcfaVvtBdsVkdPv3XjveQJsNQqmhXgRi8uhvWWuPYZCNlzT8qAyblUgNoXdHdjMTzAqeGjAoli8f+bzPA==
  dependencies:
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.npm.alibaba-inc.com/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

defined@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/defined/download/defined-1.0.1.tgz#c0b9db27bfaffd95d6f61399419b893df0f91ebf"
  integrity sha512-hsBd2qSVCRE+5PmNdHt1uzyrFu5d3RwmFDKzyNZMFq/EwDNJF7Ee5+D5oEKF0hU6LhtoUF1macFvOe4AskQC1Q==

del@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.alibaba-inc.com/del/download/del-4.1.1.tgz#9e8f117222ea44a31ff3a156c049b99052a9f0b4"
  integrity sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

depd@2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/des.js/download/des.js-1.0.1.tgz#5382142e1bdc53f85d86d53e5f4aa7deb91e0843"
  integrity sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/destroy/download/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

detect-file@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/detect-file/download/detect-file-1.0.0.tgz#f0d66d03672a825cb1b73bdb3fe62310c8e552b7"
  integrity sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc=

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/detect-indent/download/detect-indent-4.0.0.tgz#f76d064352cdf43a1cb6ce619c4ee3a9475de208"
  integrity sha1-920GQ1LN9Docts5hnE7jqUdd4gg=
  dependencies:
    repeating "^2.0.0"

detect-node-es@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/detect-node-es/download/detect-node-es-1.1.0.tgz#163acdf643330caa0b4cd7c21e7ee7755d6fa493"
  integrity sha1-FjrN9kMzDKoLTNfCHn7ndV1vpJM=

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/detect-node/download/detect-node-2.1.0.tgz#c9c70775a49c3d03bc2c06d9a73be550f978f8b1"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "https://registry.npm.alibaba-inc.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dijkstrajs@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/dijkstrajs/download/dijkstrajs-1.0.2.tgz#2e48c0d3b825462afe75ab4ad5e829c8ece36257"
  integrity sha1-LkjA07glRir+datK1egpyOzjYlc=

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/dns-equal/download/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.4"
  resolved "https://registry.npm.alibaba-inc.com/dns-packet/download/dns-packet-1.3.4.tgz#e3455065824a2507ba886c55a89963bb107dec6f"
  integrity sha1-40VQZYJKJQe6iGxVqJljuxB97G8=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/dns-txt/download/dns-txt-2.0.2.tgz#b91d806f5d27188e4ab3e7d107d881a1cc4642b6"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@1.5.0:
  version "1.5.0"
  resolved "https://registry.npm.alibaba-inc.com/doctrine/download/doctrine-1.5.0.tgz#379dce730f6166f76cefa4e6707a159b02c5a6fa"
  integrity sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-align@1.x, dom-align@^1.7.0:
  version "1.12.4"
  resolved "https://registry.npm.alibaba-inc.com/dom-align/download/dom-align-1.12.4.tgz#3503992eb2a7cfcb2ed3b2a6d21e0b9c00d54511"
  integrity sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw==

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/dom-converter/download/dom-converter-0.2.0.tgz#6721a9daee2e293682955b6afe416771627bb768"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-helpers@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npm.alibaba-inc.com/dom-helpers/download/dom-helpers-3.4.0.tgz#e9b369700f959f62ecde5a6babde4bccd9169af8"
  integrity sha1-6bNpcA+Vn2Ls3lprq95LzNkWmvg=
  dependencies:
    "@babel/runtime" "^7.1.2"

dom-serializer@0:
  version "0.2.2"
  resolved "https://registry.npm.alibaba-inc.com/dom-serializer/download/dom-serializer-0.2.2.tgz#1afb81f533717175d478655debc5e332d9f9bb51"
  integrity sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

dom-serializer@^1.0.1:
  version "1.4.1"
  resolved "https://registry.npm.alibaba-inc.com/dom-serializer/download/dom-serializer-1.4.1.tgz#de5d41b1aea290215dc45a6dae8adcf1d32e2d30"
  integrity sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

dom-to-image@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npm.alibaba-inc.com/dom-to-image/download/dom-to-image-2.6.0.tgz#8a503608088c87b1c22f9034ae032e1898955867"
  integrity sha1-ilA2CAiMh7HCL5A0rgMuGJiVWGc=

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/domain-browser/download/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@1, domelementtype@^1.3.0:
  version "1.3.1"
  resolved "https://registry.npm.alibaba-inc.com/domelementtype/download/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npm.alibaba-inc.com/domelementtype/download/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==

domhandler@^2.3.0:
  version "2.4.2"
  resolved "https://registry.npm.alibaba-inc.com/domhandler/download/domhandler-2.4.2.tgz#8805097e933d65e85546f726d60f5eb88b44f803"
  integrity sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=
  dependencies:
    domelementtype "1"

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npm.alibaba-inc.com/domhandler/download/domhandler-4.3.1.tgz#8d792033416f59d68bc03a5aa7b018c1ca89279c"
  integrity sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==
  dependencies:
    domelementtype "^2.2.0"

domutils@^1.5.1:
  version "1.7.0"
  resolved "https://registry.npm.alibaba-inc.com/domutils/download/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.5.2, domutils@^2.8.0:
  version "2.8.0"
  resolved "https://registry.npm.alibaba-inc.com/domutils/download/domutils-2.8.0.tgz#4437def5db6e2d1f5d6ee859bd95ca7d02048135"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "https://registry.npm.alibaba-inc.com/duplexify/download/duplexify-3.7.1.tgz#2a4df5317f6ccfd91f86d6fd25d8d8a103b88309"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.alibaba-inc.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.2.7, electron-to-chromium@^1.3.30, electron-to-chromium@^1.3.47, electron-to-chromium@^1.4.251:
  version "1.4.284"
  resolved "https://registry.npm.alibaba-inc.com/electron-to-chromium/download/electron-to-chromium-1.4.284.tgz#61046d1e4cab3a25238f6bf7413795270f125592"
  integrity sha512-M8WEXFuKXMYMVr45fo8mq0wUrrJHheiKZf6BArTKk9ZBYCKJEOU5H8cdWgDT+qCVZf7Na4lVUaZsA+h6uA9+PA==

elliptic@^6.5.3:
  version "6.5.4"
  resolved "https://registry.npm.alibaba-inc.com/elliptic/download/elliptic-6.5.4.tgz#da37cebd31e79a1367e941b592ed1fbebd58abbb"
  integrity sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s=
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://registry.npm.alibaba-inc.com/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npm.alibaba-inc.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npm.alibaba-inc.com/emoji-regex/download/emoji-regex-9.2.2.tgz#840c8803b0d8047f4ff0cf963176b32d4ef3ed72"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/emojis-list/download/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/emojis-list/download/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encode-utf8@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/encode-utf8/download/encode-utf8-1.0.3.tgz#f30fdd31da07fb596f281beb2f6b027851994cda"
  integrity sha1-8w/dMdoH+1lvKBvrL2sCeFGZTNo=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encoding@^0.1.11:
  version "0.1.13"
  resolved "https://registry.npm.alibaba-inc.com/encoding/download/encoding-0.1.13.tgz#56574afdd791f54a8e9b2785c0582a2d26210fa9"
  integrity sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.npm.alibaba-inc.com/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^4.1.1, enhanced-resolve@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npm.alibaba-inc.com/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz#2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec"
  integrity sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

entities@^1.1.1:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/entities/download/entities-1.1.2.tgz#bdfa735299664dfafd34529ed4f8522a275fea56"
  integrity sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=

entities@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/entities/download/entities-2.2.0.tgz#098dc90ebb83d8dffa089d55256b351d34c4da55"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

errno@^0.1.1, errno@^0.1.3, errno@~0.1.7:
  version "0.1.8"
  resolved "https://registry.npm.alibaba-inc.com/errno/download/errno-0.1.8.tgz#8bb3e9c7d463be4976ff888f76b4809ebc2e811f"
  integrity sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npm.alibaba-inc.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.19.0, es-abstract@^1.20.4:
  version "1.20.4"
  resolved "https://registry.npm.alibaba-inc.com/es-abstract/download/es-abstract-1.20.4.tgz#1d103f9f8d78d4cf0713edcd6d0ed1a46eed5861"
  integrity sha512-0UtvRN79eMe2L+UNEF1BwRe364sj/DXhQ/k5FmivgoSdpM90b8Jc0mDzKMGo7QS0BVbOP/bTwBKNnDc9rNzaPA==
  dependencies:
    call-bind "^1.0.2"
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    function.prototype.name "^1.1.5"
    get-intrinsic "^1.1.3"
    get-symbol-description "^1.0.0"
    has "^1.0.3"
    has-property-descriptors "^1.0.0"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    is-callable "^1.2.7"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-weakref "^1.0.2"
    object-inspect "^1.12.2"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.4.3"
    safe-regex-test "^1.0.0"
    string.prototype.trimend "^1.0.5"
    string.prototype.trimstart "^1.0.5"
    unbox-primitive "^1.0.2"

es-array-method-boxes-properly@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/es-array-method-boxes-properly/download/es-array-method-boxes-properly-1.0.0.tgz#873f3e84418de4ee19c5be752990b2e44718d09e"
  integrity sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4=

es-shim-unscopables@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/es-shim-unscopables/download/es-shim-unscopables-1.0.0.tgz#702e632193201e3edf8713635d083d378e510241"
  integrity sha512-Jm6GPcCdC30eMLbZ2x8z2WuRwAws3zTBBKuusffYVUrNj/GVSUAZ+xKMaUpfNDR5IbyNA5LJbaecoUVbmUcB1w==
  dependencies:
    has "^1.0.3"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.alibaba-inc.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npm.alibaba-inc.com/escalade/download/escalade-3.1.1.tgz#d8cfdc7000965c5a0174b4a82eaa5c0552742e40"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escodegen@^1.8.1:
  version "1.14.3"
  resolved "https://registry.npm.alibaba-inc.com/escodegen/download/escodegen-1.14.3.tgz#4e7b81fba61581dc97582ed78cab7f0e8d63f503"
  integrity sha1-TnuB+6YVgdyXWC7XjKt/Do1j9QM=
  dependencies:
    esprima "^4.0.1"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-airbnb-base@^13.1.0, eslint-config-airbnb-base@^13.2.0:
  version "13.2.0"
  resolved "https://registry.npm.alibaba-inc.com/eslint-config-airbnb-base/download/eslint-config-airbnb-base-13.2.0.tgz#f6ea81459ff4dec2dda200c35f1d8f7419d57943"
  integrity sha1-9uqBRZ/03sLdogDDXx2PdBnVeUM=
  dependencies:
    confusing-browser-globals "^1.0.5"
    object.assign "^4.1.0"
    object.entries "^1.1.0"

eslint-config-airbnb@^17.1.0:
  version "17.1.1"
  resolved "https://registry.npm.alibaba-inc.com/eslint-config-airbnb/download/eslint-config-airbnb-17.1.1.tgz#2272e0b86bb1e2b138cdf88d07a3b6f4cda3d626"
  integrity sha1-InLguGux4rE4zfiNB6O29M2j1iY=
  dependencies:
    eslint-config-airbnb-base "^13.2.0"
    object.assign "^4.1.0"
    object.entries "^1.1.0"

eslint-config-standard-jsx@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npm.alibaba-inc.com/eslint-config-standard-jsx/download/eslint-config-standard-jsx-6.0.2.tgz#90c9aa16ac2c4f8970c13fc7efc608bacd02da70"
  integrity sha1-kMmqFqwsT4lwwT/H78YIus0C2nA=

eslint-config-standard@^11.0.0:
  version "11.0.0"
  resolved "https://registry.npm.alibaba-inc.com/eslint-config-standard/download/eslint-config-standard-11.0.0.tgz#87ee0d3c9d95382dc761958cbb23da9eea31e0ba"
  integrity sha1-h+4NPJ2VOC3HYZWMuyPanuox4Lo=

eslint-import-resolver-node@^0.3.1, eslint-import-resolver-node@^0.3.6:
  version "0.3.6"
  resolved "https://registry.npm.alibaba-inc.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.6.tgz#4048b958395da89668252001dbd9eca6b83bacbd"
  integrity sha1-QEi5WDldqJZoJSAB29nsprg7rL0=
  dependencies:
    debug "^3.2.7"
    resolve "^1.20.0"

eslint-loader@^2.1.0:
  version "2.2.1"
  resolved "https://registry.npm.alibaba-inc.com/eslint-loader/download/eslint-loader-2.2.1.tgz#28b9c12da54057af0845e2a6112701a2f6bf8337"
  integrity sha1-KLnBLaVAV68IReKmEScBova/gzc=
  dependencies:
    loader-fs-cache "^1.0.0"
    loader-utils "^1.0.2"
    object-assign "^4.0.1"
    object-hash "^1.1.4"
    rimraf "^2.6.1"

eslint-module-utils@^2.2.0, eslint-module-utils@^2.7.3:
  version "2.7.4"
  resolved "https://registry.npm.alibaba-inc.com/eslint-module-utils/download/eslint-module-utils-2.7.4.tgz#4f3e41116aaf13a20792261e61d3a2e7e0583974"
  integrity sha512-j4GT+rqzCoRKHwURX7pddtIPGySnX9Si/cgMI5ztrcqOPtk5dDEeZ34CQVPphnqkJytlc97Vuk05Um2mJ3gEQA==
  dependencies:
    debug "^3.2.7"

eslint-plugin-autofix@^0.0.7:
  version "0.0.7"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-autofix/download/eslint-plugin-autofix-0.0.7.tgz#98279cfa3a82d7a4963630e751c8746aa3afe603"
  integrity sha1-mCec+jqC16SWNjDnUch0aqOv5gM=
  dependencies:
    eslint-rule-composer "^0.3.0"
    espree "^5.0.1"
    esutils "^2.0.2"
    lodash "^4.17.11"
    string-similarity "^3.0.0"

eslint-plugin-babel@^5.1.0:
  version "5.3.1"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-babel/download/eslint-plugin-babel-5.3.1.tgz#75a2413ffbf17e7be57458301c60291f2cfbf560"
  integrity sha1-daJBP/vxfnvldFgwHGApHyz79WA=
  dependencies:
    eslint-rule-composer "^0.3.0"

eslint-plugin-es@^1.3.1:
  version "1.4.1"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-es/download/eslint-plugin-es-1.4.1.tgz#12acae0f4953e76ba444bfd1b2271081ac620998"
  integrity sha1-EqyuD0lT52ukRL/RsicQgaxiCZg=
  dependencies:
    eslint-utils "^1.4.2"
    regexpp "^2.0.1"

eslint-plugin-import@2.14.0:
  version "2.14.0"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-import/download/eslint-plugin-import-2.14.0.tgz#6b17626d2e3e6ad52cfce8807a845d15e22111a8"
  integrity sha1-axdibS4+atUs/OiAeoRdFeIhEag=
  dependencies:
    contains-path "^0.1.0"
    debug "^2.6.8"
    doctrine "1.5.0"
    eslint-import-resolver-node "^0.3.1"
    eslint-module-utils "^2.2.0"
    has "^1.0.1"
    lodash "^4.17.4"
    minimatch "^3.0.3"
    read-pkg-up "^2.0.0"
    resolve "^1.6.0"

eslint-plugin-import@^2.14.0:
  version "2.26.0"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-import/download/eslint-plugin-import-2.26.0.tgz#f812dc47be4f2b72b478a021605a59fc6fe8b88b"
  integrity sha512-hYfi3FXaM8WPLf4S1cikh/r4IxnO6zrhZbEGz2b660EJRbuxgpDS5gkCuYgGWg2xxh2rBuIr4Pvhve/7c31koA==
  dependencies:
    array-includes "^3.1.4"
    array.prototype.flat "^1.2.5"
    debug "^2.6.9"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-module-utils "^2.7.3"
    has "^1.0.3"
    is-core-module "^2.8.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.values "^1.1.5"
    resolve "^1.22.0"
    tsconfig-paths "^3.14.1"

eslint-plugin-jest@^21.22.0, eslint-plugin-jest@^21.7.1:
  version "21.27.2"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-jest/download/eslint-plugin-jest-21.27.2.tgz#2a795b7c3b5e707df48a953d651042bd01d7b0a8"
  integrity sha1-KnlbfDtecH30ipU9ZRBCvQHXsKg=

eslint-plugin-jsx-a11y@^6.1.1:
  version "6.6.1"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-jsx-a11y/download/eslint-plugin-jsx-a11y-6.6.1.tgz#93736fc91b83fdc38cc8d115deedfc3091aef1ff"
  integrity sha512-sXgFVNHiWffBq23uiS/JaP6eVR622DqwB4yTzKvGZGcPq6/yZ3WmOZfuBks/vHWo9GaFOqC2ZK4i6+C35knx7Q==
  dependencies:
    "@babel/runtime" "^7.18.9"
    aria-query "^4.2.2"
    array-includes "^3.1.5"
    ast-types-flow "^0.0.7"
    axe-core "^4.4.3"
    axobject-query "^2.2.0"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    has "^1.0.3"
    jsx-ast-utils "^3.3.2"
    language-tags "^1.0.5"
    minimatch "^3.1.2"
    semver "^6.3.0"

eslint-plugin-node@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-node/download/eslint-plugin-node-7.0.1.tgz#a6e054e50199b2edd85518b89b4e7b323c9f36db"
  integrity sha1-puBU5QGZsu3YVRi4m057MjyfNts=
  dependencies:
    eslint-plugin-es "^1.3.1"
    eslint-utils "^1.3.1"
    ignore "^4.0.2"
    minimatch "^3.0.4"
    resolve "^1.8.1"
    semver "^5.5.0"

eslint-plugin-promise@^4.0.0:
  version "4.3.1"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-promise/download/eslint-plugin-promise-4.3.1.tgz#61485df2a359e03149fdafc0a68b0e030ad2ac45"
  integrity sha1-YUhd8qNZ4DFJ/a/AposOAwrSrEU=

eslint-plugin-react@^7.0.0, eslint-plugin-react@^7.11.1:
  version "7.31.11"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-react/download/eslint-plugin-react-7.31.11.tgz#011521d2b16dcf95795df688a4770b4eaab364c8"
  integrity sha512-TTvq5JsT5v56wPa9OYHzsrOlHzKZKjV+aLgS+55NJP/cuzdiQPC7PfYoUjMoxlffKtvijpk7vA/jmuqRb9nohw==
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flatmap "^1.3.1"
    array.prototype.tosorted "^1.1.1"
    doctrine "^2.1.0"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.6"
    object.fromentries "^2.0.6"
    object.hasown "^1.1.2"
    object.values "^1.1.6"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.3"
    semver "^6.3.0"
    string.prototype.matchall "^4.0.8"

eslint-plugin-standard@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-standard/download/eslint-plugin-standard-3.1.0.tgz#2a9e21259ba4c47c02d53b2d0c9135d4b1022d47"
  integrity sha1-Kp4hJZukxHwC1TstDJE11LECLUc=

eslint-plugin-vue@^4.7.1:
  version "4.7.1"
  resolved "https://registry.npm.alibaba-inc.com/eslint-plugin-vue/download/eslint-plugin-vue-4.7.1.tgz#c829b9fc62582c1897b5a0b94afd44ecca511e63"
  integrity sha1-yCm5/GJYLBiXtaC5Sv1E7MpRHmM=
  dependencies:
    vue-eslint-parser "^2.0.3"

eslint-rule-composer@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.alibaba-inc.com/eslint-rule-composer/download/eslint-rule-composer-0.3.0.tgz#79320c927b0c5c0d3d3d2b76c8b4a488f25bbaf9"
  integrity sha1-eTIMknsMXA09PSt2yLSkiPJbuvk=

eslint-scope@3.7.1:
  version "3.7.1"
  resolved "https://registry.npm.alibaba-inc.com/eslint-scope/download/eslint-scope-3.7.1.tgz#3d63c3edfda02e06e01a452ad88caacc7cdcb6e8"
  integrity sha1-PWPD7f2gLgbgGkUq2IyqzHzctug=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^3.7.1:
  version "3.7.3"
  resolved "https://registry.npm.alibaba-inc.com/eslint-scope/download/eslint-scope-3.7.3.tgz#bb507200d3d17f60247636160b4826284b108535"
  integrity sha1-u1ByANPRf2AkdjYWC0gmKEsQhTU=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npm.alibaba-inc.com/eslint-scope/download/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-utils@^1.3.1, eslint-utils@^1.4.2:
  version "1.4.3"
  resolved "https://registry.npm.alibaba-inc.com/eslint-utils/download/eslint-utils-1.4.3.tgz#74fec7c54d0776b6f67e0251040b5806564e981f"
  integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint@^5.4.0, eslint@^5.5.0:
  version "5.16.0"
  resolved "https://registry.npm.alibaba-inc.com/eslint/download/eslint-5.16.0.tgz#a1e3ac1aae4a3fbd8296fcf8f7ab7314cbb6abea"
  integrity sha1-oeOsGq5KP72Clvz496tzFMu2q+o=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.9.1"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^4.0.3"
    eslint-utils "^1.3.1"
    eslint-visitor-keys "^1.0.0"
    espree "^5.0.1"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^11.7.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^6.2.2"
    js-yaml "^3.13.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.11"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^5.5.1"
    strip-ansi "^4.0.0"
    strip-json-comments "^2.0.1"
    table "^5.2.3"
    text-table "^0.2.0"

espree@^3.5.2:
  version "3.5.4"
  resolved "https://registry.npm.alibaba-inc.com/espree/download/espree-3.5.4.tgz#b0f447187c8a8bed944b815a660bddf5deb5d1a7"
  integrity sha1-sPRHGHyKi+2US4FaZgvd9d610ac=
  dependencies:
    acorn "^5.5.0"
    acorn-jsx "^3.0.0"

espree@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.alibaba-inc.com/espree/download/espree-5.0.1.tgz#5d6526fa4fc7f0788a5cf75b15f30323e2f81f7a"
  integrity sha1-XWUm+k/H8HiKXPdbFfMDI+L4H3o=
  dependencies:
    acorn "^6.0.7"
    acorn-jsx "^5.0.0"
    eslint-visitor-keys "^1.0.0"

esprima@^2.6.0:
  version "2.7.3"
  resolved "https://registry.npm.alibaba-inc.com/esprima/download/esprima-2.7.3.tgz#96e3b70d5779f6ad49cd032673d1c312767ba581"
  integrity sha1-luO3DVd59q1JzQMmc9HDEnZ7pYE=

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.alibaba-inc.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.0, esquery@^1.0.1:
  version "1.4.0"
  resolved "https://registry.npm.alibaba-inc.com/esquery/download/esquery-1.4.0.tgz#2148ffc38b82e8c7057dfed48425b3e61f0f24a5"
  integrity sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npm.alibaba-inc.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.3.0"
  resolved "https://registry.npm.alibaba-inc.com/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://registry.npm.alibaba-inc.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npm.alibaba-inc.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npm.alibaba-inc.com/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://registry.npm.alibaba-inc.com/eventemitter3/download/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.0.0:
  version "3.3.0"
  resolved "https://registry.npm.alibaba-inc.com/events/download/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

eventsource@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/eventsource/download/eventsource-2.0.2.tgz#76dfcc02930fb2ff339520b6d290da573a9e8508"
  integrity sha512-IzUmBGPR3+oUG9dUeXynyNmf91/3zUSJg1lCktzKw47OXuhco54U3r9B7O4XX+Rb1Itm9OZ2b0RkTs10bICOxA==

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

exenv@^1.2.0:
  version "1.2.2"
  resolved "https://registry.npm.alibaba-inc.com/exenv/download/exenv-1.2.2.tgz#2ae78e85d9894158670b03d47bec1f03bd91bb9d"
  integrity sha1-KueOhdmJQVhnCwPUe+wfA72Ru50=

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "https://registry.npm.alibaba-inc.com/expand-brackets/download/expand-brackets-0.1.5.tgz#df07284e342a807cd733ac5af72411e581d1177b"
  integrity sha1-3wcoTjQqgHzXM6xa9yQR5YHRF3s=
  dependencies:
    is-posix-bracket "^0.1.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npm.alibaba-inc.com/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "https://registry.npm.alibaba-inc.com/expand-range/download/expand-range-1.8.2.tgz#a299effd335fe2721ebae8e257ec79644fc85337"
  integrity sha1-opnv/TNf4nIeuujiV+x5ZE/IUzc=
  dependencies:
    fill-range "^2.1.0"

expand-tilde@^2.0.0, expand-tilde@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/expand-tilde/download/expand-tilde-2.0.2.tgz#97e801aa052df02454de46b02bf621642cdc8502"
  integrity sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=
  dependencies:
    homedir-polyfill "^1.0.1"

express@^4.17.1:
  version "4.18.2"
  resolved "https://registry.npm.alibaba-inc.com/express/download/express-4.18.2.tgz#3fabe08296e930c796c19e3c516979386ba9fd59"
  integrity sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.1"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.alibaba-inc.com/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.alibaba-inc.com/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npm.alibaba-inc.com/extglob/download/extglob-0.3.2.tgz#2e18ff3d2f49ab2765cec9023f011daa8d8349a1"
  integrity sha1-Lhj/PS9JqydlzskCPwEdqo2DSaE=
  dependencies:
    is-extglob "^1.0.0"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.alibaba-inc.com/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extract-text-webpack-plugin@^4.0.0-beta.0:
  version "4.0.0-beta.0"
  resolved "https://registry.npm.alibaba-inc.com/extract-text-webpack-plugin/download/extract-text-webpack-plugin-4.0.0-beta.0.tgz#f7361d7ff430b42961f8d1321ba8c1757b5d4c42"
  integrity sha1-9zYdf/QwtClh+NEyG6jBdXtdTEI=
  dependencies:
    async "^2.4.1"
    loader-utils "^1.1.0"
    schema-utils "^0.4.5"
    webpack-sources "^1.1.0"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "https://registry.npm.alibaba-inc.com/extsprintf/download/extsprintf-1.4.1.tgz#8d172c064867f235c0c84a596806d279bf4bcc07"
  integrity sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=

fast-deep-equal@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/fast-deep-equal/download/fast-deep-equal-1.1.0.tgz#c053477817c86b51daa853c81e059b733d023614"
  integrity sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://registry.npm.alibaba-inc.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npm.alibaba-inc.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastparse@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/fastparse/download/fastparse-1.1.2.tgz#91728c5a5942eced8531283c79441ee4122c35a9"
  integrity sha1-kXKMWllC7O2FMSg8eUQe5BIsNak=

faye-websocket@^0.11.3, faye-websocket@^0.11.4:
  version "0.11.4"
  resolved "https://registry.npm.alibaba-inc.com/faye-websocket/download/faye-websocket-0.11.4.tgz#7f0d9275cfdd86a1c963dc8b65fcc451edcbb1da"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

fbjs@^0.8.3:
  version "0.8.18"
  resolved "https://registry.npm.alibaba-inc.com/fbjs/download/fbjs-0.8.18.tgz#9835e0addb9aca2eff53295cd79ca1cfc7c9662a"
  integrity sha1-mDXgrduayi7/Uylc15yhz8fJZio=
  dependencies:
    core-js "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.30"

figgy-pudding@^3.5.1:
  version "3.5.2"
  resolved "https://registry.npm.alibaba-inc.com/figgy-pudding/download/figgy-pudding-3.5.2.tgz#b4eee8148abb01dcf1d1ac34367d59e12fa61d6e"
  integrity sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=

figures@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/figures/download/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.alibaba-inc.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz#ca0f6efa6dd3d561333fb14515065c2fafdf439c"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-loader@^1.1.11:
  version "1.1.11"
  resolved "https://registry.npm.alibaba-inc.com/file-loader/download/file-loader-1.1.11.tgz#6fe886449b0f2a936e43cabaac0cdbfb369506f8"
  integrity sha1-b+iGRJsPKpNuQ8q6rAzb+zaVBvg=
  dependencies:
    loader-utils "^1.0.2"
    schema-utils "^0.4.5"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/filename-regex/download/filename-regex-2.0.1.tgz#c1c4b9bee3e09725ddb106b75c1e301fe2f18b26"
  integrity sha1-wcS5vuPglyXdsQa3XB4wH+LxiyY=

fill-range@^2.1.0:
  version "2.2.4"
  resolved "https://registry.npm.alibaba-inc.com/fill-range/download/fill-range-2.2.4.tgz#eb1e773abb056dcd8df2bfdf6af59b8b3a936565"
  integrity sha1-6x53OrsFbc2N8r/favWbizqTZWU=
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^3.0.0"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npm.alibaba-inc.com/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/finalhandler/download/finalhandler-1.2.0.tgz#7d23fe5731b207b4640e4fcd00aec1f9207a7b32"
  integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.alibaba-inc.com/find-cache-dir/download/find-cache-dir-0.1.1.tgz#c8defae57c8a52a8a784f9e31c57c742e993a0b9"
  integrity sha1-yN765XyKUqinhPnjHFfHQumToLk=
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-cache-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/find-cache-dir/download/find-cache-dir-1.0.0.tgz#9288e3e9e3cc3748717d39eade17cf71fc30ee6f"
  integrity sha1-kojj6ePMN0hxfTnq3hfPcfww7m8=
  dependencies:
    commondir "^1.0.1"
    make-dir "^1.0.0"
    pkg-dir "^2.0.0"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/find-up/download/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  integrity sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/find-up/download/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

findup-sync@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/findup-sync/download/findup-sync-3.0.0.tgz#17b108f9ee512dfb7a5c7f3c8b27ea9e1a9c08d1"
  integrity sha1-F7EI+e5RLft6XH88iyfqnhqcCNE=
  dependencies:
    detect-file "^1.0.0"
    is-glob "^4.0.0"
    micromatch "^3.0.4"
    resolve-dir "^1.0.1"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/flat-cache/download/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/flatted/download/flatted-2.0.2.tgz#4575b21e2bcee7434aa9be662f4b7b5f9c2b5138"
  integrity sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=

flatten@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/flatten/download/flatten-1.0.3.tgz#c1283ac9f27b368abc1e36d1ff7b04501a30356b"
  integrity sha1-wSg6yfJ7Noq8HjbR/3sEUBowNWs=

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/flush-write-stream/download/flush-write-stream-1.1.1.tgz#8dd7d873a1babc207d94ead0c2e0e44276ebf2e8"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "https://registry.npm.alibaba-inc.com/follow-redirects/download/follow-redirects-1.5.10.tgz#7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.0.0:
  version "1.15.2"
  resolved "https://registry.npm.alibaba-inc.com/follow-redirects/download/follow-redirects-1.15.2.tgz#b460864144ba63f2681096f274c4e57026da2c13"
  integrity sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==

for-in@^0.1.3:
  version "0.1.8"
  resolved "https://registry.npm.alibaba-inc.com/for-in/download/for-in-0.1.8.tgz#d8773908e31256109952b1fdb9b3fa867d2775e1"
  integrity sha1-2Hc5COMSVhCZUrH9ubP6hn0ndeE=

for-in@^1.0.1, for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

for-own@^0.1.4:
  version "0.1.5"
  resolved "https://registry.npm.alibaba-inc.com/for-own/download/for-own-0.1.5.tgz#5265c681a4f294dabbf17c9509b6763aa84510ce"
  integrity sha1-UmXGgaTylNq78XyVCbZ2OqhFEM4=
  dependencies:
    for-in "^1.0.1"

for-own@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/for-own/download/for-own-1.0.0.tgz#c63332f415cedc4b04dbfe70cf836494c53cb44b"
  integrity sha1-xjMy9BXO3EsE2/5wz4NklMU8tEs=
  dependencies:
    for-in "^1.0.1"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npm.alibaba-inc.com/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npm.alibaba-inc.com/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/forwarded/download/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.alibaba-inc.com/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npm.alibaba-inc.com/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npm.alibaba-inc.com/from2/download/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-exists-sync@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.alibaba-inc.com/fs-exists-sync/download/fs-exists-sync-0.1.0.tgz#982d6893af918e72d08dec9e8673ff2b5a8d6add"
  integrity sha1-mC1ok6+RjnLQjeyehnP/K1qNat0=

fs-readdir-recursive@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/fs-readdir-recursive/download/fs-readdir-recursive-1.1.0.tgz#e32fc030a2ccee44a6b5371308da54be0b397d27"
  integrity sha1-4y/AMKLM7kSmtTcTCNpUvgs5fSc=

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "https://registry.npm.alibaba-inc.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.0.0, fsevents@^1.2.7:
  version "1.2.13"
  resolved "https://registry.npm.alibaba-inc.com/fsevents/download/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.npm.alibaba-inc.com/fsevents/download/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=

fstream@^1.0.0, fstream@^1.0.12:
  version "1.0.12"
  resolved "https://registry.npm.alibaba-inc.com/fstream/download/fstream-1.0.12.tgz#4e8ba8ee2d48be4f7d0de505455548eae5932045"
  integrity sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU=
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

function.prototype.name@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npm.alibaba-inc.com/function.prototype.name/download/function.prototype.name-1.1.5.tgz#cce0505fe1ffb80503e6f9e46cc64e46a12a9621"
  integrity sha1-zOBQX+H/uAUD5vnkbMZORqEqliE=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"
    functions-have-names "^1.2.2"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

functions-have-names@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npm.alibaba-inc.com/functions-have-names/download/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://registry.npm.alibaba-inc.com/gauge/download/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  integrity sha512-14x4kjc6lkD3ltw589k0NrPD6cCNTD6CWoVUNpB85+DrtONoZn+Rug6xZU5RvSC4+TZPxA5AnBibQYAvZn41Hg==
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gaze@^1.0.0:
  version "1.1.3"
  resolved "https://registry.npm.alibaba-inc.com/gaze/download/gaze-1.1.3.tgz#c441733e13b927ac8c0ff0b4c3b033f28812924a"
  integrity sha1-xEFzPhO5J6yMD/C0w7Az8ogSkko=
  dependencies:
    globule "^1.0.0"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npm.alibaba-inc.com/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "https://registry.npm.alibaba-inc.com/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2, get-intrinsic@^1.1.0, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.alibaba-inc.com/get-intrinsic/download/get-intrinsic-1.1.3.tgz#063c84329ad93e83893c7f4f243ef63ffa351385"
  integrity sha512-QJVz1Tj7MS099PevUG5jvnt9tSkXN8K14dxQlikJuPt4uD9hHAHjLyLBiLR5zELelBdD9QNRAXZzsJx0WaDL9A==
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.3"

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.alibaba-inc.com/get-stdin/download/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"
  integrity sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/get-symbol-description/download/get-symbol-description-1.0.0.tgz#7fdb81c900101fbd564dd5f1a30af5aadc1e58d6"
  integrity sha1-f9uByQAQH71WTdXxowr1qtweWNY=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npm.alibaba-inc.com/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.npm.alibaba-inc.com/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

git-config-path@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/git-config-path/download/git-config-path-1.0.1.tgz#6d33f7ed63db0d0e118131503bab3aca47d54664"
  integrity sha1-bTP37WPbDQ4RgTFQO6s6ykfVRmQ=
  dependencies:
    extend-shallow "^2.0.1"
    fs-exists-sync "^0.1.0"
    homedir-polyfill "^1.0.0"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.alibaba-inc.com/glob-base/download/glob-base-0.3.0.tgz#dbb164f6221b1c0b1ccf82aea328b497df0ea3c4"
  integrity sha1-27Fk9iIbHAscz4Kuoyi0l98Oo8Q=
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/glob-parent/download/glob-parent-2.0.0.tgz#81383d72db054fcccf5336daa902f182f6edbb28"
  integrity sha1-gTg9ctsFT8zPUzbaqQLxgvbtuyg=
  dependencies:
    is-glob "^2.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/glob-parent/download/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npm.alibaba-inc.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob@^7.0.0, glob@^7.0.3, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4:
  version "7.2.3"
  resolved "https://registry.npm.alibaba-inc.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@~7.1.1:
  version "7.1.7"
  resolved "https://registry.npm.alibaba-inc.com/glob/download/glob-7.1.7.tgz#3b193e9233f01d42d0b3f78294bbeeb418f94a90"
  integrity sha1-Oxk+kjPwHULQs/eClLvutBj5SpA=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-modules@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/global-modules/download/global-modules-1.0.0.tgz#6d770f0eb523ac78164d72b5e71a8877265cc3ea"
  integrity sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o=
  dependencies:
    global-prefix "^1.0.1"
    is-windows "^1.0.1"
    resolve-dir "^1.0.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/global-modules/download/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/global-prefix/download/global-prefix-1.0.2.tgz#dbf743c6c14992593c655568cb66ed32c0122ebe"
  integrity sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=
  dependencies:
    expand-tilde "^2.0.2"
    homedir-polyfill "^1.0.1"
    ini "^1.3.4"
    is-windows "^1.0.1"
    which "^1.2.14"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/global-prefix/download/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0, globals@^11.7.0:
  version "11.12.0"
  resolved "https://registry.npm.alibaba-inc.com/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^9.18.0:
  version "9.18.0"
  resolved "https://registry.npm.alibaba-inc.com/globals/download/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"
  integrity sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo=

globby@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npm.alibaba-inc.com/globby/download/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globule@^1.0.0:
  version "1.3.4"
  resolved "https://registry.npm.alibaba-inc.com/globule/download/globule-1.3.4.tgz#7c11c43056055a75a6e68294453c17f2796170fb"
  integrity sha512-OPTIfhMBh7JbBYDpa5b+Q5ptmMWKwcNcFSR/0c6t8V4f3ZAVBEsKNY37QdVqmLRYSMhOUGYrY0QhSoEpzGr/Eg==
  dependencies:
    glob "~7.1.1"
    lodash "^4.17.21"
    minimatch "~3.0.2"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.4:
  version "4.2.10"
  resolved "https://registry.npm.alibaba-inc.com/graceful-fs/download/graceful-fs-4.2.10.tgz#147d3a006da4ca3ce14728c7aefc287c367d7a6c"
  integrity sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==

graph@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/graph/download/graph-0.2.0.tgz#ab609d8330d6a28a4f2022f5855e3a8fb666b00d"
  integrity sha1-q2CdgzDWoopPICL1hV46j7ZmsA0=

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/handle-thing/download/handle-thing-2.0.1.tgz#857f79ce359580c340d43081cc648970d0bb234e"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "https://registry.npm.alibaba-inc.com/har-validator/download/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/has-bigints/download/has-bigints-1.0.2.tgz#0871bd3e3d51626f6ca0966668ba35d5602d6eaa"
  integrity sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==

has-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/has-flag/download/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"
  integrity sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-property-descriptors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/has-property-descriptors/download/has-property-descriptors-1.0.0.tgz#610708600606d36961ed04c196193b6a607fa861"
  integrity sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==
  dependencies:
    get-intrinsic "^1.1.1"

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/has-unicode/download/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npm.alibaba-inc.com/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.alibaba-inc.com/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.1, has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/hash-base/download/hash-base-3.1.0.tgz#55c381d9e06e1d2997a883b4a3fddfe7f0d3af33"
  integrity sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/hash-sum/download/hash-sum-1.0.2.tgz#33b40777754c6432573c120cc3808bbd10d47f04"
  integrity sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://registry.npm.alibaba-inc.com/hash.js/download/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

he@1.2.x, he@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/he/download/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

history@^4.9.0:
  version "4.10.1"
  resolved "https://registry.npm.alibaba-inc.com/history/download/history-4.10.1.tgz#33371a65e3a83b267434e2b3f3b1b4c58aad4cf3"
  integrity sha1-MzcaZeOoOyZ0NOKz87G0xYqtTPM=
  dependencies:
    "@babel/runtime" "^7.1.2"
    loose-envify "^1.2.0"
    resolve-pathname "^3.0.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"
    value-equal "^1.0.1"

hmac-drbg@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hoist-non-react-statics@^2.1.0:
  version "2.5.5"
  resolved "https://registry.npm.alibaba-inc.com/hoist-non-react-statics/download/hoist-non-react-statics-2.5.5.tgz#c5903cf409c0dfd908f388e619d86b9c1174cb47"
  integrity sha1-xZA89AnA39kI84jmGdhrnBF0y0c=

hoist-non-react-statics@^3.0.0, hoist-non-react-statics@^3.1.0, hoist-non-react-statics@^3.3.1:
  version "3.3.2"
  resolved "https://registry.npm.alibaba-inc.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/home-or-tmp/download/home-or-tmp-2.0.0.tgz#e36c3f2d2cae7d746a857e38d18d5f32a7882db8"
  integrity sha1-42w/LSyufXRqhX440Y1fMqeILbg=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

homedir-polyfill@^1.0.0, homedir-polyfill@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/homedir-polyfill/download/homedir-polyfill-1.0.3.tgz#743298cef4e5af3e194161fbadcc2151d3a058e8"
  integrity sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg=
  dependencies:
    parse-passwd "^1.0.0"

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "https://registry.npm.alibaba-inc.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npm.alibaba-inc.com/hpack.js/download/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-comment-regex@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/html-comment-regex/download/html-comment-regex-1.1.2.tgz#97d4688aeb5c81886a364faa0cad1dda14d433a7"
  integrity sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c=

html-entities@^1.3.1:
  version "1.4.0"
  resolved "https://registry.npm.alibaba-inc.com/html-entities/download/html-entities-1.4.0.tgz#cfbd1b01d2afaf9adca1b10ae7dffab98c71d2dc"
  integrity sha1-z70bAdKvr5rcobEK59/6uYxx0tw=

html-entities@^2.1.0:
  version "2.3.3"
  resolved "https://registry.npm.alibaba-inc.com/html-entities/download/html-entities-2.3.3.tgz#117d7626bece327fc8baace8868fa6f5ef856e46"
  integrity sha512-DV5Ln36z34NNTDgnz0EWGBLZENelNAtkiFA4kyNOG2tDI6Mz1uSWiq1wAKdyjnJwyDiDO7Fa2SO1CTxPXL8VxA==

html-inline-source-webpack-plugin@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/html-inline-source-webpack-plugin/download/html-inline-source-webpack-plugin-1.1.0.tgz#011e3f22eb9151e3f0ccdc91e37e861f482ba039"
  integrity sha1-AR4/IuuRUePwzNyR436GH0groDk=
  dependencies:
    inline-source "^5.1.2"

html-minifier@^3.2.3, html-minifier@^3.4.3:
  version "3.5.21"
  resolved "https://registry.npm.alibaba-inc.com/html-minifier/download/html-minifier-3.5.21.tgz#d0040e054730e354db008463593194015212d20c"
  integrity sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.2.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-webpack-inline-source-plugin@^0.0.10:
  version "0.0.10"
  resolved "https://registry.npm.alibaba-inc.com/html-webpack-inline-source-plugin/download/html-webpack-inline-source-plugin-0.0.10.tgz#89bd5f761e4f16902aa76a44476eb52831c9f7f0"
  integrity sha1-ib1fdh5PFpAqp2pER261KDHJ9/A=
  dependencies:
    escape-string-regexp "^1.0.5"
    slash "^1.0.0"
    source-map-url "^0.4.0"

html-webpack-plugin@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npm.alibaba-inc.com/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz#b01abbd723acaaa7b37b6af4492ebda03d9dd37b"
  integrity sha1-sBq71yOsqqeze2r0SS69oD2d03s=
  dependencies:
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    tapable "^1.0.0"
    toposort "^1.0.0"
    util.promisify "1.0.0"

htmlparser2@3.9.x:
  version "3.9.2"
  resolved "https://registry.npm.alibaba-inc.com/htmlparser2/download/htmlparser2-3.9.2.tgz#1bdf87acca0f3f9e53fa4fcceb0f4b4cbb00b338"
  integrity sha1-G9+HrMoPP55T+k/M6w9LTLsAszg=
  dependencies:
    domelementtype "^1.3.0"
    domhandler "^2.3.0"
    domutils "^1.5.1"
    entities "^1.1.1"
    inherits "^2.0.1"
    readable-stream "^2.0.2"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npm.alibaba-inc.com/htmlparser2/download/htmlparser2-6.1.0.tgz#c4d762b6c3371a05dbe65e94ae43a9f845fb8fb7"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npm.alibaba-inc.com/http-deceiver/download/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npm.alibaba-inc.com/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.8"
  resolved "https://registry.npm.alibaba-inc.com/http-parser-js/download/http-parser-js-0.5.8.tgz#af23090d9ac4e24573de6f6aecc9d84a48bf20e3"
  integrity sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==

http-proxy-middleware@0.19.1:
  version "0.19.1"
  resolved "https://registry.npm.alibaba-inc.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz#183c7dc4aa1479150306498c210cdaf96080a43a"
  integrity sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy@^1.17.0:
  version "1.18.1"
  resolved "https://registry.npm.alibaba-inc.com/http-proxy/download/http-proxy-1.18.1.tgz#401541f0534884bbf95260334e72f88ee3976549"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/https-browserify/download/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://registry.npm.alibaba-inc.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2:
  version "0.6.3"
  resolved "https://registry.npm.alibaba-inc.com/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

icss-replace-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz#06ea6f83679a7749e386cfe1fe812ae5db223ded"
  integrity sha1-Bupvg2ead0njhs/h/oEq5dsiPe0=

icss-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/icss-utils/download/icss-utils-2.1.0.tgz#83f0a0ec378bf3246178b6c2ad9136f135b1c962"
  integrity sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI=
  dependencies:
    postcss "^6.0.1"

ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://registry.npm.alibaba-inc.com/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

iferr@^0.1.5:
  version "0.1.5"
  resolved "https://registry.npm.alibaba-inc.com/iferr/download/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^4.0.2, ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npm.alibaba-inc.com/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

image-size@~0.5.0:
  version "0.5.5"
  resolved "https://registry.npm.alibaba-inc.com/image-size/download/image-size-0.5.5.tgz#09dfd4ab9d20e29eb1c3e80b8990378df9e3cb9c"
  integrity sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=

immutable@^4.0.0-rc.12:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/immutable/download/immutable-4.1.0.tgz#f795787f0db780183307b9eb2091fcac1f6fafef"
  integrity sha512-oNkuqVTA8jqG1Q6c+UglTOD1xhC1BtjKI7XkCXRkZHrN5m18/XsnUp8Q89GkQO/z+0WjonSvl0FLhDYftp46nQ==

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/import-cwd/download/import-cwd-2.1.0.tgz#aa6cf36e722761285cb371ec6519f53e2435b0a9"
  integrity sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=
  dependencies:
    import-from "^2.1.0"

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/import-fresh/download/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.1.0:
  version "3.3.0"
  resolved "https://registry.npm.alibaba-inc.com/import-fresh/download/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/import-from/download/import-from-2.1.0.tgz#335db7f2a7affd53aaa471d4b8021dee36b7f3b1"
  integrity sha1-M1238qev/VOqpHHUuAId7ja387E=
  dependencies:
    resolve-from "^3.0.0"

import-local@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/import-local/download/import-local-2.0.0.tgz#55070be38a5993cf18ef6db7e961f5bee5c5a09d"
  integrity sha1-VQcL44pZk88Y72236WH1vuXFoJ0=
  dependencies:
    pkg-dir "^3.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.alibaba-inc.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

in-publish@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/in-publish/download/in-publish-2.0.1.tgz#948b1a535c8030561cea522f73f78f4be357e00c"
  integrity sha1-lIsaU1yAMFYc6lIvc/ePS+NX4Aw=

indent-string@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/indent-string/download/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  integrity sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=
  dependencies:
    repeating "^2.0.0"

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/indexes-of/download/indexes-of-1.0.1.tgz#f30f716c8e2bd346c7b67d3df3915566a7c05607"
  integrity sha1-8w9xbI4r00bHtn0985FVZqfAVgc=

infer-owner@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/infer-owner/download/infer-owner-1.0.4.tgz#c4cefcaa8e51051c2a40ba2ce8a3d27295af9467"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npm.alibaba-inc.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.npm.alibaba-inc.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/inherits/download/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.npm.alibaba-inc.com/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.4, ini@^1.3.5:
  version "1.3.8"
  resolved "https://registry.npm.alibaba-inc.com/ini/download/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

inline-source@^5.1.2:
  version "5.2.7"
  resolved "https://registry.npm.alibaba-inc.com/inline-source/download/inline-source-5.2.7.tgz#443dcd2e2948664db872a64fa5bb4c91fd1fbf84"
  integrity sha1-RD3NLilIZk24cqZPpbtMkf0fv4Q=
  dependencies:
    csso "3.4.x"
    htmlparser2 "3.9.x"
    is-plain-obj "1.1.x"
    object-assign "4.1.x"
    svgo "0.7.x"
    uglify-js "3.3.x"

inquirer@^6.2.2:
  version "6.5.2"
  resolved "https://registry.npm.alibaba-inc.com/inquirer/download/inquirer-6.5.2.tgz#ad50942375d036d327ff528c08bd5fab089928ca"
  integrity sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo=
  dependencies:
    ansi-escapes "^3.2.0"
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^2.0.0"
    lodash "^4.17.12"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.4.0"
    string-width "^2.1.0"
    strip-ansi "^5.1.0"
    through "^2.3.6"

internal-ip@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npm.alibaba-inc.com/internal-ip/download/internal-ip-4.3.0.tgz#845452baad9d2ca3b69c635a137acb9a0dad0907"
  integrity sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=
  dependencies:
    default-gateway "^4.2.0"
    ipaddr.js "^1.9.0"

internal-slot@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/internal-slot/download/internal-slot-1.0.3.tgz#7347e307deeea2faac2ac6205d4bc7d34967f59c"
  integrity sha1-c0fjB97uovqsKsYgXUvH00ln9Zw=
  dependencies:
    get-intrinsic "^1.1.0"
    has "^1.0.3"
    side-channel "^1.0.4"

interpret@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.alibaba-inc.com/interpret/download/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

invariant@^2.2.0, invariant@^2.2.2, invariant@^2.2.4:
  version "2.2.4"
  resolved "https://registry.npm.alibaba-inc.com/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/ip-regex/download/ip-regex-2.1.0.tgz#fa78bf5d2e6913c911ce9f819ee5146bb6d844e9"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@^1.1.0, ip@^1.1.5:
  version "1.1.8"
  resolved "https://registry.npm.alibaba-inc.com/ip/download/ip-1.1.8.tgz#ae05948f6b075435ed3307acce04629da8cdbf48"
  integrity sha512-PuExPYUiu6qMBQb4l06ecm6T6ujzhmh+MeJcW9wa89PoAz5pvd4zPgN5WJV104mb6S2T1AwNIAaB70JNrLQWhg==

ipaddr.js@1.9.1, ipaddr.js@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npm.alibaba-inc.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-absolute-url/download/is-absolute-url-2.1.0.tgz#50530dfb84fcc9aa7dbe7852e83a37b93b9f2aa6"
  integrity sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=

is-absolute-url@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npm.alibaba-inc.com/is-absolute-url/download/is-absolute-url-3.0.3.tgz#96c6a22b6a23929b11ea0afb1836c36ad4a5d698"
  integrity sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://registry.npm.alibaba-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/is-arguments/download/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.alibaba-inc.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npm.alibaba-inc.com/is-arrayish/download/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/is-bigint/download/is-bigint-1.0.4.tgz#08147a1875bc2b32005d41ccd8291dffc6691df3"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/is-binary-path/download/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz#5c6dc200246dd9321ae4b885a114bb1f75f63719"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://registry.npm.alibaba-inc.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npm.alibaba-inc.com/is-callable/download/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-core-module@^2.8.1, is-core-module@^2.9.0:
  version "2.11.0"
  resolved "https://registry.npm.alibaba-inc.com/is-core-module/download/is-core-module-2.11.0.tgz#ad4cb3e3863e814523c96f3f58d26cc570ff0144"
  integrity sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.alibaba-inc.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/is-date-object/download/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://registry.npm.alibaba-inc.com/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npm.alibaba-inc.com/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/is-dotfile/download/is-dotfile-1.0.3.tgz#a6a2f32ffd2dfb04f5ca25ecd0f6b83cf798a1e1"
  integrity sha1-pqLzL/0t+wT1yiXs0Pa4PPeYoeE=

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npm.alibaba-inc.com/is-equal-shallow/download/is-equal-shallow-0.1.3.tgz#2238098fc221de0bcfa5d9eac4c45d638aa1c534"
  integrity sha1-IjgJj8Ih3gvPpdnqxMRdY4qhxTQ=
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.alibaba-inc.com/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.0, is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-extglob/download/is-extglob-1.0.0.tgz#ac468177c4943405a092fc8f29760c6ffc6206c0"
  integrity sha1-rEaBd8SUNAWgkvyPKXYMb/xiBsA=

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finite@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-finite/download/is-finite-1.1.0.tgz#904135c77fb42c0641d6aa1bcdbc4daa8da082f3"
  integrity sha1-kEE1x3+0LAZB1qobzbxNqo2ggvM=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/is-glob/download/is-glob-2.0.1.tgz#d096f926a3ded5600f3fdfd91198cb0888c2d863"
  integrity sha1-0Jb5JqPe1WAPP9/ZEZjLCIjC2GM=
  dependencies:
    is-extglob "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npm.alibaba-inc.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-keyword-js@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/is-keyword-js/download/is-keyword-js-1.0.3.tgz#ac30dcf35b671f4b27b17f5cb57235126021132d"
  integrity sha1-rDDc81tnH0snsX9ctXI1EmAhEy0=

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/is-negative-zero/download/is-negative-zero-2.0.2.tgz#7bf6f03a28003b8b3965de3ac26f664d765f3150"
  integrity sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "https://registry.npm.alibaba-inc.com/is-number-object/download/is-number-object-1.0.7.tgz#59d50ada4c45251784e9904f5246c742f07a42fc"
  integrity sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-number/download/is-number-2.1.0.tgz#01fcbbb393463a548f2f466cce16dece49db908f"
  integrity sha1-Afy7s5NGOlSPL0ZszhbezknbkI8=
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-number/download/is-number-4.0.0.tgz#0026e37f5454d73e356dfe6564699867c6a7f0ff"
  integrity sha1-ACbjf1RU1z41bf5lZGmYZ8an8P8=

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-path-in-cwd/download/is-path-in-cwd-2.1.0.tgz#bfe2dca26c69f397265a4009963602935a053acb"
  integrity sha1-v+Lcomxp85cmWkAJljYCk1oFOss=
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-path-inside/download/is-path-inside-2.1.0.tgz#7c9810587d659a40d27bcdb4d5616eab059494b2"
  integrity sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=
  dependencies:
    path-is-inside "^1.0.2"

is-plain-obj@1.1.x, is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.alibaba-inc.com/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.alibaba-inc.com/is-posix-bracket/download/is-posix-bracket-0.1.1.tgz#3334dc79774368e92f016e6fbc0a88f5cd6e6bc4"
  integrity sha1-MzTceXdDaOkvAW5vvAqI9c1ua8Q=

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-primitive/download/is-primitive-2.0.0.tgz#207bab91638499c07b2adf240a41a87210034575"
  integrity sha1-IHurkWOEmcB7Kt8kCkGochADRXU=

is-regex@^1.0.4, is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.alibaba-inc.com/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.2.tgz#8f259c573b60b6a32d4058a1a07430c0a7344c79"
  integrity sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==
  dependencies:
    call-bind "^1.0.2"

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npm.alibaba-inc.com/is-string/download/is-string-1.0.7.tgz#0dd12bf2006f255bb58f695110eff7491eebc0fd"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-svg@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-svg/download/is-svg-2.1.0.tgz#cf61090da0d9efbcab8722deba6f032208dbb0e9"
  integrity sha1-z2EJDaDZ77yrhyLeum8DIgjbsOk=
  dependencies:
    html-comment-regex "^1.1.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/is-symbol/download/is-symbol-1.0.4.tgz#a6dac93b635b063ca6872236de88910a57af139c"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npm.alibaba-inc.com/is-utf8/download/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/is-weakref/download/is-weakref-1.0.2.tgz#9529f383a9338205e89765e0392efc2f100f06f2"
  integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
  dependencies:
    call-bind "^1.0.2"

is-what@^3.14.1:
  version "3.14.1"
  resolved "https://registry.npm.alibaba-inc.com/is-what/download/is-what-3.14.1.tgz#e1222f46ddda85dead0fd1c9df131760e77755c1"
  integrity sha1-4SIvRt3ahd6tD9HJ3xMXYOd3VcE=

is-windows@^1.0.1, is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.alibaba-inc.com/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isnumeric@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/isnumeric/download/isnumeric-0.2.0.tgz#a2347ba360de19e33d0ffd590fddf7755cbf2e64"
  integrity sha1-ojR7o2DeGeM9D/1ZD933dVy/LmQ=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  resolved "https://registry.npm.alibaba-inc.com/isomorphic-fetch/download/isomorphic-fetch-2.2.1.tgz#611ae1acf14f5e81f729507472819fe9733558a9"
  integrity sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.alibaba-inc.com/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

js-base64@^2.1.8, js-base64@^2.1.9:
  version "2.6.4"
  resolved "https://registry.npm.alibaba-inc.com/js-base64/download/js-base64-2.6.4.tgz#f4e686c5de1ea1f867dbcad3d46d969428df98c4"
  integrity sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ=

js-tokens@^3.0.0, js-tokens@^3.0.1, js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.alibaba-inc.com/js-tokens/download/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.0, js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npm.alibaba-inc.com/js-yaml/download/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@~3.7.0:
  version "3.7.0"
  resolved "https://registry.npm.alibaba-inc.com/js-yaml/download/js-yaml-3.7.0.tgz#5c967ddd837a9bfdca5f2de84253abe8a1c03b80"
  integrity sha1-XJZ93YN6m/3KXy3oQlOr6KHAO4A=
  dependencies:
    argparse "^1.0.7"
    esprima "^2.6.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.alibaba-inc.com/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/jsesc/download/jsesc-1.3.0.tgz#46c3fec8c1892b12b0833db9bc7622176dbab34b"
  integrity sha1-RsP+yMGJKxKwgz25vHYiF226s0s=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npm.alibaba-inc.com/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.npm.alibaba-inc.com/jsesc/download/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npm.alibaba-inc.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.3.0:
  version "0.3.1"
  resolved "https://registry.npm.alibaba-inc.com/json-schema-traverse/download/json-schema-traverse-0.3.1.tgz#349a6d44c53a51de89b40805c5d5e59b417d3340"
  integrity sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.alibaba-inc.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.alibaba-inc.com/json-schema/download/json-schema-0.4.0.tgz#f7de4cf6efab838ebaeb3236474cbba5a1930ab5"
  integrity sha1-995M9u+rg4666zI2R0y7paGTCrU=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.alibaba-inc.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@^0.5.0, json5@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npm.alibaba-inc.com/json5/download/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/json5/download/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

json5@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.alibaba-inc.com/json5/download/json5-2.2.1.tgz#655d50ed1e6f95ad1a3caababd2b0efda10b395c"
  integrity sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==

jsonschema@^1.2.4:
  version "1.4.1"
  resolved "https://registry.npm.alibaba-inc.com/jsonschema/download/jsonschema-1.4.1.tgz#cc4c3f0077fb4542982973d8a083b6b34f482dab"
  integrity sha512-S6cATIPVv1z0IlxdN+zUk5EPjkGCdnhN4wVSBlvoUO1tOLJootbo9CquNJmbIh4yikWHiUedhRYrNPn1arpEmQ==

jsprim@^1.2.2:
  version "1.4.2"
  resolved "https://registry.npm.alibaba-inc.com/jsprim/download/jsprim-1.4.2.tgz#712c65533a15c878ba59e9ed5f0e26d5b77c5feb"
  integrity sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

jsx-ast-utils@^1.3.4:
  version "1.4.1"
  resolved "https://registry.npm.alibaba-inc.com/jsx-ast-utils/download/jsx-ast-utils-1.4.1.tgz#3867213e8dd79bf1e8f2300c0cfc1efb182c0df1"
  integrity sha1-OGchPo3Xm/Ho8jAMDPwe+xgsDfE=

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.2:
  version "3.3.3"
  resolved "https://registry.npm.alibaba-inc.com/jsx-ast-utils/download/jsx-ast-utils-3.3.3.tgz#76b3e6e6cece5c69d49a5792c3d01bd1a0cdc7ea"
  integrity sha512-fYQHZTZ8jSfmWZ0iyzfwiU4WDX4HpHbMCZ3gPlWYiCl3BoeOTsqKBqnTVfH2rYT7eP5c3sVbeSPHnnJOaTrWiw==
  dependencies:
    array-includes "^3.1.5"
    object.assign "^4.1.3"

killable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/killable/download/killable-1.0.1.tgz#4c8ce441187a061c7474fb87ca08e2a638194892"
  integrity sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.npm.alibaba-inc.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npm.alibaba-inc.com/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.npm.alibaba-inc.com/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

language-subtag-registry@~0.3.2:
  version "0.3.22"
  resolved "https://registry.npm.alibaba-inc.com/language-subtag-registry/download/language-subtag-registry-0.3.22.tgz#2e1500861b2e457eba7e7ae86877cbd08fa1fd1d"
  integrity sha512-tN0MCzyWnoz/4nHS6uxdlFWoUZT7ABptwKPQ52Ea7URk6vll88bWBVhodtnlfEuCcKWNGoc+uGbw1cwa9IKh/w==

language-tags@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/language-tags/download/language-tags-1.0.5.tgz#d321dbc4da30ba8bf3024e040fa5c14661f9193a"
  integrity sha1-0yHbxNowuovzAk4ED6XBRmH5GTo=
  dependencies:
    language-subtag-registry "~0.3.2"

less-loader@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/less-loader/download/less-loader-4.1.0.tgz#2c1352c5b09a4f84101490274fd51674de41363e"
  integrity sha1-LBNSxbCaT4QQFJAnT9UWdN5BNj4=
  dependencies:
    clone "^2.1.1"
    loader-utils "^1.1.0"
    pify "^3.0.0"

less@^3.8.0:
  version "3.13.1"
  resolved "https://registry.npm.alibaba-inc.com/less/download/less-3.13.1.tgz#0ebc91d2a0e9c0c6735b83d496b0ab0583077909"
  integrity sha1-DryR0qDpwMZzW4PUlrCrBYMHeQk=
  dependencies:
    copy-anything "^2.0.1"
    tslib "^1.10.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    native-request "^1.0.5"
    source-map "~0.6.0"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.alibaba-inc.com/levn/download/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npm.alibaba-inc.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/load-json-file/download/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  integrity sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/load-json-file/download/load-json-file-2.0.0.tgz#7947e42149af80d696cbf797bcaabcfe1fe29ca8"
  integrity sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

loader-fs-cache@^1.0.0:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/loader-fs-cache/download/loader-fs-cache-1.0.3.tgz#f08657646d607078be2f0a032f8bd69dd6f277d9"
  integrity sha1-8IZXZG1gcHi+LwoDL4vWndbyd9k=
  dependencies:
    find-cache-dir "^0.1.1"
    mkdirp "^0.5.1"

loader-runner@^2.4.0:
  version "2.4.0"
  resolved "https://registry.npm.alibaba-inc.com/loader-runner/download/loader-runner-2.4.0.tgz#ed47066bfe534d7e84c4c7b9998c2a75607d9357"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "https://registry.npm.alibaba-inc.com/loader-utils/download/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348"
  integrity sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.0.1, loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.2.3, loader-utils@^1.4.0:
  version "1.4.2"
  resolved "https://registry.npm.alibaba-inc.com/loader-utils/download/loader-utils-1.4.2.tgz#29a957f3a63973883eb684f10ffd3d151fec01a3"
  integrity sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/locate-path/download/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.alibaba-inc.com/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/lodash._reinterpolate/download/lodash._reinterpolate-3.0.0.tgz#0ccf2d89166af03b3663c796538b75ac6e114d9d"
  integrity sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npm.alibaba-inc.com/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npm.alibaba-inc.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.tail@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.alibaba-inc.com/lodash.tail/download/lodash.tail-4.1.1.tgz#d2333a36d9e7717c8ad2f7cacafec7c32b444664"
  integrity sha1-0jM6NtnncXyK0vfKyv7HwytERmQ=

lodash.template@^4.2.4:
  version "4.5.0"
  resolved "https://registry.npm.alibaba-inc.com/lodash.template/download/lodash.template-4.5.0.tgz#f976195cf3f347d0d5f52483569fe8031ccce8ab"
  integrity sha1-+XYZXPPzR9DV9SSDVp/oAxzM6Ks=
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.templatesettings "^4.0.0"

lodash.templatesettings@^4.0.0:
  version "4.2.0"
  resolved "https://registry.npm.alibaba-inc.com/lodash.templatesettings/download/lodash.templatesettings-4.2.0.tgz#e481310f049d3cf6d47e912ad09313b154f0fb33"
  integrity sha1-5IExDwSdPPbUfpEq0JMTsVTw+zM=
  dependencies:
    lodash._reinterpolate "^3.0.0"

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npm.alibaba-inc.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@^4.0.0, lodash@^4.17.11, lodash@^4.17.12, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.17.3, lodash@^4.17.4, lodash@^4.17.5, lodash@^4.2.0:
  version "4.17.21"
  resolved "https://registry.npm.alibaba-inc.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

loglevel@^1.6.8:
  version "1.8.1"
  resolved "https://registry.npm.alibaba-inc.com/loglevel/download/loglevel-1.8.1.tgz#5c621f83d5b48c54ae93b6156353f555963377b4"
  integrity sha512-tCRIJM51SHjAayKwC+QAg8hT8vg6z7GSgLJKGvzuPb1Wc+hLzqtuVLxp6/HzSPOozuK+8ErAhy7U/sVzw8Dgfg==

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.2.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.alibaba-inc.com/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npm.alibaba-inc.com/loud-rejection/download/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  integrity sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "https://registry.npm.alibaba-inc.com/lower-case/download/lower-case-1.1.4.tgz#9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lru-cache@^4.0.1, lru-cache@^4.1.2:
  version "4.1.5"
  resolved "https://registry.npm.alibaba-inc.com/lru-cache/download/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npm.alibaba-inc.com/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

make-dir@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/make-dir/download/make-dir-1.3.0.tgz#79c1033b80515bd6d24ec9933e860ca75ee27f0c"
  integrity sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=
  dependencies:
    pify "^3.0.0"

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npm.alibaba-inc.com/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

math-expression-evaluator@^1.2.14:
  version "1.4.0"
  resolved "https://registry.npm.alibaba-inc.com/math-expression-evaluator/download/math-expression-evaluator-1.4.0.tgz#3d66031117fbb7b9715ea6c9c68c2cd2eebd37e2"
  integrity sha512-4vRUvPyxdO8cWULGTh9dZWL2tZK6LDBvj+OGHBER7poH9Qdt7kXEoj20wiz4lQUbUXQZFjPbe5mVDo9nutizCw==

math-random@^1.0.1:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/math-random/download/math-random-1.0.4.tgz#5dd6943c938548267016d4e34f057583080c514c"
  integrity sha1-XdaUPJOFSCZwFtTjTwV1gwgMUUw=

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://registry.npm.alibaba-inc.com/md5.js/download/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdn-data@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/mdn-data/download/mdn-data-1.2.0.tgz#eadd28b0f2d307cf27e71524609bfb749ebfc0b6"
  integrity sha1-6t0osPLTB88n5xUkYJv7dJ6/wLY=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.alibaba-inc.com/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-fs@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.alibaba-inc.com/memory-fs/download/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npm.alibaba-inc.com/memory-fs/download/memory-fs-0.5.0.tgz#324c01288b88652966d161db77838720845a8e3c"
  integrity sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^3.7.0:
  version "3.7.0"
  resolved "https://registry.npm.alibaba-inc.com/meow/download/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  integrity sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-source-map@^1.0.3, merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/merge-source-map/download/merge-source-map-1.1.0.tgz#2fdde7e6020939f70906a68f2d7ae685e4c8c646"
  integrity sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=
  dependencies:
    source-map "^0.6.1"

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^2.1.5:
  version "2.3.11"
  resolved "https://registry.npm.alibaba-inc.com/micromatch/download/micromatch-2.3.11.tgz#86677c97d1720b363431d04d0d15293bd38c1565"
  integrity sha1-hmd8l9FyCzY0MdBNDRUpO9OMFWU=
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

micromatch@^3.0.4, micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://registry.npm.alibaba-inc.com/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.alibaba-inc.com/miller-rabin/download/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.52.0, "mime-db@>= 1.43.0 < 2":
  version "1.52.0"
  resolved "https://registry.npm.alibaba-inc.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://registry.npm.alibaba-inc.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@1.6.0, mime@^1.4.1:
  version "1.6.0"
  resolved "https://registry.npm.alibaba-inc.com/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.4.4:
  version "2.6.0"
  resolved "https://registry.npm.alibaba-inc.com/mime/download/mime-2.6.0.tgz#a2a682a95cd4d0cb1d6257e28f83da7e35800367"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mini-css-extract-plugin@^0.4.1:
  version "0.4.5"
  resolved "https://registry.npm.alibaba-inc.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.4.5.tgz#c99e9e78d54f3fa775633aee5933aeaa4e80719a"
  integrity sha1-yZ6eeNVPP6d1YzruWTOuqk6AcZo=
  dependencies:
    loader-utils "^1.1.0"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@^3.0.3, minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npm.alibaba-inc.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@~3.0.2:
  version "3.0.8"
  resolved "https://registry.npm.alibaba-inc.com/minimatch/download/minimatch-3.0.8.tgz#5e6a59bd11e2ab0de1cfb843eb2d82e546c321c1"
  integrity sha512-6FsRAQsxQ61mw+qP1ZzbL9Bc78x2p5OqNgNpnoAFLTrX8n5Kxph0CsnhmKKNXTWjXqU5L0pGPR7hYk+XWZr60Q==
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.1.3, minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.7"
  resolved "https://registry.npm.alibaba-inc.com/minimist/download/minimist-1.2.7.tgz#daa1c4d91f507390437c6a8bc01078e7000c4d18"
  integrity sha512-bzfL1YUZsP41gmu/qjrEk0Q6i2ix/cVeAhbCbqH9u3zYutS1cLg00qhrD0M2MVdCcx4Sc0UpP2eBWo9rotpq6g==

mississippi@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/mississippi/download/mississippi-3.0.0.tgz#ea0a3291f97e0b5e8776b363d5f0a12d94c67022"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.npm.alibaba-inc.com/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mixin-object@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/mixin-object/download/mixin-object-2.0.1.tgz#4fb949441dab182540f1fe035ba60e1947a5e57e"
  integrity sha1-T7lJRB2rGCVA8f4DW6YOGUel5X4=
  dependencies:
    for-in "^0.1.3"
    is-extendable "^0.1.1"

"mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@^0.5.3, mkdirp@^0.5.6, mkdirp@~0.5.1:
  version "0.5.6"
  resolved "https://registry.npm.alibaba-inc.com/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==
  dependencies:
    minimist "^1.2.6"

mobx-react@^5.4.4:
  version "5.4.4"
  resolved "https://registry.npm.alibaba-inc.com/mobx-react/download/mobx-react-5.4.4.tgz#b3de9c6eabcd0ed8a40036888cb0221ab9568b80"
  integrity sha1-s96cbqvNDtikADaIjLAiGrlWi4A=
  dependencies:
    hoist-non-react-statics "^3.0.0"
    react-lifecycles-compat "^3.0.2"

mobx@^5.13.0:
  version "5.15.7"
  resolved "https://registry.npm.alibaba-inc.com/mobx/download/mobx-5.15.7.tgz#b9a5f2b6251f5d96980d13c78e9b5d8d4ce22665"
  integrity sha1-uaXytiUfXZaYDRPHjptdjUziJmU=

mockdate@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npm.alibaba-inc.com/mockdate/download/mockdate-2.0.5.tgz#70c6abf9ed4b2dae65c81dfc170dd1a5cec53620"
  integrity sha1-cMar+e1LLa5lyB38Fw3Rpc7FNiA=

mockjs@^1.0.1-beta3:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/mockjs/download/mockjs-1.1.0.tgz#e6a0c378e91906dbaff20911cc0273b3c7d75b06"
  integrity sha1-5qDDeOkZBtuv8gkRzAJzs8fXWwY=
  dependencies:
    commander "*"

moment@2.24.0:
  version "2.24.0"
  resolved "https://registry.npm.alibaba-inc.com/moment/download/moment-2.24.0.tgz#0d055d53f5052aa653c9f6eb68bb5d12bf5c2b5b"
  integrity sha1-DQVdU/UFKqZTyfbraLtdEr9cK1s=

moment@^2.24.0:
  version "2.29.4"
  resolved "https://registry.npm.alibaba-inc.com/moment/download/moment-2.29.4.tgz#3dbe052889fe7c1b2ed966fcb3a77328964ef108"
  integrity sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/move-concurrently/download/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.alibaba-inc.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@2.1.3, ms@^2.1.1:
  version "2.1.3"
  resolved "https://registry.npm.alibaba-inc.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz#899f11d9686e5e05cb91b35d5f0e63b773cfc901"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://registry.npm.alibaba-inc.com/multicast-dns/download/multicast-dns-6.2.3.tgz#a0ec7bd9055c4282f790c3c82f4e28db3b31b229"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

mute-stream@0.0.7:
  version "0.0.7"
  resolved "https://registry.npm.alibaba-inc.com/mute-stream/download/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

nan@^2.12.1, nan@^2.13.2:
  version "2.17.0"
  resolved "https://registry.npm.alibaba-inc.com/nan/download/nan-2.17.0.tgz#c0150a2368a182f033e9aa5195ec76ea41a199cb"
  integrity sha512-2ZTgtl0nJsO0KQCjEpxcIr5D+Yv90plTitZt9JBfQvVJDS5seMl3FOvsh3+9CoYWXf/1l5OaZzzF6nDm4cagaQ==

nanoid@^2.1.0:
  version "2.1.11"
  resolved "https://registry.npm.alibaba-inc.com/nanoid/download/nanoid-2.1.11.tgz#ec24b8a758d591561531b4176a01e3ab4f0f0280"
  integrity sha1-7CS4p1jVkVYVMbQXagHjq08PAoA=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.npm.alibaba-inc.com/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

native-request@^1.0.5:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/native-request/download/native-request-1.1.0.tgz#acdb30fe2eefa3e1bc8c54b3a6852e9c5c0d3cb0"
  integrity sha1-rNsw/i7vo+G8jFSzpoUunFwNPLA=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.alibaba-inc.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://registry.npm.alibaba-inc.com/negotiator/download/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

neo-async@^2.5.0, neo-async@^2.6.1:
  version "2.6.2"
  resolved "https://registry.npm.alibaba-inc.com/neo-async/download/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^2.2.0:
  version "2.3.2"
  resolved "https://registry.npm.alibaba-inc.com/no-case/download/no-case-2.3.2.tgz#60b813396be39b3f1288a4c1ed5d1e7d28b464ac"
  integrity sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=
  dependencies:
    lower-case "^1.1.1"

node-fetch@^1.0.1:
  version "1.7.3"
  resolved "https://registry.npm.alibaba-inc.com/node-fetch/download/node-fetch-1.7.3.tgz#980f6f72d85211a5347c6b2bc18c5b84c3eb47ef"
  integrity sha1-mA9vcthSEaU0fGsrwYxbhMPrR+8=
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-forge@^0.10.0:
  version "0.10.0"
  resolved "https://registry.npm.alibaba-inc.com/node-forge/download/node-forge-0.10.0.tgz#32dea2afb3e9926f02ee5ce8794902691a676bf3"
  integrity sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=

node-gyp@^3.8.0:
  version "3.8.0"
  resolved "https://registry.npm.alibaba-inc.com/node-gyp/download/node-gyp-3.8.0.tgz#540304261c330e80d0d5edce253a68cb3964218c"
  integrity sha1-VAMEJhwzDoDQ1e3OJTpoyzlkIYw=
  dependencies:
    fstream "^1.0.0"
    glob "^7.0.3"
    graceful-fs "^4.1.2"
    mkdirp "^0.5.0"
    nopt "2 || 3"
    npmlog "0 || 1 || 2 || 3 || 4"
    osenv "0"
    request "^2.87.0"
    rimraf "2"
    semver "~5.3.0"
    tar "^2.0.0"
    which "1"

node-libs-browser@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.alibaba-inc.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz#b64f513d18338625f90346d27b0d235e631f6425"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-releases@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npm.alibaba-inc.com/node-releases/download/node-releases-2.0.6.tgz#8a7088c63a55e493845683ebf3c828d8c51c5503"
  integrity sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==

node-sass@^4.7.2:
  version "4.14.1"
  resolved "https://registry.npm.alibaba-inc.com/node-sass/download/node-sass-4.14.1.tgz#99c87ec2efb7047ed638fb4c9db7f3a42e2217b5"
  integrity sha1-mch+wu+3BH7WOPtMnbfzpC4iF7U=
  dependencies:
    async-foreach "^0.1.3"
    chalk "^1.1.1"
    cross-spawn "^3.0.0"
    gaze "^1.0.0"
    get-stdin "^4.0.1"
    glob "^7.0.3"
    in-publish "^2.0.0"
    lodash "^4.17.15"
    meow "^3.7.0"
    mkdirp "^0.5.1"
    nan "^2.13.2"
    node-gyp "^3.8.0"
    npmlog "^4.0.0"
    request "^2.88.0"
    sass-graph "2.2.5"
    stdout-stream "^1.4.0"
    "true-case-path" "^1.0.2"

"nopt@2 || 3":
  version "3.0.6"
  resolved "https://registry.npm.alibaba-inc.com/nopt/download/nopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
  integrity sha1-xkZdvwirzU2zWTF/eaxopkayj/k=
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4:
  version "2.5.0"
  resolved "https://registry.npm.alibaba-inc.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.0.0, normalize-path@^2.0.1, normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.alibaba-inc.com/normalize-range/download/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@^1.4.0, normalize-url@^1.9.1:
  version "1.9.1"
  resolved "https://registry.npm.alibaba-inc.com/normalize-url/download/normalize-url-1.9.1.tgz#2cc0d66b31ea23036458436e3620d85954c66c3c"
  integrity sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

normalize.css@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.alibaba-inc.com/normalize.css/download/normalize.css-7.0.0.tgz#abfb1dd82470674e0322b53ceb1aaf412938e4bf"
  integrity sha1-q/sd2CRwZ04DIrU86xqvQSk45L8=

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

"npmlog@0 || 1 || 2 || 3 || 4", npmlog@^4.0.0:
  version "4.1.2"
  resolved "https://registry.npm.alibaba-inc.com/npmlog/download/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  integrity sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/nth-check/download/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d"
  integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
  dependencies:
    boolbase "^1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npm.alibaba-inc.com/num2fraction/download/num2fraction-1.2.2.tgz#6f682b6a027a4e9ddfa4564cd2589d1d4e669ede"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://registry.npm.alibaba-inc.com/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@4.1.x, object-assign@4.x, object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.alibaba-inc.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.alibaba-inc.com/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-hash@^1.1.4:
  version "1.3.1"
  resolved "https://registry.npm.alibaba-inc.com/object-hash/download/object-hash-1.3.1.tgz#fde452098a951cb145f039bb7d455449ddc126df"
  integrity sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=

object-inspect@^1.12.2, object-inspect@^1.9.0:
  version "1.12.2"
  resolved "https://registry.npm.alibaba-inc.com/object-inspect/download/object-inspect-1.12.2.tgz#c0641f26394532f28ab8d796ab954e43c009a8ea"
  integrity sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ==

object-is@^1.0.1:
  version "1.1.5"
  resolved "https://registry.npm.alibaba-inc.com/object-is/download/object-is-1.1.5.tgz#b9deeaa5fc7f1846a0faecdceec138e5778f53ac"
  integrity sha1-ud7qpfx/GEag+uzc7sE45XePU6w=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.0.1, object.assign@^4.1.0, object.assign@^4.1.3, object.assign@^4.1.4:
  version "4.1.4"
  resolved "https://registry.npm.alibaba-inc.com/object.assign/download/object.assign-4.1.4.tgz#9673c7c7c351ab8c4d0b516f4343ebf4dfb7799f"
  integrity sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.0, object.entries@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npm.alibaba-inc.com/object.entries/download/object.entries-1.1.6.tgz#9737d0e5b8291edd340a3e3264bb8a3b00d5fa23"
  integrity sha512-leTPzo4Zvg3pmbQ3rDK69Rl8GQvIqMWubrkxONG9/ojtFE2rD9fjMKfSI5BxW3osRH1m6VdzmqK8oAY9aT4x5w==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.fromentries@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npm.alibaba-inc.com/object.fromentries/download/object.fromentries-2.0.6.tgz#cdb04da08c539cffa912dcd368b886e0904bfa73"
  integrity sha512-VciD13dswC4j1Xt5394WR4MzmAQmlgN72phd/riNp9vtD7tp4QQWJ0R4wvclXcafgcYK8veHRed2W6XeGBvcfg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.getownpropertydescriptors@^2.0.3:
  version "2.1.5"
  resolved "https://registry.npm.alibaba-inc.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.5.tgz#db5a9002489b64eef903df81d6623c07e5b4b4d3"
  integrity sha512-yDNzckpM6ntyQiGTik1fKV1DcVDRS+w8bvpWNCBanvH5LfRX9O8WTHqQzG4RZwRAM4I0oU7TV11Lj5v0g20ibw==
  dependencies:
    array.prototype.reduce "^1.0.5"
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.hasown@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/object.hasown/download/object.hasown-1.1.2.tgz#f919e21fad4eb38a57bc6345b3afd496515c3f92"
  integrity sha512-B5UIT3J1W+WuWIU55h0mjlwaqxiE5vYENJXIXZ4VFe05pNYrkKuK0U/6aFcb0pKywYJh7IhfoqUfKVmrJJHZHw==
  dependencies:
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/object.omit/download/object.omit-2.0.1.tgz#1a9c744829f39dbb858c76ca3579ae2a54ebd1fa"
  integrity sha1-Gpx0SCnznbuFjHbKNXmuKlTr0fo=
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

object.omit@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/object.omit/download/object.omit-3.0.0.tgz#0e3edc2fce2ba54df5577ff529f6d97bd8a522af"
  integrity sha1-Dj7cL84rpU31V3/1KfbZe9ilIq8=
  dependencies:
    is-extendable "^1.0.0"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.5, object.values@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npm.alibaba-inc.com/object.values/download/object.values-1.1.6.tgz#4abbaa71eba47d63589d402856f908243eea9b1d"
  integrity sha512-FVVTkD1vENCsAcwNs9k6jea2uHC/X0+JcjG8YA60FN5CMaJmG95wT9jek/xX9nornqGRrBkKtzuAu2wuHpKqvw==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/obuf/download/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@2.4.1:
  version "2.4.1"
  resolved "https://registry.npm.alibaba-inc.com/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/on-headers/download/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.alibaba-inc.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onecolor@^3.0.4:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/onecolor/download/onecolor-3.1.0.tgz#b72522270a49569ac20d244b3cd40fe157fda4d2"
  integrity sha1-tyUiJwpJVprCDSRLPNQP4Vf9pNI=

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

opn@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npm.alibaba-inc.com/opn/download/opn-5.5.0.tgz#fc7164fab56d235904c51c3b27da6758ca3b9bfc"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.8.1, optionator@^0.8.2:
  version "0.8.3"
  resolved "https://registry.npm.alibaba-inc.com/optionator/download/optionator-0.8.3.tgz#84fa1d036fe9d3c7e21d99884b601167ec8fb495"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.alibaba-inc.com/os-browserify/download/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-tmpdir@^1.0.0, os-tmpdir@^1.0.1, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osenv@0:
  version "0.1.5"
  resolved "https://registry.npm.alibaba-inc.com/osenv/download/osenv-0.1.5.tgz#85cdfafaeb28e8677f416e287592b5f3f49ea410"
  integrity sha1-hc36+uso6Gd/QW4odZK18/SepBA=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

output-file-sync@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/output-file-sync/download/output-file-sync-1.1.2.tgz#d0a33eefe61a205facb90092e826598d5245ce76"
  integrity sha1-0KM+7+YaIF+suQCS6CZZjVJFznY=
  dependencies:
    graceful-fs "^4.1.4"
    mkdirp "^0.5.1"
    object-assign "^4.1.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/p-limit/download/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npm.alibaba-inc.com/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/p-locate/download/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/p-map/download/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-retry@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/p-retry/download/p-retry-3.0.1.tgz#316b4c8893e2c8dc1cfa891f406c4b422bebf328"
  integrity sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=
  dependencies:
    retry "^0.12.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/p-try/download/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.5:
  version "1.0.11"
  resolved "https://registry.npm.alibaba-inc.com/pako/download/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/parallel-transform/download/parallel-transform-1.2.0.tgz#9049ca37d6cb2182c3b1d2c720be94d14a5814fc"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@2.1.x:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/param-case/download/param-case-2.1.1.tgz#df94fd8cf6531ecf75e6bef9a0858fbc72be2247"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0, parse-asn1@^5.1.5:
  version "5.1.6"
  resolved "https://registry.npm.alibaba-inc.com/parse-asn1/download/parse-asn1-5.1.6.tgz#385080a3ec13cb62a62d39409cb3e88844cdaed4"
  integrity sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=
  dependencies:
    asn1.js "^5.2.0"
    browserify-aes "^1.0.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-git-config@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/parse-git-config/download/parse-git-config-1.1.1.tgz#d3a9984317132f57398712bba438e129590ddf8c"
  integrity sha1-06mYQxcTL1c5hxK7pDjhKVkN34w=
  dependencies:
    extend-shallow "^2.0.1"
    fs-exists-sync "^0.1.0"
    git-config-path "^1.0.1"
    ini "^1.3.4"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.alibaba-inc.com/parse-glob/download/parse-glob-3.0.4.tgz#b2c376cfb11f35513badd173ef0bb6e3a388391c"
  integrity sha1-ssN2z7EfNVE7rdFz7wu246OIORw=
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/parse-json/download/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npm.alibaba-inc.com/parse-json/download/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-passwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/parse-passwd/download/parse-passwd-1.0.0.tgz#6d5b934a456993b23d37f40a382d6f1666a8e5c6"
  integrity sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npm.alibaba-inc.com/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.alibaba-inc.com/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.alibaba-inc.com/path-browserify/download/path-browserify-0.0.1.tgz#e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/path-exists/download/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  integrity sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0, path-is-absolute@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/path-is-inside/download/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npm.alibaba-inc.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npm.alibaba-inc.com/path-to-regexp/download/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@^1.7.0:
  version "1.8.0"
  resolved "https://registry.npm.alibaba-inc.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz#887b3ba9d84393e87a0a0b9f4cb756198b53548a"
  integrity sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo=
  dependencies:
    isarray "0.0.1"

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/path-type/download/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  integrity sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/path-type/download/path-type-2.0.0.tgz#f012ccb8415b7096fc2daa1054c3d72389594c73"
  integrity sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=
  dependencies:
    pify "^2.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pbkdf2@^3.0.3:
  version "3.1.2"
  resolved "https://registry.npm.alibaba-inc.com/pbkdf2/download/pbkdf2-3.1.2.tgz#dd822aa0887580e52f1a039dc3eda108efae3075"
  integrity sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.alibaba-inc.com/picocolors/download/picocolors-0.2.1.tgz#570670f793646851d1ba135996962abad587859f"
  integrity sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/picocolors/download/picocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.3.1"
  resolved "https://registry.npm.alibaba-inc.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pify@^2.0.0, pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npm.alibaba-inc.com/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.alibaba-inc.com/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npm.alibaba-inc.com/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pixrem@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.alibaba-inc.com/pixrem/download/pixrem-4.0.1.tgz#2da4a1de6ec4423c5fc3794e930b81d4490ec686"
  integrity sha1-LaSh3m7EQjxfw3lOkwuB1EkOxoY=
  dependencies:
    browserslist "^2.0.0"
    postcss "^6.0.0"
    reduce-css-calc "^1.2.7"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/pkg-dir/download/pkg-dir-1.0.0.tgz#7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4"
  integrity sha1-ektQio1bstYp1EcFb/TpyTFM89Q=
  dependencies:
    find-up "^1.0.0"

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/pkg-dir/download/pkg-dir-2.0.0.tgz#f6d5d1109e19d63edf428e0bd57e12777615334b"
  integrity sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=
  dependencies:
    find-up "^2.1.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pleeease-filters@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/pleeease-filters/download/pleeease-filters-4.0.0.tgz#6632b2fb05648d2758d865384fbced79e1ccaec7"
  integrity sha1-ZjKy+wVkjSdY2GU4T7zteeHMrsc=
  dependencies:
    onecolor "^3.0.4"
    postcss "^6.0.1"

pngjs@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.alibaba-inc.com/pngjs/download/pngjs-5.0.0.tgz#e79dd2b215767fd9c04561c01236df960bce7fbb"
  integrity sha1-553SshV2f9nARWHAEjbflgvOf7s=

portfinder@^1.0.17, portfinder@^1.0.26:
  version "1.0.32"
  resolved "https://registry.npm.alibaba-inc.com/portfinder/download/portfinder-1.0.32.tgz#2fe1b9e58389712429dc2bea5beb2146146c7f81"
  integrity sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==
  dependencies:
    async "^2.6.4"
    debug "^3.2.7"
    mkdirp "^0.5.6"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.alibaba-inc.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

post-compile-webpack-plugin@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.alibaba-inc.com/post-compile-webpack-plugin/download/post-compile-webpack-plugin-0.1.2.tgz#0f6e780a4dff2e5f59f207f903a8d5f0476c842e"
  integrity sha1-D254Ck3/Ll9Z8gf5A6jV8EdshC4=

postcss-apply@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-apply/download/postcss-apply-0.8.0.tgz#14e544bbb5cb6f1c1e048857965d79ae066b1343"
  integrity sha1-FOVEu7XLbxweBIhXll15rgZrE0M=
  dependencies:
    babel-runtime "^6.23.0"
    balanced-match "^0.4.2"
    postcss "^6.0.0"

postcss-aspect-ratio-mini@0.0.2:
  version "0.0.2"
  resolved "https://registry.npm.alibaba-inc.com/postcss-aspect-ratio-mini/download/postcss-aspect-ratio-mini-0.0.2.tgz#aea35e9a0560d306caa5a607103812cc830285a3"
  integrity sha1-rqNemgVg0wbKpaYHEDgSzIMChaM=
  dependencies:
    postcss "^6.0.0"

postcss-attribute-case-insensitive@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-attribute-case-insensitive/download/postcss-attribute-case-insensitive-2.0.0.tgz#94dc422c8f90997f16bd33a3654bbbec084963b4"
  integrity sha1-lNxCLI+QmX8WvTOjZUu77AhJY7Q=
  dependencies:
    postcss "^6.0.0"
    postcss-selector-parser "^2.2.3"

postcss-calc@^5.2.0:
  version "5.3.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-calc/download/postcss-calc-5.3.1.tgz#77bae7ca928ad85716e2fda42f261bf7c1d65b5e"
  integrity sha1-d7rnypKK2FcW4v2kLyYb98HWW14=
  dependencies:
    postcss "^5.0.2"
    postcss-message-helpers "^2.0.0"
    reduce-css-calc "^1.2.6"

postcss-calc@^6.0.0:
  version "6.0.2"
  resolved "https://registry.npm.alibaba-inc.com/postcss-calc/download/postcss-calc-6.0.2.tgz#4d9a43e27dbbf27d095fecb021ac6896e2318337"
  integrity sha1-TZpD4n278n0JX+ywIaxoluIxgzc=
  dependencies:
    css-unit-converter "^1.1.1"
    postcss "^7.0.2"
    postcss-selector-parser "^2.2.2"
    reduce-css-calc "^2.0.0"

postcss-color-function@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-color-function/download/postcss-color-function-4.1.0.tgz#b6f9355e07b12fcc5c34dab957834769b03d8f57"
  integrity sha1-tvk1XgexL8xcNNq5V4NHabA9j1c=
  dependencies:
    css-color-function "~1.3.3"
    postcss "^6.0.23"
    postcss-message-helpers "^2.0.0"
    postcss-value-parser "^3.3.1"

postcss-color-gray@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-color-gray/download/postcss-color-gray-4.1.0.tgz#e5581ed57eaa826fb652ca11b1e2b7b136a9f9df"
  integrity sha1-5Vge1X6qgm+2UsoRseK3sTap+d8=
  dependencies:
    color "^2.0.1"
    postcss "^6.0.14"
    postcss-message-helpers "^2.0.0"
    reduce-function-call "^1.0.2"

postcss-color-hex-alpha@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-color-hex-alpha/download/postcss-color-hex-alpha-3.0.0.tgz#1e53e6c8acb237955e8fd08b7ecdb1b8b8309f95"
  integrity sha1-HlPmyKyyN5Vej9CLfs2xuLgwn5U=
  dependencies:
    color "^1.0.3"
    postcss "^6.0.1"
    postcss-message-helpers "^2.0.0"

postcss-color-hsl@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-color-hsl/download/postcss-color-hsl-2.0.0.tgz#12703666fa310430e3f30a454dac1386317d5844"
  integrity sha1-EnA2ZvoxBDDj8wpFTawThjF9WEQ=
  dependencies:
    postcss "^6.0.1"
    postcss-value-parser "^3.3.0"
    units-css "^0.4.0"

postcss-color-hwb@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-color-hwb/download/postcss-color-hwb-3.0.0.tgz#3402b19ef4d8497540c1fb5072be9863ca95571e"
  integrity sha1-NAKxnvTYSXVAwftQcr6YY8qVVx4=
  dependencies:
    color "^1.0.3"
    postcss "^6.0.1"
    postcss-message-helpers "^2.0.0"
    reduce-function-call "^1.0.2"

postcss-color-rebeccapurple@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-color-rebeccapurple/download/postcss-color-rebeccapurple-3.1.0.tgz#ce1269ecc2d0d8bf92aab44bd884e633124c33ec"
  integrity sha1-zhJp7MLQ2L+SqrRL2ITmMxJMM+w=
  dependencies:
    postcss "^6.0.22"
    postcss-values-parser "^1.5.0"

postcss-color-rgb@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-color-rgb/download/postcss-color-rgb-2.0.0.tgz#14539c8a7131494b482e0dd1cc265ff6514b5263"
  integrity sha1-FFOcinExSUtILg3RzCZf9lFLUmM=
  dependencies:
    postcss "^6.0.1"
    postcss-value-parser "^3.3.0"

postcss-color-rgba-fallback@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-color-rgba-fallback/download/postcss-color-rgba-fallback-3.0.0.tgz#37d5c9353a07a09270912a82606bb42a0d702c04"
  integrity sha1-N9XJNToHoJJwkSqCYGu0Kg1wLAQ=
  dependencies:
    postcss "^6.0.6"
    postcss-value-parser "^3.3.0"
    rgb-hex "^2.1.0"

postcss-colormin@^2.1.8:
  version "2.2.2"
  resolved "https://registry.npm.alibaba-inc.com/postcss-colormin/download/postcss-colormin-2.2.2.tgz#6631417d5f0e909a3d7ec26b24c8a8d1e4f96e4b"
  integrity sha1-ZjFBfV8OkJo9fsJrJMio0eT5bks=
  dependencies:
    colormin "^1.0.5"
    postcss "^5.0.13"
    postcss-value-parser "^3.2.3"

postcss-convert-values@^2.3.4:
  version "2.6.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-convert-values/download/postcss-convert-values-2.6.1.tgz#bbd8593c5c1fd2e3d1c322bb925dcae8dae4d62d"
  integrity sha1-u9hZPFwf0uPRwyK7kl3K6Nrk1i0=
  dependencies:
    postcss "^5.0.11"
    postcss-value-parser "^3.1.2"

postcss-cssnext@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-cssnext/download/postcss-cssnext-3.1.1.tgz#db7ed5e782da6516a8496cbfe080ee7393fee7e9"
  integrity sha1-237V54LaZRaoSWy/4IDuc5P+5+k=
  dependencies:
    autoprefixer "^7.1.1"
    caniuse-api "^2.0.0"
    chalk "^2.0.1"
    pixrem "^4.0.0"
    pleeease-filters "^4.0.0"
    postcss "^6.0.5"
    postcss-apply "^0.8.0"
    postcss-attribute-case-insensitive "^2.0.0"
    postcss-calc "^6.0.0"
    postcss-color-function "^4.0.0"
    postcss-color-gray "^4.0.0"
    postcss-color-hex-alpha "^3.0.0"
    postcss-color-hsl "^2.0.0"
    postcss-color-hwb "^3.0.0"
    postcss-color-rebeccapurple "^3.0.0"
    postcss-color-rgb "^2.0.0"
    postcss-color-rgba-fallback "^3.0.0"
    postcss-custom-media "^6.0.0"
    postcss-custom-properties "^6.1.0"
    postcss-custom-selectors "^4.0.1"
    postcss-font-family-system-ui "^3.0.0"
    postcss-font-variant "^3.0.0"
    postcss-image-set-polyfill "^0.3.5"
    postcss-initial "^2.0.0"
    postcss-media-minmax "^3.0.0"
    postcss-nesting "^4.0.1"
    postcss-pseudo-class-any-link "^4.0.0"
    postcss-pseudoelements "^5.0.0"
    postcss-replace-overflow-wrap "^2.0.0"
    postcss-selector-matches "^3.0.1"
    postcss-selector-not "^3.0.1"

postcss-custom-media@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-custom-media/download/postcss-custom-media-6.0.0.tgz#be532784110ecb295044fb5395a18006eb21a737"
  integrity sha1-vlMnhBEOyylQRPtTlaGABushpzc=
  dependencies:
    postcss "^6.0.1"

postcss-custom-properties@^6.1.0:
  version "6.3.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-custom-properties/download/postcss-custom-properties-6.3.1.tgz#5c52abde313d7ec9368c4abf67d27a656cba8b39"
  integrity sha1-XFKr3jE9fsk2jEq/Z9J6ZWy6izk=
  dependencies:
    balanced-match "^1.0.0"
    postcss "^6.0.18"

postcss-custom-selectors@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-custom-selectors/download/postcss-custom-selectors-4.0.1.tgz#781382f94c52e727ef5ca4776ea2adf49a611382"
  integrity sha1-eBOC+UxS5yfvXKR3bqKt9JphE4I=
  dependencies:
    postcss "^6.0.1"
    postcss-selector-matches "^3.0.0"

postcss-discard-comments@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.alibaba-inc.com/postcss-discard-comments/download/postcss-discard-comments-2.0.4.tgz#befe89fafd5b3dace5ccce51b76b81514be00e3d"
  integrity sha1-vv6J+v1bPazlzM5Rt2uBUUvgDj0=
  dependencies:
    postcss "^5.0.14"

postcss-discard-duplicates@^2.0.1:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-discard-duplicates/download/postcss-discard-duplicates-2.1.0.tgz#b9abf27b88ac188158a5eb12abcae20263b91932"
  integrity sha1-uavye4isGIFYpesSq8riAmO5GTI=
  dependencies:
    postcss "^5.0.4"

postcss-discard-empty@^2.0.1:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-discard-empty/download/postcss-discard-empty-2.1.0.tgz#d2b4bd9d5ced5ebd8dcade7640c7d7cd7f4f92b5"
  integrity sha1-0rS9nVztXr2Nyt52QMfXzX9PkrU=
  dependencies:
    postcss "^5.0.14"

postcss-discard-overridden@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-discard-overridden/download/postcss-discard-overridden-0.1.1.tgz#8b1eaf554f686fb288cd874c55667b0aa3668d58"
  integrity sha1-ix6vVU9ob7KIzYdMVWZ7CqNmjVg=
  dependencies:
    postcss "^5.0.16"

postcss-discard-unused@^2.2.1:
  version "2.2.3"
  resolved "https://registry.npm.alibaba-inc.com/postcss-discard-unused/download/postcss-discard-unused-2.2.3.tgz#bce30b2cc591ffc634322b5fb3464b6d934f4433"
  integrity sha1-vOMLLMWR/8Y0Mitfs0ZLbZNPRDM=
  dependencies:
    postcss "^5.0.14"
    uniqs "^2.0.0"

postcss-filter-plugins@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npm.alibaba-inc.com/postcss-filter-plugins/download/postcss-filter-plugins-2.0.3.tgz#82245fdf82337041645e477114d8e593aa18b8ec"
  integrity sha1-giRf34IzcEFkXkdxFNjlk6oYuOw=
  dependencies:
    postcss "^5.0.4"

postcss-font-family-system-ui@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-font-family-system-ui/download/postcss-font-family-system-ui-3.0.0.tgz#675fe7a9e029669f05f8dba2e44c2225ede80623"
  integrity sha1-Z1/nqeApZp8F+Nui5EwiJe3oBiM=
  dependencies:
    postcss "^6.0"

postcss-font-variant@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-font-variant/download/postcss-font-variant-3.0.0.tgz#08ccc88f6050ba82ed8ef2cc76c0c6a6b41f183e"
  integrity sha1-CMzIj2BQuoLtjvLMdsDGprQfGD4=
  dependencies:
    postcss "^6.0.1"

postcss-image-set-polyfill@^0.3.5:
  version "0.3.5"
  resolved "https://registry.npm.alibaba-inc.com/postcss-image-set-polyfill/download/postcss-image-set-polyfill-0.3.5.tgz#0f193413700cf1f82bd39066ef016d65a4a18181"
  integrity sha1-Dxk0E3AM8fgr05Bm7wFtZaShgYE=
  dependencies:
    postcss "^6.0.1"
    postcss-media-query-parser "^0.2.3"

postcss-import@^11.0.0:
  version "11.1.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-import/download/postcss-import-11.1.0.tgz#55c9362c9192994ec68865d224419df1db2981f0"
  integrity sha1-Vck2LJGSmU7GiGXSJEGd8dspgfA=
  dependencies:
    postcss "^6.0.1"
    postcss-value-parser "^3.2.3"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-initial@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-initial/download/postcss-initial-2.0.0.tgz#72715f7336e0bb79351d99ee65c4a253a8441ba4"
  integrity sha1-cnFfczbgu3k1HZnuZcSiU6hEG6Q=
  dependencies:
    lodash.template "^4.2.4"
    postcss "^6.0.1"

postcss-load-config@^2.0.0:
  version "2.1.2"
  resolved "https://registry.npm.alibaba-inc.com/postcss-load-config/download/postcss-load-config-2.1.2.tgz#c5ea504f2c4aef33c7359a34de3573772ad7502a"
  integrity sha1-xepQTyxK7zPHNZo03jVzdyrXUCo=
  dependencies:
    cosmiconfig "^5.0.0"
    import-cwd "^2.0.0"

postcss-loader@^2.0.9:
  version "2.1.6"
  resolved "https://registry.npm.alibaba-inc.com/postcss-loader/download/postcss-loader-2.1.6.tgz#1d7dd7b17c6ba234b9bed5af13e0bea40a42d740"
  integrity sha1-HX3XsXxrojS5vtWvE+C+pApC10A=
  dependencies:
    loader-utils "^1.1.0"
    postcss "^6.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^0.4.0"

postcss-media-minmax@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-media-minmax/download/postcss-media-minmax-3.0.0.tgz#675256037a43ef40bc4f0760bfd06d4dc69d48d2"
  integrity sha1-Z1JWA3pD70C8Twdgv9BtTcadSNI=
  dependencies:
    postcss "^6.0.1"

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npm.alibaba-inc.com/postcss-media-query-parser/download/postcss-media-query-parser-0.2.3.tgz#27b39c6f4d94f81b1a73b8f76351c609e5cef244"
  integrity sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=

postcss-merge-idents@^2.1.5:
  version "2.1.7"
  resolved "https://registry.npm.alibaba-inc.com/postcss-merge-idents/download/postcss-merge-idents-2.1.7.tgz#4c5530313c08e1d5b3bbf3d2bbc747e278eea270"
  integrity sha1-TFUwMTwI4dWzu/PSu8dH4njuonA=
  dependencies:
    has "^1.0.1"
    postcss "^5.0.10"
    postcss-value-parser "^3.1.1"

postcss-merge-longhand@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/postcss-merge-longhand/download/postcss-merge-longhand-2.0.2.tgz#23d90cd127b0a77994915332739034a1a4f3d658"
  integrity sha1-I9kM0Sewp3mUkVMyc5A0oaTz1lg=
  dependencies:
    postcss "^5.0.4"

postcss-merge-rules@^2.0.3:
  version "2.1.2"
  resolved "https://registry.npm.alibaba-inc.com/postcss-merge-rules/download/postcss-merge-rules-2.1.2.tgz#d1df5dfaa7b1acc3be553f0e9e10e87c61b5f721"
  integrity sha1-0d9d+qexrMO+VT8OnhDofGG19yE=
  dependencies:
    browserslist "^1.5.2"
    caniuse-api "^1.5.2"
    postcss "^5.0.4"
    postcss-selector-parser "^2.2.2"
    vendors "^1.0.0"

postcss-message-helpers@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-message-helpers/download/postcss-message-helpers-2.0.0.tgz#a4f2f4fab6e4fe002f0aed000478cdf52f9ba60e"
  integrity sha1-pPL0+rbk/gAvCu0ABHjN9S+bpg4=

postcss-minify-font-values@^1.0.2:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/postcss-minify-font-values/download/postcss-minify-font-values-1.0.5.tgz#4b58edb56641eba7c8474ab3526cafd7bbdecb69"
  integrity sha1-S1jttWZB66fIR0qzUmyv17vey2k=
  dependencies:
    object-assign "^4.0.1"
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-minify-gradients@^1.0.1:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/postcss-minify-gradients/download/postcss-minify-gradients-1.0.5.tgz#5dbda11373703f83cfb4a3ea3881d8d75ff5e6e1"
  integrity sha1-Xb2hE3NwP4PPtKPqOIHY11/15uE=
  dependencies:
    postcss "^5.0.12"
    postcss-value-parser "^3.3.0"

postcss-minify-params@^1.0.4:
  version "1.2.2"
  resolved "https://registry.npm.alibaba-inc.com/postcss-minify-params/download/postcss-minify-params-1.2.2.tgz#ad2ce071373b943b3d930a3fa59a358c28d6f1f3"
  integrity sha1-rSzgcTc7lDs9kwo/pZo1jCjW8fM=
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.2"
    postcss-value-parser "^3.0.2"
    uniqs "^2.0.0"

postcss-minify-selectors@^2.0.4:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-minify-selectors/download/postcss-minify-selectors-2.1.1.tgz#b2c6a98c0072cf91b932d1a496508114311735bf"
  integrity sha1-ssapjAByz5G5MtGkllCBFDEXNb8=
  dependencies:
    alphanum-sort "^1.0.2"
    has "^1.0.1"
    postcss "^5.0.14"
    postcss-selector-parser "^2.0.0"

postcss-modules-extract-imports@^1.2.0:
  version "1.2.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-1.2.1.tgz#dc87e34148ec7eab5f791f7cd5849833375b741a"
  integrity sha1-3IfjQUjsfqtfeR981YSYMzdbdBo=
  dependencies:
    postcss "^6.0.1"

postcss-modules-local-by-default@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-1.2.0.tgz#f7d80c398c5a393fa7964466bd19500a7d61c069"
  integrity sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-scope@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-modules-scope/download/postcss-modules-scope-1.1.0.tgz#d6ea64994c79f97b62a72b426fbe6056a194bb90"
  integrity sha1-1upkmUx5+XtipytCb75gVqGUu5A=
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-values@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-modules-values/download/postcss-modules-values-1.3.0.tgz#ecffa9d7e192518389f42ad0e83f72aec456ea20"
  integrity sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA=
  dependencies:
    icss-replace-symbols "^1.1.0"
    postcss "^6.0.1"

postcss-nesting@^4.0.1:
  version "4.2.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-nesting/download/postcss-nesting-4.2.1.tgz#0483bce338b3f0828ced90ff530b29b98b00300d"
  integrity sha1-BIO84ziz8IKM7ZD/UwspuYsAMA0=
  dependencies:
    postcss "^6.0.11"

postcss-normalize-charset@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-normalize-charset/download/postcss-normalize-charset-1.1.1.tgz#ef9ee71212d7fe759c78ed162f61ed62b5cb93f1"
  integrity sha1-757nEhLX/nWceO0WL2HtYrXLk/E=
  dependencies:
    postcss "^5.0.5"

postcss-normalize-url@^3.0.7:
  version "3.0.8"
  resolved "https://registry.npm.alibaba-inc.com/postcss-normalize-url/download/postcss-normalize-url-3.0.8.tgz#108f74b3f2fcdaf891a2ffa3ea4592279fc78222"
  integrity sha1-EI90s/L82viRov+j6kWSJ5/HgiI=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^1.4.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"

postcss-ordered-values@^2.1.0:
  version "2.2.3"
  resolved "https://registry.npm.alibaba-inc.com/postcss-ordered-values/download/postcss-ordered-values-2.2.3.tgz#eec6c2a67b6c412a8db2042e77fe8da43f95c11d"
  integrity sha1-7sbCpntsQSqNsgQud/6NpD+VwR0=
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.1"

postcss-pseudo-class-any-link@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-pseudo-class-any-link/download/postcss-pseudo-class-any-link-4.0.0.tgz#9152a0613d3450720513e8892854bae42d0ee68e"
  integrity sha1-kVKgYT00UHIFE+iJKFS65C0O5o4=
  dependencies:
    postcss "^6.0.1"
    postcss-selector-parser "^2.2.3"

postcss-pseudoelements@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-pseudoelements/download/postcss-pseudoelements-5.0.0.tgz#eef194e8d524645ca520a949e95e518e812402cb"
  integrity sha1-7vGU6NUkZFylIKlJ6V5RjoEkAss=
  dependencies:
    postcss "^6.0.0"

postcss-px-to-viewport@0.0.3:
  version "0.0.3"
  resolved "https://registry.npm.alibaba-inc.com/postcss-px-to-viewport/download/postcss-px-to-viewport-0.0.3.tgz#761259af20ad70adb01f503697337276d7831363"
  integrity sha1-dhJZryCtcK2wH1A2lzNydteDE2M=
  dependencies:
    object-assign "^4.0.1"
    postcss "^5.0.2"

postcss-pxtorem@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-pxtorem/download/postcss-pxtorem-4.0.1.tgz#9c64d0efe4885473cc1cb0305c6ffc3ebb45b1cd"
  integrity sha1-nGTQ7+SIVHPMHLAwXG/8PrtFsc0=
  dependencies:
    object-assign "^4.1.0"
    postcss "^5.2.10"

postcss-reduce-idents@^2.2.2:
  version "2.4.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-reduce-idents/download/postcss-reduce-idents-2.4.0.tgz#c2c6d20cc958284f6abfbe63f7609bf409059ad3"
  integrity sha1-wsbSDMlYKE9qv75j92Cb9AkFmtM=
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-reduce-initial@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-reduce-initial/download/postcss-reduce-initial-1.0.1.tgz#68f80695f045d08263a879ad240df8dd64f644ea"
  integrity sha1-aPgGlfBF0IJjqHmtJA343WT2ROo=
  dependencies:
    postcss "^5.0.4"

postcss-reduce-transforms@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/postcss-reduce-transforms/download/postcss-reduce-transforms-1.0.4.tgz#ff76f4d8212437b31c298a42d2e1444025771ae1"
  integrity sha1-/3b02CEkN7McKYpC0uFEQCV3GuE=
  dependencies:
    has "^1.0.1"
    postcss "^5.0.8"
    postcss-value-parser "^3.0.1"

postcss-replace-overflow-wrap@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-replace-overflow-wrap/download/postcss-replace-overflow-wrap-2.0.0.tgz#794db6faa54f8db100854392a93af45768b4e25b"
  integrity sha1-eU22+qVPjbEAhUOSqTr0V2i04ls=
  dependencies:
    postcss "^6.0.1"

postcss-selector-matches@^3.0.0, postcss-selector-matches@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-selector-matches/download/postcss-selector-matches-3.0.1.tgz#e5634011e13950881861bbdd58c2d0111ffc96ab"
  integrity sha1-5WNAEeE5UIgYYbvdWMLQER/8lqs=
  dependencies:
    balanced-match "^0.4.2"
    postcss "^6.0.1"

postcss-selector-not@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-selector-not/download/postcss-selector-not-3.0.1.tgz#2e4db2f0965336c01e7cec7db6c60dff767335d9"
  integrity sha1-Lk2y8JZTNsAefOx9tsYN/3ZzNdk=
  dependencies:
    balanced-match "^0.4.2"
    postcss "^6.0.1"

postcss-selector-parser@^2.0.0, postcss-selector-parser@^2.2.2, postcss-selector-parser@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npm.alibaba-inc.com/postcss-selector-parser/download/postcss-selector-parser-2.2.3.tgz#f9437788606c3c9acee16ffe8d8b16297f27bb90"
  integrity sha1-+UN3iGBsPJrO4W/+jYsWKX8nu5A=
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.2:
  version "6.0.11"
  resolved "https://registry.npm.alibaba-inc.com/postcss-selector-parser/download/postcss-selector-parser-6.0.11.tgz#2e41dc39b7ad74046e1615185185cd0b17d0c8dc"
  integrity sha512-zbARubNdogI9j7WY4nQJBiNqQf3sLS3wCP4WfOidu+p28LofJqDH1tcXypGrcmMHhDk2t9wGhCsYe/+szLTy1g==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-simple-vars@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-simple-vars/download/postcss-simple-vars-4.1.0.tgz#043248cfef8d3f51b3486a28c09f8375dbf1b2f9"
  integrity sha1-BDJIz++NP1GzSGoowJ+Dddvxsvk=
  dependencies:
    postcss "^6.0.9"

postcss-svgo@^2.1.1:
  version "2.1.6"
  resolved "https://registry.npm.alibaba-inc.com/postcss-svgo/download/postcss-svgo-2.1.6.tgz#b6df18aa613b666e133f08adb5219c2684ac108d"
  integrity sha1-tt8YqmE7Zm4TPwittSGcJoSsEI0=
  dependencies:
    is-svg "^2.0.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"
    svgo "^0.7.0"

postcss-unique-selectors@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/postcss-unique-selectors/download/postcss-unique-selectors-2.0.2.tgz#981d57d29ddcb33e7b1dfe1fd43b8649f933ca1d"
  integrity sha1-mB1X0p3csz57Hf4f1DuGSfkzyh0=
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss-url-mapper@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-url-mapper/download/postcss-url-mapper-1.2.0.tgz#223f311ec52989d5074101f1576f66975697c27e"
  integrity sha1-Ij8xHsUpidUHQQHxV29ml1aXwn4=
  dependencies:
    postcss "^6.0.13"

postcss-value-parser@^3.0.1, postcss-value-parser@^3.0.2, postcss-value-parser@^3.1.1, postcss-value-parser@^3.1.2, postcss-value-parser@^3.2.3, postcss-value-parser@^3.3.0, postcss-value-parser@^3.3.1:
  version "3.3.1"
  resolved "https://registry.npm.alibaba-inc.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz#9ff822547e2893213cf1c30efa51ac5fd1ba8281"
  integrity sha1-n/giVH4okyE88cMO+lGsX9G6goE=

postcss-values-parser@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-values-parser/download/postcss-values-parser-1.5.0.tgz#5d9fa63e2bcb0179ce48f3235303765eb89f3047"
  integrity sha1-XZ+mPivLAXnOSPMjUwN2XrifMEc=
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-viewport-units@^0.1.4:
  version "0.1.6"
  resolved "https://registry.npm.alibaba-inc.com/postcss-viewport-units/download/postcss-viewport-units-0.1.6.tgz#c1a019f0a0f677296ed53b0dffd08d6d9f4f08e8"
  integrity sha1-waAZ8KD2dylu1TsN/9CNbZ9PCOg=
  dependencies:
    postcss "^5.2.8"

postcss-zindex@^2.0.1:
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/postcss-zindex/download/postcss-zindex-2.2.0.tgz#d2109ddc055b91af67fc4cb3b025946639d2af22"
  integrity sha1-0hCd3AVbka9n/EyzsCWUZjnSryI=
  dependencies:
    has "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss@^5.0.10, postcss@^5.0.11, postcss@^5.0.12, postcss@^5.0.13, postcss@^5.0.14, postcss@^5.0.16, postcss@^5.0.2, postcss@^5.0.4, postcss@^5.0.5, postcss@^5.0.8, postcss@^5.2.10, postcss@^5.2.16, postcss@^5.2.8:
  version "5.2.18"
  resolved "https://registry.npm.alibaba-inc.com/postcss/download/postcss-5.2.18.tgz#badfa1497d46244f6390f58b319830d9107853c5"
  integrity sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U=
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^6.0, postcss@^6.0.0, postcss@^6.0.1, postcss@^6.0.11, postcss@^6.0.13, postcss@^6.0.14, postcss@^6.0.17, postcss@^6.0.18, postcss@^6.0.22, postcss@^6.0.23, postcss@^6.0.5, postcss@^6.0.6, postcss@^6.0.9:
  version "6.0.23"
  resolved "https://registry.npm.alibaba-inc.com/postcss/download/postcss-6.0.23.tgz#61c82cc328ac60e677645f979054eb98bc0e3324"
  integrity sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

postcss@^7.0.2, postcss@^7.0.36:
  version "7.0.39"
  resolved "https://registry.npm.alibaba-inc.com/postcss/download/postcss-7.0.39.tgz#9624375d965630e2e1f2c02a935c82a59cb48309"
  integrity sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/prelude-ls/download/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/prepend-http/download/prepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

preserve@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/preserve/download/preserve-0.2.0.tgz#815ed1f6ebc65926f865b310c0713bcb3315ce4b"
  integrity sha1-gV7R9uvGWSb4ZbMQwHE7yzMVzks=

"prettier@^1.18.2 || ^2.0.0":
  version "2.8.0"
  resolved "https://registry.npm.alibaba-inc.com/prettier/download/prettier-2.8.0.tgz#c7df58393c9ba77d6fba3921ae01faf994fb9dc9"
  integrity sha512-9Lmg8hTFZKG0Asr/kW9Bp8tJjRVluO8EJQVfY2T7FMw9T5jy4I/Uvx0Rca/XWf50QQ1/SS48+6IJWnrb+2yemA==

pretty-error@^2.0.2:
  version "2.1.2"
  resolved "https://registry.npm.alibaba-inc.com/pretty-error/download/pretty-error-2.1.2.tgz#be89f82d81b1c86ec8fdfbc385045882727f93b6"
  integrity sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y=
  dependencies:
    lodash "^4.17.20"
    renderkid "^2.0.4"

private@^0.1.6, private@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npm.alibaba-inc.com/private/download/private-0.1.8.tgz#2381edb3689f7a53d653190060fcf822d2f368ff"
  integrity sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "https://registry.npm.alibaba-inc.com/process/download/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress-bar-webpack-plugin@^1.10.0:
  version "1.12.1"
  resolved "https://registry.npm.alibaba-inc.com/progress-bar-webpack-plugin/download/progress-bar-webpack-plugin-1.12.1.tgz#bbf3b1137a4ba2474eeb111377d6c1a580c57dd1"
  integrity sha1-u/OxE3pLokdO6xETd9bBpYDFfdE=
  dependencies:
    chalk "^1.1.1"
    object.assign "^4.0.1"
    progress "^1.1.8"

progress@^1.1.8:
  version "1.1.8"
  resolved "https://registry.npm.alibaba-inc.com/progress/download/progress-1.1.8.tgz#e260c78f6161cdd9b0e56cc3e0a85de17c7a57be"
  integrity sha1-4mDHj2Fhzdmw5WzD4Khd4Xx6V74=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npm.alibaba-inc.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/promise-inflight/download/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

promise@^7.1.1:
  version "7.3.1"
  resolved "https://registry.npm.alibaba-inc.com/promise/download/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=
  dependencies:
    asap "~2.0.3"

prop-types@15.x, prop-types@^15.5.10, prop-types@^15.5.4, prop-types@^15.5.6, prop-types@^15.5.7, prop-types@^15.5.8, prop-types@^15.6.0, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://registry.npm.alibaba-inc.com/prop-types/download/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://registry.npm.alibaba-inc.com/proxy-addr/download/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/prr/download/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.9.0"
  resolved "https://registry.npm.alibaba-inc.com/psl/download/psl-1.9.0.tgz#d0df2a137f00794565fcaf3b2c00cd09f8d5a5a7"
  integrity sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npm.alibaba-inc.com/public-encrypt/download/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/pump/download/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "https://registry.npm.alibaba-inc.com/pumpify/download/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.npm.alibaba-inc.com/punycode/download/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^1.2.4:
  version "1.4.1"
  resolved "https://registry.npm.alibaba-inc.com/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

q@^1.1.2:
  version "1.5.1"
  resolved "https://registry.npm.alibaba-inc.com/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qrcode@^1.4.2:
  version "1.5.1"
  resolved "https://registry.npm.alibaba-inc.com/qrcode/download/qrcode-1.5.1.tgz#0103f97317409f7bc91772ef30793a54cd59f0cb"
  integrity sha512-nS8NJ1Z3md8uTjKtP+SGGhfqmTCs5flU/xR623oI0JX+Wepz9R8UrRVCTBTJm3qGw3rH6jJ6MUHjkDx15cxSSg==
  dependencies:
    dijkstrajs "^1.0.1"
    encode-utf8 "^1.0.3"
    pngjs "^5.0.0"
    yargs "^15.3.1"

qs@6.11.0, qs@^6.8.0:
  version "6.11.0"
  resolved "https://registry.npm.alibaba-inc.com/qs/download/qs-6.11.0.tgz#fd0d963446f7a65e1367e01abd85429453f0c37a"
  integrity sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==
  dependencies:
    side-channel "^1.0.4"

qs@~6.5.2:
  version "6.5.3"
  resolved "https://registry.npm.alibaba-inc.com/qs/download/qs-6.5.3.tgz#3aeeffc91967ef6e35c0e488ef46fb296ab76aad"
  integrity sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==

query-string@^4.1.0:
  version "4.3.4"
  resolved "https://registry.npm.alibaba-inc.com/query-string/download/query-string-4.3.4.tgz#bbb693b9ca915c232515b228b1a02b609043dbeb"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npm.alibaba-inc.com/querystring-es3/download/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/querystring/download/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/querystringify/download/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

raf@^3.1.0, raf@^3.3.2, raf@^3.4.0:
  version "3.4.1"
  resolved "https://registry.npm.alibaba-inc.com/raf/download/raf-3.4.1.tgz#0742e99a4a6552f445d73e3ee0328af0ff1ede39"
  integrity sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=
  dependencies:
    performance-now "^2.1.0"

randomatic@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npm.alibaba-inc.com/randomatic/download/randomatic-3.1.1.tgz#b776efc59375984e36c537b2f51a1f0aff0da1ed"
  integrity sha1-t3bvxZN1mE42xTey9RofCv8Noe0=
  dependencies:
    is-number "^4.0.0"
    kind-of "^6.0.0"
    math-random "^1.0.1"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/randomfill/download/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.alibaba-inc.com/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.5.1:
  version "2.5.1"
  resolved "https://registry.npm.alibaba-inc.com/raw-body/download/raw-body-2.5.1.tgz#fe1b1628b181b700215e5fd42389f98b71392857"
  integrity sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rc-align@^2.4.0:
  version "2.4.5"
  resolved "https://registry.npm.alibaba-inc.com/rc-align/download/rc-align-2.4.5.tgz#c941a586f59d1017f23a428f0b468663fb7102ab"
  integrity sha1-yUGlhvWdEBfyOkKPC0aGY/txAqs=
  dependencies:
    babel-runtime "^6.26.0"
    dom-align "^1.7.0"
    prop-types "^15.5.8"
    rc-util "^4.0.4"

rc-animate@2.x, rc-animate@^2.4.4:
  version "2.11.1"
  resolved "https://registry.npm.alibaba-inc.com/rc-animate/download/rc-animate-2.11.1.tgz#2666eeb6f1f2a495a13b2af09e236712278fdb2c"
  integrity sha1-JmbutvHypJWhOyrwniNnEieP2yw=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.6"
    css-animation "^1.3.2"
    prop-types "15.x"
    raf "^3.4.0"
    rc-util "^4.15.3"
    react-lifecycles-compat "^3.0.4"

rc-checkbox@~2.0.0:
  version "2.0.3"
  resolved "https://registry.npm.alibaba-inc.com/rc-checkbox/download/rc-checkbox-2.0.3.tgz#436a9d508948e224980f0535ea738b48177a8f25"
  integrity sha1-Q2qdUIlI4iSYDwU16nOLSBd6jyU=
  dependencies:
    babel-runtime "^6.23.0"
    classnames "2.x"
    prop-types "15.x"
    rc-util "^4.0.4"

rc-collapse@~1.9.1:
  version "1.9.3"
  resolved "https://registry.npm.alibaba-inc.com/rc-collapse/download/rc-collapse-1.9.3.tgz#d9741db06a823353e1fd1aec3ba4c0f9d8af4b26"
  integrity sha1-2XQdsGqCM1Ph/RrsO6TA+divSyY=
  dependencies:
    classnames "2.x"
    css-animation "1.x"
    prop-types "^15.5.6"
    rc-animate "2.x"

rc-gesture@~0.0.18, rc-gesture@~0.0.22:
  version "0.0.22"
  resolved "https://registry.npm.alibaba-inc.com/rc-gesture/download/rc-gesture-0.0.22.tgz#fbcbdd5b46387a978b3ede48b42748e8ff77dddd"
  integrity sha1-+8vdW0Y4epeLPt5ItCdI6P933d0=
  dependencies:
    babel-runtime "6.x"

rc-slider@~8.2.0:
  version "8.2.0"
  resolved "https://registry.npm.alibaba-inc.com/rc-slider/download/rc-slider-8.2.0.tgz#ae37d17144cad60e1da6eac0ee4ffcfea0b0a6e8"
  integrity sha1-rjfRcUTK1g4dpurA7k/8/qCwpug=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"
    prop-types "^15.5.4"
    rc-tooltip "^3.4.2"
    rc-util "^4.0.4"
    shallowequal "^1.0.1"
    warning "^3.0.0"

rc-swipeout@~2.0.0:
  version "2.0.11"
  resolved "https://registry.npm.alibaba-inc.com/rc-swipeout/download/rc-swipeout-2.0.11.tgz#dfad9c7b38a15ea0376e39cb3356e36fed7a4155"
  integrity sha1-362cezihXqA3bjnLM1bjb+16QVU=
  dependencies:
    babel-runtime "6.x"
    classnames "2.x"
    rc-gesture "~0.0.22"
    react-native-swipeout "^2.2.2"

rc-tooltip@^3.4.2:
  version "3.7.3"
  resolved "https://registry.npm.alibaba-inc.com/rc-tooltip/download/rc-tooltip-3.7.3.tgz#280aec6afcaa44e8dff0480fbaff9e87fc00aecc"
  integrity sha1-KArsavyqROjf8EgPuv+eh/wArsw=
  dependencies:
    babel-runtime "6.x"
    prop-types "^15.5.8"
    rc-trigger "^2.2.2"

rc-trigger@^2.2.2:
  version "2.6.5"
  resolved "https://registry.npm.alibaba-inc.com/rc-trigger/download/rc-trigger-2.6.5.tgz#140a857cf28bd0fa01b9aecb1e26a50a700e9885"
  integrity sha1-FAqFfPKL0PoBua7LHialCnAOmIU=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.6"
    prop-types "15.x"
    rc-align "^2.4.0"
    rc-animate "2.x"
    rc-util "^4.4.0"
    react-lifecycles-compat "^3.0.4"

rc-util@4.x, rc-util@^4.0.4, rc-util@^4.15.3, rc-util@^4.4.0:
  version "4.21.1"
  resolved "https://registry.npm.alibaba-inc.com/rc-util/download/rc-util-4.21.1.tgz#88602d0c3185020aa1053d9a1e70eac161becb05"
  integrity sha1-iGAtDDGFAgqhBT2aHnDqwWG+ywU=
  dependencies:
    add-dom-event-listener "^1.1.0"
    prop-types "^15.5.10"
    react-is "^16.12.0"
    react-lifecycles-compat "^3.0.4"
    shallowequal "^1.1.0"

react-dom@^16.13.1, react-dom@^16.2.0:
  version "16.14.0"
  resolved "https://registry.npm.alibaba-inc.com/react-dom/download/react-dom-16.14.0.tgz#7ad838ec29a777fb3c75c3a190f661cf92ab8b89"
  integrity sha1-etg47Cmnd/s8dcOhkPZhz5Kri4k=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    scheduler "^0.19.1"

react-error-boundary@^2.3.0:
  version "2.3.2"
  resolved "https://registry.npm.alibaba-inc.com/react-error-boundary/download/react-error-boundary-2.3.2.tgz#4d629cf958202da722e49f624bfb5cf9710b52c6"
  integrity sha1-TWKc+VggLaci5J9iS/tc+XELUsY=
  dependencies:
    "@babel/runtime" "^7.11.2"

react-imported-component@^6.0.5:
  version "6.5.3"
  resolved "https://registry.npm.alibaba-inc.com/react-imported-component/download/react-imported-component-6.5.3.tgz#2c2404fb7374823da306a1bba4c7287895b6a6ba"
  integrity sha512-ucanOHvUc2Nbme2ff24Q6qqlHui2YnGCtlZ2fyyX1yU2IXK+hJBHXg0auovYfC9Ds3C1KhmvYEmWbC6EpEno0Q==
  dependencies:
    babel-plugin-macros "^2.6.1"
    crc-32 "^1.2.0"
    detect-node-es "^1.0.0"
    scan-directory "^2.0.0"
    tslib "^2.0.0"

react-is@^16.12.0, react-is@^16.13.1, react-is@^16.6.0, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://registry.npm.alibaba-inc.com/react-is/download/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-lifecycles-compat@^3.0.2, react-lifecycles-compat@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.alibaba-inc.com/react-lifecycles-compat/download/react-lifecycles-compat-3.0.4.tgz#4f1a273afdfc8f3488a8c516bfda78f872352362"
  integrity sha1-TxonOv38jzSIqMUWv9p4+HI1I2I=

react-loading@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npm.alibaba-inc.com/react-loading/download/react-loading-2.0.3.tgz#e8138fb0c3e4674e481b320802ac7048ae14ffb9"
  integrity sha1-6BOPsMPkZ05IGzIIAqxwSK4U/7k=

react-native-swipeout@^2.2.2:
  version "2.3.6"
  resolved "https://registry.npm.alibaba-inc.com/react-native-swipeout/download/react-native-swipeout-2.3.6.tgz#47dac8a835825cf3f2eef9e495574a3d9ab6d3fa"
  integrity sha1-R9rIqDWCXPPy7vnklVdKPZq20/o=
  dependencies:
    create-react-class "^15.6.0"
    prop-types "^15.5.10"
    react-tween-state "^0.1.5"

react-router-dom@^5.0.1, react-router-dom@^5.1.2:
  version "5.3.4"
  resolved "https://registry.npm.alibaba-inc.com/react-router-dom/download/react-router-dom-5.3.4.tgz#2ed62ffd88cae6db134445f4a0c0ae8b91d2e5e6"
  integrity sha512-m4EqFMHv/Ih4kpcBCONHbkT68KoAeHN4p3lAGoNryfHi0dMy0kCzEZakiKRsvg5wHZ/JLrLW8o8KomWiz/qbYQ==
  dependencies:
    "@babel/runtime" "^7.12.13"
    history "^4.9.0"
    loose-envify "^1.3.1"
    prop-types "^15.6.2"
    react-router "5.3.4"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"

react-router@5.3.4, react-router@^5.0.1, react-router@^5.1.2:
  version "5.3.4"
  resolved "https://registry.npm.alibaba-inc.com/react-router/download/react-router-5.3.4.tgz#8ca252d70fcc37841e31473c7a151cf777887bb5"
  integrity sha512-Ys9K+ppnJah3QuaRiLxk+jDWOR1MekYQrlytiXxC1RyfbdsZkS5pvKAzCCr031xHixZwpnsYNT5xysdFHQaYsA==
  dependencies:
    "@babel/runtime" "^7.12.13"
    history "^4.9.0"
    hoist-non-react-statics "^3.1.0"
    loose-envify "^1.3.1"
    path-to-regexp "^1.7.0"
    prop-types "^15.6.2"
    react-is "^16.6.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"

react-tiny-virtual-list@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.alibaba-inc.com/react-tiny-virtual-list/download/react-tiny-virtual-list-2.2.0.tgz#eafb6fcf764e4ed41150ff9752cdaad8b35edf4a"
  integrity sha1-6vtvz3ZOTtQRUP+XUs2q2LNe30o=
  dependencies:
    prop-types "^15.5.7"

react-transition-group@^2.2.1:
  version "2.9.0"
  resolved "https://registry.npm.alibaba-inc.com/react-transition-group/download/react-transition-group-2.9.0.tgz#df9cdb025796211151a436c69a8f3b97b5b07c8d"
  integrity sha1-35zbAleWIRFRpDbGmo87l7WwfI0=
  dependencies:
    dom-helpers "^3.4.0"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"
    react-lifecycles-compat "^3.0.4"

react-tween-state@^0.1.5:
  version "0.1.5"
  resolved "https://registry.npm.alibaba-inc.com/react-tween-state/download/react-tween-state-0.1.5.tgz#e98b066551efb93cb92dd1be14995c2e3deae339"
  integrity sha1-6YsGZVHvuTy5LdG+FJlcLj3q4zk=
  dependencies:
    raf "^3.1.0"
    tween-functions "^1.0.1"

react@^16.13.1, react@^16.2.0:
  version "16.14.0"
  resolved "https://registry.npm.alibaba-inc.com/react/download/react-16.14.0.tgz#94d776ddd0aaa37da3eda8fc5b6b18a4c9a3114d"
  integrity sha1-lNd23dCqo32j7aj8W2sYpMmjEU0=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/read-cache/download/read-cache-1.0.0.tgz#e664ef31161166c9751cdbe8dbcf86b5fb58f774"
  integrity sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=
  dependencies:
    pify "^2.3.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/read-pkg-up/download/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  integrity sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/read-pkg-up/download/read-pkg-up-2.0.0.tgz#6b72a8048984e0c41e79510fd5e9fa99b3b549be"
  integrity sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/read-pkg/download/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  integrity sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/read-pkg/download/read-pkg-2.0.0.tgz#8ef1c0623c6a6db0dc6713c4bfac46332b2368f8"
  integrity sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.6, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.7"
  resolved "https://registry.npm.alibaba-inc.com/readable-stream/download/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npm.alibaba-inc.com/readable-stream/download/readable-stream-3.6.0.tgz#337bbda3adc0706bd3e024426a286d4b4b2c9198"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^2.0.0, readdirp@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.alibaba-inc.com/readdirp/download/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npm.alibaba-inc.com/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

redent@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/redent/download/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  integrity sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

reduce-css-calc@^1.2.6, reduce-css-calc@^1.2.7:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/reduce-css-calc/download/reduce-css-calc-1.3.0.tgz#747c914e049614a4c9cfbba629871ad1d2927716"
  integrity sha1-dHyRTgSWFKTJz7umKYca0dKSdxY=
  dependencies:
    balanced-match "^0.4.2"
    math-expression-evaluator "^1.2.14"
    reduce-function-call "^1.0.1"

reduce-css-calc@^2.0.0:
  version "2.1.8"
  resolved "https://registry.npm.alibaba-inc.com/reduce-css-calc/download/reduce-css-calc-2.1.8.tgz#7ef8761a28d614980dc0c982f772c93f7a99de03"
  integrity sha1-fvh2GijWFJgNwMmC93LJP3qZ3gM=
  dependencies:
    css-unit-converter "^1.1.1"
    postcss-value-parser "^3.3.0"

reduce-function-call@^1.0.1, reduce-function-call@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/reduce-function-call/download/reduce-function-call-1.0.3.tgz#60350f7fb252c0a67eb10fd4694d16909971300f"
  integrity sha1-YDUPf7JSwKZ+sQ/UaU0WkJlxMA8=
  dependencies:
    balanced-match "^1.0.0"

regenerate@^1.2.1:
  version "1.4.2"
  resolved "https://registry.npm.alibaba-inc.com/regenerate/download/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.10.5:
  version "0.10.5"
  resolved "https://registry.npm.alibaba-inc.com/regenerator-runtime/download/regenerator-runtime-0.10.5.tgz#336c3efc1220adcedda2c9fab67b5a7955a33658"
  integrity sha1-M2w+/BIgrc7dosn6tntaeVWjNlg=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npm.alibaba-inc.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.10:
  version "0.13.11"
  resolved "https://registry.npm.alibaba-inc.com/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
  integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==

regenerator-transform@^0.10.0:
  version "0.10.1"
  resolved "https://registry.npm.alibaba-inc.com/regenerator-transform/download/regenerator-transform-0.10.1.tgz#1e4996837231da8b7f3cf4114d71b5691a0680dd"
  integrity sha1-HkmWg3Ix2ot/PPQRTXG1aRoGgN0=
  dependencies:
    babel-runtime "^6.18.0"
    babel-types "^6.19.0"
    private "^0.1.6"

regex-cache@^0.4.2:
  version "0.4.4"
  resolved "https://registry.npm.alibaba-inc.com/regex-cache/download/regex-cache-0.4.4.tgz#75bdc58a2a1496cec48a12835bc54c8d562336dd"
  integrity sha1-db3FiioUls7EihKDW8VMjVYjNt0=
  dependencies:
    is-equal-shallow "^0.1.3"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0, regexp.prototype.flags@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npm.alibaba-inc.com/regexp.prototype.flags/download/regexp.prototype.flags-1.4.3.tgz#87cab30f80f66660181a3bb7bf5981a872b367ac"
  integrity sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    functions-have-names "^1.2.2"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/regexpp/download/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/regexpu-core/download/regexpu-core-2.0.0.tgz#49d038837b8dcf8bfa5b9a42139938e6ea2ae240"
  integrity sha1-SdA4g3uNz4v6W5pCE5k45uoq4kA=
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/regjsgen/download/regjsgen-0.2.0.tgz#6c016adeac554f75823fe37ac05b92d5a4edb1f7"
  integrity sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "https://registry.npm.alibaba-inc.com/regjsparser/download/regjsparser-0.1.5.tgz#7ee8f84dc6fa792d3fd0ae228d24bd949ead205c"
  integrity sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=
  dependencies:
    jsesc "~0.5.0"

relateurl@0.2.x:
  version "0.2.7"
  resolved "https://registry.npm.alibaba-inc.com/relateurl/download/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^2.0.4:
  version "2.0.7"
  resolved "https://registry.npm.alibaba-inc.com/renderkid/download/renderkid-2.0.7.tgz#464f276a6bdcee606f4a15993f9b29fc74ca8609"
  integrity sha1-Rk8namvc7mBvShWZP5sp/HTKhgk=
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^3.0.1"

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "https://registry.npm.alibaba-inc.com/repeat-element/download/repeat-element-1.1.4.tgz#be681520847ab58c7568ac75fbfad28ed42d39e9"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.5.2, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.npm.alibaba-inc.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/repeating/download/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  integrity sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=
  dependencies:
    is-finite "^1.0.0"

request@^2.87.0, request@^2.88.0:
  version "2.88.2"
  resolved "https://registry.npm.alibaba-inc.com/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npm.alibaba-inc.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/resolve-cwd/download/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
  integrity sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=
  dependencies:
    resolve-from "^3.0.0"

resolve-dir@^1.0.0, resolve-dir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/resolve-dir/download/resolve-dir-1.0.1.tgz#79a40644c362be82f26effe739c9bb5382046f43"
  integrity sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=
  dependencies:
    expand-tilde "^2.0.0"
    global-modules "^1.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-pathname@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/resolve-pathname/download/resolve-pathname-3.0.0.tgz#99d02224d3cf263689becbb393bc560313025dcd"
  integrity sha1-mdAiJNPPJjaJvsuzk7xWAxMCXc0=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.alibaba-inc.com/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.1.7, resolve@^1.10.0, resolve@^1.12.0, resolve@^1.20.0, resolve@^1.22.0, resolve@^1.6.0, resolve@^1.8.1:
  version "1.22.1"
  resolved "https://registry.npm.alibaba-inc.com/resolve/download/resolve-1.22.1.tgz#27cb2ebb53f91abb49470a928bba7558066ac177"
  integrity sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.3:
  version "2.0.0-next.4"
  resolved "https://registry.npm.alibaba-inc.com/resolve/download/resolve-2.0.0-next.4.tgz#3d37a113d6429f496ec4752d2a2e58efb1fd4660"
  integrity sha512-iMDbmAWtfU+MHpxt/I5iWI7cY6YVEZUQ3MBgPQ++XD1PELuJHIl82xBmObyP2KyQmkNB2dsqF7seoQQiAn5yDQ==
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.npm.alibaba-inc.com/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npm.alibaba-inc.com/retry/download/retry-0.12.0.tgz#1b42a6266a21f07421d1b0b54b7dc167b01c013b"
  integrity sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=

rgb-hex@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/rgb-hex/download/rgb-hex-2.1.0.tgz#c773c5fe2268a25578d92539a82a7a5ce53beda6"
  integrity sha1-x3PF/iJoolV42SU5qCp6XOU77aY=

rgb@~0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.alibaba-inc.com/rgb/download/rgb-0.1.0.tgz#be27b291e8feffeac1bd99729721bfa40fc037b5"
  integrity sha1-vieykej+/+rBvZlylyG/pA/AN7U=

rimraf@2, rimraf@^2.5.4, rimraf@^2.6.1, rimraf@^2.6.3:
  version "2.7.1"
  resolved "https://registry.npm.alibaba-inc.com/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@2.6.3:
  version "2.6.3"
  resolved "https://registry.npm.alibaba-inc.com/rimraf/download/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/ripemd160/download/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

rmc-align@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/rmc-align/download/rmc-align-1.0.0.tgz#8d64ab484609a041ab424506012a15b7c5b933dd"
  integrity sha1-jWSrSEYJoEGrQkUGASoVt8W5M90=
  dependencies:
    babel-runtime "6.x"
    dom-align "1.x"
    rc-util "4.x"

rmc-calendar@^1.0.0:
  version "1.1.4"
  resolved "https://registry.npm.alibaba-inc.com/rmc-calendar/download/rmc-calendar-1.1.4.tgz#7db4990087877cd49a7772f4524d33b8016d3bd2"
  integrity sha1-fbSZAIeHfNSad3L0Uk0zuAFtO9I=
  dependencies:
    babel-runtime "^6.26.0"
    rc-animate "^2.4.4"
    rmc-date-picker "^6.0.8"

rmc-cascader@~5.0.0:
  version "5.0.3"
  resolved "https://registry.npm.alibaba-inc.com/rmc-cascader/download/rmc-cascader-5.0.3.tgz#c605b1eac6613e4c54aa6aed2cbae7f9c5a8c65f"
  integrity sha1-xgWx6sZhPkxUqmrtLLrn+cWoxl8=
  dependencies:
    array-tree-filter "2.1.x"
    babel-runtime "6.x"
    rmc-picker "~5.0.0"

rmc-date-picker@^6.0.8:
  version "6.0.10"
  resolved "https://registry.npm.alibaba-inc.com/rmc-date-picker/download/rmc-date-picker-6.0.10.tgz#34dc7dfd424248be2d43527421576247c31583f6"
  integrity sha1-NNx9/UJCSL4tQ1J0IVdiR8MVg/Y=
  dependencies:
    babel-runtime "6.x"
    rmc-picker "~5.0.0"

rmc-dialog@^1.0.1, rmc-dialog@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/rmc-dialog/download/rmc-dialog-1.1.1.tgz#1d7fbc6b2cad5b0b53fbab71fe29636d76f78217"
  integrity sha1-HX+8ayytWwtT+6tx/iljbXb3ghc=
  dependencies:
    babel-runtime "6.x"
    rc-animate "2.x"

rmc-drawer@^0.4.11:
  version "0.4.11"
  resolved "https://registry.npm.alibaba-inc.com/rmc-drawer/download/rmc-drawer-0.4.11.tgz#9a8c6125a4ccd37b916f32f7e8b477d11d413ee3"
  integrity sha1-moxhJaTM03uRbzL36LR30R1BPuM=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.4"
    prop-types "^15.5.10"

rmc-feedback@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/rmc-feedback/download/rmc-feedback-2.0.0.tgz#cbc6cb3ae63c7a635eef0e25e4fbaf5ac366eeaa"
  integrity sha1-y8bLOuY8emNe7w4l5PuvWsNm7qo=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"

rmc-input-number@^1.0.0:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/rmc-input-number/download/rmc-input-number-1.0.5.tgz#42e02a27b0c3c366be9ff0ce19d818b71e406f8f"
  integrity sha1-QuAqJ7DDw2a+n/DOGdgYtx5Ab48=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.0"
    rmc-feedback "^2.0.0"

rmc-list-view@^0.11.0:
  version "0.11.5"
  resolved "https://registry.npm.alibaba-inc.com/rmc-list-view/download/rmc-list-view-0.11.5.tgz#8e152a5dbec6aec45a8ccd1f33cb8ef140b93a1e"
  integrity sha1-jhUqXb7GrsRajM0fM8uO8UC5Oh4=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"
    fbjs "^0.8.3"
    prop-types "^15.5.8"
    warning "^3.0.0"
    zscroller "~0.4.0"

rmc-notification@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/rmc-notification/download/rmc-notification-1.0.0.tgz#1fcee98f99b9733f7ce63a91d7663a578743d075"
  integrity sha1-H87pj5m5cz985jqR12Y6V4dD0HU=
  dependencies:
    babel-runtime "6.x"
    classnames "2.x"
    prop-types "^15.5.8"
    rc-animate "2.x"
    rc-util "^4.0.4"

rmc-nuka-carousel@~3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/rmc-nuka-carousel/download/rmc-nuka-carousel-3.0.1.tgz#a2a997676b0f986354976dac39ec66d8701b4b71"
  integrity sha1-oqmXZ2sPmGNUl22sOexm2HAbS3E=
  dependencies:
    exenv "^1.2.0"
    raf "^3.3.2"

rmc-picker@~5.0.0:
  version "5.0.10"
  resolved "https://registry.npm.alibaba-inc.com/rmc-picker/download/rmc-picker-5.0.10.tgz#9ca0acf45ad2c8afe9015a103a898436d825e18f"
  integrity sha1-nKCs9FrSyK/pAVoQOomENtgl4Y8=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.6"
    rmc-dialog "^1.1.1"
    rmc-feedback "^2.0.0"

rmc-pull-to-refresh@~1.0.1:
  version "1.0.13"
  resolved "https://registry.npm.alibaba-inc.com/rmc-pull-to-refresh/download/rmc-pull-to-refresh-1.0.13.tgz#8fc1bd36c9b09fcd50e8d8632f556b6a8462ef1a"
  integrity sha1-j8G9Nsmwn81Q6NhjL1VraoRi7xo=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"

rmc-steps@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/rmc-steps/download/rmc-steps-1.0.1.tgz#b63a0f1ddd2826d03b1bcec85963077255e8f798"
  integrity sha1-tjoPHd0oJtA7G87IWWMHclXo95g=
  dependencies:
    babel-runtime "^6.23.0"
    classnames "^2.2.3"

rmc-tabs@~1.2.0:
  version "1.2.29"
  resolved "https://registry.npm.alibaba-inc.com/rmc-tabs/download/rmc-tabs-1.2.29.tgz#dd2191525debbf8521e85aeb6d97670f652e4c83"
  integrity sha1-3SGRUl3rv4Uh6FrrbZdnD2UuTIM=
  dependencies:
    babel-runtime "6.x"
    rc-gesture "~0.0.18"

rmc-tooltip@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/rmc-tooltip/download/rmc-tooltip-1.0.1.tgz#5af16a3e8f764fa26d2b11932975bd88b1d848d2"
  integrity sha1-WvFqPo92T6JtKxGTKXW9iLHYSNI=
  dependencies:
    babel-runtime "6.x"
    rmc-trigger "1.x"

rmc-trigger@1.x:
  version "1.0.12"
  resolved "https://registry.npm.alibaba-inc.com/rmc-trigger/download/rmc-trigger-1.0.12.tgz#34df10a16f1fc8f9e8b14d13d58cabe294ab7488"
  integrity sha1-NN8QoW8fyPnosU0T1Yyr4pSrdIg=
  dependencies:
    babel-runtime "6.x"
    rc-animate "2.x"
    rc-util "4.x"
    rmc-align "~1.0.0"

run-async@^2.2.0:
  version "2.4.1"
  resolved "https://registry.npm.alibaba-inc.com/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/run-queue/download/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rxjs@^6.4.0:
  version "6.6.7"
  resolved "https://registry.npm.alibaba-inc.com/rxjs/download/rxjs-6.6.7.tgz#90ac018acabf491bf65044235d5863c4dab804c9"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npm.alibaba-inc.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.2.1, safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npm.alibaba-inc.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex-test@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/safe-regex-test/download/safe-regex-test-1.0.0.tgz#793b874d524eb3640d1873aad03596db2d4f2295"
  integrity sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    is-regex "^1.1.4"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.npm.alibaba-inc.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass-graph@2.2.5:
  version "2.2.5"
  resolved "https://registry.npm.alibaba-inc.com/sass-graph/download/sass-graph-2.2.5.tgz#a981c87446b8319d96dce0671e487879bd24c2e8"
  integrity sha1-qYHIdEa4MZ2W3OBnHkh4eb0kwug=
  dependencies:
    glob "^7.0.0"
    lodash "^4.0.0"
    scss-tokenizer "^0.2.3"
    yargs "^13.3.2"

sass-loader@^6.0.6:
  version "6.0.7"
  resolved "https://registry.npm.alibaba-inc.com/sass-loader/download/sass-loader-6.0.7.tgz#dd2fdb3e7eeff4a53f35ba6ac408715488353d00"
  integrity sha1-3S/bPn7v9KU/NbpqxAhxVIg1PQA=
  dependencies:
    clone-deep "^2.0.1"
    loader-utils "^1.0.1"
    lodash.tail "^4.1.1"
    neo-async "^2.5.0"
    pify "^3.0.0"

sax@~1.2.1:
  version "1.2.4"
  resolved "https://registry.npm.alibaba-inc.com/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

scan-directory@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/scan-directory/download/scan-directory-2.0.0.tgz#99fcb923ca26b8e2541cc3afb34d50cf494ea65f"
  integrity sha1-mfy5I8omuOJUHMOvs01Qz0lOpl8=

scheduler@^0.19.1:
  version "0.19.1"
  resolved "https://registry.npm.alibaba-inc.com/scheduler/download/scheduler-0.19.1.tgz#4f3e2ed2c1a7d65681f4c854fa8c5a1ccb40f196"
  integrity sha1-Tz4u0sGn1laB9MhU+oxaHMtA8ZY=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

schema-utils@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.alibaba-inc.com/schema-utils/download/schema-utils-0.3.0.tgz#f5877222ce3e931edae039f17eb3716e7137f8cf"
  integrity sha1-9YdyIs4+kx7a4DnxfrNxbnE3+M8=
  dependencies:
    ajv "^5.0.0"

schema-utils@^0.4.0, schema-utils@^0.4.5:
  version "0.4.7"
  resolved "https://registry.npm.alibaba-inc.com/schema-utils/download/schema-utils-0.4.7.tgz#ba74f597d2be2ea880131746ee17d0a093c68187"
  integrity sha1-unT1l9K+LqiAExdG7hfQoJPGgYc=
  dependencies:
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/schema-utils/download/schema-utils-1.0.0.tgz#0b79a93204d7b600d4b2850d1f66c2a34951c770"
  integrity sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

scss-tokenizer@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npm.alibaba-inc.com/scss-tokenizer/download/scss-tokenizer-0.2.3.tgz#8eb06db9a9723333824d3f5530641149847ce5d1"
  integrity sha1-jrBtualyMzOCTT9VMGQRSYR85dE=
  dependencies:
    js-base64 "^2.1.8"
    source-map "^0.4.2"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/select-hose/download/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^1.10.8:
  version "1.10.14"
  resolved "https://registry.npm.alibaba-inc.com/selfsigned/download/selfsigned-1.10.14.tgz#ee51d84d9dcecc61e07e4aba34f229ab525c1574"
  integrity sha512-lkjaiAye+wBZDCBsu5BGi0XiLRxeUlsGod5ZP924CRSEoGuZAw/f7y9RKu28rwTfiHVhdavhB0qH0INV6P1lEA==
  dependencies:
    node-forge "^0.10.0"

"semver@2 || 3 || 4 || 5", semver@^5.3.0, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0:
  version "5.7.1"
  resolved "https://registry.npm.alibaba-inc.com/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npm.alibaba-inc.com/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@~5.3.0:
  version "5.3.0"
  resolved "https://registry.npm.alibaba-inc.com/semver/download/semver-5.3.0.tgz#9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f"
  integrity sha1-myzl094C0XxgEq0yaqa00M9U+U8=

send@0.18.0:
  version "0.18.0"
  resolved "https://registry.npm.alibaba-inc.com/send/download/send-0.18.0.tgz#670167cc654b05f5aa4a767f9113bb371bc706be"
  integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz#b525e1238489a5ecfc42afacc3fe99e666f4b1aa"
  integrity sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://registry.npm.alibaba-inc.com/serve-index/download/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://registry.npm.alibaba-inc.com/serve-static/download/serve-static-1.15.0.tgz#faaef08cffe0a1a62f60cad0c4e513cff0ac9540"
  integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/set-value/download/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4, setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.alibaba-inc.com/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://registry.npm.alibaba-inc.com/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/shallow-clone/download/shallow-clone-1.0.0.tgz#4480cd06e882ef68b2ad88a3ea54832e2c48b571"
  integrity sha1-RIDNBuiC72iyrYij6lSDLixItXE=
  dependencies:
    is-extendable "^0.1.1"
    kind-of "^5.0.0"
    mixin-object "^2.0.1"

shallow-element-equals@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/shallow-element-equals/download/shallow-element-equals-1.0.1.tgz#50739b7d94ad7567a134173d3f4422387ed57ce6"
  integrity sha1-UHObfZStdWehNBc9P0QiOH7VfOY=
  dependencies:
    style-equal "^1.0.0"

shallowequal@^1.0.1, shallowequal@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/shallowequal/download/shallowequal-1.1.0.tgz#188d521de95b9087404fd4dcb68b13df0ae4e7f8"
  integrity sha1-GI1SHelbkIdAT9TctosT3wrk5/g=

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shortid@^2.2.15:
  version "2.2.16"
  resolved "https://registry.npm.alibaba-inc.com/shortid/download/shortid-2.2.16.tgz#b742b8f0cb96406fd391c76bfc18a67a57fe5608"
  integrity sha1-t0K48MuWQG/Tkcdr/Bimelf+Vgg=
  dependencies:
    nanoid "^2.1.0"

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/side-channel/download/side-channel-1.0.4.tgz#efce5c8fdc104ee751b25c58d4290011fa5ea2cf"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.7"
  resolved "https://registry.npm.alibaba-inc.com/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npm.alibaba-inc.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/slash/download/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.alibaba-inc.com/slice-ansi/download/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.npm.alibaba-inc.com/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@^1.5.0:
  version "1.6.1"
  resolved "https://registry.npm.alibaba-inc.com/sockjs-client/download/sockjs-client-1.6.1.tgz#350b8eda42d6d52ddc030c39943364c11dcad806"
  integrity sha512-2g0tjOR+fRs0amxENLi/q5TiJTqY+WXFOzb5UwXndlK6TO3U/mirZznpx6w34HVMoc3g7cY24yC/ZMIYnDlfkw==
  dependencies:
    debug "^3.2.7"
    eventsource "^2.0.2"
    faye-websocket "^0.11.4"
    inherits "^2.0.4"
    url-parse "^1.5.10"

sockjs@^0.3.21:
  version "0.3.24"
  resolved "https://registry.npm.alibaba-inc.com/sockjs/download/sockjs-0.3.24.tgz#c9bc8995f33a111bea0395ec30aa3206bdb5ccce"
  integrity sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/sort-keys/download/sort-keys-1.1.2.tgz#441b6d4d346798f1b4e49e8920adfba0e543f9ad"
  integrity sha1-RBttTTRnmPG05J6JIK37oOVD+a0=
  dependencies:
    is-plain-obj "^1.0.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/source-list-map/download/source-list-map-2.0.1.tgz#3993bd873bfc48479cca9ea3a547835c7c154b34"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://registry.npm.alibaba-inc.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.4.15:
  version "0.4.18"
  resolved "https://registry.npm.alibaba-inc.com/source-map-support/download/source-map-support-0.4.18.tgz#0286a6de8be42641338594e97ccea75f0a2c585f"
  integrity sha1-Aoam3ovkJkEzhZTpfM6nXwosWF8=
  dependencies:
    source-map "^0.5.6"

source-map-support@~0.5.12:
  version "0.5.21"
  resolved "https://registry.npm.alibaba-inc.com/source-map-support/download/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "https://registry.npm.alibaba-inc.com/source-map-url/download/source-map-url-0.4.1.tgz#0af66605a745a5a2f91cf1bbf8a7afbc283dec56"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.4.2:
  version "0.4.4"
  resolved "https://registry.npm.alibaba-inc.com/source-map/download/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  integrity sha1-66T12pwNyZneaAMti092FzZSA2s=
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.3, source-map@^0.5.6, source-map@^0.5.7:
  version "0.5.7"
  resolved "https://registry.npm.alibaba-inc.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npm.alibaba-inc.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npm.alibaba-inc.com/spdx-correct/download/spdx-correct-3.1.1.tgz#dece81ac9c1e6713e5f7d1b6f17d468fa53d89a9"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npm.alibaba-inc.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.12"
  resolved "https://registry.npm.alibaba-inc.com/spdx-license-ids/download/spdx-license-ids-3.0.12.tgz#69077835abe2710b65f03969898b6637b505a779"
  integrity sha512-rr+VVSXtRhO4OHbXUiAF7xW3Bo9DuuF6C5jH+q/x15j2jniycgKbxU09Hr0WqlSLUs4i4ltHGXqTe7VHclYWyA==

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/spdy-transport/download/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.alibaba-inc.com/spdy/download/spdy-4.0.2.tgz#b74f466203a3eda452c02492b91fb9e84a27677b"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.17.0"
  resolved "https://registry.npm.alibaba-inc.com/sshpk/download/sshpk-1.17.0.tgz#578082d92d4fe612b13007496e543fa0fbcbe4c5"
  integrity sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npm.alibaba-inc.com/ssri/download/ssri-6.0.2.tgz#157939134f20464e7301ddba3e90ffa8f7728ac5"
  integrity sha1-FXk5E08gRk5zAd26PpD/qPdyisU=
  dependencies:
    figgy-pudding "^3.5.1"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.alibaba-inc.com/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

statuses@2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

"statuses@>= 1.4.0 < 2":
  version "1.5.0"
  resolved "https://registry.npm.alibaba-inc.com/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stdout-stream@^1.4.0:
  version "1.4.1"
  resolved "https://registry.npm.alibaba-inc.com/stdout-stream/download/stdout-stream-1.4.1.tgz#5ac174cdd5cd726104aa0c0b2bd83815d8d535de"
  integrity sha1-WsF0zdXNcmEEqgwLK9g4FdjVNd4=
  dependencies:
    readable-stream "^2.0.1"

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/stream-browserify/download/stream-browserify-2.0.2.tgz#87521d38a44aa7ee91ce1cd2a47df0cb49dd660b"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "https://registry.npm.alibaba-inc.com/stream-each/download/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "https://registry.npm.alibaba-inc.com/stream-http/download/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/stream-shift/download/stream-shift-1.0.1.tgz#d7088281559ab2778424279b0877da3c392d5a3d"
  integrity sha1-1wiCgVWasneEJCebCHfaPDktWj0=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz#279b225df1d582b1f54e65addd4352e18faa0713"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

string-similarity@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/string-similarity/download/string-similarity-3.0.0.tgz#07b0bc69fae200ad88ceef4983878d03793847c7"
  integrity sha1-B7C8afriAK2Izu9Jg4eNA3k4R8c=

string-width@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.3"
  resolved "https://registry.npm.alibaba-inc.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.alibaba-inc.com/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string.prototype.matchall@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npm.alibaba-inc.com/string.prototype.matchall/download/string.prototype.matchall-4.0.8.tgz#3bf85722021816dcd1bf38bb714915887ca79fd3"
  integrity sha512-6zOCOcJ+RJAQshcTvXPHoxoQGONa3e/Lqx90wUA+wEzX78sg5Bo+1tQo4N0pohS0erG9qtCqJDjNCQBjeWVxyg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    get-intrinsic "^1.1.3"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    regexp.prototype.flags "^1.4.3"
    side-channel "^1.0.4"

string.prototype.trimend@^1.0.5:
  version "1.0.6"
  resolved "https://registry.npm.alibaba-inc.com/string.prototype.trimend/download/string.prototype.trimend-1.0.6.tgz#c4a27fa026d979d79c04f17397f250a462944533"
  integrity sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trimstart@^1.0.5:
  version "1.0.6"
  resolved "https://registry.npm.alibaba-inc.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.6.tgz#e90ab66aa8e4007d92ef591bbf3cd422c56bdcf4"
  integrity sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string_decoder@^1.0.0, string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npm.alibaba-inc.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.alibaba-inc.com/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.alibaba-inc.com/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npm.alibaba-inc.com/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npm.alibaba-inc.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/strip-bom/download/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/strip-indent/download/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  integrity sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=
  dependencies:
    get-stdin "^4.0.1"

strip-json-comments@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

style-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/style-equal/download/style-equal-1.0.0.tgz#98a1c5922226bfe13c196e73f1940e91b8e66595"
  integrity sha1-mKHFkiImv+E8GW5z8ZQOkbjmZZU=

style-loader@^0.13.2:
  version "0.13.2"
  resolved "https://registry.npm.alibaba-inc.com/style-loader/download/style-loader-0.13.2.tgz#74533384cf698c7104c7951150b49717adc2f3bb"
  integrity sha1-dFMzhM9pjHEEx5URULSXF63C87s=
  dependencies:
    loader-utils "^1.0.2"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npm.alibaba-inc.com/supports-color/download/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
  integrity sha1-ZawFBLOVQXHYpklGsq48u4pfVPY=
  dependencies:
    has-flag "^1.0.0"

supports-color@^5.3.0, supports-color@^5.4.0:
  version "5.5.0"
  resolved "https://registry.npm.alibaba-inc.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npm.alibaba-inc.com/supports-color/download/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svgo@0.7.x, svgo@^0.7.0:
  version "0.7.2"
  resolved "https://registry.npm.alibaba-inc.com/svgo/download/svgo-0.7.2.tgz#9f5772413952135c6fefbf40afe6a4faa88b4bb5"
  integrity sha1-n1dyQTlSE1xv779Ar+ak+qiLS7U=
  dependencies:
    coa "~1.0.1"
    colors "~1.1.2"
    csso "~2.3.1"
    js-yaml "~3.7.0"
    mkdirp "~0.5.1"
    sax "~1.2.1"
    whet.extend "~0.9.9"

table@^5.2.3:
  version "5.4.6"
  resolved "https://registry.npm.alibaba-inc.com/table/download/table-5.4.6.tgz#1292d19500ce3f86053b05f0e8e7e4a3bb21079e"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tapable@^1.0.0, tapable@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.alibaba-inc.com/tapable/download/tapable-1.1.3.tgz#a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

tar@^2.0.0:
  version "2.2.2"
  resolved "https://registry.npm.alibaba-inc.com/tar/download/tar-2.2.2.tgz#0ca8848562c7299b8b446ff6a4d60cdbb23edc40"
  integrity sha1-DKiEhWLHKZuLRG/2pNYM27I+3EA=
  dependencies:
    block-stream "*"
    fstream "^1.0.12"
    inherits "2"

terser-webpack-plugin@^1.4.3:
  version "1.4.5"
  resolved "https://registry.npm.alibaba-inc.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.5.tgz#a217aefaea330e734ffacb6120ec1fa312d6040b"
  integrity sha1-oheu+uozDnNP+sthIOwfoxLWBAs=
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^4.0.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser@^4.1.2:
  version "4.8.1"
  resolved "https://registry.npm.alibaba-inc.com/terser/download/terser-4.8.1.tgz#a00e5634562de2239fd404c649051bf6fc21144f"
  integrity sha512-4GnLC0x667eJG0ewJTa6z/yXrbLGv80D9Ru6HIpCQmO+Q4PfEtBFi0ObSckqwL6VyQv/7ENJieXHo2ANmdQwgw==
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

through2@^2.0.0:
  version "2.0.5"
  resolved "https://registry.npm.alibaba-inc.com/through2/download/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.npm.alibaba-inc.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npm.alibaba-inc.com/thunky/download/thunky-1.1.0.tgz#5abaf714a9405db0504732bbccd2cedd9ef9537d"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

timers-browserify@^2.0.4:
  version "2.0.12"
  resolved "https://registry.npm.alibaba-inc.com/timers-browserify/download/timers-browserify-2.0.12.tgz#44a45c11fbf407f34f97bccd1577c652361b00ee"
  integrity sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=
  dependencies:
    setimmediate "^1.0.4"

tiny-invariant@^1.0.2:
  version "1.3.1"
  resolved "https://registry.npm.alibaba-inc.com/tiny-invariant/download/tiny-invariant-1.3.1.tgz#8560808c916ef02ecfd55e66090df23a4b7aa642"
  integrity sha512-AD5ih2NlSssTCwsMznbvwMZpJ1cbhkGd2uueNxzv2jDlEeZdU04JQfRnggJQ8DrcVBGjAsCKwFBbDlVNtEMlzw==

tiny-warning@^1.0.0:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/tiny-warning/download/tiny-warning-1.0.3.tgz#94a30db453df4c643d0fd566060d60a875d84754"
  integrity sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npm.alibaba-inc.com/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/to-fast-properties/download/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"
  integrity sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.alibaba-inc.com/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.alibaba-inc.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.alibaba-inc.com/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

toposort@^1.0.0:
  version "1.0.7"
  resolved "https://registry.npm.alibaba-inc.com/toposort/download/toposort-1.0.7.tgz#2e68442d9f64ec720b8cc89e6443ac6caa950029"
  integrity sha1-LmhELZ9k7HILjMieZEOsbKqVACk=

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://registry.npm.alibaba-inc.com/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/trim-newlines/download/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"
  integrity sha1-WIeWa7WCpFA6QetST301ARgVphM=

trim-right@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/trim-right/download/trim-right-1.0.1.tgz#cb2e1203067e0c8de1f614094b9fe45704ea6003"
  integrity sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=

"true-case-path@^1.0.2":
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/true-case-path/download/true-case-path-1.0.3.tgz#f813b5a8c86b40da59606722b144e3225799f47d"
  integrity sha1-+BO1qMhrQNpZYGcisUTjIleZ9H0=
  dependencies:
    glob "^7.1.2"

tsconfig-paths@^3.14.1:
  version "3.14.1"
  resolved "https://registry.npm.alibaba-inc.com/tsconfig-paths/download/tsconfig-paths-3.14.1.tgz#ba0734599e8ea36c862798e920bcf163277b137a"
  integrity sha512-fxDhWnFSLt3VuTwtvJt5fpwxBHg5AdKWMsgcPOOIilyjymcYVZoCQF8fvFRezCNfblEXmi+PcM1eYHeOAgXCOQ==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.10.0, tslib@^1.9.0:
  version "1.14.1"
  resolved "https://registry.npm.alibaba-inc.com/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.0:
  version "2.4.1"
  resolved "https://registry.npm.alibaba-inc.com/tslib/download/tslib-2.4.1.tgz#0d0bfbaac2880b91e22df0768e55be9753a5b17e"
  integrity sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "https://registry.npm.alibaba-inc.com/tty-browserify/download/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npm.alibaba-inc.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tween-functions@^1.0.1:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/tween-functions/download/tween-functions-1.2.0.tgz#1ae3a50e7c60bb3def774eac707acbca73bbc3ff"
  integrity sha1-GuOlDnxguz3vd06scHrLynO7w/8=

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.npm.alibaba-inc.com/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.npm.alibaba-inc.com/type-check/download/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npm.alibaba-inc.com/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typecast@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.alibaba-inc.com/typecast/download/typecast-0.0.1.tgz#fffb75dcb6bdf1def8e293b6b6e893d6c1ed19de"
  integrity sha1-//t13La98d744pO2tuiT1sHtGd4=

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npm.alibaba-inc.com/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

ua-parser-js@^0.7.30:
  version "0.7.32"
  resolved "https://registry.npm.alibaba-inc.com/ua-parser-js/download/ua-parser-js-0.7.32.tgz#cd8c639cdca949e30fa68c44b7813ef13e36d211"
  integrity sha512-f9BESNVhzlhEFf2CHMSj40NWOjYPl1YKYbrvIr/hFTDEmLq7SRbWvm7FcdcpCYT95zrOhC7gZSxjdnnTpBcwVw==

uglify-js@3.3.x:
  version "3.3.28"
  resolved "https://registry.npm.alibaba-inc.com/uglify-js/download/uglify-js-3.3.28.tgz#0efb9a13850e11303361c1051f64d2ec68d9be06"
  integrity sha1-DvuaE4UOETAzYcEFH2TS7GjZvgY=
  dependencies:
    commander "~2.15.0"
    source-map "~0.6.1"

uglify-js@3.4.x:
  version "3.4.10"
  resolved "https://registry.npm.alibaba-inc.com/uglify-js/download/uglify-js-3.4.10.tgz#9ad9563d8eb3acdfb8d38597d2af1d815f6a755f"
  integrity sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=
  dependencies:
    commander "~2.19.0"
    source-map "~0.6.1"

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/unbox-primitive/download/unbox-primitive-1.0.2.tgz#29032021057d5e6cdbd08c5129c226dff8ed6f9e"
  integrity sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

uniq@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/uniq/download/uniq-1.0.1.tgz#b31c5ae8254844a3a8281541ce2b04b865a734ff"
  integrity sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=

uniqs@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/uniqs/download/uniqs-2.0.0.tgz#ffede4b36b25290696e6e165d4a59edb998e6b02"
  integrity sha1-/+3ks2slKQaW5uFl1KWe25mOawI=

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/unique-filename/download/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npm.alibaba-inc.com/unique-slug/download/unique-slug-2.0.2.tgz#baabce91083fc64e945b0f3ad613e264f7cd4e6c"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

units-css@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.alibaba-inc.com/units-css/download/units-css-0.4.0.tgz#d6228653a51983d7c16ff28f8b9dc3b1ffed3a07"
  integrity sha1-1iKGU6UZg9fBb/KPi53Dsf/tOgc=
  dependencies:
    isnumeric "^0.2.0"
    viewport-dimensions "^0.2.0"

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npm.alibaba-inc.com/upath/download/upath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

update-browserslist-db@^1.0.9:
  version "1.0.10"
  resolved "https://registry.npm.alibaba-inc.com/update-browserslist-db/download/update-browserslist-db-1.0.10.tgz#0f54b876545726f17d00cd9a2561e6dade943ff3"
  integrity sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

upper-case@^1.1.1:
  version "1.1.3"
  resolved "https://registry.npm.alibaba-inc.com/upper-case/download/upper-case-1.1.3.tgz#f6b4501c2ec4cdd26ba78be7222961de77621598"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npm.alibaba-inc.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.alibaba-inc.com/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-join@^2.0.2:
  version "2.0.5"
  resolved "https://registry.npm.alibaba-inc.com/url-join/download/url-join-2.0.5.tgz#5af22f18c052a000a48d7b82c5e9c2e2feeda728"
  integrity sha1-WvIvGMBSoACkjXuCxenC4v7tpyg=

url-loader@^0.6.2:
  version "0.6.2"
  resolved "https://registry.npm.alibaba-inc.com/url-loader/download/url-loader-0.6.2.tgz#a007a7109620e9d988d14bce677a1decb9a993f7"
  integrity sha1-oAenEJYg6dmI0UvOZ3od7Lmpk/c=
  dependencies:
    loader-utils "^1.0.2"
    mime "^1.4.1"
    schema-utils "^0.3.0"

url-parse@^1.5.10:
  version "1.5.10"
  resolved "https://registry.npm.alibaba-inc.com/url-parse/download/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npm.alibaba-inc.com/url/download/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npm.alibaba-inc.com/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

user-home@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.alibaba-inc.com/user-home/download/user-home-1.1.1.tgz#2b5be23a32b63a7c9deb8d0f28d485724a3df190"
  integrity sha1-K1viOjK2Onyd640PKNSFcko98ZA=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.alibaba-inc.com/util.promisify/download/util.promisify-1.0.0.tgz#440f7165a459c9a16dc145eb8e72f35687097030"
  integrity sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=
  dependencies:
    define-properties "^1.1.2"
    object.getownpropertydescriptors "^2.0.3"

util@0.10.3:
  version "0.10.3"
  resolved "https://registry.npm.alibaba-inc.com/util/download/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

util@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npm.alibaba-inc.com/util/download/util-0.11.1.tgz#3236733720ec64bb27f6e26f421aaa2e1b588d61"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

utila@~0.4:
  version "0.4.0"
  resolved "https://registry.npm.alibaba-inc.com/utila/download/utila-0.4.0.tgz#8a16a05d445657a3aea5eecc5b12a4fa5379772c"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2:
  version "3.4.0"
  resolved "https://registry.npm.alibaba-inc.com/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.npm.alibaba-inc.com/uuid/download/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

v8-compile-cache@^2.1.1:
  version "2.3.0"
  resolved "https://registry.npm.alibaba-inc.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz#2de19618c66dc247dcfb6f99338035d8245a2cee"
  integrity sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=

v8flags@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.alibaba-inc.com/v8flags/download/v8flags-2.1.1.tgz#aab1a1fa30d45f88dd321148875ac02c0b55e5b4"
  integrity sha1-qrGh+jDUX4jdMhFIh1rALAtV5bQ=
  dependencies:
    user-home "^1.1.1"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.npm.alibaba-inc.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validate@^5.1.0:
  version "5.2.0"
  resolved "https://registry.npm.alibaba-inc.com/validate/download/validate-5.2.0.tgz#fe7355c1644092b67dd07731ad5716cb99f689a5"
  integrity sha512-pVADd6GfDT7bALYvvzhfHnfmxCDem2bG7lGY3mwHzY7ktMH/SaczoF+eKkGokQEdGKozkJvyuOiSfQEHXp4zIA==
  dependencies:
    "@eivifj/dot" "^1.0.1"
    component-type "1.2.1"
    typecast "0.0.1"

validator@^12.2.0:
  version "12.2.0"
  resolved "https://registry.npm.alibaba-inc.com/validator/download/validator-12.2.0.tgz#660d47e96267033fd070096c3b1a6f2db4380a0a"
  integrity sha1-Zg1H6WJnAz/QcAlsOxpvLbQ4Cgo=

value-equal@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.alibaba-inc.com/value-equal/download/value-equal-1.0.1.tgz#1e0b794c734c5c0cade179c437d356d931a34d6c"
  integrity sha1-Hgt5THNMXAyt4XnEN9NW2TGjTWw=

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vendors@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npm.alibaba-inc.com/vendors/download/vendors-1.0.4.tgz#e2b800a53e7a29b93506c3cf41100d16c4c4ad8e"
  integrity sha1-4rgApT56Kbk1BsPPQRANFsTErY4=

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.npm.alibaba-inc.com/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

viewport-dimensions@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.alibaba-inc.com/viewport-dimensions/download/viewport-dimensions-0.2.0.tgz#de740747db5387fd1725f5175e91bac76afdf36c"
  integrity sha1-3nQHR9tTh/0XJfUXXpG6x2r982w=

vm-browserify@^1.0.1:
  version "1.1.2"
  resolved "https://registry.npm.alibaba-inc.com/vm-browserify/download/vm-browserify-1.1.2.tgz#78641c488b8e6ca91a75f511e7a3b32a86e5dda0"
  integrity sha1-eGQcSIuObKkadfUR56OzKobl3aA=

vue-eslint-parser@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npm.alibaba-inc.com/vue-eslint-parser/download/vue-eslint-parser-2.0.3.tgz#c268c96c6d94cfe3d938a5f7593959b0ca3360d1"
  integrity sha1-wmjJbG2Uz+PZOKX3WTlZsMozYNE=
  dependencies:
    debug "^3.1.0"
    eslint-scope "^3.7.1"
    eslint-visitor-keys "^1.0.0"
    espree "^3.5.2"
    esquery "^1.0.0"
    lodash "^4.17.4"

vue-hot-reload-api@^2.3.0:
  version "2.3.4"
  resolved "https://registry.npm.alibaba-inc.com/vue-hot-reload-api/download/vue-hot-reload-api-2.3.4.tgz#532955cc1eb208a3d990b3a9f9a70574657e08f2"
  integrity sha1-UylVzB6yCKPZkLOp+acFdGV+CPI=

vue-loader@^15.4.1:
  version "15.10.1"
  resolved "https://registry.npm.alibaba-inc.com/vue-loader/download/vue-loader-15.10.1.tgz#c451c4cd05a911aae7b5dbbbc09fb913fb3cca18"
  integrity sha512-SaPHK1A01VrNthlix6h1hq4uJu7S/z0kdLUb6klubo738NeQoLbS6V9/d8Pv19tU0XdQKju3D1HSKuI8wJ5wMA==
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    vue-hot-reload-api "^2.3.0"
    vue-style-loader "^4.1.0"

vue-style-loader@^4.1.0, vue-style-loader@^4.1.2:
  version "4.1.3"
  resolved "https://registry.npm.alibaba-inc.com/vue-style-loader/download/vue-style-loader-4.1.3.tgz#6d55863a51fa757ab24e89d9371465072aa7bc35"
  integrity sha1-bVWGOlH6dXqyTonZNxRlByqnvDU=
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-compiler@^2.5.17:
  version "2.7.14"
  resolved "https://registry.npm.alibaba-inc.com/vue-template-compiler/download/vue-template-compiler-2.7.14.tgz#4545b7dfb88090744c1577ae5ac3f964e61634b1"
  integrity sha512-zyA5Y3ArvVG0NacJDkkzJuPQDF8RFeRlzV2vLeSnhSpieO6LK2OVbdLPi5MPPs09Ii+gMO8nY4S3iKQxBxDmWQ==
  dependencies:
    de-indent "^1.0.2"
    he "^1.2.0"

vue-template-es2015-compiler@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npm.alibaba-inc.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz#1ee3bc9a16ecbf5118be334bb15f9c46f82f5825"
  integrity sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=

warning@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.alibaba-inc.com/warning/download/warning-3.0.0.tgz#32e5377cb572de4ab04753bdf8821c01ed605b7c"
  integrity sha1-MuU3fLVy3kqwR1O9+IIcAe1gW3w=
  dependencies:
    loose-envify "^1.0.0"

watchpack-chokidar2@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.alibaba-inc.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz#38500072ee6ece66f3769936950ea1771be1c957"
  integrity sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.7.4:
  version "1.7.5"
  resolved "https://registry.npm.alibaba-inc.com/watchpack/download/watchpack-1.7.5.tgz#1267e6c55e0b9b5be44c2023aed5437a2c26c453"
  integrity sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.1"
    watchpack-chokidar2 "^2.0.1"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://registry.npm.alibaba-inc.com/wbuf/download/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

webpack-cli@^3.0.8:
  version "3.3.12"
  resolved "https://registry.npm.alibaba-inc.com/webpack-cli/download/webpack-cli-3.3.12.tgz#94e9ada081453cd0aa609c99e500012fd3ad2d4a"
  integrity sha1-lOmtoIFFPNCqYJyZ5QABL9OtLUo=
  dependencies:
    chalk "^2.4.2"
    cross-spawn "^6.0.5"
    enhanced-resolve "^4.1.1"
    findup-sync "^3.0.0"
    global-modules "^2.0.0"
    import-local "^2.0.0"
    interpret "^1.4.0"
    loader-utils "^1.4.0"
    supports-color "^6.1.0"
    v8-compile-cache "^2.1.1"
    yargs "^13.3.2"

webpack-dev-middleware@^3.1.3, webpack-dev-middleware@^3.7.2:
  version "3.7.3"
  resolved "https://registry.npm.alibaba-inc.com/webpack-dev-middleware/download/webpack-dev-middleware-3.7.3.tgz#0639372b143262e2b84ab95d3b91a7597061c2c5"
  integrity sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU=
  dependencies:
    memory-fs "^0.4.1"
    mime "^2.4.4"
    mkdirp "^0.5.1"
    range-parser "^1.2.1"
    webpack-log "^2.0.0"

webpack-dev-server@^3.1.4:
  version "3.11.3"
  resolved "https://registry.npm.alibaba-inc.com/webpack-dev-server/download/webpack-dev-server-3.11.3.tgz#8c86b9d2812bf135d3c9bce6f07b718e30f7c3d3"
  integrity sha1-jIa50oEr8TXTybzm8HtxjjD3w9M=
  dependencies:
    ansi-html-community "0.0.8"
    bonjour "^3.5.0"
    chokidar "^2.1.8"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    debug "^4.1.1"
    del "^4.1.1"
    express "^4.17.1"
    html-entities "^1.3.1"
    http-proxy-middleware "0.19.1"
    import-local "^2.0.0"
    internal-ip "^4.3.0"
    ip "^1.1.5"
    is-absolute-url "^3.0.3"
    killable "^1.0.1"
    loglevel "^1.6.8"
    opn "^5.5.0"
    p-retry "^3.0.1"
    portfinder "^1.0.26"
    schema-utils "^1.0.0"
    selfsigned "^1.10.8"
    semver "^6.3.0"
    serve-index "^1.9.1"
    sockjs "^0.3.21"
    sockjs-client "^1.5.0"
    spdy "^4.0.2"
    strip-ansi "^3.0.1"
    supports-color "^6.1.0"
    url "^0.11.0"
    webpack-dev-middleware "^3.7.2"
    webpack-log "^2.0.0"
    ws "^6.2.1"
    yargs "^13.3.2"

webpack-hot-middleware@^2.22.2:
  version "2.25.3"
  resolved "https://registry.npm.alibaba-inc.com/webpack-hot-middleware/download/webpack-hot-middleware-2.25.3.tgz#be343ce2848022cfd854dd82820cd730998c6794"
  integrity sha512-IK/0WAHs7MTu1tzLTjio73LjS3Ov+VvBKQmE8WPlJutgG5zT6Urgq/BbAdRrHTRpyzK0dvAvFh1Qg98akxgZpA==
  dependencies:
    ansi-html-community "0.0.8"
    html-entities "^2.1.0"
    strip-ansi "^6.0.0"

webpack-log@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/webpack-log/download/webpack-log-2.0.0.tgz#5b7928e0637593f119d32f6227c1e0ac31e1b47f"
  integrity sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=
  dependencies:
    ansi-colors "^3.0.0"
    uuid "^3.3.2"

webpack-sources@^1.1.0, webpack-sources@^1.4.0, webpack-sources@^1.4.1:
  version "1.4.3"
  resolved "https://registry.npm.alibaba-inc.com/webpack-sources/download/webpack-sources-1.4.3.tgz#eedd8ec0b928fbf1cbfe994e22d2d890f330a933"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack@^4.16.0:
  version "4.46.0"
  resolved "https://registry.npm.alibaba-inc.com/webpack/download/webpack-4.46.0.tgz#bf9b4404ea20a073605e0a011d188d77cb6ad542"
  integrity sha1-v5tEBOogoHNgXgoBHRiNd8tq1UI=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.5.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.7.4"
    webpack-sources "^1.4.1"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "https://registry.npm.alibaba-inc.com/websocket-driver/download/websocket-driver-0.7.4.tgz#89ad5295bbf64b480abcba31e4953aca706f5760"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.npm.alibaba-inc.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz#7f8473bc839dfd87608adb95d7eb075211578a42"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

whatwg-fetch@>=0.10.0:
  version "3.6.2"
  resolved "https://registry.npm.alibaba-inc.com/whatwg-fetch/download/whatwg-fetch-3.6.2.tgz#dced24f37f2624ed0281725d51d0e2e3fe677f8c"
  integrity sha1-3O0k838mJO0CgXJdUdDi4/5nf4w=

whet.extend@~0.9.9:
  version "0.9.9"
  resolved "https://registry.npm.alibaba-inc.com/whet.extend/download/whet.extend-0.9.9.tgz#f877d5bf648c97e5aa542fadc16d6a259b9c11a1"
  integrity sha1-+HfVv2SMl+WqVC+twW1qJZucEaE=

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz#13757bc89b209b049fe5d86430e21cf40a89a8e6"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.alibaba-inc.com/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@1, which@^1.2.14, which@^1.2.9, which@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npm.alibaba-inc.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.5"
  resolved "https://registry.npm.alibaba-inc.com/wide-align/download/wide-align-1.1.5.tgz#df1d4c206854369ecf3c9a4898f1b23fbd9d15d3"
  integrity sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://registry.npm.alibaba-inc.com/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npm.alibaba-inc.com/worker-farm/download/worker-farm-1.7.0.tgz#26a94c5391bbca926152002f69b84a4bf772e5a8"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npm.alibaba-inc.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npm.alibaba-inc.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npm.alibaba-inc.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write@1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.alibaba-inc.com/write/download/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

ws@^6.2.1:
  version "6.2.2"
  resolved "https://registry.npm.alibaba-inc.com/ws/download/ws-6.2.2.tgz#dd5cdbd57a9979916097652d78f1cc5faea0c32e"
  integrity sha1-3Vzb1XqZeZFgl2UtePHMX66gwy4=
  dependencies:
    async-limiter "~1.0.0"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "https://registry.npm.alibaba-inc.com/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npm.alibaba-inc.com/y18n/download/y18n-4.0.3.tgz#b5f259c82cd6e336921efd7bfd8bf560de9eeedf"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.alibaba-inc.com/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npm.alibaba-inc.com/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yaml@^1.7.2:
  version "1.10.2"
  resolved "https://registry.npm.alibaba-inc.com/yaml/download/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "https://registry.npm.alibaba-inc.com/yargs-parser/download/yargs-parser-13.1.2.tgz#130f09702ebaeef2650d54ce6e3e5706f7a4fb38"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://registry.npm.alibaba-inc.com/yargs-parser/download/yargs-parser-18.1.3.tgz#be68c4975c6b2abf469236b0c870362fab09a7b0"
  integrity sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^8.0.0:
  version "8.1.0"
  resolved "https://registry.npm.alibaba-inc.com/yargs-parser/download/yargs-parser-8.1.0.tgz#f1376a33b6629a5d063782944da732631e966950"
  integrity sha1-8TdqM7Ziml0GN4KUTacyYx6WaVA=
  dependencies:
    camelcase "^4.1.0"

yargs@^13.3.2:
  version "13.3.2"
  resolved "https://registry.npm.alibaba-inc.com/yargs/download/yargs-13.3.2.tgz#ad7ffefec1aa59565ac915f82dccb38a9c31a2dd"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^15.3.1:
  version "15.4.1"
  resolved "https://registry.npm.alibaba-inc.com/yargs/download/yargs-15.4.1.tgz#0d87a16de01aee9d8bec2bfbf74f67851730f4f8"
  integrity sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

zscroller@~0.4.0:
  version "0.4.8"
  resolved "https://registry.npm.alibaba-inc.com/zscroller/download/zscroller-0.4.8.tgz#69eed68690808eedf81f9714014356b36cdd20f4"
  integrity sha1-ae7WhpCAju34H5cUAUNWs2zdIPQ=
  dependencies:
    babel-runtime "6.x"
