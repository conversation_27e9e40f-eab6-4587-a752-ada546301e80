# 商品库昆仑、饿百 PC 前端

## 安装

1. 安装 tnpm：npm i -g tnpm --registry=http://registry.npm.alibaba-inc.com
2. 安装 DEF SDK：tnpm i -g @ali/def
3. 执行 def --version，正常返回版本信息，表示安装成功
4. 执行 def login 完成用户登录注册

> 更多 def 相关信息请访问 [def](https://def.alibaba-inc.com/doc/start/install_sdk)

## 目录结构

```
src
├── assets
│   ├── buffet.png
│   └── urlMap.json
├── common
│   ├── js
│   │   └── mockCore.js
│   └── style
│       ├── _reboot.less
│       └── ....
├── source // ui和接口数据源
│   ├──serve
│   │   ├── benefit-datasource.js
│   │   └── ....
│   └── ui
│        ├── form-uisource.js
│        └── ....
├── components // render组件
│   ├── benefit.js
│   └── ...
├── store // 状态机
│   ├── benefit.js
│   └── ...
├── api // 接口请求
│   ├── index.js
│   └── ...
├── packages // json render的组件（之后会放在组件库里 @ali/newtrade）
│   ├── index.js
│   └── ...
├── utils
│   ├── index.js
│   └── ...
├── app // 业务代码
│   ├── index.js
│   ├── routes.js
│   └── ...
|
└── pages // 多平台入口
    ├── benefit
    │   ├── mock
    │   ├── index.html
    │   ├── index.jsx
        ├── ....jsx
    │   └── index.less
    └── cardcoupon
        ├── mock
        ├── index.html
        ├── index.jsx
        └── index.less
```

1. assets 包含静态资源，以及静态资源映射表
2. pages 为项目的入口和业务代码
3. utils 包含一些通用的工具函数
4. source 接口和 ui 数据源
5. components 包含公用组件及页面级别的组件
6. api 接口请求
7. store 状态机

## 使用

### 开发

1. **需要连 alibaba-inc wifi**
2. 必须使用 tnpm
3. tnpm i
4. def dev

### 本地 build

dev build

### 分支命名规范

> DEF 前端发布仓库分支规范如下：
> 发布分支名称格式为：{prefix}/{semver}, 其中 prefix 描述分支名称；semver 为本次发布的迭代版本号，格式需要遵循 semantic version。
> 如以下格式都满足规范
>
> feat-xxx/1.0.0: 新增 xxx 功能, 迭代版本号为 1.0.0
> feat-xxx/1.0.0-alpha.1: 新增 xxx 功能，迭代版本号为 1.0.0-alpha.1, 适用于 tnpm 发布流程的 alpha 版本包
> bugfix-xxx/1.0.0: 修复 xxx 问题，迭代版本号为 1.0.0
> daily/0.0.1: 历史兼容，不再推荐

### 日常发布

def p -d

### 发布线上

def p -o

## 技术细节

> 新昆仑主题包： [@alife/theme-nr-op](https://web.npm.alibaba-inc.com/package/@alife/theme-nr-op) > https://fusion.alibaba-inc.com/34021/setting/theme
> def 运行时环境变量： http://def.alibaba-inc.com/doc/build/env/README
> 模拟环境变量： # -- 后面表示传入云构建的环境变量
> def build -- --def_publish_type=webapp --def_publish_env=daily
> kunlun-base 包地址： https://web.npm.alibaba-inc.com/package/@alife/kunlun-base

### 多主题

在 babelrc 中 babel-plugin-import 的 fusion 相关配置不要配置样式，然后在 pages/\${entry}/index.jsx 里直接引用主题 css。

### 环境区分

```
 <!-- "BUILD_ARGV":"["--def_publish_type=webapp","--def_publish_pages=[\"index.html\",\"map.html\",\"search.html\",\"shopinfo.html\"]","--def_publish_env=daily"]" -->

 const build_argv = process.env.BUILD_ARGV ? JSON.parse(process.env.BUILD_ARGV) : []

  let publishEnv = 'local'
  if (build_argv.length !== 0) {
    build_argv.forEach(item => {
      const [option, value] = item.split('=')
      if (option === '--def_publish_env') {
        publishEnv = value
      }
   })
  }

```

### 依赖注释

- [@alife/waterMark](https://ysf-luna.alibaba.net/luna/homepage#/component/5b0e3e98862364086c018ea0)：添加明暗水印
- mock [https://mocks.alibaba-inc.com/](mock https://mocks.alibaba-inc.com/)


### auz benefit-kunlun
https://ppe-auz.faas.ele.me/home-page

https://ssomdc.rajax.me/auth/ssoselect


### acl菜单管理
https://acl-test.alibaba-inc.com/ecology-app/index.htm?__id=nav_menu

