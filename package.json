{"name": "market_kun", "version": "1.0.0", "description": "", "dependencies": {"@ali/aes-tracker": "^3.1.0", "@ali/aes-tracker-plugin-api": "^3.1.2", "@ali/aes-tracker-plugin-autolog": "^3.0.11", "@ali/aes-tracker-plugin-emogine": "^3.0.15", "@ali/aes-tracker-plugin-event": "^3.0.0", "@ali/aes-tracker-plugin-eventTiming": "^3.0.0", "@ali/aes-tracker-plugin-jserror": "^3.0.3", "@ali/aes-tracker-plugin-longtask": "^3.0.1", "@ali/aes-tracker-plugin-perf": "^3.1.0", "@ali/aes-tracker-plugin-pv": "^3.0.5", "@ali/aes-tracker-plugin-resourceError": "^3.0.4", "@ali/aes-tracker-plugin-survey": "^3.0.19", "@ali/boreas2": "0.7.5-beta.24995495", "@ali/imagespace-upload-sdk": "^0.1.9", "@ali/shell-sdk": "^0.0.9", "@alifd/next": "^1.19.13", "@alife/kunlun-base": "^1.2.3", "@alife/theme-nr-op": "^0.14.0", "@loadable/component": "^5.10.2", "axios": "^0.19.2", "babel-polyfill": "^6.26.0", "bignumber.js": "^9.0.0", "classnames": "^2.2.6", "dom-to-image": "^2.6.0", "graph": "^0.2.0", "immutable": "^4.0.0-rc.12", "invariant": "^2.2.4", "lodash": "^4.17.15", "mobx": "^5.13.0", "mobx-react": "^5.4.4", "mockjs": "^1.0.1-beta3", "moment": "^2.24.0", "object.omit": "^3.0.0", "prop-types": "^15.7.2", "qrcode": "^1.4.2", "qs": "^6.8.0", "react": "^16.13.1", "react-dom": "^16.13.1", "react-imported-component": "^6.0.5", "react-router": "^5.0.1", "react-router-dom": "^5.0.1"}, "devDependencies": {"@ali/builder-rum": "^2.0.0", "babel-cli": "^6.26.0", "babel-eslint": "^8.2.2", "babel-plugin-import": "^1.12.0", "babel-plugin-syntax-async-functions": "^6.13.0", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-decorators-legacy": "^1.3.5", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "eslint": "^5.5.0", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-autofix": "^0.0.7", "eslint-plugin-import": "2.14.0", "eslint-plugin-jest": "^21.7.1", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-react": "^7.0.0", "mockjs": "^1.0.1-beta3"}, "scripts": {"lint": "eslint ."}, "tnpm": {"mode": "yarn", "lockfile": "enable"}, "author": {"name": "zefang.zzf", "email": "<EMAIL>"}, "license": "ISC", "repository": "http://gitlab.alibaba-inc.com/eleme-b-newretail/new_kun.git"}